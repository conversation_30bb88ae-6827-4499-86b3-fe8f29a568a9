/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Tool } from "./Tool";

export class Brush extends Tool {
  protected name: string;
  protected BRUSH_POINT_SPACING: number;
  protected BRUSH_STABILIZER_LEVEL: number;
  protected POTRACE_RESOLUTION: number;
  protected MIN_PRESSURE: number;
  protected croquis: any;
  protected croquisDOMElement: HTMLElement | null;
  protected croquisBrush: any;
  protected cachedCursor: string | null;
  protected lastPressure: number | null;
  protected errorOccured: boolean;
  protected _isInProgress: boolean;
  protected _croquisStartTimeout: NodeJS.Timeout | null;
  protected strokeBounds: any;
  protected _lastMousePoint: any;
  protected _lastMousePressure: number;
  protected _currentDrawingFrame: any;
  protected project: any;

  static get CROQUIS_WAIT_AMT_MS(): number {
    return 100;
  }

  get doubleClickEnabled(): boolean {
    return false;
  }

  /**
   * Creates the brush tool.
   */
  constructor() {
    super();

    this.name = "brush";

    this.BRUSH_POINT_SPACING = 0.2;
    this.BRUSH_STABILIZER_LEVEL = 3;
    this.POTRACE_RESOLUTION = 1.0;

    this.MIN_PRESSURE = 0.14;

    this.croquis = null;
    this.croquisDOMElement = null;
    this.croquisBrush = null;

    this.cachedCursor = null;

    this.lastPressure = null;

    this.errorOccured = false;

    this._isInProgress = false;

    this._croquisStartTimeout = null;

    // These are used to crop the final path image.
    this.strokeBounds = new (this.paper as any).Rectangle();
    this._lastMousePoint = new (this.paper as any).Point(0, 0);
    this._lastMousePressure = 1;

    // The frame that the brush started the current stroke on.
    this._currentDrawingFrame = null;
  }

  get cursor(): string {
    // the brush cursor is done in a custom way using _regenCursor().
    return "default";
  }

  get isDrawingTool(): boolean {
    return true;
  }

  onActivate(e: any): void {
    if (this._isInProgress) {
      this.finishStrokeEarly();
    }

    if (!this.croquis) {
      this.croquis = new (window as any).Croquis();
      this.croquis.setCanvasSize(500, 500);
      this.croquis.addLayer();
      this.croquis.fillLayer("rgba(0,0,0,0)");
      this.croquis.addLayer();
      this.croquis.selectLayer(1);
      this.croquis.lockHistory();

      this.croquisBrush = new (window as any).Croquis.Brush();
      this.croquis.setTool(this.croquisBrush);

      this.croquisDOMElement = this.croquis.getDOMElement();
      if (this.croquisDOMElement) {
        this.croquisDOMElement.style.position = "absolute";
        this.croquisDOMElement.style.left = "0px";
        this.croquisDOMElement.style.top = "0px";
        this.croquisDOMElement.style.width = "100%";
        this.croquisDOMElement.style.height = "100%";
        this.croquisDOMElement.style.display = "block";
        this.croquisDOMElement.style.pointerEvents = "none";
      }
    }

    this._isInProgress = false;

    this._lastMousePoint = new (this.paper as any).Point(0, 0);
    this._lastMousePressure = 1;
  }

  onDeactivate(e: any): void {
    // This prevents croquis from leaving stuck brush strokes on the screen.
    this.finishStrokeEarly();
  }

  onMouseMove(e: any): void {
    super.onMouseMove(e);

    this._updateCanvasAttributes();
    this._regenCursor();
  }

  onMouseDown(e: any): void {
    if (this._isInProgress) {
      this.discard();
    }

    this._currentDrawingFrame = this.project.activeFrame;

    clearTimeout(this._croquisStartTimeout as NodeJS.Timeout);
    this._isInProgress = true;

    this._updateCanvasAttributes();

    // Update croquis params
    this.croquisBrush.setSize(this._getRealBrushSize());
    this.croquisBrush.setColor(this.getSetting("fillColor").hex);
    this.croquisBrush.setSpacing(this.BRUSH_POINT_SPACING);
    this.croquis.setToolStabilizeLevel(this.BRUSH_STABILIZER_LEVEL);
    this.croquis.setToolStabilizeWeight(
      this.getSetting("brushStabilizerWeight") / 100.0 + 0.3
    );
    this.croquis.setToolStabilizeInterval(1);

    // Forward mouse event to croquis canvas
    const point = this._croquisToPaperPoint(e.point);
    this._updateStrokeBounds(point);
    try {
      this._updateLastMouseState(point, this.pressure);
      this.croquis.down(point.x, point.y, this.pressure);
    } catch (e) {
      this.handleBrushError(e);
      return;
    }
  }

  onMouseDrag(e: any): void {
    if (!this._isInProgress) return;

    // Forward mouse event to croquis canvas
    const point = this._croquisToPaperPoint(e.point);
    this._updateStrokeBounds(point);
    try {
      this._updateLastMouseState(point, this.pressure);
      this.croquis.move(point.x, point.y, this.pressure);
    } catch (e) {
      this.handleBrushError(e);
      return;
    }

    this.lastPressure = this.pressure;
  }

  onMouseUp(e: any): void {
    if (!this._isInProgress) return;
    this._isInProgress = false;

    const point = this._croquisToPaperPoint(e.point);
    this._calculateStrokeBounds(point);

    try {
      this.croquis.up(point.x, point.y, this.lastPressure || 0);
    } catch (e) {
      this.handleBrushError(e);
      return;
    }

    this._potraceCroquisCanvas(point);
  }

  /**
   * The current amount of pressure applied to the paper js canvas this tool belongs to.
   */
  get pressure(): number {
    if (this.getSetting("pressureEnabled")) {
      const pressure = this.paper.view.pressure;
      return this.convertRange(pressure, [0, 1], [this.MIN_PRESSURE, 1]);
    } else {
      return 1;
    }
  }

  /**
   * Croquis throws a lot of errors. This is a helpful function to handle those errors gracefully.
   */
  handleBrushError(e: any): void {
    this._isInProgress = false;
    console.error("Brush Error:", e);
    this.errorOccured = true;
  }

  // Helper methods (to be implemented)
  protected _updateCanvasAttributes(): void {}
  protected _regenCursor(): void {}
  protected _getRealBrushSize(): number {
    return 0;
  }
  protected getSetting(name: string): any {
    return {};
  }
  protected _croquisToPaperPoint(point: any): any {
    return point;
  }
  protected _updateStrokeBounds(point: any): void {}
  protected _updateLastMouseState(point: any, pressure: number): void {}
  protected _calculateStrokeBounds(point: any): void {}
  protected _potraceCroquisCanvas(point: any): void {}
  protected finishStrokeEarly(): void {}
  protected discard(): void {}
  protected convertRange(value: number, r1: number[], r2: number[]): number {
    return ((value - r1[0]) * (r2[1] - r2[0])) / (r1[1] - r1[0]) + r2[0];
  }
}
