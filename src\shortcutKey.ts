/**
 * 快捷键管理模块
 * 处理全局快捷键事件
 */

import { saveOperationRecords } from './projectPro';
import { saveProject } from './save';
import { save } from '@tauri-apps/plugin-dialog';
import { getCurrentProjectPath, setCurrentProjectPath } from './stores/projectPathStore';

/**
 * 快捷键配置接口
 */
interface ShortcutConfig {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  action: () => void;
  description: string;
}

// 全局单例键名
const GLOBAL_SHORTCUT_MANAGER_KEY = '__gamespriteStudioShortcutManager';

/**
 * 快捷键管理器类 - 真正的全局单例模式
 */
class ShortcutKeyManager {
  private shortcuts: Map<string, ShortcutConfig> = new Map();
  private isInitialized = false;
  private boundHandleKeyDown?: (event: KeyboardEvent) => void;

  // 私有构造函数，防止外部实例化
  private constructor() {}

  /**
   * 获取全局单例实例
   */
  public static getInstance(): ShortcutKeyManager {
    console.log('🔍 ShortcutKeyManager.getInstance() 被调用');

    if (typeof window !== 'undefined') {
      // 检查现有实例
      const existingInstance = (window as any)[GLOBAL_SHORTCUT_MANAGER_KEY];
      console.log('🔍 现有实例:', existingInstance ? '存在' : '不存在');

      // 先清理可能存在的旧实例
      if (existingInstance) {
        console.log('🧹 清理现有实例的事件监听器');
        if (existingInstance.boundHandleKeyDown) {
          document.removeEventListener('keydown', existingInstance.boundHandleKeyDown);
          console.log('🧹 移除了旧的事件监听器');
        }
      }

      // 创建或获取全局实例
      if (!existingInstance) {
        console.log('🆕 创建新的全局实例');
        (window as any)[GLOBAL_SHORTCUT_MANAGER_KEY] = new ShortcutKeyManager();
      } else {
        console.log('♻️ 重用现有全局实例');
      }

      const instance = (window as any)[GLOBAL_SHORTCUT_MANAGER_KEY];
      console.log('🎯 返回实例:', instance);
      return instance;
    }

    // 服务端渲染时的后备方案
    console.log('🖥️ 服务端渲染，创建临时实例');
    return new ShortcutKeyManager();
  }

  /**
   * 销毁全局单例实例
   */
  public static destroyInstance(): void {
    if (typeof window !== 'undefined' && (window as any)[GLOBAL_SHORTCUT_MANAGER_KEY]) {
      const instance = (window as any)[GLOBAL_SHORTCUT_MANAGER_KEY];
      instance.destroy();
      delete (window as any)[GLOBAL_SHORTCUT_MANAGER_KEY];
    }
  }

  /**
   * 初始化快捷键管理器
   */
  init(): void {
    console.log('🚀 ShortcutKeyManager.init() 被调用');
    console.log('🔍 当前初始化状态:', this.isInitialized);
    console.log('🔍 当前实例:', this);

    if (this.isInitialized) {
      console.warn('⚠️ ShortcutKeyManager: 已经初始化过了，跳过');
      return;
    }

    console.log('🎯 ShortcutKeyManager: 开始初始化快捷键系统');

    // 注册默认快捷键
    console.log('📝 注册默认快捷键');
    this.registerDefaultShortcuts();

    // 绑定事件处理器并添加全局键盘事件监听器
    console.log('🎧 绑定事件处理器');
    this.boundHandleKeyDown = this.handleKeyDown.bind(this);
    document.addEventListener('keydown', this.boundHandleKeyDown);
    console.log('🎧 事件监听器已添加:', this.boundHandleKeyDown);

    // 跟踪这个监听器
    if (typeof window !== 'undefined') {
      if (!(window as any).__allKeydownListeners) {
        (window as any).__allKeydownListeners = [];
      }
      (window as any).__allKeydownListeners.push(this.boundHandleKeyDown);
      console.log('🎧 监听器已记录，总数:', (window as any).__allKeydownListeners.length);
    }

    this.isInitialized = true;
    console.log('✅ ShortcutKeyManager: 快捷键系统初始化完成');
  }

  /**
   * 注册默认快捷键
   */
  private registerDefaultShortcuts(): void {
    console.log('ShortcutKeyManager: 注册默认快捷键');

    // Ctrl+O: 打开项目
    this.register({
      key: 'o',
      ctrl: true,
      action: this.handleOpenProject.bind(this),
      description: '打开项目'
    });

    // Ctrl+S: 保存项目
    this.register({
      key: 's',
      ctrl: true,
      action: this.handleSaveProject.bind(this),
      description: '保存项目'
    });

    // Ctrl+Shift+S: 另存为项目
    this.register({
      key: 's',
      ctrl: true,
      shift: true,
      action: this.handleSaveAsProject.bind(this),
      description: '另存为项目'
    });

    console.log('ShortcutKeyManager: 默认快捷键注册完成');
  }

  /**
   * 注册快捷键
   */
  register(config: ShortcutConfig): void {
    const key = this.getShortcutKey(config);
    this.shortcuts.set(key, config);
    console.log(`ShortcutKeyManager: 注册快捷键 ${key} - ${config.description}`);
  }

  /**
   * 注销快捷键
   */
  unregister(config: Omit<ShortcutConfig, 'action' | 'description'>): void {
    const key = this.getShortcutKey(config);
    if (this.shortcuts.delete(key)) {
      console.log(`ShortcutKeyManager: 注销快捷键 ${key}`);
    }
  }

  /**
   * 生成快捷键标识符
   */
  private getShortcutKey(config: Omit<ShortcutConfig, 'action' | 'description'>): string {
    const parts: string[] = [];

    if (config.ctrl) parts.push('ctrl');
    if (config.alt) parts.push('alt');
    if (config.shift) parts.push('shift');
    parts.push(config.key.toLowerCase());

    return parts.join('+');
  }

  /**
   * 处理键盘按下事件
   */
  private handleKeyDown(event: KeyboardEvent): void {
    // console.log('⌨️ handleKeyDown 被调用, 实例:', this);
    // console.log('⌨️ 按键事件:', event.key, event.ctrlKey, event.altKey, event.shiftKey);

    // 检查是否在输入框中
    if (this.isInputElement(event.target as Element)) {
      console.log('⌨️ 在输入框中，忽略');
      return;
    }

    // 构建当前按键组合
    const currentKey = this.getCurrentKey(event);
    // console.log('⌨️ 当前按键组合:', currentKey);

    // 如果是空的按键组合（比如只按修饰键），直接返回
    if (!currentKey) {
      return;
    }

    // 查找匹配的快捷键
    const shortcut = this.shortcuts.get(currentKey);
    // console.log('⌨️ 找到的快捷键:', shortcut);

    if (shortcut) {
      console.log(`🎯 ShortcutKeyManager: 触发快捷键 ${currentKey} - ${shortcut.description}`);

      // 阻止默认行为
      event.preventDefault();
      event.stopPropagation();

      // 执行快捷键动作
      try {
        shortcut.action();
      } catch (error) {
        console.error(`❌ ShortcutKeyManager: 执行快捷键 ${currentKey} 时出错:`, error);
      }
    }
  }

  /**
   * 获取当前按键组合
   */
  private getCurrentKey(event: KeyboardEvent): string {
    const parts: string[] = [];

    // 忽略修饰键本身，只处理实际的功能键
    const key = event.key.toLowerCase();

    // 如果按下的就是修饰键本身，不处理
    if (key === 'control' || key === 'alt' || key === 'shift' || key === 'meta') {
      return '';
    }

    if (event.ctrlKey) parts.push('ctrl');
    if (event.altKey) parts.push('alt');
    if (event.shiftKey) parts.push('shift');
    parts.push(key);

    return parts.join('+');
  }

  /**
   * 检查是否在输入元素中
   */
  private isInputElement(element: Element | null): boolean {
    if (!element) return false;

    const tagName = element.tagName.toLowerCase();
    const isInput = tagName === 'input' || tagName === 'textarea';
    const isContentEditable = element.getAttribute('contenteditable') === 'true';
    const isInInputContainer = element.closest('.MuiTextField-root') !== null ||
      element.closest('[contenteditable="true"]') !== null;

    return isInput || isContentEditable || isInInputContainer;
  }

  /**
   * 获取当前项目路径
   */
  private getCurrentProjectPath(): string | null {
    return getCurrentProjectPath();
  }

  /**
   * 设置当前项目路径
   */
  private setCurrentProjectPath(path: string | null): void {
    setCurrentProjectPath(path);
    console.log('📁 ShortcutKeyManager: 项目路径已更新:', path);
  }

  /**
   * 处理打开项目快捷键 (Ctrl+O)
   */
  private async handleOpenProject(): Promise<void> {
    console.log('ShortcutKeyManager: 执行打开项目操作 (Ctrl+O)');

    try {
      // 动态导入加载功能
      const { loadProject } = await import('./save');

      // 使用文件对话框让用户选择文件
      const { open } = await import('@tauri-apps/plugin-dialog');

      const result = await open({
        directory: false,
        multiple: false,
        title: '打开 GameSprite Studio 项目文件',
        filters: [
          {
            name: 'GameSprite Studio Project',
            extensions: ['gss']
          },
          {
            name: 'All Files',
            extensions: ['*']
          }
        ]
      });

      if (result && typeof result === 'string') {
        console.log('📂 用户选择了项目文件:', result);

        // 调用加载功能
        const loadResult = await loadProject(result);

        if (loadResult.success) {
          // 加载成功后记录项目路径
          this.setCurrentProjectPath(result);
          console.log('✅ 项目加载成功');

          // 显示警告信息（如果有）
          if (loadResult.warnings && loadResult.warnings.length > 0) {
            const warningMsg = `项目加载成功，但有以下警告:\n${loadResult.warnings.join('\n')}`;
            alert(warningMsg);
          } else {
            alert('项目加载成功！');
          }

        } else {
          console.error('❌ 项目加载失败:', loadResult.error);
          alert(`项目加载失败: ${loadResult.error}`);
        }
      } else {
        console.log('ShortcutKeyManager: 用户取消了文件选择');
      }

    } catch (error) {
      console.error('ShortcutKeyManager: 打开项目时出错:', error);
      alert(`打开项目失败: ${error}`);
    }
  }

  /**
   * 处理保存项目快捷键 (Ctrl+S)
   */
  private async handleSaveProject(): Promise<void> {
    console.log('ShortcutKeyManager: 执行保存项目操作 (Ctrl+S)');

    try {
      // 获取当前保存路径（从全局状态或其他地方）
      let filePath = this.getCurrentProjectPath();

      // 如果没有保存路径，弹出保存对话框
      if (!filePath) {
        const result = await save({
          title: '保存项目文件',
          defaultPath: 'untitled.gss',
          filters: [
            {
              name: 'GameSprite Studio Project',
              extensions: ['gss']
            },
            {
              name: 'All Files',
              extensions: ['*']
            }
          ]
        });

        if (result && typeof result === 'string') {
          filePath = result.endsWith('.gss') ? result : `${result}.gss`;
          console.log('ShortcutKeyManager: 用户选择保存路径:', filePath);
        } else {
          console.log('ShortcutKeyManager: 用户取消保存操作');
          return;
        }
      } else {
        console.log('ShortcutKeyManager: 使用已有保存路径:', filePath);
      }

      // 从文件路径提取项目名称
      const fileName = filePath.split(/[/\\]/).pop() || 'Untitled';
      const projectName = fileName.replace('.gss', '');

      // 调用保存功能
      const saveResult = await saveProject(
        filePath,
        projectName,
        '通过快捷键 Ctrl+S 保存的项目',
        ['quicksave']
      );

      if (saveResult.success) {
        // 保存成功后记录路径
        this.setCurrentProjectPath(filePath);
        console.log('✅ ShortcutKeyManager: 项目保存成功', {
          filePath: saveResult.file_path,
          fileSize: saveResult.file_size
        });

        // 可以在这里添加成功提示
        // 例如显示一个 toast 通知
      } else {
        console.error('❌ ShortcutKeyManager: 项目保存失败', saveResult.error);

        // 可以在这里添加错误提示
        alert(`保存失败: ${saveResult.error}`);
      }

    } catch (error) {
      console.error('ShortcutKeyManager: 保存项目时出错:', error);
      alert(`保存项目时出错: ${error}`);
    }
  }

  /**
   * 处理另存为项目快捷键 (Ctrl+Shift+S)
   */
  private async handleSaveAsProject(): Promise<void> {
    console.log('ShortcutKeyManager: 执行另存为项目操作 (Ctrl+Shift+S)');

    try {
      // 打开另存为文件对话框
      const result = await save({
        title: '另存为项目文件',
        defaultPath: 'untitled.gss',
        filters: [
          {
            name: 'GameSprite Studio Project',
            extensions: ['gss']
          },
          {
            name: 'All Files',
            extensions: ['*']
          }
        ]
      });

      if (result && typeof result === 'string') {
        console.log('ShortcutKeyManager: 用户选择另存为路径:', result);

        // 确保文件扩展名为 .gss
        const filePath = result.endsWith('.gss') ? result : `${result}.gss`;

        // 从文件路径提取项目名称
        const fileName = filePath.split(/[/\\]/).pop() || 'Untitled';
        const projectName = fileName.replace('.gss', '');

        // 调用保存功能
        const saveResult = await saveProject(
          filePath,
          projectName,
          '通过快捷键 Ctrl+Shift+S 另存为的项目',
          ['saveas']
        );

        if (saveResult.success) {
          // 另存为成功后更新当前项目路径
          this.setCurrentProjectPath(filePath);
          console.log('✅ ShortcutKeyManager: 项目另存为成功', {
            filePath: saveResult.file_path,
            fileSize: saveResult.file_size
          });

          // 可以在这里添加成功提示
          alert('项目另存为成功！');
        } else {
          console.error('❌ ShortcutKeyManager: 项目另存为失败', saveResult.error);

          // 可以在这里添加错误提示
          alert(`另存为失败: ${saveResult.error}`);
        }
      } else {
        console.log('ShortcutKeyManager: 用户取消另存为操作');
      }

    } catch (error) {
      console.error('ShortcutKeyManager: 另存为项目时出错:', error);
      alert(`另存为项目时出错: ${error}`);
    }
  }

  /**
   * 获取所有已注册的快捷键
   */
  getRegisteredShortcuts(): Array<{ key: string; description: string }> {
    return Array.from(this.shortcuts.entries()).map(([key, config]) => ({
      key,
      description: config.description
    }));
  }

  /**
   * 销毁快捷键管理器
   */
  destroy(): void {
    if (!this.isInitialized) return;

    console.log('ShortcutKeyManager: 销毁快捷键系统');

    // 移除事件监听器
    if (this.boundHandleKeyDown) {
      document.removeEventListener('keydown', this.boundHandleKeyDown);
      this.boundHandleKeyDown = undefined;
    }

    // 清空快捷键
    this.shortcuts.clear();

    this.isInitialized = false;
    console.log('ShortcutKeyManager: 快捷键系统已销毁');
  }
}

/**
 * 初始化快捷键系统
 */
export function initShortcutKeys(): void {
  console.log('🎬 initShortcutKeys() 被调用');
  const manager = ShortcutKeyManager.getInstance();
  console.log('🎬 获取到管理器实例:', manager);
  manager.init();
  console.log('🎬 initShortcutKeys() 完成');
}

/**
 * 注册快捷键
 */
export function registerShortcut(config: ShortcutConfig): void {
  const manager = ShortcutKeyManager.getInstance();
  manager.register(config);
}

/**
 * 注销快捷键
 */
export function unregisterShortcut(config: Omit<ShortcutConfig, 'action' | 'description'>): void {
  const manager = ShortcutKeyManager.getInstance();
  manager.unregister(config);
}

/**
 * 获取所有已注册的快捷键
 */
export function getRegisteredShortcuts(): Array<{ key: string; description: string }> {
  const manager = ShortcutKeyManager.getInstance();
  return manager.getRegisteredShortcuts();
}

/**
 * 销毁快捷键系统
 */
export function destroyShortcutKeys(): void {
  ShortcutKeyManager.destroyInstance();
}

/**
 * 默认导出
 */
export default {
  init: initShortcutKeys,
  register: registerShortcut,
  unregister: unregisterShortcut,
  getRegisteredShortcuts,
  destroy: destroyShortcutKeys
};

// 调试函数：检查当前事件监听器数量
(window as any).debugShortcutKeys = function() {
  console.log('🔍 调试快捷键系统:');
  console.log('🔍 全局管理器实例:', (window as any)[GLOBAL_SHORTCUT_MANAGER_KEY]);

  // 检查 document 上的事件监听器（这个方法在开发工具中可用）
  if ((window as any).getEventListeners) {
    const listeners = (window as any).getEventListeners(document);
    console.log('🔍 document 上的事件监听器:', listeners);
  }

  // 检查我们的全局实例
  const manager = (window as any)[GLOBAL_SHORTCUT_MANAGER_KEY];
  if (manager) {
    console.log('🔍 管理器初始化状态:', manager.isInitialized);
    console.log('🔍 管理器绑定的处理器:', manager.boundHandleKeyDown);
    console.log('🔍 管理器注册的快捷键:', manager.shortcuts);
  }
};

// 模块加载时的信息输出
console.log('📦 ShortcutKey 模块已加载');
console.log('📦 使用 initShortcutKeys() 来初始化快捷键系统');
console.log('📦 默认快捷键:');
console.log('📦   Ctrl+S - 保存项目');
console.log('📦 调试命令: window.debugShortcutKeys()');

// 模块加载时强制清理所有旧的快捷键监听器
if (typeof window !== 'undefined') {
  console.log('🧹 模块加载时开始清理旧的事件监听器');

  // 检查现有管理器
  const existingManager = (window as any)[GLOBAL_SHORTCUT_MANAGER_KEY];
  if (existingManager) {
    console.log('⚠️ 发现现有的管理器实例，强制清理');
    console.log('⚠️ 现有实例:', existingManager);

    // 强制销毁现有实例
    if (existingManager.boundHandleKeyDown) {
      document.removeEventListener('keydown', existingManager.boundHandleKeyDown);
      console.log('🧹 移除了现有实例的事件监听器');
    }
    existingManager.isInitialized = false;
    existingManager.shortcuts.clear();
  }

  // 清理所有已知的快捷键监听器
  const allListeners = (window as any).__allKeydownListeners || [];
  console.log('🧹 清理', allListeners.length, '个已知的快捷键监听器');
  allListeners.forEach((listener: EventListener) => {
    try {
      document.removeEventListener('keydown', listener);
    } catch (e) {
      // 忽略清理错误
    }
  });
  (window as any).__allKeydownListeners = [];

  console.log('🧹 旧监听器清理完成');
}