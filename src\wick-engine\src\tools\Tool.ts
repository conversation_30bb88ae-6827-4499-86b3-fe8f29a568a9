/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

interface EventCallback {
  (e: any): void;
}

interface EventCallbacks {
  [key: string]: EventCallback;
}

export class Tool {
  protected paperTool: any;
  protected _eventCallbacks: EventCallbacks;
  protected _lastMousedownTimestamp: number | null;
  protected _lastMousedownPoint: any;
  protected doubleClickEnabled: boolean;

  static get DOUBLE_CLICK_TIME(): number {
    return 300;
  }

  static get DOUBLE_CLICK_MAX_DISTANCE(): number {
    return 20;
  }

  /**
   * Creates a new Wick Tool.
   */
  constructor() {
    this.paperTool = new (this.paper as any).Tool();
    this._eventCallbacks = {};
    this._lastMousedownTimestamp = null;
    this.doubleClickEnabled = true;

    // Attach onActivate event
    this.paperTool.onActivate = (e: any) => {
      this.onActivate(e);
    };

    // Attach onDeactivate event
    this.paperTool.onDeactivate = (e: any) => {
      this.onDeactivate(e);
    };

    // Attach mouse move event
    this.paperTool.onMouseMove = (e: any) => {
      this.onMouseMove(e);
    };

    // Attach mouse down + double click event
    this.paperTool.onMouseDown = (e: any) => {
      if (
        this.doubleClickEnabled &&
        this._lastMousedownTimestamp !== null &&
        e.timeStamp - this._lastMousedownTimestamp < Tool.DOUBLE_CLICK_TIME &&
        e.point.subtract(this._lastMousedownPoint).length <
          Tool.DOUBLE_CLICK_MAX_DISTANCE
      ) {
        this.onDoubleClick(e);
      } else {
        this.onMouseDown(e);
      }
      this._lastMousedownTimestamp = e.timeStamp;
      this._lastMousedownPoint = e.point;
    };

    // Attach key events
    this.paperTool.onKeyDown = (e: any) => {
      this.onKeyDown(e);
    };
    this.paperTool.onKeyUp = (e: any) => {
      this.onKeyUp(e);
    };

    // Attach mouse move event
    this.paperTool.onMouseDrag = (e: any) => {
      this.onMouseDrag(e);
    };

    // Attach mouse up event
    this.paperTool.onMouseUp = (e: any) => {
      this.onMouseUp(e);
    };
  }

  /**
   * The paper.js scope to use.
   */
  get paper(): any {
    return (window as any).Wick.View.paperScope;
  }

  /**
   * The CSS cursor to display for this tool.
   */
  get cursor(): string {
    console.warn("Warning: Tool is missing a cursor!");
    return "default";
  }

  /**
   * Called when the tool is activated
   */
  onActivate(e: any): void {}

  /**
   * Called when the tool is deactivated (another tool is activated)
   */
  onDeactivate(e: any): void {}

  /**
   * Called when the mouse moves and the tool is active.
   */
  onMouseMove(e: any): void {
    this.setCursor(this.cursor);
  }

  /**
   * Called when the mouse clicks the paper.js canvas and this is the active tool.
   */
  onMouseDown(e: any): void {}

  /**
   * Called when the mouse is dragged on the paper.js canvas and this is the active tool.
   */
  onMouseDrag(e: any): void {}

  /**
   * Called when the mouse is clicked on the paper.js canvas and this is the active tool.
   */
  onMouseUp(e: any): void {}

  /**
   * Called when the mouse double clicks on the paper.js canvas and this is the active tool.
   */
  onDoubleClick(e: any): void {}

  /**
   * Called when a key is pressed and this is the active tool.
   */
  onKeyDown(e: any): void {}

  /**
   * Called when a key is released and this is the active tool.
   */
  onKeyUp(e: any): void {}

  /**
   * Should reset the state of the tool.
   */
  reset(): void {}

  /**
   * Activates this tool in paper.js.
   */
  activate(): void {
    this.paperTool.activate();
  }

  /**
   * Sets the cursor of the paper.js canvas that the tool belongs to.
   * @param {string} cursor - a CSS cursor style
   */
  setCursor(cursor: string): void {
    (this.paper.view._element as HTMLElement).style.cursor = cursor;
  }

  /**
   * Attach a function to get called when an event happens.
   * @param {string} eventName - the name of the event
   * @param {function} fn - the function to call when the event is fired
   */
  on(eventName: string, fn: EventCallback): void {
    this._eventCallbacks[eventName] = fn;
  }

  /**
   * Call the functions attached to a given event.
   * @param {string} eventName - the name of the event to fire
   * @param {object} e - (optional) an object to attach some data to, if needed
   * @param {string} actionName - Name of the action committed.
   */
  fire(eventName: string, e: any = {}, actionName?: string): void {
    if (this._eventCallbacks[eventName]) {
      this._eventCallbacks[eventName](e);
    }
  }
}
