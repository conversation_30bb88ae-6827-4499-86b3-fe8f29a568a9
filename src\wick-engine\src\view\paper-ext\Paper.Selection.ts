/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import * as paper from "paper";

interface SelectionTransformation {
  x: number;
  y: number;
  scaleX: number;
  scaleY: number;
  rotation: number;
  originX: number;
  originY: number;
}

interface SelectionConstructorArgs {
  layer?: paper.Layer;
  items?: paper.Item[];
  x?: number;
  y?: number;
  scaleX?: number;
  scaleY?: number;
  rotation?: number;
  originX?: number;
  originY?: number;
}

class Selection {
  private _layer: paper.Layer;
  private _items: paper.Item[];
  private _transformation: SelectionTransformation;
  private _untransformedBounds: paper.Rectangle;
  private _lockScalingToAspectRatio: boolean;
  private _gui: any; // paper.SelectionGUI type

  constructor(args: SelectionConstructorArgs = {}) {
    this._lockScalingToAspectRatio = false;

    this._layer = args.layer || paper.project.activeLayer;
    this._items = args.items || [];
    this._transformation = {
      x: args.x || 0,
      y: args.y || 0,
      scaleX: args.scaleX || 1.0,
      scaleY: args.scaleY || 1.0,
      rotation: args.rotation || 0,
      originX: 0,
      originY: 0,
    };

    this._untransformedBounds = Selection._getBoundsOfItems(this._items);

    if (args.originX !== undefined) {
      this._transformation.originX = args.originX;
    } else {
      this._transformation.originX = this._untransformedBounds.center.x;
    }

    if (args.originY !== undefined) {
      this._transformation.originY = args.originY;
    } else {
      this._transformation.originY = this._untransformedBounds.center.y;
    }

    this._rebuild();
  }

  get layer(): paper.Layer {
    return this._layer;
  }

  get items(): paper.Item[] {
    return this._items;
  }

  set items(items: paper.Item[]) {
    this._destroy(false);
    this._items = items;
  }

  get transformation(): SelectionTransformation {
    return JSON.parse(JSON.stringify(this._transformation));
  }

  set transformation(transformation: SelectionTransformation) {
    this._destroy(true);
    this._transformation = transformation;
    this._create();
  }

  updateTransformation(
    newTransformation: Partial<SelectionTransformation>
  ): void {
    this.transformation = Object.assign(this.transformation, newTransformation);
  }

  get lockScalingToAspectRatio(): boolean {
    return this._lockScalingToAspectRatio;
  }

  set lockScalingToAspectRatio(lockScalingToAspectRatio: boolean) {
    this._lockScalingToAspectRatio = lockScalingToAspectRatio;
  }

  get position(): paper.Point {
    return this._untransformedBounds.topLeft.transform(this._matrix);
  }

  set position(position: paper.Point) {
    const d = position.subtract(this.position);
    this.updateTransformation({
      x: this.transformation.x + d.x,
      y: this.transformation.y + d.y,
    });
  }

  get origin(): paper.Point {
    return new paper.Point(
      this._transformation.originX,
      this._transformation.originY
    ).transform(this._matrix);
  }

  set origin(origin: paper.Point) {
    const d = origin.subtract(this.origin);
    this.updateTransformation({
      x: this.transformation.x + d.x,
      y: this.transformation.y + d.y,
    });
  }

  get width(): number {
    return this._untransformedBounds.width * this.transformation.scaleX;
  }

  set width(width: number) {
    const d = this.width / width;
    this.updateTransformation({
      scaleX: this.transformation.scaleX / d,
    });
  }

  get height(): number {
    return this._untransformedBounds.height * this.transformation.scaleY;
  }

  set height(height: number) {
    const d = this.height / height;
    this.updateTransformation({
      scaleY: this.transformation.scaleY / d,
    });
  }

  get scaleX(): number {
    return this.transformation.scaleX;
  }

  set scaleX(scaleX: number) {
    this.updateTransformation({ scaleX });
  }

  get scaleY(): number {
    return this.transformation.scaleY;
  }

  set scaleY(scaleY: number) {
    this.updateTransformation({ scaleY });
  }

  get rotation(): number {
    return this.transformation.rotation;
  }

  set rotation(rotation: number) {
    this.updateTransformation({ rotation });
  }

  getPathAttribute(attributeName: string): any {
    if (this.items.length === 0) {
      console.error("getPathAttribute(): Nothing in the selection!");
    }
    return this.items[0][attributeName];
  }

  setPathAttrribute(attributeName: string, attributeValue: any): void {
    this.items.forEach((item) => {
      item[attributeName] = attributeValue;
    });
  }

  get fillColor(): paper.Color {
    return this.getPathAttribute("fillColor");
  }

  set fillColor(fillColor: paper.Color) {
    this.setPathAttrribute("fillColor", fillColor);
  }

  get strokeColor(): paper.Color {
    return this.getPathAttribute("strokeColor");
  }

  set strokeColor(strokeColor: paper.Color) {
    this.setPathAttrribute("strokeColor", strokeColor);
  }

  get strokeWidth(): number {
    return this.getPathAttribute("strokeWidth");
  }

  set strokeWidth(strokeWidth: number) {
    this.setPathAttrribute("strokeWidth", strokeWidth);
  }

  flipHorizontally(): void {
    this.updateTransformation({
      scaleX: -this.transformation.scaleX,
    });
  }

  flipVertically(): void {
    this.updateTransformation({
      scaleY: -this.transformation.scaleY,
    });
  }

  moveForwards(): void {
    Selection._sortItemsByLayer(this._items).forEach((items) => {
      Selection._sortItemsByZIndex(items)
        .reverse()
        .forEach((item) => {
          if (
            item.nextSibling &&
            this._items.indexOf(item.nextSibling) === -1
          ) {
            item.insertAbove(item.nextSibling);
          }
        });
    });
  }

  moveBackwards(): void {
    Selection._sortItemsByLayer(this._items).forEach((items) => {
      Selection._sortItemsByZIndex(items).forEach((item) => {
        if (
          item.previousSibling &&
          this._items.indexOf(item.previousSibling) === -1
        ) {
          item.insertBelow(item.previousSibling);
        }
      });
    });
  }

  bringToFront(): void {
    Selection._sortItemsByLayer(this._items).forEach((items) => {
      Selection._sortItemsByZIndex(items).forEach((item) => {
        item.bringToFront();
      });
    });
  }

  sendToBack(): void {
    Selection._sortItemsByLayer(this._items).forEach((items) => {
      Selection._sortItemsByZIndex(items)
        .reverse()
        .forEach((item) => {
          item.sendToBack();
        });
    });
  }

  nudge(x: number, y: number): void {
    this.position = this.position.add(new paper.Point(x, y));
  }

  moveHandleAndScale(handleName: string, position: paper.Point): void {
    const newHandlePosition = position;
    const currentHandlePosition = this._untransformedBounds[handleName];

    currentHandlePosition.add(
      new paper.Point(this.transformation.x, this.transformation.y)
    );

    newHandlePosition.subtract(this.origin);
    currentHandlePosition.subtract(this.origin);

    newHandlePosition.rotate(-this.rotation, new paper.Point(0, 0));

    const newScale = newHandlePosition.divide(currentHandlePosition);

    const lockYScale =
      handleName === "leftCenter" || handleName === "rightCenter";
    const lockXScale =
      handleName === "bottomCenter" || handleName === "topCenter";

    if (lockXScale) newScale.x = this.transformation.scaleX;
    if (lockYScale) newScale.y = this.transformation.scaleY;

    this.updateTransformation({
      scaleX: newScale.x,
      scaleY: this.lockScalingToAspectRatio ? newScale.x : newScale.y,
    });
  }

  moveHandleAndRotate(handleName: string, position: paper.Point): void {
    const newHandlePosition = position;
    const currentHandlePosition = this._untransformedBounds[handleName];

    currentHandlePosition.transform(this._matrix);

    newHandlePosition.subtract(this.origin);
    currentHandlePosition.subtract(this.origin);

    const angleDiff = newHandlePosition.angle - currentHandlePosition.angle;

    this.updateTransformation({
      rotation: this.transformation.rotation + angleDiff,
    });
  }

  private _rebuild(): void {
    if (this._gui) {
      this._gui.destroy();
    }

    this._gui = new (paper as any).SelectionGUI({
      items: this._items,
      transformation: this._transformation,
      bounds: this._untransformedBounds,
    });

    if (this._items.length > 0) {
      this._layer.addChild(this._gui.item);
    }
  }

  private _destroy(): void {
    this._gui.destroy();
  }

  private static _getBoundsOfItems(items: paper.Item[]): paper.Rectangle {
    if (items.length === 0) {
      return new paper.Rectangle();
    }

    let bounds: paper.Rectangle | null = null;
    items.forEach((item) => {
      bounds = bounds ? bounds.unite(item.bounds) : item.bounds;
    });

    return bounds!;
  }

  private static _sortItemsByLayer(items: paper.Item[]): paper.Item[][] {
    const layerLists: { [key: string]: paper.Item[] } = {};

    items.forEach((item) => {
      const layerID = item.layer.id;
      if (!layerLists[layerID]) {
        layerLists[layerID] = [];
      }
      layerLists[layerID].push(item);
    });

    const layerItemsArrays: paper.Item[][] = [];
    for (const layerID in layerLists) {
      layerItemsArrays.push(layerLists[layerID]);
    }
    return layerItemsArrays;
  }

  private static _sortItemsByZIndex(items: paper.Item[]): paper.Item[] {
    return items.sort((a, b) => a.index - b.index);
  }
}

(paper as any).PaperScope.inject({
  Selection: Selection,
});
