/**
 * 合并设置Store - 管理图集合并的全局设置
 */

import { writable } from 'svelte/store';

// 合并设置接口
export interface MergeSettings {
  atlasName: string;
  maxWidth: number;
  maxHeight: number;
  padding: number; // 图片间距
  algorithm: 'maxrects' | 'shelf' | 'guillotine'; // 布局算法
  powerOfTwo: boolean; // 尺寸为2的幂
  allowRotation: boolean; // 允许旋转
  outputFormat: 'png' | 'jpg' | 'webp'; // 输出格式
  quality: number; // 图片质量
  generateData: boolean; // 生成数据文件
  dataFormat: 'json' | 'xml' | 'txt'; // 数据格式
}

// 默认设置
const defaultSettings: MergeSettings = {
  atlasName: '新图集',
  maxWidth: 2048,
  maxHeight: 2048,
  padding: 2,
  algorithm: 'maxrects',
  powerOfTwo: true,
  allowRotation: false,
  outputFormat: 'png',
  quality: 90,
  generateData: true,
  dataFormat: 'json'
};

// 创建store
export const mergeSettingsStore = writable<MergeSettings>(defaultSettings);

// 辅助函数
export const mergeSettingsActions = {
  // 更新设置
  updateSettings(newSettings: Partial<MergeSettings>) {
    mergeSettingsStore.update(settings => ({
      ...settings,
      ...newSettings
    }));
    console.log('🔄 MergeSettings: 设置已更新', newSettings);
  },

  // 重置为默认设置
  resetToDefaults() {
    mergeSettingsStore.set({ ...defaultSettings });
    console.log('🔄 MergeSettings: 重置为默认设置');
  },

  // 获取当前设置
  getCurrentSettings(): Promise<MergeSettings> {
    return new Promise((resolve) => {
      mergeSettingsStore.subscribe(settings => {
        resolve(settings);
      })();
    });
  }
};
