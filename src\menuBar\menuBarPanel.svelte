<script lang="ts">
  import { onMount } from 'svelte';
  import { Menu } from '@tauri-apps/api/menu';
  import { TauriAPI } from '../lib/tauriAPI';
  import { loadProject, saveProject } from '../save';
  import { open, save } from '@tauri-apps/plugin-dialog';

  // 导入项目路径状态管理
  import { getCurrentProjectPath, setCurrentProjectPath } from '../stores/projectPathStore';

  // 导入导出设置弹窗
  import ExportSettingsDialog from './ExportSettingsDialog.svelte';

  // 导入国际化相关
  import { supportedLocales, _ } from '../lib/i18n';
  import { currentLocale, changeLocale } from '../stores/localeStore';

  // 导入Toast提示
  import { toast } from '../stores/toastStore';



  // 菜单状态
  let menuCreated = $state(false);
  let isMaximized = $state(false);
  let isLoading = $state(false);

  // 导出设置弹窗状态
  let showExportSettings = $state(false);

  // 实现保存功能
  async function handleSaveGameSpriteProject() {
    console.log('=== 开始保存 GameSprite Studio 项目 ===');

    try {
      isLoading = true;

      let filePath = getCurrentProjectPath();

      // 如果没有保存路径，弹出保存对话框
      if (!filePath) {
        const result = await save({
          title: '保存 GameSprite Studio 项目',
          defaultPath: 'untitled.gss',
          filters: [
            {
              name: 'GameSprite Studio Project',
              extensions: ['gss']
            }
          ]
        });

        if (result && typeof result === 'string') {
          filePath = result;
          console.log('📂 用户选择了保存路径:', filePath);
        } else {
          console.log('📂 用户取消了保存');
          return;
        }
      } else {
        console.log('📂 使用已有保存路径:', filePath);
      }

      // 调用保存功能
      const saveResult = await saveProject(filePath, 'GameSprite Project', '保存的项目');

      if (saveResult.success) {
        // 保存成功后记录路径
        setCurrentProjectPath(filePath);
        console.log('✅ 项目保存成功，路径已记录:', filePath);
        toast.success($_('project.saveSuccess'));
      } else {
        console.error('❌ 项目保存失败:', saveResult.error);
        toast.error(`${saveResult.error}`, $_('project.saveFailed'));
      }

    } catch (error) {
      console.error('❌ 保存项目文件过程中发生错误:', error);
      toast.error(`${error}`, $_('project.saveFailed'));
    } finally {
      isLoading = false;
    }
  }

  async function handleSaveAsGameSpriteProject() {
    console.log('=== 开始另存为 GameSprite Studio 项目 ===');

    try {
      isLoading = true;

      // 打开另存为文件对话框
      const result = await save({
        title: '另存为 GameSprite Studio 项目',
        defaultPath: 'untitled.gss',
        filters: [
          {
            name: 'GameSprite Studio Project',
            extensions: ['gss']
          }
        ]
      });

      if (result && typeof result === 'string') {
        console.log('📂 用户选择了另存为路径:', result);

        // 调用保存功能
        const saveResult = await saveProject(result, 'GameSprite Project', '通过另存为保存的项目');

        if (saveResult.success) {
          // 另存为成功后更新当前项目路径
          setCurrentProjectPath(result);
          console.log('✅ 项目另存为成功，路径已更新:', result);
          // 另存为成功不显示弹窗提示
        } else {
          console.error('❌ 项目另存为失败:', saveResult.error);
          toast.error(`${saveResult.error}`, $_('project.saveFailed'));
        }
      } else {
        console.log('📂 用户取消了另存为');
      }

    } catch (error) {
      console.error('❌ 另存为项目文件过程中发生错误:', error);
      toast.error(`${error}`, $_('project.saveFailed'));
    } finally {
      isLoading = false;
    }
  }

  // 创建原生窗口菜单
  async function createNativeMenu() {
    try {
      // 创建文件菜单
      const menu = await Menu.new({
        items: [
          {
            id: 'file',
            text: '文件',
            items: [
              {
                id: 'new_project',
                text: '新建项目',
                accelerator: 'Ctrl+N',
                action: () => {
                  handleMenuAction('new_project');
                }
              },
              {
                id: 'open_project',
                text: '打开项目',
                accelerator: 'Ctrl+O',
                action: () => {
                  handleMenuAction('open_project');
                }
              },
              {
                id: 'save_project',
                text: '保存项目',
                accelerator: 'Ctrl+S',
                action: () => {
                  handleMenuAction('save_project');
                }
              },
              {
                id: 'save_as_project',
                text: '另存为',
                accelerator: 'Ctrl+Shift+S',
                action: () => {
                  handleMenuAction('save_as_project');
                }
              },
              {
                id: 'separator1',
                text: '',
                item: 'Separator'
              },
              {
                id: 'open',
                text: '打开文件',
                accelerator: 'Ctrl+Alt+O',
                action: () => {
                  handleMenuAction('open');
                }
              },
              {
                id: 'save',
                text: '保存文件',
                accelerator: 'Ctrl+Alt+S',
                action: () => {
                  handleMenuAction('save');
                }
              },
              {
                id: 'separator2',
                text: '',
                item: 'Separator'
              },
              {
                id: 'exit',
                text: '退出',
                accelerator: 'Ctrl+Q',
                action: () => {
                  handleMenuAction('exit');
                }
              }
            ]
          },
          {
            id: 'edit',
            text: '编辑',
            items: [
              {
                id: 'undo',
                text: '撤销',
                accelerator: 'Ctrl+Z',
                action: () => {
                  handleMenuAction('undo');
                }
              },
              {
                id: 'redo',
                text: '重做',
                accelerator: 'Ctrl+Y',
                action: () => {
                  handleMenuAction('redo');
                }
              },
              {
                id: 'separator2',
                text: '',
                item: 'Separator'
              },
              {
                id: 'cut',
                text: '剪切',
                accelerator: 'Ctrl+X',
                action: () => {
                  handleMenuAction('cut');
                }
              },
              {
                id: 'copy',
                text: '复制',
                accelerator: 'Ctrl+C',
                action: () => {
                  handleMenuAction('copy');
                }
              },
              {
                id: 'paste',
                text: '粘贴',
                accelerator: 'Ctrl+V',
                action: () => {
                  handleMenuAction('paste');
                }
              }
            ]
          },
          {
            id: 'settings',
            text: '设置',
            items: [
              {
                id: 'zoom_in',
                text: '放大',
                accelerator: 'Ctrl+Plus',
                action: () => {
                  handleMenuAction('zoom_in');
                }
              },
              {
                id: 'zoom_out',
                text: '缩小',
                accelerator: 'Ctrl+-',
                action: () => {
                  handleMenuAction('zoom_out');
                }
              },
              {
                id: 'zoom_reset',
                text: '重置缩放',
                accelerator: 'Ctrl+0',
                action: () => {
                  handleMenuAction('zoom_reset');
                }
              }
            ]
          },
          {
            id: 'help',
            text: '帮助',
            items: [
              {
                id: 'about',
                text: '关于',
                action: () => {
                  handleMenuAction('about');
                }
              }
            ]
          }
        ]
      });

      // 将菜单设置为应用程序菜单
      await menu.setAsAppMenu();

      menuCreated = true;
      console.log('原生菜单创建成功');

    } catch (error) {
      console.error('创建菜单失败:', error);
    }
  }

  // 处理菜单项点击事件
  async function handleMenuAction(actionId: string) {
    console.log(`菜单项被点击: ${actionId}`);

    switch (actionId) {
      case 'open':
        await handleOpenFile();
        break;
      case 'save':
        await handleSaveGameSpriteProject();
        break;
      case 'save_as':
        await handleSaveAsGameSpriteProject();
        break;
      case 'exit':
        await handleExitApp();
        break;
      case 'undo':
        handleUndo();
        break;
      case 'redo':
        handleRedo();
        break;
      case 'cut':
        handleCut();
        break;
      case 'copy':
        handleCopy();
        break;
      case 'paste':
        handlePaste();
        break;
      case 'zoom_in':
        handleZoomIn();
        break;
      case 'zoom_out':
        handleZoomOut();
        break;
      case 'zoom_reset':
        handleZoomReset();
        break;
      case 'about':
        handleAbout();
        break;
      case 'export_settings':
        handleExportSettings();
        break;
      default:
        console.log(`未处理的菜单项: ${actionId}`);
    }
  }

  // 文件操作处理函数
  async function handleOpenFile() {
    console.log('=== 开始加载 GameSprite Studio 项目文件 ===');

    try {
      isLoading = true;

      // 打开文件选择对话框
      const result = await open({
        directory: false,
        multiple: false,
        title: '打开 GameSprite Studio 项目文件',
        filters: [
          {
            name: 'GameSprite Studio Project',
            extensions: ['gss']
          },
          {
            name: 'All Files',
            extensions: ['*']
          }
        ]
      });

      if (result && typeof result === 'string') {
        console.log('📂 用户选择了项目文件:', result);

        // 调用加载功能
        const loadResult = await loadProject(result);

        if (loadResult.success) {
          // 加载成功后记录项目路径
          setCurrentProjectPath(result);
          console.log('✅ 项目加载成功，路径已记录:', result);

          // 显示警告信息（如果有）
          if (loadResult.warnings && loadResult.warnings.length > 0) {
            const warningMsg = `${$_('project.loadSuccess')}<br>${$_('ui.warning')}: ${loadResult.warnings.join('<br>')}`;
            toast.warning(warningMsg, $_('project.loadSuccess'));
          } else {
            // toast.success($_('project.loadSuccess'));
          }

          // 更新项目状态（如果需要）
          // 注意：这里不需要更新 projectPro 的状态，因为我们使用的是不同的保存系统
          console.log('📊 项目数据已恢复到各个 store');

        } else {
          console.error('❌ 项目加载失败:', loadResult.error);
          toast.error(`${loadResult.error}`, $_('project.loadFailed'));
        }
      } else {
        console.log('📂 用户取消了文件选择');
      }

    } catch (error) {
      console.error('❌ 加载项目文件过程中发生错误:', error);
      toast.error(`${error}`, $_('project.loadFailed'));
    } finally {
      isLoading = false;
    }
  }



  async function handleExitApp() {
    const result = await TauriAPI.Window.close();
    if (!result.success) {
      console.error('退出失败:', result.error);
    }
  }

  // 编辑操作处理函数
  function handleUndo() {
    console.log('执行撤销操作');
  }

  function handleRedo() {
    console.log('执行重做操作');
  }

  function handleCut() {
    console.log('执行剪切操作');
  }

  function handleCopy() {
    console.log('执行复制操作');
  }

  function handlePaste() {
    console.log('执行粘贴操作');
  }

  // 视图操作处理函数
  function handleZoomIn() {
    console.log('执行放大操作');
  }

  function handleZoomOut() {
    console.log('执行缩小操作');
  }

  function handleZoomReset() {
    console.log('执行重置缩放操作');
  }

  function handleAbout() {
    console.log('显示关于对话框');
  }

  // 导出设置处理函数
  function handleExportSettings() {
    console.log('打开导出设置');
    showExportSettings = true;
  }

  // 语言切换处理函数
  async function handleLanguageChange(locale: string) {
    console.log(`切换语言到: ${locale}`);
    try {
      await changeLocale(locale as any);
      console.log(`语言已切换到: ${locale}`);
    } catch (error) {
      console.error('语言切换失败:', error);
    }
  }



  // 窗口控制函数
  async function minimizeWindow() {
    const result = await TauriAPI.Window.minimize();
    if (!result.success) {
      console.error('最小化失败:', result.error);
    }
  }

  async function maximizeWindow() {
    const result = await TauriAPI.Window.toggleMaximize();
    if (result.success && result.data !== undefined) {
      isMaximized = result.data;
    } else {
      console.error('操作失败:', result.error);
    }
  }

  async function closeWindow() {
    const result = await TauriAPI.Window.close();
    if (!result.success) {
      console.error('关闭失败:', result.error);
    }
  }

  // 检查窗口状态
  async function checkWindowState() {
    const result = await TauriAPI.Window.isMaximized();
    if (result.success && result.data !== undefined) {
      isMaximized = result.data;
    }
  }

  // 组件挂载时创建菜单和检查窗口状态
  onMount(() => {
    createNativeMenu();
    checkWindowState();

    // 定期检查窗口状态（简单的轮询方式）
    const interval = setInterval(checkWindowState, 1000);

    return () => {
      clearInterval(interval);
    };
  });
</script>

<!-- 自定义标题栏 -->
<div class="title-bar" data-tauri-drag-region>
  <!-- 应用图标和标题 -->
  <div class="title-section">
    <div class="app-icon">
      <img src="/logo.svg" alt="GameSprite Studio" width="20" height="20" />
    </div>
    <span class="app-title">Game Sprite Stuio</span>
  </div>

  <!-- 菜单栏 -->
  <div class="menu-section">
    <div class="menu-item" data-menu="file">
      {$_('menu.file')}
      <div class="dropdown-menu">
        <div class="menu-option" onclick={() => handleMenuAction('open')}>
          <span>{$_('actions.open')}</span>
          <span class="shortcut">Ctrl+O</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('save')}>
          <span>{$_('actions.save')}</span>
          <span class="shortcut">Ctrl+S</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('save_as')}>
          <span>{$_('actions.saveAs')}</span>
          <span class="shortcut">Ctrl+Shift+S</span>
        </div>
        <div class="menu-separator"></div>
        <div class="menu-option" onclick={() => handleMenuAction('exit')}>
          <span>{$_('actions.exit')}</span>
          <span class="shortcut">Ctrl+Q</span>
        </div>
      </div>
    </div>

    <div class="menu-item" data-menu="edit">
      {$_('menu.edit')}
      <div class="dropdown-menu">
        <div class="menu-option" onclick={() => handleMenuAction('undo')}>
          <span>{$_('actions.undo')}</span>
          <span class="shortcut">Ctrl+Z</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('redo')}>
          <span>{$_('actions.redo')}</span>
          <span class="shortcut">Ctrl+Y</span>
        </div>
        <div class="menu-separator"></div>
        <div class="menu-option" onclick={() => handleMenuAction('cut')}>
          <span>{$_('actions.cut')}</span>
          <span class="shortcut">Ctrl+X</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('copy')}>
          <span>{$_('actions.copy')}</span>
          <span class="shortcut">Ctrl+C</span>
        </div>
        <div class="menu-option" onclick={() => handleMenuAction('paste')}>
          <span>{$_('actions.paste')}</span>
          <span class="shortcut">Ctrl+V</span>
        </div>
      </div>
    </div>

    <div class="menu-item" data-menu="view">
      {$_('menu.settings')}
      <div class="dropdown-menu">
        <!-- 语言设置子菜单 -->
        <div class="menu-option submenu-item">
          <span>{$_('menu.language')}</span>
          <span class="submenu-arrow">▶</span>
          <div class="submenu">
            {#each supportedLocales as lang}
              <!-- svelte-ignore a11y_click_events_have_key_events -->
              <!-- svelte-ignore a11y_no_static_element_interactions -->
              <div
                class="menu-option language-option"
                class:active={$currentLocale === lang.code}
                onclick={() => handleLanguageChange(lang.code)}
                role="menuitem"
                tabindex="0"
              >
                <span class="language-flag">{lang.flag}</span>
                <span class="language-name">{lang.name}</span>
                {#if $currentLocale === lang.code}
                  <span class="check-mark">✓</span>
                {/if}
              </div>
            {/each}
          </div>
        </div>
        <div class="menu-separator"></div>
        <!-- svelte-ignore a11y_click_events_have_key_events -->
        <!-- svelte-ignore a11y_no_static_element_interactions -->
        <div class="menu-option" onclick={() => handleMenuAction('export_settings')} role="menuitem" tabindex="0">
          <span>{$_('actions.exportSettings')}</span>
          <span class="shortcut">Ctrl+E</span>
        </div>
      </div>
    </div>

    <div class="menu-item" data-menu="help">
      {$_('menu.help')}
      <div class="dropdown-menu">
        <div class="menu-option" onclick={() => handleMenuAction('about')}>
          <span>{$_('dialog.about')}</span>
        </div>
      </div>
    </div>
  </div>



  <!-- 空白拖拽区域 -->
  <div class="drag-area"></div>

  <!-- 窗口控制按钮 -->
  <div class="window-controls">
    <button class="control-btn minimize-btn" onclick={minimizeWindow} title={$_('ui.minimize')}>
      <svg width="12" height="12" viewBox="0 0 12 12">
        <rect x="2" y="5" width="8" height="2" fill="currentColor"/>
      </svg>
    </button>

    <button class="control-btn maximize-btn" onclick={maximizeWindow} title={isMaximized ? $_('ui.restore') : $_('ui.maximize')}>
      {#if isMaximized}
        <svg width="12" height="12" viewBox="0 0 12 12">
          <rect x="2" y="2" width="6" height="6" stroke="currentColor" stroke-width="1" fill="none"/>
          <rect x="4" y="4" width="6" height="6" stroke="currentColor" stroke-width="1" fill="none"/>
        </svg>
      {:else}
        <svg width="12" height="12" viewBox="0 0 12 12">
          <rect x="2" y="2" width="8" height="8" stroke="currentColor" stroke-width="1" fill="none"/>
        </svg>
      {/if}
    </button>

    <button class="control-btn close-btn" onclick={closeWindow} title={$_('actions.close')}>
      <svg width="12" height="12" viewBox="0 0 12 12">
        <path d="M2 2 L10 10 M10 2 L2 10" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
</div>

<!-- 导出设置弹窗 -->
{#if showExportSettings}
  <ExportSettingsDialog
    show={showExportSettings}
    onClose={() => showExportSettings = false}
  />
{/if}





<style>
  /* 标题栏样式 */
  .title-bar {
    height: var(--title-bar-height);
    background: linear-gradient(135deg, var(--theme-primary-dark) 0%, var(--theme-primary) 100%);
    color: var(--theme-text);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--theme-border-light);
    user-select: none;
    position: relative;
    z-index: 2001;
  }

  /* 标题区域 */
  .title-section {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 0 12px;
    flex-shrink: 0;
  }

  .app-icon {
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-base);
  }

  .app-icon img {
    transition: var(--transition-base);
    filter: brightness(1);
  }

  .app-icon:hover img {
    transform: scale(1.1);
    filter: brightness(1.2);
  }

  .app-title {
    font-size: 13px;
    font-weight: 500;
    opacity: 0.9;
  }

  /* 菜单区域 */
  .menu-section {
    display: flex;
    align-items: center;
    padding: 0 8px;
  }

  /* 拖拽区域 */
  .drag-area {
    flex: 1;
    height: 100%;
    min-width: 100px;
  }

  .menu-item {
    position: relative;
    padding: 6px 12px;
    font-size: 13px;
    cursor: pointer;
    border-radius: 3px;
    transition: background-color 0.2s;
  }

  .menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .menu-item:hover .dropdown-menu {
    display: block;
  }

  /* 下拉菜单 */
  .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 180px;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px var(--theme-shadow);
    z-index: 2002;
    padding: var(--spacing-1) 0;
  }

  .menu-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .menu-option:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .shortcut {
    font-size: 11px;
    opacity: 0.7;
    font-family: 'Courier New', monospace;
  }

  .menu-separator {
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
    margin: 4px 8px;
  }

  /* 子菜单样式 */
  .submenu-item {
    position: relative;
  }

  .submenu-item:hover .submenu {
    display: block;
  }

  .submenu-arrow {
    font-size: 10px;
    opacity: 0.7;
  }

  .submenu {
    display: none;
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 160px;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px var(--theme-shadow);
    z-index: 2003;
    padding: var(--spacing-1) 0;
  }

  /* 语言选项样式 */
  .language-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .language-option:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .language-option.active {
    background: rgba(255, 255, 255, 0.15);
  }

  .language-flag {
    font-size: 14px;
    width: 16px;
    text-align: center;
  }

  .language-name {
    flex: 1;
    font-size: 12px;
  }

  .check-mark {
    font-size: 12px;
    color: var(--theme-primary);
    font-weight: bold;
  }

  /* 子菜单样式 */
  .submenu-item {
    position: relative;
  }

  .submenu-item:hover .submenu {
    display: block;
  }

  .submenu-arrow {
    font-size: 10px;
    opacity: 0.7;
  }

  .submenu {
    display: none;
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 160px;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px var(--theme-shadow);
    z-index: 2003;
    padding: var(--spacing-1) 0;
  }

  /* 语言选项样式 */
  .language-option {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .language-option:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .language-option.active {
    background: rgba(255, 255, 255, 0.15);
  }

  .language-flag {
    font-size: 14px;
    width: 16px;
    text-align: center;
  }

  .language-name {
    flex: 1;
    font-size: 12px;
  }

  .check-mark {
    font-size: 12px;
    color: var(--theme-primary);
    font-weight: bold;
  }

  /* 窗口控制按钮 */
  .window-controls {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .control-btn {
    width: 46px;
    height: 32px;
    border: none;
    background: transparent;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
  }

  .control-btn:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .close-btn:hover {
    background: #e53e3e;
  }





  /* 拖拽区域配置 */
  [data-tauri-drag-region] {
    -webkit-app-region: drag;
  }

  /* 防止菜单和按钮被拖拽 */
  .menu-item,
  .control-btn,
  .menu-option,
  .dropdown-menu {
    -webkit-app-region: no-drag;
  }

  /* 确保下拉菜单不会被拖拽影响 */
  .dropdown-menu {
    pointer-events: auto;
  }

  /* 确保整个标题栏可以拖拽，但菜单和按钮除外 */
  .title-bar {
    -webkit-app-region: drag;
  }

  .menu-section,
  .window-controls {
    -webkit-app-region: no-drag;
  }



</style>