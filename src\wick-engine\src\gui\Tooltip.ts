/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>..
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";

export class Tooltip extends GUIElement {
  protected label: string;

  constructor(model: Base, label: string) {
    super(model);
    this.label = label;
  }

  draw(x: number, y: number): void {
    super.draw();

    // No label was given yet - don't render.
    if (!this.label) return;

    const ctx = this.ctx;

    // Font settings
    ctx.font = "14px Nunito Sans";
    const textContent = this.label;
    const textWidth = ctx.measureText(textContent).width;
    const textHeight = 14;

    // Tooltip
    ctx.save();
    let tx = x - textWidth / 2;
    let ty = y + textHeight;

    // Restrict tooltip so it's always on-screen
    const xMin = 3;
    if (tx < xMin) tx = xMin;

    if (ty > this.canvas.height) {
      ty = this.canvas.height - 35;
    } else if (ty > this.canvas.height - 25) {
      ty = this.canvas.height - 20;
    }

    ctx.translate(tx, ty);

    // Body
    const margin = 4;
    const r = GUIElement.FRAME_BORDER_RADIUS;
    ctx.fillStyle = "#3878AF";
    ctx.beginPath();
    ctx.roundRect(
      -margin / 2,
      -margin / 2,
      textWidth + margin,
      textHeight + margin,
      r
    );
    ctx.fill();

    // Label text
    ctx.fillStyle = "#FFFFFF";
    ctx.fillText(textContent, 0, 12);

    ctx.restore();
  }
}
