/**
 * 🎯 统一项目保存管理器 - 新版本
 * 所有序列化/反序列化逻辑都在后端处理
 */

import { get } from 'svelte/store';
import { resourceStore } from '../stores/resourceStore';
import { atlasStore } from '../stores/atlasStore';
import { exportSettingsActions } from '../stores/exportSettingsStore';
import { TauriAPI } from '../lib/tauriAPI';

export interface UnifiedSaveResult {
  success: boolean;
  filePath?: string;
  fileSize?: number;
  processingTime?: number;
  error?: string;
}

export interface UnifiedLoadResult {
  success: boolean;
  data?: any;
  error?: string;
  warnings?: string[];
}

/**
 * 🎯 统一项目保存管理器
 */
export class UnifiedProjectSaveManager {
  private static instance: UnifiedProjectSaveManager;

  public static getInstance(): UnifiedProjectSaveManager {
    if (!UnifiedProjectSaveManager.instance) {
      UnifiedProjectSaveManager.instance = new UnifiedProjectSaveManager();
    }
    return UnifiedProjectSaveManager.instance;
  }

  /**
   * 🎯 保存项目 - 统一后端处理
   */
  public async save(
    filePath: string,
    projectName: string,
    description?: string,
    tags?: string[]
  ): Promise<UnifiedSaveResult> {
    try {
      console.log('🚀 UnifiedProjectSave: 开始保存项目', { filePath, projectName });

      // 1. 收集前端数据（不做任何序列化处理）
      const imageResources = this.collectImageResources();
      const atlasData = this.collectAtlasData();
      const exportSettings = await this.collectExportSettings();

      console.log('📊 UnifiedProjectSave: 数据收集完成', {
        imageResourcesCount: imageResources?.resources?.length || 0,
        atlasCount: atlasData?.atlases?.length || 0,
        hasExportSettings: !!exportSettings
      });

      // 2. 调用后端统一保存API
      const result = await TauriAPI.Project.saveProjectUnified(
        filePath,
        projectName,
        description,
        tags,
        imageResources,
        atlasData,
        exportSettings
      );

      if (result.success) {
        console.log('✅ UnifiedProjectSave: 项目保存成功', result.data);
        return {
          success: true,
          filePath: result.data?.file_path,
          fileSize: result.data?.file_size,
          processingTime: result.data?.processing_time_ms
        };
      } else {
        console.error('❌ UnifiedProjectSave: 项目保存失败', result.error);
        return {
          success: false,
          error: result.error
        };
      }
    } catch (error) {
      console.error('❌ UnifiedProjectSave: 保存过程中发生错误', error);
      return {
        success: false,
        error: `保存失败: ${error}`
      };
    }
  }

  /**
   * 🎯 加载项目 - 统一后端处理
   */
  public async load(filePath: string): Promise<UnifiedLoadResult> {
    try {
      console.log('🚀 UnifiedProjectSave: 开始加载项目', { filePath });

      // 调用后端统一加载API
      const result = await TauriAPI.Project.loadProjectUnified(filePath);

      if (result.success && result.data) {
        console.log('✅ UnifiedProjectSave: 项目加载成功');

        // 直接更新store，数据已经完全处理好了
        await this.restoreProjectData(result.data.data);

        return {
          success: true,
          data: result.data.data,
          warnings: result.data.warnings
        };
      } else {
        console.error('❌ UnifiedProjectSave: 项目加载失败', result.error);
        return {
          success: false,
          error: result.error
        };
      }
    } catch (error) {
      console.error('❌ UnifiedProjectSave: 加载过程中发生错误', error);
      return {
        success: false,
        error: `加载失败: ${error}`
      };
    }
  }

  /**
   * 🎯 收集图片资源数据（处理ArrayBuffer为可序列化格式）
   */
  private collectImageResources(): any {
    const state = get(resourceStore);
    const processedResources = this.processResourcesForSerialization(state.rootResources || []);

    return {
      resources: processedResources,
      currentFolder: null,
      navigationStack: [],
      exportedAt: new Date().toISOString()
    };
  }

  /**
   * 🎯 处理资源中的ArrayBuffer，转换为可序列化的数组格式
   */
  private processResourcesForSerialization(resources: any[]): any[] {
    return resources.map(resource => {
      const processedResource = { ...resource };

      // 处理图片资源的ArrayBuffer
      if (processedResource.type === 'image' && processedResource.data instanceof ArrayBuffer) {
        // 将ArrayBuffer转换为数字数组
        processedResource.data = Array.from(new Uint8Array(processedResource.data));
        console.log('🔄 转换ArrayBuffer为数组:', {
          name: processedResource.name,
          arrayLength: processedResource.data.length
        });
      }

      // 递归处理子资源
      if (processedResource.children && Array.isArray(processedResource.children)) {
        processedResource.children = this.processResourcesForSerialization(processedResource.children);
      }

      return processedResource;
    });
  }

  /**
   * 🎯 恢复资源中的ArrayBuffer（从数组格式转换回ArrayBuffer）
   */
  private restoreArrayBuffersInResources(resources: any[]): any[] {
    return resources.map(resource => {
      const restoredResource = { ...resource };

      // 处理图片资源的数组数据，转换回ArrayBuffer
      if (restoredResource.type === 'image' && Array.isArray(restoredResource.data)) {
        // 将数字数组转换回ArrayBuffer
        const uint8Array = new Uint8Array(restoredResource.data);
        restoredResource.data = uint8Array.buffer.slice(
          uint8Array.byteOffset,
          uint8Array.byteOffset + uint8Array.byteLength
        );
        console.log('🔄 恢复ArrayBuffer:', {
          name: restoredResource.name,
          bufferSize: restoredResource.data.byteLength
        });
      }

      // 递归处理子资源
      if (restoredResource.children && Array.isArray(restoredResource.children)) {
        restoredResource.children = this.restoreArrayBuffersInResources(restoredResource.children);
      }

      return restoredResource;
    });
  }

  /**
   * 🎯 收集图集数据（原始格式，不做处理）
   */
  private collectAtlasData(): any {
    return atlasStore.exportAtlasData();
  }

  /**
   * 🎯 收集导出设置（原始格式，不做处理）
   */
  private async collectExportSettings(): Promise<any> {
    return await exportSettingsActions.getCurrentSettings();
  }

  /**
   * 🎯 恢复项目数据到各个store
   */
  private async restoreProjectData(data: any): Promise<void> {
    console.log('🔄 UnifiedProjectSave: 开始恢复项目数据');

    try {
      // 恢复图集数据
      if (data.atlas_data) {
        atlasStore.importAtlasData(data.atlas_data);
        console.log('✅ UnifiedProjectSave: 图集数据恢复完成');
      }

      // 恢复资源数据（数据已经包含完整的processedData）
      if (data.resource_data && data.resource_data.resources) {
        // 🎯 处理数组格式的data字段，转换回ArrayBuffer
        const processedResources = this.restoreArrayBuffersInResources(data.resource_data.resources);

        resourceStore.update(state => ({
          ...state,
          rootResources: processedResources,
          currentFolder: null,
          navigationStack: [],
          selectedResource: null
        }));
        console.log('✅ UnifiedProjectSave: 资源数据恢复完成');
      }

      // 恢复导出设置
      if (data.export_settings) {
        exportSettingsActions.updateSettings(data.export_settings);
        console.log('✅ UnifiedProjectSave: 导出设置恢复完成');
      }

      console.log('✅ UnifiedProjectSave: 所有数据恢复完成');
    } catch (error) {
      console.error('❌ UnifiedProjectSave: 数据恢复过程中发生错误', error);
      throw error;
    }
  }

  /**
   * 验证项目文件
   */
  public async validate(filePath: string): Promise<boolean> {
    try {
      const result = await TauriAPI.Project.validateProjectFile(filePath);
      return result.success && result.data;
    } catch (error) {
      console.error('❌ UnifiedProjectSave: 验证项目文件失败', error);
      return false;
    }
  }
}

// 导出单例实例
export const unifiedProjectSaveManager = UnifiedProjectSaveManager.getInstance();
