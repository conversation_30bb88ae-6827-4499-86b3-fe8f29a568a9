/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "./Base";
import { Transformation } from "../Transformation";
import { Frame } from "./Frame";
import { Layer } from "./Layer";
import { Clip } from "./Clip";

declare const TWEEN: {
  Easing: {
    Linear: { None: (t: number) => number };
    Quadratic: {
      In: (t: number) => number;
      Out: (t: number) => number;
      InOut: (t: number) => number;
    };
  };
};

declare function lerp(start: number, end: number, amt: number): number;

/**
 * Class representing a tween.
 */
export class Tween extends Base {
  protected _playheadPosition: number;
  protected _transformation: Transformation;
  protected _easingType: string;
  protected _originalLayerIndex: number;
  public fullRotations: number;

  static get VALID_EASING_TYPES(): string[] {
    return ["none", "in", "out", "in-out"];
  }

  static _calculateTimeValue(
    tweenA: Tween,
    tweenB: Tween,
    playheadPosition: number
  ): number {
    const tweenAPlayhead = tweenA.playheadPosition;
    const tweenBPlayhead = tweenB.playheadPosition;
    const dist = tweenBPlayhead - tweenAPlayhead;
    const t = (playheadPosition - tweenAPlayhead) / dist;
    return t;
  }

  /**
   * Create a tween
   * @param {object} args - The tween arguments
   * @param {number} args.playheadPosition - the playhead position relative to the frame that the tween belongs to
   * @param {Transformation} args.transformation - the transformation this tween will apply to child objects
   * @param {number} args.fullRotations - the number of rotations to add to the tween's transformation
   * @param {string} args.easingType - the type of easing to apply
   */
  constructor(
    args: {
      playheadPosition?: number;
      transformation?: Transformation;
      fullRotations?: number;
      easingType?: string;
    } = {}
  ) {
    super(args);

    this._playheadPosition = args.playheadPosition || 1;
    this._transformation = args.transformation || new Transformation();
    this.fullRotations =
      args.fullRotations === undefined ? 0 : args.fullRotations;
    this.easingType = args.easingType || "none";

    this._originalLayerIndex = -1;
  }

  /**
   * Create a tween by interpolating two existing tweens.
   * @param {Tween} tweenA - The first tween
   * @param {Tween} tweenB - The second tween
   * @param {number} playheadPosition - The point between the two tweens to use to interpolate
   */
  static interpolate(
    tweenA: Tween,
    tweenB: Tween,
    playheadPosition: number
  ): Tween {
    const interpTween = new Tween();

    // Calculate value (0.0-1.0) to pass to tweening function
    const t = Tween._calculateTimeValue(tweenA, tweenB, playheadPosition);

    // Interpolate every transformation attribute using the t value
    ["x", "y", "scaleX", "scaleY", "rotation", "opacity"].forEach(
      (propName) => {
        const tweenFn = tweenA._getTweenFunction();
        const tt = tweenFn(t);
        const valA = tweenA.transformation[propName];
        const valB = tweenB.transformation[propName];
        if (propName === "rotation") {
          // Convert full rotations to 360 degree amounts
          valB += tweenA.fullRotations * 360;
        }
        interpTween.transformation[propName] = lerp(valA, valB, tt);
      }
    );

    interpTween.playheadPosition = playheadPosition;
    return interpTween;
  }

  get classname(): string {
    return "Tween";
  }

  protected _serialize(args: any): any {
    const data = super._serialize(args);

    data.playheadPosition = this.playheadPosition;
    data.transformation = this._transformation.values;
    data.fullRotations = this.fullRotations;
    data.easingType = this.easingType;

    data.originalLayerIndex =
      this.layerIndex !== -1 ? this.layerIndex : this._originalLayerIndex;

    return data;
  }

  protected _deserialize(data: any): void {
    super._deserialize(data);

    this.playheadPosition = data.playheadPosition;
    this._transformation = new Transformation(data.transformation);
    this.fullRotations = data.fullRotations;
    this.easingType = data.easingType;

    this._originalLayerIndex = data.originalLayerIndex;
  }

  /**
   * The playhead position of the tween.
   */
  get playheadPosition(): number {
    return this._playheadPosition;
  }

  set playheadPosition(playheadPosition: number) {
    this._playheadPosition = playheadPosition;
  }

  /**
   * The transformation representing the position, rotation and other elements of the tween.
   */
  get transformation(): Transformation {
    return this._transformation;
  }

  set transformation(transformation: Transformation) {
    this._transformation = transformation;
  }

  /**
   * The type of interpolation to use for easing.
   */
  get easingType(): string {
    return this._easingType;
  }

  set easingType(easingType: string) {
    if (Tween.VALID_EASING_TYPES.indexOf(easingType) === -1) {
      console.warn("Invalid easingType. Valid easingTypes: ");
      console.warn(Tween.VALID_EASING_TYPES);
      return;
    }
    this._easingType = easingType;
  }

  /**
   * Remove this tween from its parent frame.
   */
  remove(): void {
    this.parent.removeTween(this);
  }

  /**
   * Set the transformation of a clip to this tween's transformation.
   * @param {Clip} clip - the clip to apply the tween transforms to.
   */
  applyTransformsToClip(clip: Clip): void {
    clip.transformation = this.transformation.copy();
  }

  /**
   * The tween that comes after this tween in the parent frame.
   */
  getNextTween(): Tween | null {
    if (!this.parentFrame) return null;

    const frontTween = this.parentFrame.seekTweenInFront(
      this.playheadPosition + 1
    );
    return frontTween;
  }

  /**
   * Prevents tweens from existing outside of the frame's length. Call this after changing the length of the parent frame.
   */
  restrictToFrameSize(): void {
    const playheadPosition = this.playheadPosition;

    // Remove tween if playheadPosition is out of bounds
    if (playheadPosition < 1 || playheadPosition > this.parentFrame.length) {
      this.remove();
    }
  }

  /**
   * The index of the parent layer of this tween.
   */
  get layerIndex(): number {
    return this.parentLayer ? this.parentLayer.index : -1;
  }

  /**
   * The index of the layer that this tween last belonged to. Used when copying and pasting tweens.
   */
  get originalLayerIndex(): number {
    return this._originalLayerIndex;
  }

  /**
   * Retrieve Tween.js easing functions by name
   */
  protected _getTweenFunction(): (t: number) => number {
    return {
      none: TWEEN.Easing.Linear.None,
      in: TWEEN.Easing.Quadratic.In,
      out: TWEEN.Easing.Quadratic.Out,
      "in-out": TWEEN.Easing.Quadratic.InOut,
    }[this.easingType];
  }

  get parentFrame(): Frame | null {
    return this.parent as Frame | null;
  }

  get parentLayer(): Layer | null {
    return this.parentFrame?.parent as Layer | null;
  }
}
