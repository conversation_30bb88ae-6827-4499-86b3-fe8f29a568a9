<script lang="ts">
  /**
   * Toast 容器组件
   * 渲染所有活跃的 Toast 提示
   */
  import { toastStore } from '../stores/toastStore';
  import Toast from './Toast.svelte';

  // 订阅 toast 状态
  let toastState = $state($toastStore);

  $effect(() => {
    const unsubscribe = toastStore.subscribe(state => {
      toastState = state;
    });

    return unsubscribe;
  });

  function handleToastClose(toastId: string) {
    toastStore.remove(toastId);
  }
</script>

<!-- 渲染所有活跃的 Toast -->
{#each toastState.toasts as toast (toast.id)}
  <Toast
    type={toast.type}
    title={toast.title || ''}
    message={toast.message}
    duration={toast.duration || 0}
    showCloseButton={toast.showCloseButton !== false}
    onClose={() => handleToastClose(toast.id)}
  />
{/each}
