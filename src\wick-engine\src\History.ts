/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

interface StateObject {
  state: any[];
  objects: Set<string>;
  actionName: string;
  timeSinceLastPush: number;
}

/**
 * 用于实现撤销/重做功能的历史记录工具类
 */
export class History {
  protected static _undoStack: StateObject[] = [];
  protected static _redoStack: StateObject[] = [];
  protected static _snapshots: { [key: string]: any[] } = {};
  protected lastHistoryPush: number;

  /**
   * 是否启用详细日志
   */
  static get VERBOSE(): boolean {
    return false;
  }

  /**
   * 所有状态保存类型的枚举
   */
  static get StateType(): { [key: string]: number } {
    return {
      ALL_OBJECTS: 1,
      ALL_OBJECTS_WITHOUT_PATHS: 2,
      ONLY_VISIBLE_OBJECTS: 3,
    };
  }

  /**
   * 创建一个新的历史记录对象
   */
  constructor() {
    this.reset();
    this.lastHistoryPush = Date.now();
  }

  /**
   * 重置编辑器中的历史记录。此操作不可逆
   */
  reset(): void {
    History._undoStack = [];
    History._redoStack = [];
    History._snapshots = {};
  }

  /**
   * 返回当前被历史记录引用的所有对象
   * @returns 历史记录中当前引用的所有对象的UUID集合
   */
  getObjectUUIDs(): Set<string> {
    let objects = new Set<string>();

    for (let state of History._undoStack) {
      objects = new Set([...objects, ...state.objects]);
    }

    for (let state of History._redoStack) {
      objects = new Set([...objects, ...state.objects]);
    }

    return objects;
  }

  /**
   * 将ObjectCache的当前状态推送到撤销栈
   * @param filter - 选择要序列化的对象的过滤器。参见History.StateType
   * @param actionName - 可选：生成此状态的操作的名称。如果未提供名称，则显示"Unknown Action"
   */
  pushState(filter: number, actionName?: string): void {
    History._redoStack = [];
    const now = Date.now();

    const state = this._generateState(filter);
    const objects = new Set(state.map((obj) => obj.uuid));
    const stateObject: StateObject = {
      state: this._generateState(filter),
      objects: objects,
      actionName: actionName || "Unknown Action",
      timeSinceLastPush: now - this.lastHistoryPush,
    };

    this.lastHistoryPush = now;

    History._undoStack.push(stateObject);
    History._undoStack = History._undoStack.slice(-64); // 获取撤销栈中的最后64个项目
  }

  /**
   * 从撤销栈中弹出最后一个状态，并将新的最后一个状态应用到项目
   * @returns 如果撤销栈非空则返回true，否则返回false
   */
  popState(): boolean {
    if (History._undoStack.length <= 1) {
      return false;
    }

    const lastState = History._undoStack.pop();
    if (lastState) {
      History._redoStack.push(lastState);
    }

    const currentStateObject =
      History._undoStack[History._undoStack.length - 1];

    // 1.17.1 历史记录更新，提取实际状态信息
    let currentState = currentStateObject;

    if (currentStateObject.state) {
      currentState = currentStateObject.state;
    }

    this._recoverState(currentState);

    return true;
  }

  /**
   * 恢复已撤销的状态
   * @returns 如果重做栈非空则返回true，否则返回false
   */
  recoverState(): boolean {
    if (History._redoStack.length === 0) {
      return false;
    }

    const recoveredState = History._redoStack.pop();
    if (recoveredState) {
      History._undoStack.push(recoveredState);
      this._recoverState(recoveredState.state);
    }

    return true;
  }

  /**
   * 保存状态到快照列表中，以便随时恢复
   * @param name - 快照的名称
   * @param filter - 选择要序列化的对象的过滤器。参见History.StateType
   */
  saveSnapshot(name: string, filter?: number): void {
    History._snapshots[name] = this._generateState(
      filter || History.StateType.ALL_OBJECTS_WITHOUT_PATHS
    );
  }

  /**
   * 从快照列表中加载状态
   * @param name - 要恢复的快照的名称
   */
  loadSnapshot(name: string): void {
    this._recoverState(History._snapshots[name]);
  }

  /**
   * 当前存储用于撤销的状态数量
   */
  get numUndoStates(): number {
    return History._undoStack.length;
  }

  /**
   * 当前存储用于重做的状态数量
   */
  get numRedoStates(): number {
    return History._redoStack.length;
  }

  /**
   * 生成状态
   * @param stateType - 状态类型
   * @returns 生成的状态
   */
  protected _generateState(stateType?: number): any[] {
    let objects: any[] = [];

    if (stateType === undefined) {
      stateType = History.StateType.ALL_OBJECTS;
    }

    if (stateType === History.StateType.ALL_OBJECTS) {
      objects = this._getAllObjects();
    } else if (stateType === History.StateType.ALL_OBJECTS_WITHOUT_PATHS) {
      objects = this._getAllObjectsWithoutPaths();
    } else if (stateType === History.StateType.ONLY_VISIBLE_OBJECTS) {
      objects = this._getVisibleObjects();
    } else {
      console.error("History._generateState: A valid stateType is required.");
      return [];
    }

    if (History.VERBOSE) {
      console.log(
        "History._generateState: Serializing " +
          objects.length +
          " objects using mode=" +
          stateType
      );
    }

    return objects;
  }

  /**
   * 恢复状态
   * @param state - 要恢复的状态
   */
  protected _recoverState(state: any): void {
    // 此方法需要在子类中实现
  }

  /**
   * 获取所有对象
   */
  protected _getAllObjects(): any[] {
    // 此方法需要在子类中实现
    return [];
  }

  /**
   * 获取所有不包含路径的对象
   */
  protected _getAllObjectsWithoutPaths(): any[] {
    // 此方法需要在子类中实现
    return [];
  }

  /**
   * 获取所有可见对象
   */
  protected _getVisibleObjects(): any[] {
    // 此方法需要在子类中实现
    return [];
  }
}
