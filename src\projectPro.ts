/**
 * 项目管理模块
 * 统一管理项目的创建、打开、保存等操作
 */

import { OperationRecordsAPI } from './lib';

// 项目数据类型定义
export interface GameSpriteProject {
  name: string;
  version: string;
  createdAt: string;
  updatedAt: string;
  settings: {
    width: number;
    height: number;
    backgroundColor: string;
  };
  assets: any[];
  scenes: any[];
}

export interface ProjectCreateOptions {
  name: string;
  width: number;
  height: number;
  backgroundColor: string;
}

// 当前项目状态
let currentProject: GameSpriteProject | null = null;
let currentProjectPath: string | null = null;
let isDirty = false;

/**
 * 创建新项目
 */
export async function createProject(options: ProjectCreateOptions): Promise<{ success: boolean; message?: string }> {
  console.log('=== 开始创建新项目 ===');
  console.log('项目选项:', options);

  try {
    // 创建新项目数据
    const newProject: GameSpriteProject = {
      name: options.name,
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      settings: {
        width: options.width,
        height: options.height,
        backgroundColor: options.backgroundColor
      },
      assets: [],
      scenes: []
    };

    // 更新当前项目状态
    currentProject = newProject;
    currentProjectPath = null; // 新项目还没有保存路径
    isDirty = true;

    console.log('=== 新项目创建完成 ===');
    return { success: true, message: '项目创建成功' };

  } catch (error) {
    console.error('创建项目时发生异常:', error);
    return { success: false, message: String(error) };
  }
}

/**
 * 打开项目
 */
export async function openProject(): Promise<{ success: boolean; message?: string }> {
  console.log('=== 开始打开项目 ===');

  try {
    // 在浏览器环境中使用文件选择器
    if (typeof window !== 'undefined') {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.gss,.json';

      return new Promise((resolve) => {
        input.onchange = async (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          if (!file) {
            resolve({ success: false, message: '未选择文件' });
            return;
          }

          try {
            const content = await file.text();
            const projectData = JSON.parse(content) as GameSpriteProject;

            // 验证项目数据
            if (!projectData.name || !projectData.settings) {
              resolve({ success: false, message: '无效的项目文件格式' });
              return;
            }

            // 更新当前项目状态
            currentProject = projectData;
            currentProjectPath = file.name;
            isDirty = false;

            console.log('=== 项目打开完成 ===');
            resolve({ success: true, message: '项目打开成功' });

          } catch (error) {
            console.error('解析项目文件失败:', error);
            resolve({ success: false, message: '项目文件格式错误' });
          }
        };

        input.click();
      });
    }

    return { success: false, message: '不支持的环境' };

  } catch (error) {
    console.error('打开项目时发生异常:', error);
    return { success: false, message: String(error) };
  }
}

/**
 * 保存项目
 */
export async function saveProject(saveAs: boolean = false): Promise<{ success: boolean; message?: string }> {
  console.log('=== 开始保存项目 ===');
  console.log('另存为模式:', saveAs);

  try {
    if (!currentProject) {
      return { success: false, message: '没有打开的项目' };
    }

    // 更新项目的修改时间
    currentProject.updatedAt = new Date().toISOString();

    // 在浏览器环境中使用下载功能
    if (typeof window !== 'undefined') {
      const projectJson = JSON.stringify(currentProject, null, 2);
      const blob = new Blob([projectJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentProject.name}.gss`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      // 更新项目状态
      if (saveAs || !currentProjectPath) {
        currentProjectPath = `${currentProject.name}.gss`;
      }
      isDirty = false;

      console.log('=== 项目保存完成 ===');
      return { success: true, message: '项目保存成功' };
    }

    return { success: false, message: '不支持的环境' };

  } catch (error) {
    console.error('保存项目时发生异常:', error);
    return { success: false, message: String(error) };
  }
}

/**
 * 获取当前项目信息
 */
export function getCurrentProject(): GameSpriteProject | null {
  return currentProject;
}

/**
 * 获取当前项目路径
 */
export function getCurrentProjectPath(): string | null {
  return currentProjectPath;
}

/**
 * 检查项目是否有未保存的更改
 */
export function isProjectDirty(): boolean {
  return isDirty;
}

/**
 * 标记项目为已修改
 */
export function markProjectDirty(): void {
  isDirty = true;
}

/**
 * 保存操作记录到当前项目
 */
export async function saveOperationRecords(): Promise<void> {
  console.log('=== 开始保存操作记录 ===');

  try {
    if (!currentProject || !currentProjectPath) {
      console.log('没有加载的项目，跳过保存操作记录');
      return;
    }


  } catch (error) {
    console.error('保存操作记录时发生异常:', error);
  }
}

/**
 * 从指定项目路径加载操作记录
 */
export async function loadOperationRecords(projectPath: string): Promise<{ success: boolean; message?: string }> {
  console.log('=== 开始加载操作记录 ===');
  console.log('项目路径:', projectPath);

  try {
    const loadResult = await OperationRecordsAPI.loadOperationRecords(projectPath);

    if (!loadResult.success) {
      console.error('加载操作记录失败:', loadResult.error);
      return { success: false, message: loadResult.error };
    }

    if (!loadResult.data) {
      console.log('项目中没有操作记录文件');
      return { success: true, message: '项目中没有操作记录文件' };
    }


    console.log('=== 操作记录加载完成 ===');
    return { success: true, message: '操作记录加载成功' };

  } catch (error) {
    console.error('加载操作记录时发生异常:', error);
    return { success: false, message: String(error) };
  }
}

/**
 * 导出插件到当前项目
 */
export async function exportPlug(): Promise<void> {
  console.log('=== 开始导出插件 ===');

  try {
    saveOperationRecords();

    if (!currentProject || !currentProjectPath) {
      console.log('没有加载的项目，无法导出插件');

      if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
        new Notification('导出插件失败', {
          body: '没有加载的项目',
          icon: '/favicon.ico'
        });
      }
      return;
    }

    // 1. 生成插件代码



  } catch (error) {
    console.error('导出插件时发生异常:', error);

    if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {
      new Notification('插件导出异常', {
        body: String(error),
        icon: '/favicon.ico'
      });
    }
  }
}

