/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { View } from "./View";
import { Frame } from "../base/Frame";
import { Path } from "../base/Path";
import * as paper from "paper";

export class FrameView extends View {
  protected static readonly RASTERIZE_RESOLUTION_MODIFIER = 1;

  protected static get RASTERIZE_RESOLUTION_MODIFIER_FOR_DEVICE(): number {
    return FrameView.RASTERIZE_RESOLUTION_MODIFIER / window.devicePixelRatio;
  }

  protected objectsLayer: paper.Layer;
  protected _model: Frame;

  /**
   * Create a frame view.
   */
  constructor(model: Frame) {
    super(model);

    this.objectsLayer = new this.paper.Layer();
    this.objectsLayer.remove();
  }

  /**
   * Write the changes made to the view to the frame.
   */
  applyChanges(): void {
    this._applyDrawableChanges();
  }

  /**
   * Update the view based on the model
   */
  render(): void {
    this._renderObjects();
  }

  protected _renderObjects(): void {
    this.objectsLayer.data.wickUUID = this.model.uuid;
    this.objectsLayer.data.wickType = "clipsandpaths";
    this.objectsLayer.removeChildren();

    // Remove placeholder paths if
    // 1) this frame is focused, or
    // 2) the project is playing
    if (
      this.model.parentClip.isFocus ||
      (this.model.project && this.model.project.playing)
    ) {
      this.model.paths.forEach((path) => {
        if (path.isPlaceholder) {
          path.remove();
        }
      });
    }

    const children = this.model.drawable.map((object) => {
      object.view.render();
      if (object.view.model instanceof Path) {
        return object.view.item;
      } else {
        return object.view.group;
      }
    });

    this.objectsLayer.addChildren(children);
  }

  protected _applyDrawableChanges(): void {
    this.model.drawable
      .filter((path) => path instanceof Path && path.isDynamicText)
      .forEach((path) => {
        path.view.item.bringToFront();
      });

    // Clear all WickPaths from the frame
    // Reorder clips
    const drawables = this.model.drawable.concat([]);
    drawables.forEach((drawable) => {
      // should really be remove child
      this.model.removeClip(drawable);
    });

    this.objectsLayer.children
      .filter((child) => child.data.wickType !== "gui")
      .forEach((child) => {
        if (child instanceof paper.Group) {
          this.model.addClip(
            drawables.find((g) => g.uuid === child.data.wickUUID)
          );
        } else {
          const originalWickPath = child.data.wickUUID
            ? this.model.project.ObjectCache.getObjectByUUID(
                child.data.wickUUID
              )
            : null;
          const pathJSON = this.model.project.View.Path.exportJSON(child);
          const wickPath = new Path({
            project: this.model.project,
            json: pathJSON,
          });
          this.model.addPath(wickPath);
          wickPath.fontWeight = originalWickPath
            ? originalWickPath.fontWeight
            : 400;
          wickPath.fontStyle = originalWickPath
            ? originalWickPath.fontStyle
            : "normal";
          wickPath.identifier = originalWickPath
            ? originalWickPath.identifier
            : null;
          child.name = wickPath.uuid;
        }
      });

    // Update clip transforms
    this.objectsLayer.children
      .filter((child) => child.data.wickType !== "gui")
      .forEach((child) => {
        if (child instanceof paper.Group) {
          const wickClip = this.model.project.ObjectCache.getObjectByUUID(
            child.data.wickUUID
          );
          wickClip.transformation = {
            x: child.position.x,
            y: child.position.y,
            scaleX: child.scaling.x,
            scaleY: child.scaling.y,
            rotation: child.rotation,
            opacity: child.opacity,
          };
        }
      });
  }
}
