/**
 * 图集批量导出工具函数
 * 参考 AtlasMergeSettings 中的导出设置，实现所有图集的批量导出
 */

import { open } from '@tauri-apps/plugin-dialog';
import { exportManager } from '../exportDialog/exportUtils';
import { exportSettingsStore, globalExportState } from '../stores/exportSettingsStore';
import type { AtlasResource, ImageResource } from '../types/imageType';
import type { ExportItem } from '../exportDialog/exportTypes';

/**
 * 🎯 图集批量导出预览数据接口
 */
export interface AtlasExportPreview {
  totalAtlases: number;
  validAtlases: number;
  invalidAtlases: number;
  totalImages: number;
  estimatedExportTime: number;
  atlasNames: string[];
}

/**
 * 🎯 分析所有图集的导出预览信息
 * @param atlases 所有图集数组
 * @returns 导出预览数据
 */
export async function analyzeAtlasesForExport(atlases: AtlasResource[]): Promise<AtlasExportPreview> {
  console.log('🔍 开始分析图集导出预览', {
    atlasesCount: atlases.length
  });

  const preview: AtlasExportPreview = {
    totalAtlases: atlases.length,
    validAtlases: 0,
    invalidAtlases: 0,
    totalImages: 0,
    estimatedExportTime: 0,
    atlasNames: []
  };

  for (const atlas of atlases) {
    // 检查图集是否有效（有子图片）
    const validImages = atlas.children?.filter(child => {
      return child.data ||
             child.processedData?.preview?.dataUrl ||
             child.processedData?.thumbnail?.dataUrl ||
             child.processedData?.original?.dataUrl ||
             child.blobUrl ||
             child.dataBase64;
    }) || [];

    if (validImages.length > 0) {
      preview.validAtlases++;
      preview.totalImages += validImages.length;
      preview.atlasNames.push(atlas.name);
    } else {
      preview.invalidAtlases++;
    }
  }

  // 预估导出时间（每个图集约2秒，每张图片约0.1秒）
  preview.estimatedExportTime = Math.ceil(preview.validAtlases * 2 + preview.totalImages * 0.1);

  console.log('📊 图集分析完成', preview);
  return preview;
}

/**
 * 🎯 导出所有图集
 * @param atlases 所有图集数组
 * @returns 导出结果
 */
export async function exportAllAtlases(
  atlases: AtlasResource[]
): Promise<{
  success: boolean;
  exportedCount: number;
  skippedCount: number;
  errors: string[];
}> {
  console.log('📤 开始导出所有图集', {
    atlasesCount: atlases.length
  });

  const result = {
    success: false,
    exportedCount: 0,
    skippedCount: 0,
    errors: [] as string[]
  };

  try {
    // 1. 获取当前导出设置
    let currentSettings: any = null;
    const unsubscribe = exportSettingsStore.subscribe(settings => {
      currentSettings = settings;
    });
    unsubscribe();

    if (!currentSettings) {
      throw new Error('无法获取导出设置');
    }

    // 2. 选择导出文件夹
    const selectedPath = await open({
      title: '选择图集导出文件夹',
      directory: true,
      multiple: false
    });

    if (!selectedPath || typeof selectedPath !== 'string') {
      throw new Error('用户取消了导出');
    }

    console.log('📁 选择的导出文件夹:', selectedPath);

    // 🎯 只有在选择完文件夹后才开始显示全局导出遮罩层
    globalExportState.update(state => ({
      ...state,
      isExporting: true,
      exportType: 'atlas',
      currentFile: '',
      progress: { current: 0, total: atlases.length, stage: 'preparing' },
      startTime: Date.now()
    }));

    // 3. 过滤出有效的图集
    const validAtlases = atlases.filter(atlas => {
      const validImages = atlas.children?.filter(child => {
        return child.data ||
               child.processedData?.preview?.dataUrl ||
               child.processedData?.thumbnail?.dataUrl ||
               child.processedData?.original?.dataUrl ||
               child.blobUrl ||
               child.dataBase64;
      }) || [];

      return validImages.length > 0;
    });

    console.log('📋 有效图集数量:', validAtlases.length);

    // 4. 🎯 确保导出器已注册
    const { registerExporters } = await import('../exportDialog/formats');
    registerExporters();

    // 5. 逐个导出图集
    let exportedCount = 0;

    for (let i = 0; i < validAtlases.length; i++) {
      const atlas = validAtlases[i];

      globalExportState.update(state => ({
        ...state,
        currentFile: atlas.name,
        progress: { ...state.progress, current: i + 1 }
      }));

      try {
        console.log(`📤 导出图集 ${i + 1}/${validAtlases.length}: ${atlas.name}`);

        // 使用现有的图集导出逻辑
        const success = await exportSingleAtlas(atlas, selectedPath, currentSettings);

        if (success) {
          exportedCount++;
          console.log('✅ 图集导出成功:', atlas.name);
        } else {
          result.errors.push(`图集 ${atlas.name} 导出失败`);
          result.skippedCount++;
        }

      } catch (error) {
        console.error('❌ 图集导出失败:', atlas.name, error);
        result.errors.push(`图集 ${atlas.name} 导出失败: ${error instanceof Error ? error.message : String(error)}`);
        result.skippedCount++;
      }
    }

    result.exportedCount = exportedCount;
    result.success = result.errors.length === 0 || exportedCount > 0;

    console.log('✅ 所有图集导出完成', result);

  } catch (error) {
    console.error('❌ 图集批量导出失败:', error);
    result.errors.push(`导出过程发生错误: ${error instanceof Error ? error.message : String(error)}`);
  } finally {
    // 清除全局导出状态
    globalExportState.update(state => ({
      ...state,
      isExporting: false,
      exportType: null,
      currentFile: '',
      progress: { current: 0, total: 0, stage: 'preparing' },
      startTime: null
    }));
  }

  return result;
}

/**
 * 🎯 导出单个图集（复用 AtlasMergeSettings 的逻辑）
 * @param atlas 图集资源
 * @param exportPath 导出路径
 * @param settings 导出设置
 * @returns 是否成功
 */
async function exportSingleAtlas(
  atlas: AtlasResource,
  exportPath: string,
  settings: any
): Promise<boolean> {
  try {
    console.log('📤 开始导出单个图集:', atlas.name);

    // 检查图集中的图片
    if (!atlas.children || atlas.children.length === 0) {
      console.warn('⚠️ 图集中没有可导出的图片');
      return false;
    }

    // 🎯 过滤出有效的图片资源（有图片数据的）
    const validImages = atlas.children.filter(child => {
      const hasData = child.data ||
                     child.processedData?.preview?.dataUrl ||
                     child.processedData?.thumbnail?.dataUrl ||
                     child.processedData?.original?.dataUrl ||
                     child.blobUrl ||
                     child.dataBase64;

      if (!hasData) {
        console.warn('⚠️ 跳过没有图片数据的资源:', child.name);
      }

      return hasData;
    });

    if (validImages.length === 0) {
      console.warn('⚠️ 图集中没有有效的图片数据');
      return false;
    }

    // 🎯 使用布局算法计算图片位置
    // const { calculateImageLayout } = await import('../center/imageLayoutCalculator');
    const layoutSettings = atlas.layoutSettings || {
      padding: 10,
      spacing: 5,
      algorithm: 'maxrects',
      powerOfTwo: false,
      allowRotation: false,
      maxWidth: 1024,
      maxHeight: 1024
    };

    console.log('🎯 计算图集布局', { layoutSettings, imageCount: validImages.length });

    // const layoutResult = await calculateImageLayout(validImages, layoutSettings);

    // if (!layoutResult.success) {
    //   console.error('❌ 图集布局计算失败');
    //   return false;
    // }

    // // 🎯 生成合并的图集图片
    // const mergedImageBuffer = await generateMergedAtlasImage(layoutResult);

    // if (!mergedImageBuffer) {
    //   console.error('❌ 生成合并图集图片失败');
    //   return false;
    // }

    // // 🎯 将布局结果转换为 cropAreas 格式
    // const cropAreas = layoutResult.images.map((imageLayout: any) => ({
    //   id: imageLayout.resource.id,
    //   name: imageLayout.resource.name,
    //   x: imageLayout.x,
    //   y: imageLayout.y,
    //   width: imageLayout.width,
    //   height: imageLayout.height
    // }));

    // 🎯 创建图集导出项目
    // const exportItems: ExportItem[] = [{
    //   id: atlas.id,
    //   name: atlas.name,
    //   resource: {
    //     id: atlas.id,
    //     name: atlas.name,
    //     buffer: mergedImageBuffer,
    //     path: atlas.path || atlas.name,
    //     width: layoutResult.boundingBox.width,
    //     height: layoutResult.boundingBox.height
    //   } as any,
    //   cropAreas: cropAreas
    // }];

    // 执行导出
    // const result = await exportManager.export(
    //   settings.selectedType || 'plist',
    //   exportItems,
    //   {
    //     type: settings.selectedType || 'plist',
    //     outputPath: exportPath,
    //     fileName: settings.fileName || atlas.name,
    //     format: settings.format || 'png',
    //     quality: settings.quality || 0.9,
    //     includeOriginal: settings.includeOriginal || false,
    //     namePrefix: settings.namePrefix || '',
    //     nameSuffix: settings.nameSuffix || ''
    //   },
    //   exportPath,
    //   (progress) => {
    //     console.log('📊 图集导出进度', progress);
    //   }
    // );

    // return result.success;

  } catch (error) {
    console.error('❌ 导出单个图集时发生错误:', error);
    return false;
  }
}

/**
 * 🎯 生成合并的图集图片（复用 exportUtils.ts 的逻辑）
 * @param layoutResult 布局计算结果
 * @returns 合并后的图片数据
 */
async function generateMergedAtlasImage(layoutResult: any): Promise<Uint8Array | null> {
  try {
    console.log('🎨 开始生成合并图集图片', {
      boundingBox: layoutResult.boundingBox,
      imageCount: layoutResult.images.length
    });

    // 创建Canvas
    const canvas = document.createElement('canvas');
    canvas.width = layoutResult.boundingBox.width;
    canvas.height = layoutResult.boundingBox.height;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      console.error('❌ 无法获取Canvas上下文');
      return null;
    }

    // 清空Canvas（透明背景）
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 加载并绘制每个图片
    // const imagePromises = layoutResult.images.map(async (imageLayout: any) => {
    //   try {
    //     const imageResource = imageLayout.resource;

    //     // 🎯 从ImageResource加载图片
    //     const { loadImageFromResource } = await import('../center/imageLoader');
    //     const loadResult = await loadImageFromResource(imageResource);

    //     if (!loadResult.success || !loadResult.image) {
    //       console.warn('⚠️ 加载图片失败:', imageResource.name);
    //       return false;
    //     }

    //     // 绘制到Canvas上
    //     ctx.drawImage(
    //       loadResult.image,
    //       imageLayout.x,
    //       imageLayout.y,
    //       imageLayout.width,
    //       imageLayout.height
    //     );

    //     console.log('✅ 绘制图片成功:', {
    //       name: imageResource.name,
    //       position: `${imageLayout.x},${imageLayout.y}`,
    //       size: `${imageLayout.width}×${imageLayout.height}`
    //     });

    //     return true;
    //   } catch (error) {
    //     console.error('❌ 绘制图片失败:', imageLayout.resource.name, error);
    //     return false;
    //   }
    // });

    // 等待所有图片绘制完成
    // const results = await Promise.all(imagePromises);
    // const successCount = results.filter(Boolean).length;

    // console.log('🎨 图片绘制完成', {
    //   成功: successCount,
    //   总数: layoutResult.images.length,
    //   Canvas尺寸: `${canvas.width}×${canvas.height}`
    // });

    // if (successCount === 0) {
    //   console.error('❌ 没有成功绘制任何图片');
    //   return null;
    // }

    // // 将Canvas转换为Blob
    // const blob = await new Promise<Blob | null>((resolve) => {
    //   canvas.toBlob(resolve, 'image/png', 1.0);
    // });

    // if (!blob) {
    //   console.error('❌ Canvas转换为Blob失败');
    //   return null;
    // }

    // // 将Blob转换为ArrayBuffer
    // const arrayBuffer = await blob.arrayBuffer();
    // const uint8Array = new Uint8Array(arrayBuffer);

    // console.log('✅ 合并图集图片生成成功', {
    //   尺寸: `${canvas.width}×${canvas.height}`,
    //   文件大小: `${(uint8Array.length / 1024).toFixed(2)}KB`
    // });

    // return uint8Array;

  } catch (error) {
    console.error('❌ 生成合并图集图片时发生错误:', error);
    return null;
  }
}
