/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";

export class SelectionBox extends GUIElement {
  protected _mouseStart: { x: number; y: number } | null;
  protected _mouseEnd: { x: number; y: number } | null;
  protected _mouseDiff: { x: number; y: number } | null;

  constructor(model: Base) {
    super(model);
    this._mouseStart = null;
    this._mouseEnd = null;
    this._mouseDiff = null;
  }

  draw(): void {
    super.draw();

    const ctx = this.ctx;

    this._mouseStart = this._mouseStart || {
      x: this.localMouse.x,
      y: this.localMouse.y,
    };
    this._mouseEnd = {
      x: this.localMouse.x,
      y: this.localMouse.y,
    };
    this._mouseDiff = {
      x: this._mouseEnd.x - this._mouseStart.x,
      y: this._mouseEnd.y - this._mouseStart.y,
    };

    // Draw selection box
    ctx.strokeStyle = GUIElement.SELECTION_BOX_COLOR;
    ctx.lineWidth = GUIElement.SELECTION_BOX_LINE_WIDTH;
    ctx.setLineDash([5, 5]);

    const x = Math.min(this._mouseStart.x, this._mouseEnd.x);
    const y = Math.min(this._mouseStart.y, this._mouseEnd.y);
    const width = Math.abs(this._mouseEnd.x - this._mouseStart.x);
    const height = Math.abs(this._mouseEnd.y - this._mouseStart.y);

    ctx.strokeRect(x, y, width, height);
  }

  finish(): void {
    const timeline = this.model.parentTimeline;
    const frames = timeline.getAllFrames();

    // Get frames that are inside the selection box
    const selectedFrames = frames.filter((frame) => {
      const frameX = frame.start * this.gridCellWidth;
      const frameY = frame.parentLayer.index * this.gridCellHeight;
      const frameWidth = frame.length * this.gridCellWidth;
      const frameHeight = this.gridCellHeight;

      const selectionX = Math.min(this._mouseStart.x, this._mouseEnd.x);
      const selectionY = Math.min(this._mouseStart.y, this._mouseEnd.y);
      const selectionWidth = Math.abs(this._mouseEnd.x - this._mouseStart.x);
      const selectionHeight = Math.abs(this._mouseEnd.y - this._mouseStart.y);

      return (
        frameX < selectionX + selectionWidth &&
        frameX + frameWidth > selectionX &&
        frameY < selectionY + selectionHeight &&
        frameY + frameHeight > selectionY
      );
    });

    // Select the frames
    timeline.project.selection.clear();
    timeline.project.selection.select(selectedFrames);
  }
}
