<script lang="ts">
  /**
   * ResourcePreview 组件 - 使用统一Canvas预览组件
   * 支持缩放、拖拽、键盘控制等功能
   */

  import type { ResourceItem } from '../../types';
  import CanvasPreview from './CanvasPreview.svelte';

  interface Props {
    resource: ResourceItem;
    height?: number;
    showControls?: boolean;
  }

  let {
    resource,
    height = 200,
    showControls = true
  }: Props = $props();
</script>

<div class="resource-preview" style="height: {height}px;">
  {#if resource.type === 'image'}
    <!-- 使用统一的Canvas预览组件 -->
    <CanvasPreview
      {resource}
      width={400}
      {height}
      {showControls}
    />
  {:else}
    <!-- 文件夹预览 -->
    <div class="folder-preview">
      <span class="folder-icon">📁</span>
      <span class="folder-label">文件夹</span>
      <span class="folder-name">{resource.name}</span>
    </div>
  {/if}
</div>

<style>
  .resource-preview {
    background: var(--theme-surface-light, #f8f9fa);
    border: 1px solid var(--theme-border, #e1e5e9);
    border-radius: var(--border-radius-large, 8px);
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  /* 文件夹预览 */
  .folder-preview {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2, 8px);
    color: var(--theme-text-secondary, #6b7280);
    height: 100%;
  }

  .folder-icon {
    font-size: 3rem;
    opacity: 0.7;
  }

  .folder-label {
    font-size: var(--font-size-sm, 14px);
    font-weight: 500;
    color: var(--theme-text-secondary, #6b7280);
  }

  .folder-name {
    font-size: var(--font-size-xs, 12px);
    color: var(--theme-text, #2c3e50);
    text-align: center;
    word-break: break-word;
    max-width: 80%;
  }
</style>
