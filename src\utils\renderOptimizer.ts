/**
 * 渲染优化工具类
 * 提供Canvas渲染、图片处理等性能优化功能
 */

import { rafDebounce, throttle } from './debounce';
import { performanceMonitor } from './performanceMonitor';

/**
 * Canvas渲染优化器
 */
export class CanvasRenderOptimizer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private renderQueue: Array<() => void> = [];
  private isRendering = false;
  private lastRenderTime = 0;
  private targetFPS = 60;
  private frameInterval: number;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    const context = canvas.getContext('2d');
    if (!context) {
      throw new Error('无法获取Canvas 2D上下文');
    }
    this.ctx = context;
    this.frameInterval = 1000 / this.targetFPS;
  }

  /**
   * 🎯 优化的渲染调度
   */
  private scheduleRender = rafDebounce(() => {
    if (this.isRendering) return;

    const now = performance.now();
    if (now - this.lastRenderTime < this.frameInterval) {
      // 如果距离上次渲染时间太短，延迟到下一帧
      setTimeout(() => this.scheduleRender(), this.frameInterval - (now - this.lastRenderTime));
      return;
    }

    this.isRendering = true;
    this.lastRenderTime = now;

    performanceMonitor.startMeasure('canvas-render-batch');

    try {
      // 批量执行渲染队列
      while (this.renderQueue.length > 0) {
        const renderFunc = this.renderQueue.shift();
        if (renderFunc) {
          renderFunc();
        }
      }
    } finally {
      this.isRendering = false;
      performanceMonitor.endMeasure('canvas-render-batch');
    }
  });

  /**
   * 添加渲染任务到队列
   */
  queueRender(renderFunc: () => void): void {
    this.renderQueue.push(renderFunc);
    this.scheduleRender();
  }

  /**
   * 🎯 优化的图片绘制
   */
  drawImageOptimized(
    image: HTMLImageElement,
    sx: number, sy: number, sw: number, sh: number,
    dx: number, dy: number, dw: number, dh: number
  ): void {
    this.queueRender(() => {
      // 检查图片是否在可视区域内
      if (this.isInViewport(dx, dy, dw, dh)) {
        this.ctx.drawImage(image, sx, sy, sw, sh, dx, dy, dw, dh);
      }
    });
  }

  /**
   * 检查矩形是否在Canvas可视区域内
   */
  private isInViewport(x: number, y: number, width: number, height: number): boolean {
    return !(x + width < 0 || y + height < 0 || x > this.canvas.width || y > this.canvas.height);
  }

  /**
   * 清空Canvas
   */
  clear(): void {
    this.queueRender(() => {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    });
  }

  /**
   * 设置目标FPS
   */
  setTargetFPS(fps: number): void {
    this.targetFPS = Math.max(1, Math.min(120, fps));
    this.frameInterval = 1000 / this.targetFPS;
  }

  /**
   * 获取渲染统计信息
   */
  getRenderStats(): {
    queueLength: number;
    isRendering: boolean;
    targetFPS: number;
    lastRenderTime: number;
  } {
    return {
      queueLength: this.renderQueue.length,
      isRendering: this.isRendering,
      targetFPS: this.targetFPS,
      lastRenderTime: this.lastRenderTime
    };
  }
}

/**
 * 图片处理优化器
 */
export class ImageProcessingOptimizer {
  private static instance: ImageProcessingOptimizer;
  private processingQueue: Array<() => Promise<void>> = [];
  private isProcessing = false;
  private maxConcurrent = 3;

  static getInstance(): ImageProcessingOptimizer {
    if (!ImageProcessingOptimizer.instance) {
      ImageProcessingOptimizer.instance = new ImageProcessingOptimizer();
    }
    return ImageProcessingOptimizer.instance;
  }

  /**
   * 🎯 队列化图片处理任务
   */
  async queueImageProcessing<T>(
    task: () => Promise<T>,
    priority: 'high' | 'normal' | 'low' = 'normal'
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const wrappedTask = async () => {
        try {
          const result = await task();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      };

      // 根据优先级插入队列
      if (priority === 'high') {
        this.processingQueue.unshift(wrappedTask);
      } else {
        this.processingQueue.push(wrappedTask);
      }

      this.processQueue();
    });
  }

  /**
   * 处理队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.processingQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // 并发处理多个任务
      const currentBatch = this.processingQueue.splice(0, this.maxConcurrent);
      await Promise.all(currentBatch.map(task => task()));

      // 如果还有任务，继续处理
      if (this.processingQueue.length > 0) {
        setTimeout(() => this.processQueue(), 0);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 🎯 优化的图片缩放
   */
  async resizeImageOptimized(
    imageData: ArrayBuffer,
    targetWidth: number,
    targetHeight: number,
    quality = 0.9
  ): Promise<Blob> {
    return this.queueImageProcessing(async () => {
      performanceMonitor.startMeasure('image-resize', {
        targetSize: `${targetWidth}×${targetHeight}`,
        quality
      });

      try {
        // 使用OffscreenCanvas进行图片处理（如果支持）
        if ('OffscreenCanvas' in window) {
          return await this.resizeWithOffscreenCanvas(imageData, targetWidth, targetHeight, quality);
        } else {
          return await this.resizeWithCanvas(imageData, targetWidth, targetHeight, quality);
        }
      } finally {
        performanceMonitor.endMeasure('image-resize');
      }
    }, 'normal');
  }

  /**
   * 使用OffscreenCanvas缩放图片
   */
  private async resizeWithOffscreenCanvas(
    imageData: ArrayBuffer,
    targetWidth: number,
    targetHeight: number,
    quality: number
  ): Promise<Blob> {
    const blob = new Blob([imageData]);
    const bitmap = await createImageBitmap(blob);

    const canvas = new OffscreenCanvas(targetWidth, targetHeight);
    const ctx = canvas.getContext('2d')!;

    // 使用高质量缩放
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    ctx.drawImage(bitmap, 0, 0, targetWidth, targetHeight);
    bitmap.close();

    return await canvas.convertToBlob({ type: 'image/png', quality });
  }

  /**
   * 使用普通Canvas缩放图片
   */
  private async resizeWithCanvas(
    imageData: ArrayBuffer,
    targetWidth: number,
    targetHeight: number,
    quality: number
  ): Promise<Blob> {
    return new Promise((resolve, reject) => {
      const blob = new Blob([imageData]);
      const img = new Image();

      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;

        canvas.width = targetWidth;
        canvas.height = targetHeight;

        // 使用高质量缩放
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('图片缩放失败'));
            }
          },
          'image/png',
          quality
        );

        URL.revokeObjectURL(img.src);
      };

      img.onerror = () => {
        reject(new Error('图片加载失败'));
        URL.revokeObjectURL(img.src);
      };

      img.src = URL.createObjectURL(blob);
    });
  }

  /**
   * 设置最大并发数
   */
  setMaxConcurrent(max: number): void {
    this.maxConcurrent = Math.max(1, Math.min(10, max));
  }

  /**
   * 获取处理统计信息
   */
  getProcessingStats(): {
    queueLength: number;
    isProcessing: boolean;
    maxConcurrent: number;
  } {
    return {
      queueLength: this.processingQueue.length,
      isProcessing: this.isProcessing,
      maxConcurrent: this.maxConcurrent
    };
  }
}

// 导出单例实例
export const canvasRenderOptimizer = {
  create: (canvas: HTMLCanvasElement) => new CanvasRenderOptimizer(canvas)
};

export const imageProcessingOptimizer = ImageProcessingOptimizer.getInstance();
