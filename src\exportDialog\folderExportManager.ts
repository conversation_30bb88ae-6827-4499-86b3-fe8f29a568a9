/**
 * 文件夹导出管理器
 * 扩展现有的导出系统，支持保持文件夹结构的批量导出
 */

import { invoke } from '@tauri-apps/api/core';
import type { ExportConfig, ExportResult, ExportProgress } from './exportTypes';
import type { ExportItemWithPath } from '../utils/folderExportUtils';
import { groupItemsByFolder } from '../utils/folderExportUtils';
import { exportManager } from './exportUtils';
import { sanitizeFileName, formatFileSize } from './exportUtils';
import { registerExporters } from './formats';

/**
 * 文件夹导出配置
 */
export interface FolderExportConfig extends ExportConfig {
  preserveFolderStructure: boolean;  // 是否保持文件夹结构
  createSubfolders: boolean;         // 是否创建子文件夹
  flattenStructure: boolean;         // 是否扁平化结构（所有文件放在一个目录）
}

/**
 * 文件夹导出结果
 */
export interface FolderExportResult extends ExportResult {
  foldersCreated: string[];          // 创建的文件夹列表
  folderStructure: {                 // 文件夹结构信息
    [folderPath: string]: {
      fileCount: number;
      totalSize: number;
    };
  };
}

/**
 * 文件夹导出管理器类
 */
export class FolderExportManager {

  /**
   * 导出文件夹，保持目录结构
   * @param type 导出类型
   * @param items 带路径信息的导出项目
   * @param config 导出配置
   * @param selectedFolder 目标文件夹
   * @param onProgress 进度回调
   * @returns 导出结果
   */
  async exportWithFolderStructure(
    type: string,
    items: ExportItemWithPath[],
    config: FolderExportConfig,
    selectedFolder: string,
    onProgress?: (progress: ExportProgress) => void
  ): Promise<FolderExportResult> {
    const startTime = Date.now();
    const exportedFiles: FolderExportResult['files'] = [];
    const foldersCreated: string[] = [];
    const folderStructure: FolderExportResult['folderStructure'] = {};
    let totalSize = 0;

    console.log('🚀 FolderExportManager: 开始文件夹导出', {
      type,
      itemCount: items.length,
      config,
      selectedFolder
    });

    try {
      // 🎯 确保导出器已注册
      registerExporters();
      console.log('✅ FolderExportManager: 导出器注册完成');

      // 报告准备阶段
      onProgress?.({
        current: 0,
        total: items.length,
        currentFile: '准备导出...',
        stage: 'preparing'
      });

      // 按目录分组
      const folderGroups = groupItemsByFolder(items);

      // 如果配置为扁平化结构，将所有文件放在根目录
      if (config.flattenStructure) {
        const allItems = Array.from(folderGroups.values()).flat();
        folderGroups.clear();
        folderGroups.set('', allItems); // 空字符串表示根目录
      }

      let processedItems = 0;
      const totalItems = items.length;

      // 为每个目录组处理导出
      for (const [relativePath, groupItems] of folderGroups) {
        const targetDir = this.getTargetDirectory(selectedFolder, relativePath, config);

        console.log('📁 FolderExportManager: 处理目录组', {
          relativePath: relativePath || '(根目录)',
          targetDir,
          itemCount: groupItems.length
        });

        // 创建目标目录（如果需要）
        if (relativePath && config.createSubfolders) {
          await this.ensureDirectoryExists(targetDir);
          foldersCreated.push(targetDir);
        }

        // 初始化文件夹结构统计
        const folderKey = relativePath || '(根目录)';
        folderStructure[folderKey] = {
          fileCount: 0,
          totalSize: 0
        };

        // 转换为标准导出项目（移除路径信息）
        const standardItems = groupItems.map(item => ({
          id: item.id,
          name: item.name,
          resource: item.resource,
          cropAreas: item.cropAreas
        }));

        // 使用现有的导出器导出这个目录的文件
        try {
          onProgress?.({
            current: processedItems,
            total: totalItems,
            currentFile: `导出目录: ${folderKey}`,
            stage: 'processing'
          });

          // 🎯 修复：将targetDir设置到config.outputPath中，避免重复的文件夹选择
          const configWithOutputPath = {
            ...config,
            outputPath: targetDir
          };

          const result = await exportManager.export(
            type,
            standardItems,
            configWithOutputPath,
            targetDir,
            (progress) => {
              // 转换进度信息
              const adjustedProgress = {
                current: processedItems + progress.current,
                total: totalItems,
                currentFile: progress.currentFile,
                stage: progress.stage
              };
              onProgress?.(adjustedProgress);
            }
          );

          if (result.success) {
            // 合并导出结果
            exportedFiles.push(...result.files);
            totalSize += result.totalSize;

            // 更新文件夹统计
            folderStructure[folderKey].fileCount = result.files.length;
            folderStructure[folderKey].totalSize = result.totalSize;

            console.log('✅ FolderExportManager: 目录导出成功', {
              folderKey,
              fileCount: result.files.length,
              totalSize: formatFileSize(result.totalSize)
            });
          } else {
            console.error('❌ FolderExportManager: 目录导出失败', {
              folderKey,
              error: result.error
            });
            throw new Error(`导出目录 "${folderKey}" 失败: ${result.error}`);
          }

        } catch (error) {
          console.error('❌ FolderExportManager: 目录导出异常', {
            folderKey,
            error
          });
          throw error;
        }

        processedItems += groupItems.length;
      }

      // 报告完成
      onProgress?.({
        current: totalItems,
        total: totalItems,
        currentFile: '导出完成',
        stage: 'completed'
      });

      const duration = Date.now() - startTime;

      const finalResult: FolderExportResult = {
        success: true,
        files: exportedFiles,
        totalSize,
        duration,
        foldersCreated,
        folderStructure
      };

      console.log('✅ FolderExportManager: 文件夹导出完成', {
        totalFiles: exportedFiles.length,
        totalSize: formatFileSize(totalSize),
        foldersCreated: foldersCreated.length,
        duration: `${duration}ms`
      });

      return finalResult;

    } catch (error) {
      console.error('❌ FolderExportManager: 文件夹导出失败', error);

      const duration = Date.now() - startTime;
      return {
        success: false,
        files: exportedFiles,
        totalSize,
        duration,
        foldersCreated,
        folderStructure,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 获取目标目录路径
   * @param baseFolder 基础文件夹
   * @param relativePath 相对路径
   * @param config 配置
   * @returns 目标目录路径
   */
  private getTargetDirectory(
    baseFolder: string,
    relativePath: string,
    config: FolderExportConfig
  ): string {
    if (!relativePath || !config.preserveFolderStructure) {
      return baseFolder;
    }

    // 清理和标准化路径
    const cleanPath = relativePath.replace(/[<>:"|?*]/g, '_').replace(/\\/g, '/');
    return `${baseFolder}/${cleanPath}`;
  }

  /**
   * 确保目录存在
   * @param dirPath 目录路径
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      console.log('📁 FolderExportManager: 创建目录', dirPath);

      // 调用Rust后端创建目录
      await invoke('create_directory', { path: dirPath });

      console.log('✅ FolderExportManager: 目录创建成功', dirPath);
    } catch (error) {
      console.error('❌ FolderExportManager: 目录创建失败', { dirPath, error });
      throw new Error(`创建目录失败: ${dirPath} - ${error}`);
    }
  }

  /**
   * 验证导出配置
   * @param config 导出配置
   * @returns 验证结果
   */
  validateConfig(config: FolderExportConfig): { isValid: boolean; error?: string } {
    if (config.flattenStructure && config.preserveFolderStructure) {
      return {
        isValid: false,
        error: '不能同时启用扁平化结构和保持文件夹结构'
      };
    }

    if (config.createSubfolders && !config.preserveFolderStructure) {
      return {
        isValid: false,
        error: '创建子文件夹需要启用保持文件夹结构'
      };
    }

    return { isValid: true };
  }
}

// 创建全局实例
export const folderExportManager = new FolderExportManager();
