<script lang="ts">
  /**
   * 导出选项组件 - 右侧导出设置面板
   */

  import type { ExportUIState, ExportProgress } from './exportTypes';
  import { EXPORT_TYPES, type ExportTypeKey } from './formats';

  // 导入国际化
  import { _ } from '../lib/i18n';

  interface Props {
    state: ExportUIState;
    progress?: ExportProgress;
    isExporting?: boolean;
    onStateChange: (newState: Partial<ExportUIState>) => void;
    onExport: () => void;
    onCancel: () => void;
  }

  let {
    state,
    progress,
    isExporting = false,
    onStateChange,
    onExport,
    onCancel
  }: Props = $props();

  // 当前导出类型配置
  const currentTypeConfig = $derived(() => {
    return EXPORT_TYPES[state.selectedType];
  });

  // 获取国际化的导出类型配置
  const getLocalizedExportTypes = $derived(() => {
    return Object.entries(EXPORT_TYPES).map(([key, config]) => ({
      key: key as ExportTypeKey,
      ...config,
      name: $_(`exportTypes.${key}.name`),
      description: $_(`exportTypes.${key}.description`)
    }));
  });

  // 更新状态的辅助函数
  function updateState(updates: Partial<ExportUIState>) {
    onStateChange(updates);
  }

  // 格式化进度百分比
  const progressPercent = $derived(() => {
    if (!progress) return 0;
    return Math.round((progress.current / progress.total) * 100);
  });

  // 进度阶段文本
  const progressStageText = $derived(() => {
    if (!progress) return '';
    switch (progress.stage) {
      case 'preparing': return '准备中...';
      case 'processing': return '处理中...';
      case 'saving': return '保存中...';
      case 'completed': return '完成';
      default: return '';
    }
  });
</script>

<div class="export-options">
  <div class="options-header">
    <h4>导出选项</h4>
  </div>

  <div class="options-content">
    <!-- 导出类型选择 -->
    <div class="option-group">
      <label class="option-label">{$_('export.type')}</label>
      <div class="export-types">
        {#each getLocalizedExportTypes() as config}
          <button
            class="type-btn"
            class:active={state.selectedType === config.key}
            onclick={() => updateState({ selectedType: config.key })}
            disabled={isExporting}
          >
            <span class="type-icon">{config.icon}</span>
            <div class="type-info">
              <span class="type-name">{config.name}</span>
              <span class="type-desc">{config.description}</span>
            </div>
          </button>
        {/each}
      </div>
    </div>

    <!-- 通用选项 -->
    <div class="option-group">
      <label class="option-label">{$_('export.filename')}</label>
      <input
        type="text"
        class="option-input"
        bind:value={state.fileName}
        placeholder={$_('ui.placeholder')}
        disabled={isExporting}
      />
    </div>

    <!-- 图片导出特有选项 -->
    {#if state.selectedType === 'cropped-images'}
      <div class="option-group">
        <label class="option-label">{$_('export.format')}</label>
        <select
          class="option-select"
          bind:value={state.format}
          disabled={isExporting}
        >
          {#each currentTypeConfig().formats as format}
            <option value={format}>{format.toUpperCase()}</option>
          {/each}
        </select>
      </div>

      {#if state.format === 'jpg' || state.format === 'webp'}
        <div class="option-group">
          <label class="option-label">
            {$_('export.quality')} ({Math.round(state.quality * 100)}%)
          </label>
          <input
            type="range"
            class="option-range"
            min="0.1"
            max="1"
            step="0.1"
            bind:value={state.quality}
            disabled={isExporting}
          />
        </div>
      {/if}

      <div class="option-group">
        <label class="option-checkbox">
          <input
            type="checkbox"
            bind:checked={state.includeOriginal}
            disabled={isExporting}
          />
          <span>{$_('export.includeOriginal')}</span>
        </label>
      </div>

      <div class="option-group">
        <label class="option-label">{$_('export.prefix')}</label>
        <input
          type="text"
          class="option-input"
          bind:value={state.namePrefix}
          placeholder={$_('ui.optional')}
          disabled={isExporting}
        />
      </div>

      <div class="option-group">
        <label class="option-label">{$_('export.suffix')}</label>
        <input
          type="text"
          class="option-input"
          bind:value={state.nameSuffix}
          placeholder={$_('ui.optional')}
          disabled={isExporting}
        />
      </div>
    {/if}

    <!-- 图集格式导出特有选项 -->
    {#if ['plist', 'json-array', 'json-hash', 'unity', 'phaser3', 'gamemaker', 'unreal-paper2d', 'godot', 'defold', 'libgdx', 'xml-generic'].includes(state.selectedType)}
      <div class="option-group">
        <label class="option-checkbox">
          <input
            type="checkbox"
            bind:checked={state.includeOriginal}
            disabled={isExporting}
          />
          <span>{$_('export.includeOriginal')}（推荐）</span>
        </label>
        <div class="option-hint">
          游戏引擎需要数据文件和对应的图片文件才能正常工作
        </div>
      </div>
    {/if}

    <!-- 导出进度 -->
    {#if isExporting && progress}
      <div class="progress-section">
        <div class="progress-header">
          <span class="progress-text">{progressStageText}</span>
          <span class="progress-percent">{progressPercent}%</span>
        </div>
        <div class="progress-bar">
          <div
            class="progress-fill"
            style="width: {progressPercent}%"
          ></div>
        </div>
        <div class="progress-file">
          {progress.currentFile}
        </div>
      </div>
    {/if}
  </div>

  <!-- 操作按钮 -->
  <div class="options-footer">
    <button
      class="btn btn-secondary"
      onclick={onCancel}
      disabled={isExporting}
    >
      {$_('actions.cancel')}
    </button>
    <button
      class="btn btn-primary"
      onclick={onExport}
      disabled={isExporting}
    >
      {#if isExporting}
        <span class="loading-spinner"></span>
        {$_('status.exporting')}
      {:else}
        {$_('actions.export')}
      {/if}
    </button>
  </div>
</div>

<style>
  .export-options {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius-large);
    overflow: hidden;
  }

  .options-header {
    padding: var(--spacing-3);
    border-bottom: 1px solid var(--theme-border);
    background: var(--theme-surface-light);
  }

  .options-header h4 {
    margin: 0;
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--theme-text);
  }

  .options-content {
    flex: 1;
    padding: var(--spacing-3);
    overflow-y: auto;
  }

  .option-group {
    margin-bottom: var(--spacing-4);
  }

  .option-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--theme-text);
    margin-bottom: var(--spacing-2);
  }

  .export-types {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .type-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    background: var(--theme-surface-light);
    border: 2px solid var(--theme-border);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-base);
    text-align: left;
  }

  .type-btn:hover {
    border-color: var(--theme-primary);
    background: var(--theme-surface);
  }

  .type-btn.active {
    border-color: var(--theme-primary);
    background: var(--theme-primary-light);
  }

  .type-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .type-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
  }

  .type-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }

  .type-name {
    font-weight: 500;
    color: var(--theme-text);
    font-size: var(--font-size-sm);
  }

  .type-desc {
    font-size: var(--font-size-xs);
    color: var(--theme-text-secondary);
    line-height: 1.3;
  }

  .option-input,
  .option-select {
    width: 100%;
    padding: var(--spacing-2);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    background: var(--theme-surface-light);
    color: var(--theme-text);
    font-size: var(--font-size-sm);
    transition: var(--transition-base);
  }

  .option-input:focus,
  .option-select:focus {
    outline: none;
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px var(--theme-primary-light);
  }

  .option-input:disabled,
  .option-select:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .option-range {
    width: 100%;
    height: 6px;
    background: var(--theme-border);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
  }

  .option-range::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--theme-primary);
    border-radius: 50%;
    cursor: pointer;
  }

  .option-checkbox {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    cursor: pointer;
    font-size: var(--font-size-sm);
  }

  .option-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--theme-primary);
  }

  .progress-section {
    padding: var(--spacing-3);
    background: var(--theme-surface-light);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-4);
  }

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-2);
  }

  .progress-text {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--theme-text);
  }

  .progress-percent {
    font-size: var(--font-size-sm);
    color: var(--theme-primary);
    font-weight: 600;
  }

  .progress-bar {
    height: 8px;
    background: var(--theme-border);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: var(--spacing-2);
  }

  .progress-fill {
    height: 100%;
    background: var(--theme-primary);
    transition: width 0.3s ease;
  }

  .progress-file {
    font-size: var(--font-size-xs);
    color: var(--theme-text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .options-footer {
    padding: var(--spacing-3);
    border-top: 1px solid var(--theme-border);
    display: flex;
    gap: var(--spacing-2);
    justify-content: flex-end;
    background: var(--theme-surface-light);
  }

  .btn {
    padding: var(--spacing-2) var(--spacing-4);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-base);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-secondary {
    background: var(--theme-surface);
    color: var(--theme-text);
    border: 1px solid var(--theme-border);
  }

  .btn-secondary:hover:not(:disabled) {
    background: var(--theme-surface-light);
    border-color: var(--theme-border-dark);
  }

  .btn-primary {
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
  }

  .btn-primary:hover:not(:disabled) {
    background: var(--theme-primary-dark);
  }

  .loading-spinner {
    width: 14px;
    height: 14px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .option-hint {
    font-size: var(--font-size-xs);
    color: var(--theme-text-secondary);
    margin-top: var(--spacing-1);
    line-height: 1.3;
    font-style: italic;
  }
</style>
