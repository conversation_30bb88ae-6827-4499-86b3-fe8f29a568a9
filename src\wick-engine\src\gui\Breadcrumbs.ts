/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { BreadcrumbsButton } from "./BreadcrumbsButton";

interface ButtonMap {
  [key: string]: BreadcrumbsButton;
}

export class Breadcrumbs extends GUIElement {
  protected _buttons: ButtonMap;

  constructor(model: Base) {
    super(model);
    this._buttons = {};
  }

  draw(): void {
    const ctx = this.ctx;

    // Background rectangle to cover rest of the GUI
    ctx.fillStyle = GUIElement.BREADCRUMBS_BG_COLOR;
    ctx.beginPath();
    ctx.rect(0, 0, this.canvas.width, GUIElement.BREADCRUMBS_HEIGHT);
    ctx.fill();

    // Generate buttons for each Clip in the lineage
    let totalWidth = 0;
    this.model.project.focus.lineage.reverse().forEach((clip) => {
      // Lazy generate buttons
      let button = this._buttons[clip.uuid];
      if (!button) {
        button = new BreadcrumbsButton(clip);
        this._buttons[clip.uuid] = button;
      }

      // Draw the button
      ctx.save();
      ctx.translate(totalWidth, 0);
      button.draw();
      ctx.restore();
      totalWidth += button.buttonWidth;
    });
  }
}
