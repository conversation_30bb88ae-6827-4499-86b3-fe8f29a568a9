<script lang="ts">
  /**
   * 项目管理组件
   * 用于选择和管理外部 RPG Maker 项目
   */
  
  import { TauriAPI } from '../lib/tauriAPI';
  import type { ProjectConfig, ScriptInfo } from '../lib/tauriAPI';
  
  // 状态管理
  let currentProject: ProjectConfig | null = $state(null);
  let projectScripts: ScriptInfo[] = $state([]);
  let isLoading = $state(false);
  let statusMessage = $state('');
  let recentProjects: ProjectConfig[] = $state([]);
  
  // 组件挂载时加载最近的项目
  $effect(() => {
    loadRecentProjects();
  });
  
  /**
   * 加载最近的项目列表
   */
  async function loadRecentProjects() {
    const result = await TauriAPI.Project.getRecentProjects();
    if (result.success && result.data) {
      recentProjects = result.data;
    }
  }
  
  /**
   * 选择项目目录
   */
  async function selectProjectDirectory() {
    isLoading = true;
    statusMessage = '正在选择项目目录...';
    
    try {
      const result = await TauriAPI.Project.selectProjectDirectory();
      
      if (result.success && result.data) {
        await loadProject(result.data);
      } else {
        statusMessage = result.error || '用户取消选择';
      }
    } catch (error) {
      statusMessage = `选择目录失败: ${error}`;
    } finally {
      isLoading = false;
    }
  }
  
  /**
   * 加载项目
   */
  async function loadProject(projectPath: string) {
    isLoading = true;
    statusMessage = '正在验证项目...';
    
    try {
      // 1. 验证项目
      const validateResult = await TauriAPI.Project.validateProject(projectPath);
      if (!validateResult.success || !validateResult.data) {
        statusMessage = '这不是一个有效的 RPG Maker 项目目录';
        return;
      }
      
      statusMessage = '正在加载项目配置...';
      
      // 2. 获取项目配置
      const configResult = await TauriAPI.Project.getProjectConfig(projectPath);
      if (configResult.success && configResult.data) {
        currentProject = configResult.data;
      } else {
        // 如果没有配置文件，创建默认配置
        currentProject = {
          name: projectPath.split(/[/\\]/).pop() || 'Unknown Project',
          path: projectPath,
          engine_version: 'MZ',
          script_files: [],
          data_path: projectPath + '/data',
          img_path: projectPath + '/img',
          audio_path: projectPath + '/audio'
        };
      }
      
      statusMessage = '正在扫描项目脚本...';
      
      // 3. 扫描项目脚本
      const scriptsResult = await TauriAPI.Project.scanProjectScripts(projectPath);
      if (scriptsResult.success && scriptsResult.data) {
        projectScripts = scriptsResult.data;
        statusMessage = `项目加载成功！发现 ${projectScripts.length} 个脚本文件`;
      } else {
        projectScripts = [];
        statusMessage = '项目加载成功，但未发现脚本文件';
      }
      
      // 4. 设置引擎项目路径
      if (window.RPGMakerEngine) {
        window.RPGMakerEngine.setProjectPath(projectPath);
        statusMessage += ' - 引擎已配置';
      }
      
    } catch (error) {
      statusMessage = `加载项目失败: ${error}`;
      currentProject = null;
      projectScripts = [];
    } finally {
      isLoading = false;
    }
  }
  
  /**
   * 重新加载项目脚本
   */
  async function reloadProjectScripts() {
    if (!currentProject) return;
    
    isLoading = true;
    statusMessage = '正在重新加载项目脚本...';
    
    try {
      if (window.RPGMakerEngine) {
        await window.RPGMakerEngine.reloadProjectScripts();
        statusMessage = '项目脚本重新加载完成';
      } else {
        statusMessage = '引擎未初始化';
      }
    } catch (error) {
      statusMessage = `重新加载失败: ${error}`;
    } finally {
      isLoading = false;
    }
  }
  
  /**
   * 清除当前项目
   */
  function clearProject() {
    currentProject = null;
    projectScripts = [];
    statusMessage = '项目已清除';
    
    if (window.RPGMakerEngine) {
      window.RPGMakerEngine.removeProjectScripts();
    }
  }
</script>

<div class="project-manager">
  <div class="header">
    <h2>项目管理器</h2>
    <div class="actions">
      <button 
        class="btn btn-primary" 
        onclick={selectProjectDirectory}
        disabled={isLoading}
      >
        {isLoading ? '加载中...' : '选择项目'}
      </button>
      
      {#if currentProject}
        <button 
          class="btn btn-secondary" 
          onclick={reloadProjectScripts}
          disabled={isLoading}
        >
          重新加载脚本
        </button>
        
        <button 
          class="btn btn-danger" 
          onclick={clearProject}
          disabled={isLoading}
        >
          清除项目
        </button>
      {/if}
    </div>
  </div>
  
  <div class="content">
    <!-- 状态信息 -->
    <div class="status-section">
      <div class="status-message" class:loading={isLoading}>
        {statusMessage || '请选择一个 RPG Maker 项目目录'}
      </div>
    </div>
    
    <!-- 当前项目信息 -->
    {#if currentProject}
      <div class="project-info">
        <h3>当前项目</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>项目名称:</label>
            <span>{currentProject.name}</span>
          </div>
          <div class="info-item">
            <label>项目路径:</label>
            <span class="path">{currentProject.path}</span>
          </div>
          <div class="info-item">
            <label>引擎版本:</label>
            <span>{currentProject.engine_version}</span>
          </div>
          <div class="info-item">
            <label>脚本文件:</label>
            <span>{projectScripts.length} 个</span>
          </div>
        </div>
      </div>
      
      <!-- 脚本文件列表 -->
      {#if projectScripts.length > 0}
        <div class="scripts-section">
          <h3>脚本文件</h3>
          <div class="scripts-list">
            {#each projectScripts as script}
              <div class="script-item" class:required={script.is_required}>
                <div class="script-name">{script.local_path}</div>
                <div class="script-status">
                  {script.is_required ? '必需' : '可选'}
                </div>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    {:else}
      <!-- 最近项目列表 -->
      {#if recentProjects.length > 0}
        <div class="recent-projects">
          <h3>最近的项目</h3>
          <div class="projects-list">
            {#each recentProjects as project}
              <div class="project-item" onclick={() => loadProject(project.path)}>
                <div class="project-name">{project.name}</div>
                <div class="project-path">{project.path}</div>
              </div>
            {/each}
          </div>
        </div>
      {/if}
    {/if}
  </div>
</div>

<style>
  .project-manager {
    height: 100%;
    background: var(--theme-background);
    color: var(--theme-text);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .header {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--theme-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--theme-surface);
  }
  
  .header h2 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
  }
  
  .actions {
    display: flex;
    gap: var(--spacing-2);
  }
  
  .btn {
    padding: var(--spacing-2) var(--spacing-4);
    border: none;
    border-radius: var(--border-radius);
    font-family: var(--font-family-base);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition-base);
  }
  
  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .btn-primary {
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
  }
  
  .btn-primary:hover:not(:disabled) {
    background: var(--theme-primary-dark);
  }
  
  .btn-secondary {
    background: var(--theme-secondary);
    color: var(--theme-text-inverse);
  }
  
  .btn-secondary:hover:not(:disabled) {
    background: var(--theme-secondary-dark);
  }
  
  .btn-danger {
    background: var(--theme-error);
    color: var(--theme-text-inverse);
  }
  
  .btn-danger:hover:not(:disabled) {
    background: #dc2626;
  }
  
  .content {
    flex: 1;
    padding: var(--spacing-4);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
  }
  
  .status-section {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-3);
  }
  
  .status-message {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
  }
  
  .status-message.loading {
    color: var(--theme-warning);
    font-weight: 500;
  }
  
  .project-info {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
  }
  
  .project-info h3 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--theme-primary);
  }
  
  .info-grid {
    display: grid;
    gap: var(--spacing-2);
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2) 0;
    border-bottom: 1px solid var(--theme-border-light);
  }
  
  .info-item:last-child {
    border-bottom: none;
  }
  
  .info-item label {
    font-weight: 500;
    color: var(--theme-text);
  }
  
  .info-item span {
    color: var(--theme-text-secondary);
    font-size: var(--font-size-sm);
  }
  
  .path {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-xs);
    word-break: break-all;
  }
  
  .scripts-section {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
  }
  
  .scripts-section h3 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--theme-primary);
  }
  
  .scripts-list {
    max-height: 300px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
  }
  
  .script-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2);
    background: var(--theme-background-light);
    border: 1px solid var(--theme-border-light);
    border-radius: var(--border-radius-small);
  }
  
  .script-item.required {
    border-color: var(--theme-primary);
    background: rgba(74, 85, 104, 0.1);
  }
  
  .script-name {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-xs);
    color: var(--theme-text);
  }
  
  .script-status {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-small);
    font-weight: 500;
  }
  
  .script-item.required .script-status {
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
  }
  
  .script-item:not(.required) .script-status {
    background: var(--theme-surface-light);
    color: var(--theme-text-secondary);
  }
  
  .recent-projects {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-4);
  }
  
  .recent-projects h3 {
    margin: 0 0 var(--spacing-3) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--theme-primary);
  }
  
  .projects-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }
  
  .project-item {
    padding: var(--spacing-3);
    background: var(--theme-background-light);
    border: 1px solid var(--theme-border-light);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-base);
  }
  
  .project-item:hover {
    background: var(--theme-surface-light);
    border-color: var(--theme-primary);
  }
  
  .project-name {
    font-weight: 500;
    color: var(--theme-text);
    margin-bottom: var(--spacing-1);
  }
  
  .project-path {
    font-family: var(--font-family-mono);
    font-size: var(--font-size-xs);
    color: var(--theme-text-secondary);
    word-break: break-all;
  }
</style>
