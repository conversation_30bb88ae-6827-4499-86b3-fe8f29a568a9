/**
 * 国际化工具函数
 */

import { get } from 'svelte/store';
import { locale } from 'svelte-i18n';
import type { SupportedLocale } from './index';

/**
 * 格式化数字
 */
export function formatNumber(num: number, options?: Intl.NumberFormatOptions): string {
  const currentLocale = get(locale) as SupportedLocale;
  return new Intl.NumberFormat(currentLocale, options).format(num);
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  const size = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
  return `${formatNumber(size)} ${sizes[i]}`;
}

/**
 * 格式化日期
 */
export function formatDate(date: Date, options?: Intl.DateTimeFormatOptions): string {
  const currentLocale = get(locale) as SupportedLocale;
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  };
  
  return new Intl.DateTimeFormat(currentLocale, { ...defaultOptions, ...options }).format(date);
}

/**
 * 格式化相对时间
 */
export function formatRelativeTime(date: Date): string {
  const currentLocale = get(locale) as SupportedLocale;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  const rtf = new Intl.RelativeTimeFormat(currentLocale, { numeric: 'auto' });
  
  if (diffInSeconds < 60) {
    return rtf.format(-diffInSeconds, 'second');
  } else if (diffInSeconds < 3600) {
    return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
  } else if (diffInSeconds < 86400) {
    return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
  } else if (diffInSeconds < 2592000) {
    return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
  } else if (diffInSeconds < 31536000) {
    return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
  } else {
    return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
  }
}

/**
 * 格式化百分比
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  const currentLocale = get(locale) as SupportedLocale;
  return new Intl.NumberFormat(currentLocale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(value);
}

/**
 * 格式化持续时间（毫秒）
 */
export function formatDuration(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  
  if (hours > 0) {
    return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
  } else if (minutes > 0) {
    return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * 复数形式处理
 */
export function pluralize(count: number, singular: string, plural?: string): string {
  if (count === 1) {
    return singular;
  }
  return plural || `${singular}s`;
}

/**
 * 获取语言方向（LTR/RTL）
 */
export function getTextDirection(): 'ltr' | 'rtl' {
  const currentLocale = get(locale) as SupportedLocale;
  // 目前支持的语言都是LTR，如果以后添加阿拉伯语等RTL语言需要修改
  return 'ltr';
}

/**
 * 检查是否为中文语言环境
 */
export function isChineseLocale(): boolean {
  const currentLocale = get(locale) as SupportedLocale;
  return currentLocale.startsWith('zh');
}

/**
 * 检查是否为英文语言环境
 */
export function isEnglishLocale(): boolean {
  const currentLocale = get(locale) as SupportedLocale;
  return currentLocale.startsWith('en');
}
