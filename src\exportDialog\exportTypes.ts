/**
 * 导出功能相关类型定义
 */

import type { BaseResource, CropArea } from '../types/imageType';

// 导出配置
export interface ExportConfig {
  type: 'cropped-images' | 'plist' | 'json-array' | 'json-hash' | 'unity' | 'phaser3' | 'gamemaker' | 'unreal-paper2d' | 'godot' | 'defold' | 'libgdx' | 'xml-generic' | 'pixi';
  outputPath?: string;
  fileName?: string;
  format?: 'png' | 'jpg' | 'webp';
  quality?: number;
  // 图片特有配置
  includeOriginal?: boolean;
  namePrefix?: string;
  nameSuffix?: string;
}

// 导出项目
export interface ExportItem {
  id: string;
  name: string;
  resource: BaseResource;  // 支持所有基础资源类型
  cropAreas?: CropArea[];
}

// 导出结果
export interface ExportResult {
  success: boolean;
  files: Array<{
    name: string;
    path: string;
    size: number;
    type: 'image' | 'data';
  }>;
  totalSize: number;
  duration: number;
  error?: string;
}

// 导出进度
export interface ExportProgress {
  current: number;
  total: number;
  currentFile: string;
  stage: 'preparing' | 'processing' | 'saving' | 'completed';
}

// plist帧数据 - 真实TexturePacker格式
export interface PlistFrame {
  aliases: string[];       // 别名数组
  spriteOffset: string;    // 精灵偏移 {x,y}
  spriteSize: string;      // 精灵大小 {w,h}
  spriteSourceSize: string; // 精灵源大小 {w,h}
  textureRect: string;     // 纹理矩形 {{x,y},{w,h}}
  textureRotated: boolean; // 是否旋转
}

// plist数据结构
export interface PlistData {
  frames: Record<string, PlistFrame>;
  metadata: {
    format: number;
    textureFileName: string;
    size: string;
    smartupdate: string;
    premultiplyAlpha: boolean;
  };
}

// 导出选项UI状态
export interface ExportUIState {
  selectedType: ExportConfig['type'];
  outputPath: string;
  fileName: string;
  format: ExportConfig['format'];
  quality: number;
  includeOriginal: boolean;
  namePrefix: string;
  nameSuffix: string;
}
