import * as PIXI from "pixi.js";
import { Base } from "./Base";

/**
 * Path类，用于处理图形路径和绘制
 */
export class Path extends Base {
  private _graphics: PIXI.Graphics;
  private _strokeColor: number;
  private _strokeWidth: number;
  private _fillColor: number;
  private _opacity: number;
  private _closed: boolean;
  private _points: Array<{ x: number; y: number }>;

  /**
   * 创建一个路径对象
   * @param args - 路径参数
   */
  constructor(
    args: {
      strokeColor?: number;
      strokeWidth?: number;
      fillColor?: number;
      opacity?: number;
      project?: any;
    } = {}
  ) {
    super({ project: args.project });

    this._strokeColor = args.strokeColor || 0x000000;
    this._strokeWidth = args.strokeWidth || 1;
    this._fillColor = args.fillColor || 0xffffff;
    this._opacity = args.opacity || 1;
    this._closed = false;
    this._points = [];

    // 初始化PIXI图形对象
    this._graphics = new PIXI.Graphics();
    this.updateGraphics();
  }

  /**
   * 更新图形显示
   */
  private updateGraphics(): void {
    this._graphics.clear();
    this._graphics.lineStyle(this._strokeWidth, this._strokeColor);
    this._graphics.beginFill(this._fillColor);
    this._graphics.alpha = this._opacity;

    if (this._points.length > 0) {
      this._graphics.moveTo(this._points[0].x, this._points[0].y);
      for (let i = 1; i < this._points.length; i++) {
        this._graphics.lineTo(this._points[i].x, this._points[i].y);
      }
      if (this._closed) {
        this._graphics.closePath();
      }
    }

    this._graphics.endFill();
  }

  /**
   * 添加点到路径
   */
  addPoint(x: number, y: number): void {
    this._points.push({ x, y });
    this.updateGraphics();
  }

  /**
   * 清除所有点
   */
  clearPoints(): void {
    this._points = [];
    this.updateGraphics();
  }

  /**
   * 设置路径是否闭合
   */
  set closed(value: boolean) {
    this._closed = value;
    this.updateGraphics();
  }

  /**
   * 获取路径是否闭合
   */
  get closed(): boolean {
    return this._closed;
  }

  /**
   * 设置描边颜色
   */
  set strokeColor(value: number) {
    this._strokeColor = value;
    this.updateGraphics();
  }

  /**
   * 获取描边颜色
   */
  get strokeColor(): number {
    return this._strokeColor;
  }

  /**
   * 设置描边宽度
   */
  set strokeWidth(value: number) {
    this._strokeWidth = value;
    this.updateGraphics();
  }

  /**
   * 获取描边宽度
   */
  get strokeWidth(): number {
    return this._strokeWidth;
  }

  /**
   * 设置填充颜色
   */
  set fillColor(value: number) {
    this._fillColor = value;
    this.updateGraphics();
  }

  /**
   * 获取填充颜色
   */
  get fillColor(): number {
    return this._fillColor;
  }

  /**
   * 设置透明度
   */
  set opacity(value: number) {
    this._opacity = value;
    this.updateGraphics();
  }

  /**
   * 获取透明度
   */
  get opacity(): number {
    return this._opacity;
  }

  /**
   * 获取PIXI图形对象
   */
  get graphics(): PIXI.Graphics {
    return this._graphics;
  }
}
