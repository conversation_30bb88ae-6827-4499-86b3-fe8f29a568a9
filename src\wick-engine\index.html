<!DOCTYPE html>
<html>
  <head>
    <title>Wick Engine Pixi Example</title>
    <style>
      #wickCanvas {
        width: 800px;
        height: 600px;
        border: 1px solid #ccc;
        margin: 20px auto;
      }
    </style>
  </head>
  <body>
    <div id="wickCanvas"></div>
    <script type="module">
      import Project from "./src/base/Project.js";
      import Base from "./src/base/Base.js";
      import Wick from "./src/Wick.js";

      // 创建一个新的项目
      const project = new Project({
        width: 800,
        height: 600,
        backgroundColor: 0xffffff,
      });

      // 挂载到DOM容器
      project.mount(document.getElementById("wickCanvas"));

      // 重置视图位置
      project.recenter();
    </script>
  </body>
</html>
