<script lang="ts">
  /**
   * Image 组件 - 纯图片显示组件，只负责渲染
   */

  interface Props {
    src: string;
    alt?: string;
    width?: number | string;
    height?: number | string;
    fit?: 'contain' | 'cover' | 'fill' | 'scale-down' | 'none';
    loading?: 'lazy' | 'eager';
    className?: string;
    onLoad?: () => void;
    onError?: (event: Event) => void;
    // 拖拽相关属性
    draggable?: boolean;
    dragData?: {
      type: 'image';
      src: string;
      alt?: string;
      name: string;
      resourceId?: string;
      [key: string]: any;
    };
  }

  let {
    src,
    alt = '',
    width,
    height,
    fit = 'contain',
    loading = 'lazy',
    className = '',
    onLoad,
    onError,
    draggable = false,
    dragData,
    ...restProps
  }: Props = $props();

  // 状态管理
  let imageElement = $state<HTMLImageElement>();
  let isLoading = $state(true);
  let hasError = $state(false);

  // 拖拽相关
  import { dragStore } from '../../stores/dragStore';
  let isDragging = $state(false);

  // 处理图片加载完成
  function handleLoad() {
    console.log('✅ Image组件: 图片加载完成');
    isLoading = false;
    hasError = false;
    onLoad?.();
  }

  // 处理图片加载错误
  function handleError(event: Event) {
    console.log('❌ Image组件: 图片加载失败:', event);
    isLoading = false;
    hasError = true;
    onError?.(event);
  }

  // 监听src变化重置状态
  $effect(() => {
    if (src) {
      isLoading = true;
      hasError = false;
    }
  });

  // 拖拽事件处理
  function handleMouseDown(event: MouseEvent) {
    if (!draggable || !dragData) {
      console.log('🎯 Image: 拖拽被跳过 - draggable:', draggable, 'dragData:', dragData);
      return;
    }

    console.log('🎯 Image: 开始拖拽', dragData.name);
    isDragging = true;

    // 启动拖拽状态
    dragStore.startDrag(dragData, {
      x: event.clientX,
      y: event.clientY
    });

    // 监听全局鼠标事件 - 添加节流优化性能
    let lastUpdateTime = 0;
    const handleMouseMove = (e: MouseEvent) => {
      const now = Date.now();
      // 限制更新频率为60fps (16.67ms)
      if (now - lastUpdateTime > 16) {
        dragStore.updateMousePosition({
          x: e.clientX,
          y: e.clientY
        });
        lastUpdateTime = now;
      }
    };

    const handleMouseUp = () => {
      console.log('🎯 Image: 结束拖拽');
      isDragging = false;
      dragStore.endDrag();

      // 移除事件监听
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    // 添加全局事件监听
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    // 阻止默认行为和事件冒泡
    event.preventDefault();
    event.stopPropagation();
  }

  // 计算样式
  const imageStyle = $derived(() => {
    const styles: Record<string, string> = {
      'object-fit': fit
    };

    if (width) {
      styles.width = typeof width === 'number' ? `${width}px` : width;
    }

    if (height) {
      styles.height = typeof height === 'number' ? `${height}px` : height;
    }

    return Object.entries(styles)
      .map(([key, value]) => `${key}: ${value}`)
      .join('; ');
  });
</script>

<div
  class="image-wrapper {className}"
  class:draggable
  class:dragging={isDragging}
  onmousedown={draggable ? handleMouseDown : undefined}
  role={draggable ? "button" : undefined}
  tabindex={draggable ? 0 : undefined}
  {...restProps}
>
  {#if src}
    <!-- 始终渲染img元素，用CSS控制显示 -->
    <img
      bind:this={imageElement}
      {src}
      {alt}
      {loading}
      style={imageStyle()}
      class="image"
      class:loading={isLoading}
      class:error={hasError}
      class:draggable
      class:dragging={isDragging}
      draggable="false"
      onload={handleLoad}
      onerror={handleError}
      ondragstart={(e) => e.preventDefault()}
    />

    <!-- 加载状态覆盖层 -->
    {#if isLoading}
      <div class="image-loading-overlay">
        <div class="loading-spinner"></div>
        <span class="loading-text">加载中...</span>
      </div>
    {/if}

    <!-- 错误状态覆盖层 -->
    {#if hasError}
      <div class="image-error-overlay">
        <div class="error-icon">🖼️</div>
        <span class="error-text">图片加载失败</span>
      </div>
    {/if}
  {:else}
    <div class="image-loading">
      <div class="loading-spinner"></div>
      <span class="loading-text">准备中...</span>
    </div>
  {/if}
</div>

<style>
  .image-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    width: 100%;
    height: 100%;
    background: transparent;
  }

  .image-wrapper.draggable {
    cursor: grab;
    user-select: none;
  }

  .image-wrapper.draggable:active,
  .image-wrapper.dragging {
    cursor: grabbing;
  }

  .image-wrapper.draggable:focus {
    outline: 2px solid var(--color-primary, #007bff);
    outline-offset: 2px;
  }

  .image {
    display: block;
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain;
    transition: opacity 0.3s ease;
  }

  .image.loading {
    opacity: 0;
  }

  .image.error {
    opacity: 0;
  }

  .image-loading,
  .image-loading-overlay,
  .image-error-overlay {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 50px;
    padding: 0.5rem;
    background: var(--color-surface, #f5f5f5);
    border: 1px dashed var(--color-border, #ddd);
    border-radius: 6px;
    color: var(--color-text-secondary, #666);
  }

  .image-loading-overlay,
  .image-error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(245, 245, 245, 0.9);
    backdrop-filter: blur(2px);
    z-index: 10;
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--color-border, #ddd);
    border-top: 2px solid var(--color-primary, #007bff);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 0.25rem;
  }

  .error-icon {
    font-size: 1.2rem;
    margin-bottom: 0.25rem;
    opacity: 0.5;
  }

  .loading-text,
  .error-text {
    font-size: 0.75rem;
    line-height: 1.2;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
