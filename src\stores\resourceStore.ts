import { writable, get } from 'svelte/store';
import type { ResourceItem, ImageResource, FolderResource } from '../types';
import { TauriAPI } from '../lib/tauriAPI';
import { readDir } from '@tauri-apps/plugin-fs';

// 资源管理器状态接口
export interface ResourceStore {
  rootResources: ResourceItem[];      // 🎯 完整的文件夹树结构（单一数据源）
  currentFolder: FolderResource | null;       // 当前所在的文件夹对象
  selectedResource: ResourceItem | null; // 当前选中的资源
  loading: boolean;
  error: string | null;
  navigationStack: string[];          // 导航栈，支持多级文件夹导航
}

// 初始状态
const initialState: ResourceStore = {
  rootResources: [],
  currentFolder: null,
  selectedResource: null,
  loading: false,
  error: null,
  navigationStack: []
};

// 创建资源管理器 store
export const resourceStore = writable<ResourceStore>(initialState);

// 🎯 辅助函数：根据currentFolder计算当前显示的资源
function getCurrentDisplayedResources(state: ResourceStore): ResourceItem[] {
  if (!state.currentFolder) {
    // 根级别：显示所有根级别资源
    return state.rootResources;
  } else {
    // 文件夹内：直接返回当前文件夹的children
    return state.currentFolder.children || [];
  }
}

// 🎯 辅助函数：递归查找文件夹
function findFolderInResources(resources: ResourceItem[], targetPath: string): FolderResource | null {
  for (const resource of resources) {
    if (resource.type === 'folder' && resource.path === targetPath) {
      return resource as FolderResource;
    }
    if (resource.type === 'folder' && 'children' in resource && resource.children) {
      const found = findFolderInResources(resource.children, targetPath);
      if (found) return found;
    }
  }
  return null;
}

// 🎯 辅助函数：根据ID查找资源
function findResourceById(resources: ResourceItem[], resourceId: string): ResourceItem | null {
  for (const resource of resources) {
    if (resource.id === resourceId) {
      return resource;
    }
    if (resource.type === 'folder' && 'children' in resource && resource.children) {
      const found = findResourceById(resource.children, resourceId);
      if (found) return found;
    }
  }
  return null;
}

// 生成唯一ID
function generateId(): string {
  return `resource_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

// 获取文件扩展名
function getFileExtension(path: string): string {
  const lastDot = path.lastIndexOf('.');
  return lastDot > 0 ? path.substring(lastDot + 1).toLowerCase() : '';
}

// 检查是否为图片文件
function isImageFile(path: string): boolean {
  const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'];
  const extension = getFileExtension(path);
  return imageExtensions.includes(extension);
}

// 获取MIME类型
function getMimeType(path: string): string {
  const extension = getFileExtension(path);
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    case 'svg':
      return 'image/svg+xml';
    case 'bmp':
      return 'image/bmp';
    default:
      return 'image/png';
  }
}

// 资源管理器操作方法
export const resourceActions = {
  // 设置加载状态
  setLoading(loading: boolean) {
    resourceStore.update(state => ({
      ...state,
      loading
    }));
  },

  // 设置错误信息
  setError(error: string | null) {
    resourceStore.update(state => ({
      ...state,
      error
    }));
  },



  // 添加文件夹资源
  async addFolder(folderPath: string): Promise<ResourceItem | null> {
    try {
      const folderName = folderPath.split(/[/\\]/).pop() || 'Unknown Folder';
      const now = new Date().toISOString();

      const folderResource: FolderResource = {
        id: generateId(),
        name: folderName,
        type: 'folder',
        path: folderPath,
        createdAt: now,
        updatedAt: now,
        isLoaded: false,
        children: []
      };

      resourceStore.update(state => ({
        ...state,
        rootResources: [...state.rootResources, folderResource]
      }));

      return folderResource;
    } catch (error) {
      console.error('添加文件夹失败:', error);
      this.setError(`添加文件夹失败: ${error}`);
      return null;
    }
  },





  // 🎯 退出当前文件夹（支持多级返回）
  async exitFolder(): Promise<void> {
    resourceStore.update(state => {
      const currentDisplayedResources = getCurrentDisplayedResources(state);

      console.log('🔙 ResourceStore: 退出文件夹', {
        currentFolder: state.currentFolder?.name,
        navigationStack: state.navigationStack,
        currentResourcesCount: currentDisplayedResources.length,
        rootResourcesCount: state.rootResources.length
      });

      // 🎯 如果导航栈为空，返回根目录
      if (state.navigationStack.length === 0) {
        return {
          ...state,
          currentFolder: null,
          selectedResource: null
        };
      }

      // 🎯 从导航栈中弹出上一级文件夹路径，并找到对应的文件夹对象
      const newNavigationStack = [...state.navigationStack];
      const parentFolderPath = newNavigationStack.pop();

      // 🎯 根据路径找到父文件夹对象
      let parentFolder: FolderResource | null = null;
      if (parentFolderPath) {
        parentFolder = findFolderInResources(state.rootResources, parentFolderPath);
      }

      console.log('🔙 ResourceStore: 返回上级文件夹', {
        parentFolderPath,
        parentFolderName: parentFolder?.name,
        newNavigationStack
      });

      return {
        ...state,
        currentFolder: parentFolder,
        navigationStack: newNavigationStack,
        selectedResource: null
      };
    });
  },


  // 清除选中
  clearSelection(): void {
    resourceStore.update(state => ({
      ...state,
      selectedResource: null
    }));
  },
  // 🎯 修复的获取资源by ID
  async getResourceById(resourceId: string): Promise<ResourceItem | null> {
    return new Promise((resolve) => {
      let unsubscribe: (() => void) | undefined;

      unsubscribe = resourceStore.subscribe(state => {
        const resource = findResourceById(state.rootResources, resourceId);
        if (unsubscribe) {
          unsubscribe(); // 立即取消订阅，避免内存泄漏
        }
        resolve(resource || null);
      });
    });
  },

  // 清空所有资源
  clearResources(): void {
    resourceStore.update(state => ({
      ...state,
      rootResources: [], // 🎯 使用新接口：清空 rootResources
      selectedResource: null,
      currentFolder: null,
      navigationStack: [] // 🎯 清空导航栈
    }));
  },

  // 删除资源
  removeResource(resourceId: string): void {
    resourceStore.update(state => {
      // 🎯 递归删除函数，直接修改原对象而不创建副本
      const removeFromResources = (resources: ResourceItem[]): { removedCount: number, updatedFolder: any } => {
        let removedCount = 0;
        let updatedFolder = null;

        for (let i = resources.length - 1; i >= 0; i--) {
          const resource = resources[i];
          if (resource.id === resourceId) {
            // 找到要删除的资源，直接从数组中移除
            resources.splice(i, 1);
            removedCount++;
            console.log('🗑️ ResourceStore: 删除资源', {
              resourceId,
              resourceName: resource.name,
              resourceType: resource.type
            });
          } else if (resource.type === 'folder' && 'children' in resource && resource.children) {
            // 递归处理文件夹的children
            const result = removeFromResources(resource.children);
            removedCount += result.removedCount;
            // 如果当前文件夹就是currentFolder，记录它
            if (state.currentFolder && resource.path === state.currentFolder.path) {
              updatedFolder = resource;
            }
          }
        }
        return { removedCount, updatedFolder };
      };

      // 🎯 直接修改rootResources，不创建副本
      const result = removeFromResources(state.rootResources);

      // 🎯 如果删除操作影响了当前文件夹，确保currentFolder指向rootResources中的同一对象
      let updatedCurrentFolder = state.currentFolder;
      if (result.updatedFolder && state.currentFolder && result.updatedFolder.path === state.currentFolder.path) {
        updatedCurrentFolder = result.updatedFolder;
        console.log('🎯 ResourceStore: 更新currentFolder为rootResources中的同一对象', {
          folderName: result.updatedFolder.name,
          childrenCount: result.updatedFolder.children?.length || 0,
          isSameObject: updatedCurrentFolder === result.updatedFolder
        });
      }

      console.log('✅ ResourceStore: 资源删除完成', {
        resourceId,
        removedCount: result.removedCount,
        updatedCurrentFolder: updatedCurrentFolder !== state.currentFolder
      });

      return {
        ...state,
        currentFolder: updatedCurrentFolder,
        selectedResource: state.selectedResource?.id === resourceId ? null : state.selectedResource
      };
    });
  },
};
