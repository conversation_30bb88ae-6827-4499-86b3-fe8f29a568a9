/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Tool } from "./Tool";

export class Eyedropper extends Tool {
  protected name: string;
  protected canvasCtx: CanvasRenderingContext2D | null;
  protected hoverColor: string;
  protected colorPreview: any;

  /**
   * Creates an instance of the eyedropper tool.
   */
  constructor() {
    super();

    this.name = "eyedropper";
    this.canvasCtx = null;
    this.hoverColor = "#ffffff";
    this.colorPreview = null;
  }

  get doubleClickEnabled(): boolean {
    return false;
  }

  /**
   * The cursor style for the eyedropper tool.
   */
  get cursor(): string {
    return "url(cursors/eyedropper.png) 32 32, auto";
  }

  onActivate(e: any): void {}

  onDeactivate(e: any): void {
    this._destroyColorPreview();
  }

  onMouseMove(e: any): void {
    super.onMouseMove(e);

    const canvas = this.paper.view._element as HTMLCanvasElement;
    const ctx = canvas.getContext("2d");

    if (ctx) {
      const pointPx = this.paper.view.projectToView(e.point);
      pointPx.x = Math.round(pointPx.x) * window.devicePixelRatio;
      pointPx.y = Math.round(pointPx.y) * window.devicePixelRatio;
      const colorData = ctx.getImageData(pointPx.x, pointPx.y, 1, 1).data;
      const colorCSS = `rgb(${colorData[0]},${colorData[1]},${colorData[2]})`;

      this.hoverColor = colorCSS;

      this._createColorPreview(e.point);
    }
  }

  onMouseDown(e: any): void {
    this._destroyColorPreview();

    this.fire("eyedropperPickedColor", {
      color: this.hoverColor,
    });
  }

  onMouseDrag(e: any): void {}

  onMouseUp(e: any): void {
    this._createColorPreview(e.point);
  }

  protected _createColorPreview(point: any): void {
    this._destroyColorPreview();

    const offset = 10 / this.paper.view.zoom;
    const center = point.add(
      new (this.paper as any).Point(offset + 0.5, offset + 0.5)
    );
    const radius = 10 / (this.paper as any).view.zoom;
    const size = new (this.paper as any).Size(radius, radius);

    this.colorPreview = new (this.paper as any).Group();
    this.colorPreview.addChild(
      new (this.paper as any).Path.Rectangle({
        center: center,
        size: size,
        strokeColor: "#000000",
        fillColor: this.hoverColor,
        strokeWidth: 1.0 / this.paper.view.zoom,
      })
    );
  }

  protected _destroyColorPreview(): void {
    if (this.colorPreview) {
      this.colorPreview.remove();
      this.colorPreview = null;
    }
  }
}
