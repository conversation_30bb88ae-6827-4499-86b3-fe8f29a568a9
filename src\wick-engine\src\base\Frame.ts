import { Base } from "./Base";
import { Path } from "./Path";

/**
 * Frame类，用于管理动画帧
 */
export class Frame extends Base {
  private _content: Path[];
  private _duration: number;
  private _startPosition: number;
  private _isKeyframe: boolean;
  private _isBlank: boolean;

  /**
   * 创建一个帧对象
   */
  constructor(
    args: {
      duration?: number;
      startPosition?: number;
      isKeyframe?: boolean;
      project?: any;
    } = {}
  ) {
    super({ project: args.project });

    this._content = [];
    this._duration = args.duration || 1;
    this._startPosition = args.startPosition || 0;
    this._isKeyframe = args.isKeyframe || true;
    this._isBlank = true;
  }

  /**
   * 添加路径到帧
   */
  addPath(path: Path): void {
    this._content.push(path);
    path.parent = this;
    this._isBlank = false;
  }

  /**
   * 移除路径
   */
  removePath(path: Path): void {
    const index = this._content.indexOf(path);
    if (index !== -1) {
      this._content.splice(index, 1);
      path.parent = null;
      this._isBlank = this._content.length === 0;
    }
  }

  /**
   * 清除所有内容
   */
  clear(): void {
    this._content.forEach((path) => {
      path.parent = null;
    });
    this._content = [];
    this._isBlank = true;
  }

  /**
   * 获取帧持续时间
   */
  get duration(): number {
    return this._duration;
  }

  /**
   * 设置帧持续时间
   */
  set duration(value: number) {
    this._duration = Math.max(1, value);
  }

  /**
   * 获取帧起始位置
   */
  get startPosition(): number {
    return this._startPosition;
  }

  /**
   * 设置帧起始位置
   */
  set startPosition(value: number) {
    this._startPosition = Math.max(0, value);
  }

  /**
   * 获取是否为关键帧
   */
  get isKeyframe(): boolean {
    return this._isKeyframe;
  }

  /**
   * 设置是否为关键帧
   */
  set isKeyframe(value: boolean) {
    this._isKeyframe = value;
  }

  /**
   * 获取是否为空白帧
   */
  get isBlank(): boolean {
    return this._isBlank;
  }

  /**
   * 获取帧内容
   */
  get content(): Path[] {
    return this._content;
  }
}
