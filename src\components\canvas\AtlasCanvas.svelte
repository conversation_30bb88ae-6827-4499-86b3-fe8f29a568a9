<script lang="ts">
  /**
   * AtlasCanvas 组件 - 基于CanvasPreview扩展，用于显示图集中的所有图片
   */
  import { onMount, onDestroy } from 'svelte';
  import type { AtlasData, ResourceItem, CropArea, LayoutResult, MergeSettings } from '../../types/imageType';
  // 移除mergeSettingsStore的导入，MergeSettings从types导入
  import { calculateAtlasLayout } from '../../utils/atlasLayout';
  import { imagePreviewCache } from '../../utils/imagePreviewCache';
  import { performanceMonitor } from '../../utils/performanceMonitor';
  import { atlasStore } from '../../stores/atlasStore';
  import {
    layoutSettingsStore,
    type LayoutSettings
  } from '../../stores/exportSettingsStore';

  interface Props {
    width?: number;
    height?: number;
    onAtlasUpdate?: (updates: Partial<AtlasData>) => void;
  }

  let { width = 1024, height = 1024, onAtlasUpdate }: Props = $props();

  // 🎯 直接从selectionStore获取当前图集
  let currentAtlas = $state<AtlasData | null>(null);
  let lastAtlasSignature = $state<string | undefined>(undefined);

  // 🎯 全局布局设置状态
  let currentLayoutSettings = $state<LayoutSettings | null>(null);
  let layoutSettingsUnsubscribe: (() => void) | null = null;

  // 🎯 优化的状态管理 - 与其他组件保持一致
  let canvasElement = $state<HTMLCanvasElement>();
  let ctx = $state<CanvasRenderingContext2D | null>(null);
  let isLoading = $state(false);
  let error = $state<string | null>(null);

  // 图片加载状态
  let loadedImages = $state<Map<string, HTMLImageElement>>(new Map());
  let loadingCount = $state(0);
  let totalImages = $state(0);
  let lastLayoutResult = $state<any>(null);

  // 🎯 操作状态管理 - 与其他组件保持一致
  let operationState = $state<{
    isProcessing: boolean;
    progress?: number;
    error?: string;
    operation?: 'loading' | 'rendering' | 'calculating';
  }>({ isProcessing: false });

  // 🎯 计算属性 - 减少重复计算
  const hasImages = $derived((currentAtlas?.children?.length || 0) > 0);
  const isCanvasReady = $derived(!!canvasElement && !!ctx);

  // 🎯 监听atlasStore，获取当前选中的图集和mergeSettings
  $effect(() => {
    const unsubscribe = atlasStore.subscribe(() => {
      // 获取当前选中的图集
      const selectedAtlas = atlasStore.getCurrentSelectedAtlas();
      const selectedAtlasId = atlasStore.getCurrentSelectedAtlasId();

      console.log('🔍 AtlasCanvas: atlasStore变化检查', {
        selectedAtlasId,
        hasSelectedAtlas: !!selectedAtlas,
        selectedAtlasName: selectedAtlas?.name,
        childrenCount: selectedAtlas?.children?.length || 0,
        isCanvasReady,
        currentAtlasId: currentAtlas?.id
      });

      if (selectedAtlas) {
        // 🎯 修复死循环：签名只包含影响渲染的数据，排除cropAreas
        const childrenSignature = selectedAtlas.children?.map(child => `${child.id}_${child.width}_${child.height}`).join('|') || '';
        const newSignature = `${selectedAtlas.id}_${childrenSignature}_${JSON.stringify(selectedAtlas.mergeSettings || {})}`;

        console.log('🔍 AtlasCanvas: 签名检查', {
          oldSignature: lastAtlasSignature,
          newSignature,
          signatureChanged: newSignature !== lastAtlasSignature
        });

        if (newSignature !== lastAtlasSignature) {
          console.log('🔄 AtlasCanvas: 检测到图集变化，重新加载', {
            atlasName: selectedAtlas.name,
            atlasId: selectedAtlas.id,
            childrenCount: selectedAtlas.children?.length || 0,
            lastModified: selectedAtlas.lastModified?.toLocaleTimeString(),
            oldSignature: lastAtlasSignature,
            newSignature,
            hasMergeSettings: !!selectedAtlas.mergeSettings,
            isCanvasReady
          });

          currentAtlas = selectedAtlas;
          lastAtlasSignature = newSignature;

          // 🎯 从atlas获取mergeSettings
          if (selectedAtlas.mergeSettings) {
            const oldAlgorithm = mergeSettings.algorithm;
            const oldPadding = mergeSettings.padding;
            mergeSettings = selectedAtlas.mergeSettings;

            console.log('🔄 AtlasCanvas: 更新合并设置', {
              algorithm: `${oldAlgorithm} → ${mergeSettings.algorithm}`,
              padding: `${oldPadding} → ${mergeSettings.padding}`,
              allowRotation: mergeSettings.allowRotation,
              powerOfTwo: mergeSettings.powerOfTwo
            });
          }

          // 清除缓存，强制重新加载
          loadedImages.clear();

          if (isCanvasReady) {
            console.log('✅ AtlasCanvas: Canvas已准备好，开始加载图片');
            loadAllImages();
          } else {
            console.log('⚠️ AtlasCanvas: Canvas未准备好，等待初始化完成');
          }
        }
      } else {
        // 如果没有选中的图集，清空当前图集
        if (currentAtlas) {
          console.log('🔄 AtlasCanvas: 清空图集选择');
          currentAtlas = null;
          lastAtlasSignature = undefined;
          if (ctx) {
            renderEmptyState();
          }
        }
      }
    });

    return unsubscribe;
  });

  // 🎯 监听全局布局设置变化
  $effect(() => {
    layoutSettingsUnsubscribe = layoutSettingsStore.subscribe((settings) => {
      console.log('🎯 AtlasCanvas: 收到全局布局设置变化', settings);
      const oldSettings = currentLayoutSettings;
      currentLayoutSettings = settings;

      // 如果有当前图集且设置发生了变化，重新计算布局
      if (currentAtlas && oldSettings && isCanvasReady) {
        const settingsChanged =
          oldSettings.algorithm !== settings.algorithm ||
          oldSettings.padding !== settings.padding ||
          oldSettings.powerOfTwo !== settings.powerOfTwo ||
          oldSettings.allowRotation !== settings.allowRotation ||
          oldSettings.maxWidth !== settings.maxWidth ||
          oldSettings.maxHeight !== settings.maxHeight;

        if (settingsChanged) {
          console.log('🎯 AtlasCanvas: 布局设置变化，重新计算布局', {
            oldAlgorithm: oldSettings.algorithm,
            newAlgorithm: settings.algorithm,
            oldPadding: oldSettings.padding,
            newPadding: settings.padding,
            oldPowerOfTwo: oldSettings.powerOfTwo,
            newPowerOfTwo: settings.powerOfTwo,
            oldAllowRotation: oldSettings.allowRotation,
            newAllowRotation: settings.allowRotation
          });

          // 更新 mergeSettings 以使用全局设置
          // 🎯 处理算法类型转换：MergeSettings 不支持 potpack
          const compatibleAlgorithm = settings.algorithm === 'potpack' ? 'maxrects' : settings.algorithm as 'maxrects' | 'shelf' | 'guillotine';

          mergeSettings = {
            ...mergeSettings,
            algorithm: compatibleAlgorithm,
            padding: settings.padding,
            powerOfTwo: settings.powerOfTwo,
            allowRotation: settings.allowRotation,
            maxWidth: settings.maxWidth,
            maxHeight: settings.maxHeight
          };

          // 重新渲染图集
          setTimeout(() => {
            renderAtlas();
          }, 0);
        }
      }
    });

    return () => {
      if (layoutSettingsUnsubscribe) {
        layoutSettingsUnsubscribe();
        layoutSettingsUnsubscribe = null;
      }
    };
  });

  // 合并设置
  let mergeSettings = $state<MergeSettings>({
    atlasName: '新图集',
    maxWidth: 2048,
    maxHeight: 2048,
    padding: 2,
    algorithm: 'maxrects',
    powerOfTwo: true,
    allowRotation: false,
    outputFormat: 'png',
    quality: 90,
    generateData: true,
    dataFormat: 'json'
  });

  // 🎯 mergeSettings现在从atlas.mergeSettings获取，在上面的$effect中处理

  // 初始化Canvas
  function initCanvas() {
    if (!canvasElement) {
      console.error('❌ AtlasCanvas: canvasElement未找到');
      return;
    }

    const context = canvasElement.getContext('2d');
    if (!context) {
      error = '无法获取Canvas上下文';
      console.error('❌ AtlasCanvas: 无法获取Canvas上下文');
      return;
    }

    ctx = context;
    canvasElement.width = width;
    canvasElement.height = height;

    console.log('🎨 AtlasCanvas: Canvas初始化完成', {
      width,
      height,
      atlasName: currentAtlas?.name || '无图集',
      imageCount: currentAtlas?.children?.length || 0,
      canvasElement: !!canvasElement,
      ctx: !!ctx,
      isCanvasReady
    });

    // 设置Canvas尺寸变化监听
    if (canvasElement) {
      setupCanvasResizeObserver(canvasElement);
    }

    // 初始化完成后检查是否有待加载的图集
    const selectedAtlas = atlasStore.getCurrentSelectedAtlas();
    if (selectedAtlas && selectedAtlas.children && selectedAtlas.children.length > 0) {
      console.log('🔄 AtlasCanvas: Canvas初始化完成，发现待加载的图集，开始加载', {
        atlasName: selectedAtlas.name,
        childrenCount: selectedAtlas.children.length
      });
      currentAtlas = selectedAtlas;
      loadAllImages();
    } else {
      console.log('🎨 AtlasCanvas: Canvas初始化完成，无待加载图集');
      loadAllImages(); // 这会渲染空状态
    }
  }

  // 🎯 优化的图片加载 - 集成缓存系统和性能监控
  async function loadImage(resource: ResourceItem): Promise<HTMLImageElement> {
    // 🎯 开始性能监控
    performanceMonitor.startMeasure('atlas-image-load', {
      resourceId: resource.id,
      resourceName: resource.name,
      hasData: !!resource.data
    });

    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        console.log('✅ AtlasCanvas: 图片加载完成', resource.name, {
          naturalSize: `${img.naturalWidth}×${img.naturalHeight}`,
          resourceSize: `${resource.width}×${resource.height}`
        });

        // 更新ResourceItem的尺寸信息（如果尚未设置或不正确）
        if (!resource.width || !resource.height ||
            resource.width !== img.naturalWidth || resource.height !== img.naturalHeight) {
          resource.width = img.naturalWidth;
          resource.height = img.naturalHeight;
          console.log('🔧 AtlasCanvas: 更新ResourceItem尺寸', {
            resourceName: resource.name,
            width: resource.width,
            height: resource.height
          });
        }

        loadedImages.set(resource.id, img);
        loadingCount--;

        // 🎯 结束性能监控
        performanceMonitor.endMeasure('atlas-image-load');
        resolve(img);
      };

      img.onerror = () => {
        console.error('❌ AtlasCanvas: 图片加载失败', resource.name);
        loadingCount--;

        // 🎯 设置错误状态
        operationState = {
          isProcessing: false,
          error: `图片加载失败: ${resource.name}`
        };

        performanceMonitor.endMeasure('atlas-image-load');
        reject(new Error(`图片加载失败: ${resource.name}`));
      };

      // 🎯 优先使用缓存系统
      if (resource.type === 'image') {
        const cachedPreview = imagePreviewCache.getPreview(resource);
        if (cachedPreview) {
          img.src = cachedPreview;
          console.log('✅ AtlasCanvas: 使用缓存的图片预览', resource.name);
          return;
        }
      }

      // 创建图片数据URL
      if (resource.data) {


        try {
          const mimeType = resource.originalFile?.type || 'image/png';
          let blobData;

          // 🎯 处理不同的数据格式
          if (Array.isArray(resource.data)) {
            blobData = new Uint8Array(resource.data);
          } else if (resource.data instanceof ArrayBuffer) {
            blobData = resource.data;
          } else if ((resource.data as any) instanceof Uint8Array) {
            blobData = resource.data;
          } else {
            throw new Error(`不支持的数据格式: ${(resource.data as any)?.constructor?.name || 'unknown'}`);
          }

          const blob = new Blob([blobData], { type: mimeType });
          const url = URL.createObjectURL(blob);



          img.src = url;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.error('❌ AtlasCanvas: 创建图片URL失败', {
            resourceName: resource.name,
            error: errorMessage,
            dataInfo: {
              type: (resource.data as any)?.constructor?.name,
              length: Array.isArray(resource.data) ? resource.data.length :
                     (resource.data instanceof ArrayBuffer) ? resource.data.byteLength :
                     (resource.data as any)?.length || 0
            }
          });
          reject(new Error(`创建图片URL失败: ${resource.name} - ${errorMessage}`));
        }
      } else if (resource.blobUrl) {
        console.log('🔍 AtlasCanvas: 使用现有blobUrl', {
          resourceName: resource.name,
          blobUrl: resource.blobUrl.substring(0, 50) + '...'
        });
        img.src = resource.blobUrl;
      } else {
        console.error('❌ AtlasCanvas: 资源缺少图片数据', {
          resourceName: resource.name,
          resourceId: resource.id,
          hasData: !!resource.data,
          hasBlobUrl: !!resource.blobUrl
        });
        reject(new Error(`资源缺少图片数据: ${resource.name}`));
      }
    });
  }

  // 🎯 优化的加载所有图片 - 添加性能监控和状态反馈
  async function loadAllImages() {
    if (!hasImages || !currentAtlas) {
      console.log('🎨 AtlasCanvas: 图集为空，无需加载图片');
      renderEmptyState();
      return;
    }

    // 🎯 开始性能监控
    performanceMonitor.startMeasure('atlas-load-all-images', {
      atlasName: currentAtlas.name,
      imageCount: currentAtlas.children.length
    });

    isLoading = true;
    error = null;
    totalImages = currentAtlas.children.length;
    loadingCount = totalImages;
    loadedImages.clear();

    // 🎯 设置加载状态
    operationState = {
      isProcessing: true,
      progress: 10,
      operation: 'loading'
    };

    console.log('🔄 AtlasCanvas: 开始加载图集图片', {
      atlasName: currentAtlas.name,
      imageCount: totalImages
    });

    try {
      // 🎯 分批加载图片，避免一次性加载太多
      const batchSize = 5;
      const batches = [];
      for (let i = 0; i < currentAtlas.children.length; i += batchSize) {
        batches.push(currentAtlas.children.slice(i, i + batchSize));
      }

      let completedBatches = 0;
      for (const batch of batches) {
        const loadPromises = batch.map((resource: any) => loadImage(resource));
        await Promise.all(loadPromises);

        completedBatches++;
        operationState.progress = 10 + (completedBatches / batches.length) * 70;
      }

      console.log('✅ AtlasCanvas: 所有图片加载完成');

      operationState.progress = 90;
      operationState.operation = 'rendering';

      // 🎯 延迟渲染，让进度条更新
      setTimeout(() => {
        renderAtlas();
        operationState = { isProcessing: false };
      }, 100);

      // 🎯 结束性能监控
      performanceMonitor.endMeasure('atlas-load-all-images');

    } catch (err) {
      console.error('❌ AtlasCanvas: 图片加载失败', err);
      error = err instanceof Error ? err.message : '图片加载失败';
      operationState = {
        isProcessing: false,
        error: `图片加载失败: ${err instanceof Error ? err.message : '未知错误'}`
      };

      performanceMonitor.endMeasure('atlas-load-all-images');
    } finally {
      isLoading = false;
    }
  }

  // 渲染空状态
  function renderEmptyState() {
    if (!ctx) return;

    ctx.clearRect(0, 0, width, height);

    // 绘制背景
    ctx.fillStyle = '#f5f5f5';
    ctx.fillRect(0, 0, width, height);

    // 绘制边框
    ctx.strokeStyle = '#ddd';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    ctx.strokeRect(10, 10, width - 20, height - 20);

    // 绘制文字
    ctx.fillStyle = '#999';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('图集为空', width / 2, height / 2 - 10);
    ctx.fillText('拖拽图片到左侧图集中添加', width / 2, height / 2 + 15);
  }

  // 🎯 获取当前 canvas 的图片数据 - 暴露给父组件
  export function getCanvasImageData(): Promise<{ blob: Blob; width: number; height: number } | null> {
    return new Promise((resolve) => {
      if (!canvasElement || !ctx) {
        console.warn('❌ AtlasCanvas: Canvas 或 Context 不可用');
        resolve(null);
        return;
      }

      try {
        // 使用 toBlob 方法获取高质量的图片数据
        canvasElement.toBlob((blob) => {
          if (blob && canvasElement) {
            console.log('✅ AtlasCanvas: 成功获取Canvas图片数据', {
              size: blob.size,
              type: blob.type,
              canvasSize: `${canvasElement.width}×${canvasElement.height}`
            });
            resolve({
              blob,
              width: canvasElement.width,
              height: canvasElement.height
            });
          } else {
            console.error('❌ AtlasCanvas: Canvas.toBlob 返回 null');
            resolve(null);
          }
        }, 'image/png', 1.0); // 使用最高质量的 PNG 格式
      } catch (error) {
        console.error('❌ AtlasCanvas: 获取Canvas图片数据失败', error);
        resolve(null);
      }
    });
  }

  // 🎯 生成 cropAreas 数据并更新图集
  function generateAndUpdateCropAreas(layoutResult: LayoutResult) {
    if (!layoutResult || !layoutResult.items || layoutResult.items.length === 0) {
      console.log('🎯 AtlasCanvas: 布局结果为空，跳过生成cropAreas');
      return;
    }

    // 根据布局结果生成 cropAreas
    const cropAreas: CropArea[] = layoutResult.items.map((item) => {
      // 清理文件名，移除重复的扩展名
      let cleanName = item.resource.name;
      if (cleanName.endsWith('.png.png')) {
        cleanName = cleanName.replace('.png.png', '.png');
      } else if (cleanName.endsWith('.jpg.jpg')) {
        cleanName = cleanName.replace('.jpg.jpg', '.jpg');
      } else if (cleanName.endsWith('.jpeg.jpeg')) {
        cleanName = cleanName.replace('.jpeg.jpeg', '.jpeg');
      }

      return {
        id: item.resource.id,
        x: item.x,
        y: item.y,
        width: item.width,
        height: item.height,
        name: cleanName,
        selected: false
      };
    });

    console.log('🎯 AtlasCanvas: 生成cropAreas数据', {
      atlasName: currentAtlas?.name || '无图集',
      itemCount: cropAreas.length,
      canvasSize: `${layoutResult.width}×${layoutResult.height}`,
      areas: cropAreas.map(area => ({
        name: area.name,
        position: `(${area.x},${area.y})`,
        size: `${area.width}×${area.height}`
      }))
    });

    // 🎯 使用回调函数通知父组件更新图集数据，而不是直接修改 prop
    try {
      const updates: Partial<AtlasData> = {
        cropAreas: cropAreas,
        canvas: {
          width: layoutResult.width,
          height: layoutResult.height,
          backgroundColor: '#2a2a2a'
        }
      };

      if (onAtlasUpdate && currentAtlas) {
        onAtlasUpdate(updates);
        console.log('✅ AtlasCanvas: 通过回调更新图集cropAreas数据', {
          atlasId: currentAtlas.id,
          cropAreasCount: cropAreas.length,
          canvasSize: `${updates.canvas!.width}×${updates.canvas!.height}`
        });
      } else {
        console.warn('⚠️ AtlasCanvas: onAtlasUpdate 回调未提供或无当前图集，无法更新图集数据');
      }
    } catch (error) {
      console.error('❌ AtlasCanvas: 更新图集cropAreas失败', error);
    }
  }

  // 🎯 Canvas尺寸调整完成检测
  function waitForCanvasReady(canvas: HTMLCanvasElement, expectedWidth: number, expectedHeight: number): Promise<void> {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = 20; // 最多等待1秒 (50ms * 20)

      function checkCanvas() {
        attempts++;

        // 检查Canvas是否达到预期尺寸
        const isReady = canvas.width === expectedWidth &&
                       canvas.height === expectedHeight &&
                       canvas.offsetWidth > 0 &&
                       canvas.offsetHeight > 0;



        if (isReady) {
          console.log('✅ Canvas已准备好');
          resolve();
        } else if (attempts < maxAttempts) {
          setTimeout(checkCanvas, 50);
        } else {
          console.warn('⚠️ Canvas准备检查超时，强制继续');
          resolve();
        }
      }

      checkCanvas();
    });
  }

  // 🎯 使用ResizeObserver监听Canvas尺寸变化
  let canvasResizeObserver: ResizeObserver | null = null;

  function setupCanvasResizeObserver(canvas: HTMLCanvasElement) {
    // 清理旧的observer
    if (canvasResizeObserver) {
      canvasResizeObserver.disconnect();
    }

    canvasResizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        console.log('🔍 ResizeObserver: Canvas尺寸变化', `${width}×${height}`);
      }
    });

    canvasResizeObserver.observe(canvas);
  }

  // 🎯 延迟绘制函数
  function performDelayedRender(layoutResult: LayoutResult, ctx: CanvasRenderingContext2D) {
    console.log('🎨 AtlasCanvas: 延迟绘制开始');

    // 清理并绘制背景
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    ctx.fillStyle = '#2a2a2a'; // 深色背景
    ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);

    // 按面积从大到小排序
    const sortedItems = [...layoutResult.items].sort((a, b) => {
      const areaA = a.width * a.height;
      const areaB = b.width * b.height;
      return areaB - areaA;
    });

    // 绘制图片
    sortedItems.forEach((item) => {
      const img = loadedImages.get(item.resource.id);
      if (!img || !ctx) {
        console.warn(`❌ 延迟绘制跳过 ${item.resource.name}: img=${!!img}, ctx=${!!ctx}`);
        return;
      }

      const { x, y } = item;
      const actualWidth = img.naturalWidth || img.width;
      const actualHeight = img.naturalHeight || img.height;

      try {
        if (item.rotated) {
          ctx.save();
          ctx.translate(x + actualWidth / 2, y + actualHeight / 2);
          ctx.rotate(Math.PI / 2);
          ctx.drawImage(img, -actualHeight / 2, -actualWidth / 2, actualHeight, actualWidth);
          ctx.restore();
        } else {
          ctx.drawImage(img, x, y, actualWidth, actualHeight);
        }
        console.log(`✅ 延迟绘制成功 ${item.resource.name} at (${x},${y})`);
      } catch (error) {
        console.error(`❌ 延迟绘制失败 ${item.resource.name}:`, error);
      }
    });



    // 生成cropAreas
    generateAndUpdateCropAreas(layoutResult);

    console.log('✅ AtlasCanvas: 延迟绘制完成');
  }

  // 渲染图集 - 使用真正的布局算法，按实际尺寸显示
  function renderAtlas() {
    if (!ctx || !currentAtlas?.children || currentAtlas.children.length === 0) return;

    // 🎯 第一步：计算布局
    const layoutResult = calculateAtlasLayout(currentAtlas.children, {
      algorithm: mergeSettings.algorithm,
      maxWidth: mergeSettings.maxWidth, // 🎯 使用mergeSettings中的设置，不限制
      maxHeight: mergeSettings.maxHeight, // 🎯 使用mergeSettings中的设置，不限制
      padding: mergeSettings.padding,
      allowRotation: mergeSettings.allowRotation,
      powerOfTwo: mergeSettings.powerOfTwo
    });

    // 🎯 安全的Canvas尺寸调整方法
    const newWidth = layoutResult.width;
    const newHeight = layoutResult.height;
    let canvasSizeChanged = false;

    // 检查是否需要调整尺寸
    if (canvasElement && (canvasElement.width !== newWidth || canvasElement.height !== newHeight)) {
      console.log('🔄 AtlasCanvas: 需要调整canvas尺寸', {
        from: `${canvasElement.width}×${canvasElement.height}`,
        to: `${newWidth}×${newHeight}`,
        layoutSize: `${layoutResult.width}×${layoutResult.height}`
      });

      // 🎯 延迟绘制方案：调整尺寸后延迟绘制
      canvasSizeChanged = true;

      // 直接调整尺寸（这会清空Canvas）
      canvasElement.width = newWidth;
      canvasElement.height = newHeight;

      // 更新组件的尺寸状态
      width = newWidth;
      height = newHeight;

      // 重新获取context（必须在尺寸调整后）
      ctx = canvasElement.getContext('2d');
      if (!ctx) {
        console.error('❌ AtlasCanvas: 重新获取context失败');
        return;
      }

      console.log('✅ AtlasCanvas: Canvas尺寸调整完成，延迟绘制', {
        newSize: `${newWidth}×${newHeight}`,
        contextValid: !!ctx
      });

      // 🎯 等待Canvas真正准备好后再绘制
      waitForCanvasReady(canvasElement, newWidth, newHeight).then(() => {
        if (ctx && canvasElement) {
          console.log('🔄 AtlasCanvas: Canvas准备完成，开始绘制');
          performDelayedRender(layoutResult, ctx);
        }
      });

      return; // 跳过立即绘制
    }

    // 保存布局结果供header显示
    lastLayoutResult = layoutResult;

    // 🎯 第三步：确保context有效
    if (!ctx) {
      console.error('❌ AtlasCanvas: Context无效，无法绘制');
      return;
    }

    // 🎯 第四步：清理并绘制背景
    console.log('🎨 AtlasCanvas: 开始清理和绘制背景', {
      canvasSize: `${ctx.canvas.width}×${ctx.canvas.height}`,
      contextValid: !!ctx,
      sizeChanged: canvasSizeChanged
    });

    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    ctx.fillStyle = '#2a2a2a'; // 深色背景
    ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);



    // 🎯 第五步：开始绘制图集
    console.log('🎨 AtlasCanvas: 渲染图集', {
      algorithm: mergeSettings.algorithm,
      itemCount: layoutResult.items.length,
      efficiency: `${(layoutResult.efficiency * 100).toFixed(1)}%`,
      finalSize: `${layoutResult.width}x${layoutResult.height}`,
      canvasSize: `${ctx.canvas.width}×${ctx.canvas.height}`,
      sizeChanged: canvasSizeChanged,
      items: layoutResult.items.map(item => ({
        name: item.resource.name,
        position: `(${item.x},${item.y})`,
        size: `${item.width}×${item.height}`
      }))
    });

    // 按面积从大到小排序，确保大图片先绘制（在下层）
    const sortedItems = [...layoutResult.items].sort((a, b) => {
      const areaA = a.width * a.height;
      const areaB = b.width * b.height;
      return areaB - areaA; // 大图片优先绘制
    });

    console.log('🎨 AtlasCanvas: 绘制顺序', sortedItems.map(item =>
      `${item.resource.name}(${item.width}×${item.height}) at (${item.x},${item.y})`
    ));

    // 绘制每个图片 - 按实际尺寸显示，允许超出canvas边界
    console.log('🎨 开始绘制图片', {
      canvasSize: `${width}×${height}`,
      itemCount: sortedItems.length,
      canvasElement: !!canvasElement,
      ctx: !!ctx
    });

    sortedItems.forEach((item, index) => {
      const img = loadedImages.get(item.resource.id);
      if (!img || !ctx) {
        console.warn(`❌ 跳过绘制 ${item.resource.name}: img=${!!img}, ctx=${!!ctx}`);
        return;
      }

      const { x, y } = item;

      // 使用图片的实际尺寸，不缩放
      const actualWidth = img.naturalWidth || img.width;
      const actualHeight = img.naturalHeight || img.height;

      console.log(`🎨 绘制第${index+1}个图片 ${item.resource.name}:`, {
        position: `(${x},${y})`,
        size: `${actualWidth}×${actualHeight}`,
        canvasSize: `${ctx.canvas.width}×${ctx.canvas.height}`,
        inBounds: x >= 0 && y >= 0 && x < ctx.canvas.width && y < ctx.canvas.height,
        imgComplete: img.complete,
        imgSrc: img.src.substring(0, 30) + '...'
      });

      try {


        // 检查是否需要旋转
        if (item.rotated) {
          // 保存当前状态
          ctx.save();

          // 移动到图片中心点
          ctx.translate(x + actualWidth / 2, y + actualHeight / 2);

          // 旋转90度
          ctx.rotate(Math.PI / 2);

          // 绘制旋转后的图片（注意：旋转后宽高互换）
          ctx.drawImage(img, -actualHeight / 2, -actualWidth / 2, actualHeight, actualWidth);

          // 恢复状态
          ctx.restore();

          console.log(`✅ 成功绘制旋转图片 ${item.resource.name} at (${x},${y}) size ${actualWidth}×${actualHeight} (rotated)`);
        } else {
          // 直接绘制图片，不旋转
          ctx.drawImage(img, x, y, actualWidth, actualHeight);

          console.log(`✅ 成功绘制 ${item.resource.name} at (${x},${y}) size ${actualWidth}×${actualHeight}`);
        }
      } catch (error) {
        console.error(`❌ 绘制失败 ${item.resource.name}:`, error);

        // 绘制错误占位符
        ctx.fillStyle = '#ff0000';
        ctx.fillRect(x, y, Math.min(actualWidth, 100), Math.min(actualHeight, 100));
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('ERROR', x + 50, y + 50);
      }
    });

    // 🎯 第六步：绘制完成
    if (canvasSizeChanged) {
      console.log('✅ AtlasCanvas: Canvas尺寸调整后绘制完成');
    }

      console.log('✅ AtlasCanvas: 渲染完成', {
        canvasSize: `${ctx.canvas.width}×${ctx.canvas.height}`,
        itemCount: sortedItems.length,
        sizeChanged: canvasSizeChanged
      });



    // 🎯 第七步：渲染完成后生成 cropAreas 数据并更新图集
    generateAndUpdateCropAreas(layoutResult);
  }

  // 🎯 修复的状态管理 - 避免无限循环
  let lastCanvasElement: HTMLCanvasElement | undefined;

  $effect(() => {
    // 避免重复初始化相同的Canvas元素
    if (canvasElement && canvasElement !== lastCanvasElement) {
      console.log('🔄 AtlasCanvas: Canvas元素变化，重新初始化');
      lastCanvasElement = canvasElement;

      // 异步初始化避免循环
      setTimeout(() => {
        initCanvas();
      }, 0);
    }
  });



  // 🎯 自动清理错误状态
  $effect(() => {
    if (operationState.error) {
      const timer = setTimeout(() => {
        operationState = { isProcessing: false };
      }, 5000); // 5秒后自动清理错误状态

      return () => clearTimeout(timer);
    }
  });

  // 🎯 组件销毁时清理资源
  $effect(() => {
    return () => {
      // 清理操作状态
      operationState = { isProcessing: false };

      // 清理图片缓存
      loadedImages.forEach(img => {
        if (img.src.startsWith('blob:')) {
          URL.revokeObjectURL(img.src);
        }
      });

      console.log('🧹 AtlasCanvas: 组件销毁，清理资源');
    };
  });

  onMount(() => {
    console.log('🎨 AtlasCanvas: 组件挂载', currentAtlas?.name || '无图集');
    // initCanvas会在$effect中调用，这里不需要重复调用
  });

  onDestroy(() => {
    // 清理创建的URL对象
    loadedImages.forEach(img => {
      if (img.src.startsWith('blob:')) {
        URL.revokeObjectURL(img.src);
      }
    });
    console.log('🎨 AtlasCanvas: 组件销毁');
  });

  // 组件销毁时清理
  onDestroy(() => {
    if (canvasResizeObserver) {
      canvasResizeObserver.disconnect();
      canvasResizeObserver = null;
    }
  });
</script>

<div class="atlas-canvas-container" style="width: {width}px; height: {height}px;">
  <!-- 🎯 操作状态指示器 -->
  {#if operationState.isProcessing}
    <div class="operation-status">
      <span class="loading-spinner">⏳</span>
      <div class="status-info">
        <span class="status-text">
          {operationState.operation === 'loading' ? '加载图片中...' :
           operationState.operation === 'rendering' ? '渲染图集中...' :
           operationState.operation === 'calculating' ? '计算布局中...' : '处理中...'}
        </span>
        <span class="status-detail">图集: {currentAtlas?.name || '无图集'}</span>
        {#if operationState.progress !== undefined}
          <div class="progress-bar">
            <div class="progress-fill" style="width: {operationState.progress}%"></div>
          </div>
        {/if}
      </div>
    </div>
  {:else if operationState.error}
    <div class="operation-error">
      <span class="error-icon">❌</span>
      <span class="error-text">{operationState.error}</span>
    </div>
  {/if}



  <div class="canvas-wrapper">
    <canvas
      bind:this={canvasElement}
      class="atlas-canvas"
      {width}
      {height}
    ></canvas>
  </div>
</div>

<style>
  .atlas-canvas-container {
    display: flex;
    flex-direction: column;
    /* 🎯 移除固定高度，使用动态尺寸 - 通过内联样式设置 */
    background: var(--theme-surface);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    /* 🎯 确保容器尺寸等于内部canvas尺寸 */
    box-sizing: border-box;
  }

  /* 🎯 操作状态指示器样式 */
  .operation-status {
    position: absolute;
    top: var(--spacing-3);
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background: rgba(59, 130, 246, 0.95);
    border-radius: var(--border-radius-large);
    font-size: var(--font-size-sm);
    color: white;
    border: 1px solid rgba(59, 130, 246, 0.3);
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .loading-spinner {
    animation: spin 1s linear infinite;
    font-size: 1.2em;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .status-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
    min-width: 0;
  }

  .status-text {
    font-weight: 600;
    white-space: nowrap;
  }

  .status-detail {
    font-size: var(--font-size-xs);
    opacity: 0.9;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
  }

  .progress-bar {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: white;
    transition: width 0.3s ease;
    border-radius: 2px;
  }

  .operation-error {
    position: absolute;
    top: var(--spacing-3);
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background: rgba(239, 68, 68, 0.95);
    border-radius: var(--border-radius-large);
    font-size: var(--font-size-sm);
    color: white;
    border: 1px solid rgba(239, 68, 68, 0.3);
    backdrop-filter: blur(8px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .error-text {
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300px;
  }



  .canvas-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #1a1a1a; /* 深色背景 */
    overflow: hidden; /* 🎯 删除滚动条 */
    /* 🎯 填充整个容器空间，容器尺寸由父级控制 */
    flex: 1;
    width: 100%;
    height: 100%;
  }

  .atlas-canvas {
    background: #2a2a2a; /* 深色canvas背景 */
    display: block;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
    /* 🎯 删除边框、圆角、阴影和尺寸限制 */
  }
</style>
