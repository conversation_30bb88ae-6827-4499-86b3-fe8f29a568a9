/**
 * 导出工具函数
 */

import type { ExportItem, ExportConfig, ExportResult, ExportProgress } from './exportTypes';

// 导出器接口
export interface IExporter {
  export(items: ExportItem[], config: ExportConfig, selectedFolder: string): Promise<ExportResult>;
}

// 抽象导出器基类
export abstract class BaseExporter implements IExporter {
  protected onProgress?: (progress: ExportProgress) => void;

  setProgressCallback(callback: (progress: ExportProgress) => void) {
    this.onProgress = callback;
  }

  abstract export(items: ExportItem[], config: ExportConfig, selectedFolder: string): Promise<ExportResult>;

  protected reportProgress(current: number, total: number, currentFile: string, stage: ExportProgress['stage']) {
    if (this.onProgress) {
      this.onProgress({
        current,
        total,
        currentFile,
        stage
      });
    }
  }
}

// 导出管理器
export class ExportManager {
  private exporters = new Map<string, IExporter>();

  registerExporter(type: string, exporter: IExporter) {
    this.exporters.set(type, exporter);
  }

  async export(
    type: string,
    items: ExportItem[],
    config: ExportConfig,
    selectedFolder: string,
    onProgress?: (progress: ExportProgress) => void
  ): Promise<ExportResult> {
    const exporter = this.exporters.get(type);
    if (!exporter) {
      throw new Error(`未找到导出器: ${type}`);
    }

    // 如果导出器支持进度回调，设置它
    if (onProgress && 'setProgressCallback' in exporter && typeof exporter.setProgressCallback === 'function') {
      (exporter as any).setProgressCallback(onProgress);
    }

    const startTime = Date.now();
    try {
      const result = await exporter.export(items, config, selectedFolder);
      result.duration = Date.now() - startTime;
      return result;
    } catch (error) {
      return {
        success: false,
        files: [],
        totalSize: 0,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  getAvailableExporters(): string[] {
    return Array.from(this.exporters.keys());
  }
}

// 全局导出管理器实例
export const exportManager = new ExportManager();

// 工具函数：从ArrayBuffer创建Image元素
export async function createImageFromBuffer(buffer: ArrayBuffer, mimeType: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const blob = new Blob([buffer], { type: mimeType });
    const url = URL.createObjectURL(blob);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve(img);
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error('图片加载失败'));
    };

    img.src = url;
  });
}

// 工具函数：Canvas转Blob
export async function canvasToBlob(canvas: HTMLCanvasElement, format: string = 'image/png', quality?: number): Promise<Blob> {
  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob);
      } else {
        reject(new Error('Canvas转换失败'));
      }
    }, format, quality);
  });
}

// 工具函数：获取MIME类型
export function getMimeType(path: string): string {
  const extension = path.toLowerCase().split('.').pop();
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    case 'svg':
      return 'image/svg+xml';
    case 'bmp':
      return 'image/bmp';
    default:
      return 'image/png';
  }
}

// 工具函数：格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函数：生成安全的文件名
export function sanitizeFileName(name: string): string {
  return name
    .replace(/[<>:"/\\|?*]/g, '_')  // 替换非法字符
    .replace(/\s+/g, '_')           // 替换空格
    .replace(/[^\w\-_.]/g, '_')     // 只保留字母、数字、下划线、连字符和点
    .replace(/_+/g, '_')            // 合并多个下划线
    .replace(/^_|_$/g, '');         // 移除开头和结尾的下划线
}

// 工具函数：确保文件扩展名
export function ensureExtension(fileName: string, extension: string): string {
  const ext = extension.startsWith('.') ? extension : `.${extension}`;
  if (fileName.toLowerCase().endsWith(ext.toLowerCase())) {
    return fileName;
  }
  return fileName + ext;
}
