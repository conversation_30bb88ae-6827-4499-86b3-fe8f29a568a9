/**
 * 国际化配置和初始化
 */

import { browser } from '$app/environment';
import { init, register, locale, waitLocale } from 'svelte-i18n';

// 支持的语言列表
export const supportedLocales = [
  { code: 'zh-CN', name: '中文（简体）', flag: '🇨🇳' },
  { code: 'en-US', name: 'English', flag: '🇺🇸' }
] as const;

export type SupportedLocale = typeof supportedLocales[number]['code'];

// 默认语言
const defaultLocale: SupportedLocale = 'zh-CN';

// 注册语言包
register('zh-CN', () => import('./locales/zh-CN.json'));
register('en-US', () => import('./locales/en-US.json'));

// 获取浏览器语言偏好
function getBrowserLocale(): SupportedLocale {
  if (!browser) return defaultLocale;

  // 首先检查localStorage中保存的语言设置
  const savedLocale = localStorage.getItem('locale') as SupportedLocale;
  if (savedLocale && supportedLocales.some(l => l.code === savedLocale)) {
    return savedLocale;
  }

  // 检查浏览器语言设置
  const browserLang = navigator.language || navigator.languages?.[0];
  if (browserLang) {
    // 精确匹配
    if (supportedLocales.some(l => l.code === browserLang)) {
      return browserLang as SupportedLocale;
    }

    // 语言代码匹配（如 'zh' 匹配 'zh-CN'）
    const langCode = browserLang.split('-')[0];
    const matchedLocale = supportedLocales.find(l => l.code.startsWith(langCode));
    if (matchedLocale) {
      return matchedLocale.code;
    }
  }

  return defaultLocale;
}

// 初始化i18n
export function initI18n() {
  const initialLocale = getBrowserLocale();

  init({
    fallbackLocale: defaultLocale,
    initialLocale,
    loadingDelay: 200, // 延迟显示加载状态
  });

  console.log(`🌐 i18n initialized with locale: ${initialLocale}`);
  return waitLocale();
}

// 切换语言
export function setLocale(newLocale: SupportedLocale) {
  if (!supportedLocales.some(l => l.code === newLocale)) {
    console.warn(`Unsupported locale: ${newLocale}`);
    return;
  }

  locale.set(newLocale);

  if (browser) {
    localStorage.setItem('locale', newLocale);
    console.log(`🌐 Locale changed to: ${newLocale}`);
  }
}

// 获取当前语言信息
export function getCurrentLocaleInfo() {
  return supportedLocales.find(l => l.code === locale);
}

// 导出常用的i18n函数
export { locale, _, isLoading } from 'svelte-i18n';
