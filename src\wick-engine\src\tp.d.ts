import { Base } from "./base/Base";
import { Project } from "./base/Project";
import { Frame } from "./base/Frame";
import { Tween } from "./base/Tween";

export interface WickNamespace {
  version: string;
  resourcepath: string;
  _originals: Record<string, any>;
  Clipboard: any;
  ToolSettings: any;
  Base: typeof Base;
  Project: typeof Project;
  Frame: typeof Frame;
  Tween: typeof Tween;
  Color: any;
}

declare global {
  interface Window {
    WICK_ENGINE_BUILD_VERSION?: string;
    Wick: WickNamespace;
  }
}
