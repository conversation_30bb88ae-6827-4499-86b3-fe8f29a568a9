/**
 * GameSprite Engine - 全局事件系统
 * 使用静态类实现全局事件管理
 */

export type EventCallback<T = any> = (data?: T) => void;

interface EventListener {
  id: string;
  callback: EventCallback;
  once?: boolean;
}

export class EventSystem {
  private static listeners: Map<string, EventListener[]> = new Map();
  private static listenerIdCounter: number = 0;

  /**
   * 注册事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   * @param once 是否只执行一次
   * @returns 监听器ID，用于取消监听
   */
  static on<T = any>(eventName: string, callback: EventCallback<T>, once: boolean = false): string {
    const listenerId = `listener_${++this.listenerIdCounter}`;
    
    if (!this.listeners.has(eventName)) {
      this.listeners.set(eventName, []);
    }
    
    const listeners = this.listeners.get(eventName)!;
    listeners.push({
      id: listenerId,
      callback,
      once
    });
    
    console.log(`📡 EventSystem: 注册监听器 [${eventName}] ID: ${listenerId}`);
    return listenerId;
  }

  /**
   * 注册一次性事件监听器
   * @param eventName 事件名称
   * @param callback 回调函数
   * @returns 监听器ID
   */
  static once<T = any>(eventName: string, callback: EventCallback<T>): string {
    return this.on(eventName, callback, true);
  }

  /**
   * 取消事件监听器
   * @param eventName 事件名称
   * @param listenerId 监听器ID
   */
  static off(eventName: string, listenerId: string): boolean {
    const listeners = this.listeners.get(eventName);
    if (!listeners) return false;
    
    const index = listeners.findIndex(listener => listener.id === listenerId);
    if (index === -1) return false;
    
    listeners.splice(index, 1);
    
    // 如果没有监听器了，删除事件
    if (listeners.length === 0) {
      this.listeners.delete(eventName);
    }
    
    console.log(`📡 EventSystem: 取消监听器 [${eventName}] ID: ${listenerId}`);
    return true;
  }

  /**
   * 取消所有事件监听器
   * @param eventName 事件名称
   */
  static offAll(eventName: string): void {
    this.listeners.delete(eventName);
    console.log(`📡 EventSystem: 取消所有监听器 [${eventName}]`);
  }

  /**
   * 派发事件
   * @param eventName 事件名称
   * @param data 事件数据
   */
  static emit<T = any>(eventName: string, data?: T): void {
    const listeners = this.listeners.get(eventName);
    if (!listeners || listeners.length === 0) {
      console.log(`📡 EventSystem: 派发事件 [${eventName}] - 无监听器`);
      return;
    }
    
    console.log(`📡 EventSystem: 派发事件 [${eventName}] - ${listeners.length}个监听器`);
    
    // 复制监听器数组，避免在执行过程中被修改
    const listenersToExecute = [...listeners];
    
    // 移除一次性监听器
    const onceListeners = listeners.filter(listener => listener.once);
    onceListeners.forEach(listener => {
      this.off(eventName, listener.id);
    });
    
    // 执行回调
    listenersToExecute.forEach(listener => {
      try {
        listener.callback(data);
      } catch (error) {
        console.error(`📡 EventSystem: 监听器执行错误 [${eventName}] ID: ${listener.id}`, error);
      }
    });
  }

  /**
   * 获取事件监听器数量
   * @param eventName 事件名称
   */
  static getListenerCount(eventName: string): number {
    const listeners = this.listeners.get(eventName);
    return listeners ? listeners.length : 0;
  }

  /**
   * 获取所有事件名称
   */
  static getAllEventNames(): string[] {
    return Array.from(this.listeners.keys());
  }

  /**
   * 清除所有事件监听器
   */
  static clear(): void {
    this.listeners.clear();
    console.log('📡 EventSystem: 清除所有事件监听器');
  }

  /**
   * 获取调试信息
   */
  static getDebugInfo(): { [eventName: string]: number } {
    const info: { [eventName: string]: number } = {};
    this.listeners.forEach((listeners, eventName) => {
      info[eventName] = listeners.length;
    });
    return info;
  }
}

// 预定义的事件名称常量
export const ENGINE_EVENTS = {
  // 引擎生命周期
  ENGINE_INIT: 'engine:init',
  ENGINE_DESTROY: 'engine:destroy',
  
  // 场景事件
  SCENE_CHANGE: 'scene:change',
  SCENE_CREATE: 'scene:create',
  SCENE_DESTROY: 'scene:destroy',
  
  // 视口事件
  VIEWPORT_ZOOM: 'viewport:zoom',
  VIEWPORT_PAN: 'viewport:pan',
  VIEWPORT_ROTATE: 'viewport:rotate',
  VIEWPORT_RESET: 'viewport:reset',
  
  // 项目事件
  PROJECT_CREATE: 'project:create',
  PROJECT_SAVE: 'project:save',
  PROJECT_LOAD: 'project:load',
  
  // 工具事件
  TOOL_SELECT: 'tool:select',
  TOOL_ACTIVATE: 'tool:activate',
  TOOL_DEACTIVATE: 'tool:deactivate',
  
  // 快捷键事件
  HOTKEY_PRESSED: 'hotkey:pressed',
  
  // 渲染事件
  RENDER_FRAME: 'render:frame',
  RENDER_RESIZE: 'render:resize'
} as const;

export type EngineEventName = typeof ENGINE_EVENTS[keyof typeof ENGINE_EVENTS];
