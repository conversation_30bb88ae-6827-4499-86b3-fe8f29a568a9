/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

/** 全局API类 */
export class GlobalAPI {
  protected scriptOwner: any;

  /**
   * 定义所有API成员，如函数和属性
   */
  static get apiMemberNames(): string[] {
    return [
      "stop",
      "play",
      "gotoAndStop",
      "gotoAndPlay",
      "gotoNextFrame",
      "gotoPrevFrame",
      "isMouseDown",
      "mouseX",
      "mouseY",
      "mouseMoveX",
      "mouseMoveY",
      "key",
      "keys",
      "isKeyDown",
      "keyIsDown",
      "isKeyJustPressed",
      "keyIsJustPressed",
      "random",
      "playSound",
      "stopAllSounds",
      "onEvent",
      "hideCursor",
      "showCursor",
      "hitTestOptions",
    ];
  }

  /**
   * @param scriptOwner - 拥有正在执行的脚本的可tick对象
   */
  constructor(scriptOwner: any) {
    this.scriptOwner = scriptOwner;
  }

  /**
   * 返回绑定到脚本所有者的API成员列表
   * @returns API成员数组
   */
  get apiMembers(): { name: string; fn: any }[] {
    const members: { name: string; fn: any }[] = [];

    GlobalAPI.apiMemberNames.forEach((name) => {
      let fn = (this as any)[name];
      if (fn instanceof Function) {
        fn = fn.bind(this);
      }
      members.push({
        name: name,
        fn: fn,
      });
    });

    return members;
  }

  /**
   * 停止对象父剪辑的时间轴
   */
  stop(): void {
    this.scriptOwner.parentClip.stop();
  }

  /**
   * 播放对象父剪辑的时间轴
   */
  play(): void {
    this.scriptOwner.parentClip.play();
  }

  /**
   * 将父剪辑的播放头移动到某一帧并停止该父剪辑的时间轴
   * @param frame - 要移动播放头到的帧名称或帧号
   */
  gotoAndStop(frame: string | number): void {
    this.scriptOwner.parentClip.gotoAndStop(frame);
  }

  /**
   * 将父剪辑的播放头移动到某一帧并播放该父剪辑的时间轴
   * @param frame - 要移动播放头到的帧名称或帧号
   */
  gotoAndPlay(frame: string | number): void {
    this.scriptOwner.parentClip.gotoAndPlay(frame);
  }

  /**
   * 将对象父剪辑的播放头移动到下一帧
   */
  gotoNextFrame(): void {
    this.scriptOwner.parentClip.gotoNextFrame();
  }

  /**
   * 将对象父剪辑的播放头移动到上一帧
   */
  gotoPrevFrame(): void {
    this.scriptOwner.parentClip.gotoPrevFrame();
  }

  /**
   * 设置命中测试选项
   * @param options - 命中测试选项
   */
  hitTestOptions(options: any): void {
    this.scriptOwner.project.hitTestOptions = options;
  }

  /**
   * 返回表示项目的对象，具有宽度、高度、帧率、背景颜色和名称等属性
   */
  get project(): any {
    const project = this.scriptOwner.project && this.scriptOwner.project.root;
    if (project) {
      // 附加一些项目设置的别名
      project.width = this.scriptOwner.project.width;
      project.height = this.scriptOwner.project.height;
      project.framerate = this.scriptOwner.project.framerate;
      project.backgroundColor = this.scriptOwner.project.backgroundColor;
      project.name = this.scriptOwner.project.name;
      project.hitTestOptions = this.scriptOwner.project.hitTestOptions;
    }
    return project;
  }

  /**
   * @deprecated
   * 返回项目的遗留项。使用'project'代替
   */
  get root(): any {
    return this.project;
  }

  /**
   * 返回当前对象的父对象的引用
   */
  get parent(): any {
    return this.scriptOwner.parentClip;
  }

  /**
   * @deprecated
   * 返回父剪辑的遗留项。使用'parent'代替
   */
  get parentObject(): any {
    return this.scriptOwner.parentClip;
  }

  /**
   * 返回最后一个按下的键
   * @returns 如果还没有按键，则返回null
   */
  get key(): string | null {
    if (!this.scriptOwner.project) return null;
    return this.scriptOwner.project.currentKey;
  }

  /**
   * 返回当前按下的所有键的列表
   * @returns 所有键的字符串数组。如果没有按键，则返回空数组
   */
  get keys(): string[] | null {
    if (!this.scriptOwner.project) return null;
    return this.scriptOwner.project.keysDown;
  }

  /**
   * 如果给定的键当前处于按下状态，则返回true
   * @param key - 要检查的键
   */
  isKeyDown(key: string): boolean | null {
    if (!this.scriptOwner.project) return null;
    return this.scriptOwner.project.isKeyDown(key);
  }

  /**
   * @deprecated
   * 遗留项，使用'isKeyDown'代替
   */
  keyIsDown(key: string): boolean | null {
    return this.isKeyDown(key.toLowerCase());
  }

  /**
   * 如果给定的键在最后一个tick内刚刚被按下，则返回true
   * @param key - 要检查的键
   */
  isKeyJustPressed(key: string): boolean | null {
    if (!this.scriptOwner.project) return null;
    return this.scriptOwner.project.isKeyJustPressed(key);
  }

  /**
   * @deprecated
   * 遗留项，使用'isKeyJustPressed'代替
   */
  keyIsJustPressed(key: string): boolean | null {
    return this.isKeyJustPressed(key.toLowerCase());
  }
}
