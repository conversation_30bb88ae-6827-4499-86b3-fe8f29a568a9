/**
 * 图片处理模块
 * 提供高性能的图片裁剪功能
 */

use serde::{Deserialize, Serialize};
use image::{ImageFormat, imageops::FilterType};
use rayon::prelude::*;
use std::io::Cursor;
use tauri::Emitter;

#[derive(Debug, Deserialize)]
pub struct CropArea {
    pub id: String,
    pub x: u32,
    pub y: u32,
    pub width: u32,
    pub height: u32,
    pub name: String,
}

#[derive(Debug, Deserialize)]
pub struct SplitSettings {
    #[serde(rename = "cellWidth")]
    pub cell_width: u32,
    #[serde(rename = "cellHeight")]
    pub cell_height: u32,
    #[serde(rename = "gridMode")]
    pub grid_mode: bool,
    pub padding: u32,
    pub spacing: u32,
    #[serde(rename = "autoDetect")]
    pub auto_detect: bool,
    #[serde(rename = "minWidth")]
    pub min_width: u32,
    #[serde(rename = "minHeight")]
    pub min_height: u32,
}

#[derive(Debug, Deserialize)]
pub struct CropImageRequest {
    #[serde(rename = "imageId")]
    pub image_id: String,
    #[serde(rename = "imageName")]
    pub image_name: String,
    #[serde(rename = "imageData")]
    pub image_data: Vec<u8>,
    #[serde(rename = "imageWidth")]
    pub image_width: u32,
    #[serde(rename = "imageHeight")]
    pub image_height: u32,
    #[serde(rename = "splitSettings")]
    pub split_settings: SplitSettings,
    #[serde(rename = "cropAreas")]
    pub crop_areas: Option<Vec<CropArea>>,
    #[serde(rename = "outputFormat")]
    pub output_format: String,
    pub quality: f32,
}

#[derive(Debug, Serialize)]
pub struct CroppedImageResult {
    pub id: String,
    pub name: String,
    pub data: Vec<u8>,
    pub width: u32,
    pub height: u32,
    pub format: String,
    #[serde(rename = "sizeBytes")]
    pub size_bytes: usize,
    #[serde(rename = "cellIndex")]
    pub cell_index: Option<usize>,
    // 🎯 新增：ProcessedImageData支持
    #[serde(rename = "dataUrl")]
    pub data_url: Option<String>,
    #[serde(rename = "thumbnailUrl")]
    pub thumbnail_url: Option<String>,
    #[serde(rename = "previewUrl")]
    pub preview_url: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct CropImageResponse {
    pub success: bool,
    #[serde(rename = "imageId")]
    pub image_id: String,
    #[serde(rename = "totalCropped")]
    pub total_cropped: usize,
    #[serde(rename = "croppedImages")]
    pub cropped_images: Vec<CroppedImageResult>,
    #[serde(rename = "processingTimeMs")]
    pub processing_time_ms: u64,
    pub error: Option<String>,
}

// 🎯 新增：批量图片处理相关结构
#[derive(Debug, Clone, Deserialize)]
pub struct ImageInput {
    pub id: String,
    pub buffer: Vec<u8>,
    #[serde(rename = "originalType")]
    pub original_type: String,
    #[serde(rename = "fileName")]
    pub file_name: String,
}

#[derive(Debug, Deserialize)]
pub struct ProcessOptions {
    #[serde(rename = "generateThumbnail")]
    pub generate_thumbnail: Option<bool>,
    #[serde(rename = "thumbnailSize")]
    pub thumbnail_size: Option<u32>,
    #[serde(rename = "generatePreview")]
    pub generate_preview: Option<bool>,
    #[serde(rename = "previewSize")]
    pub preview_size: Option<u32>,
    pub quality: Option<f32>,
    pub format: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ProcessedImageData {
    pub original: Option<ImageData>,
    pub thumbnail: Option<ImageData>,
    pub preview: Option<ImageData>,
    #[serde(rename = "processedAt")]
    pub processed_at: String,
}

#[derive(Debug, Serialize)]
pub struct ImageData {
    pub width: u32,
    pub height: u32,
    #[serde(rename = "dataUrl")]
    pub data_url: String,
    pub size: Option<u32>,
}

#[derive(Debug, Serialize)]
pub struct ProcessedImageResult {
    pub id: String,
    #[serde(rename = "uniqueId")]
    pub unique_id: String,
    pub success: bool,
    #[serde(rename = "fileName")]
    pub file_name: String,
    #[serde(rename = "originalType")]
    pub original_type: String,
    #[serde(rename = "processedData")]
    pub processed_data: Option<ProcessedImageData>,
    pub error: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct BatchProcessResponse {
    pub success: bool,
    pub results: Vec<ProcessedImageResult>,
    #[serde(rename = "totalProcessed")]
    pub total_processed: usize,
    #[serde(rename = "processingTimeMs")]
    pub processing_time_ms: u64,
    pub error: Option<String>,
}

/// 主要的图片裁剪命令
#[tauri::command]
pub async fn crop_image_batch(
    request: CropImageRequest,
    window: tauri::Window,
) -> Result<CropImageResponse, String> {
    let start_time = std::time::Instant::now();

    println!("🎯 ImageProcessor: 开始处理裁剪请求");
    println!("   图片ID: {}", request.image_id);
    println!("   图片名称: {}", request.image_name);
    println!("   图片尺寸: {}×{}", request.image_width, request.image_height);
    println!("   数据大小: {} bytes", request.image_data.len());
    println!("   输出格式: {}", request.output_format);

    // 1. 解码原始图片
    let img = match image::load_from_memory(&request.image_data) {
        Ok(img) => {
            println!("✅ ImageProcessor: 图片解码成功 {}×{}", img.width(), img.height());
            img
        },
        Err(e) => {
            let error_msg = format!("Failed to decode image: {}", e);
            println!("❌ ImageProcessor: {}", error_msg);
            return Ok(CropImageResponse {
                success: false,
                image_id: request.image_id,
                total_cropped: 0,
                cropped_images: vec![],
                processing_time_ms: 0,
                error: Some(error_msg),
            });
        }
    };

    // 2. 生成或使用现有的裁切区域
    let crop_areas = match request.crop_areas {
        Some(areas) => {
            println!("🎯 ImageProcessor: 使用前端提供的裁切区域 {} 个", areas.len());
            areas
        },
        None => {
            println!("🎯 ImageProcessor: 从splitSettings生成裁切区域");
            generate_crop_areas_from_settings(&request.split_settings, img.width(), img.height())
        }
    };

    println!("📊 ImageProcessor: 总共需要裁切 {} 个区域", crop_areas.len());

    // 3. 确定输出格式
    let output_format = match request.output_format.as_str() {
        "webp" => ImageFormat::WebP,
        "jpeg" | "jpg" => ImageFormat::Jpeg,
        _ => ImageFormat::Png,
    };

    // 4. 并行裁切所有区域
    let results: Vec<Result<CroppedImageResult, String>> = crop_areas
        .par_iter()
        .enumerate()
        .map(|(index, area)| {
            // 验证裁切区域是否在图片范围内
            if area.x + area.width > img.width() || area.y + area.height > img.height() {
                return Err(format!(
                    "裁切区域超出图片范围: area({},{},{},{}) vs image({}×{})",
                    area.x, area.y, area.width, area.height, img.width(), img.height()
                ));
            }

            // 裁切单个区域
            let cropped = img.crop_imm(area.x, area.y, area.width, area.height);

            // 编码为指定格式
            let mut buffer = Vec::new();
            let encode_result = match output_format {
                ImageFormat::WebP => {
                    // WebP编码
                    cropped.write_to(&mut Cursor::new(&mut buffer), ImageFormat::WebP)
                },
                ImageFormat::Jpeg => {
                    // JPEG编码，使用质量参数
                    cropped.write_to(&mut Cursor::new(&mut buffer), ImageFormat::Jpeg)
                },
                _ => {
                    // PNG编码
                    cropped.write_to(&mut Cursor::new(&mut buffer), ImageFormat::Png)
                }
            };

            if let Err(e) = encode_result {
                return Err(format!("Failed to encode image {}: {}", area.id, e));
            }

            // 🎯 生成DataURL
            let data_url = generate_data_url(&buffer, &request.output_format);

            // 发送进度更新（每10个或最后一个）
            if (index + 1) % 10 == 0 || index + 1 == crop_areas.len() {
                let progress = (index + 1) as f32 / crop_areas.len() as f32 * 100.0;
                let _ = window.emit("crop_progress", serde_json::json!({
                    "completed": index + 1,
                    "total": crop_areas.len(),
                    "percentage": progress,
                    "currentArea": area.name
                }));
            }

            Ok(CroppedImageResult {
                id: area.id.clone(),
                name: area.name.clone(),
                data: buffer.clone(),
                width: area.width,
                height: area.height,
                format: request.output_format.clone(),
                size_bytes: buffer.len(),
                cell_index: Some(index),
                // 🎯 ProcessedImageData支持
                data_url: Some(data_url),
                thumbnail_url: None, // 暂时不生成缩略图
                preview_url: None,   // 暂时不生成预览图
            })
        })
        .collect();

    // 5. 处理结果
    let mut cropped_images = Vec::new();
    let mut error_count = 0;

    for result in results {
        match result {
            Ok(cropped) => cropped_images.push(cropped),
            Err(e) => {
                println!("❌ ImageProcessor: 裁切失败: {}", e);
                error_count += 1;
            }
        }
    }

    let processing_time = start_time.elapsed().as_millis() as u64;

    println!("✅ ImageProcessor: 裁切完成");
    println!("   成功: {} 个", cropped_images.len());
    println!("   失败: {} 个", error_count);
    println!("   总耗时: {} ms", processing_time);
    println!("   平均耗时: {:.2} ms/图", processing_time as f64 / cropped_images.len() as f64);

    Ok(CropImageResponse {
        success: error_count == 0,
        image_id: request.image_id,
        total_cropped: cropped_images.len(),
        cropped_images,
        processing_time_ms: processing_time,
        error: if error_count > 0 {
            Some(format!("部分裁切失败: {} 个区域", error_count))
        } else {
            None
        },
    })
}

/// 从splitSettings生成cropAreas
fn generate_crop_areas_from_settings(
    settings: &SplitSettings,
    image_width: u32,
    image_height: u32,
) -> Vec<CropArea> {
    let mut areas = Vec::new();

    println!("🎯 ImageProcessor: 生成网格裁切区域");
    println!("   单元格尺寸: {}×{}", settings.cell_width, settings.cell_height);
    println!("   外边距: {}", settings.padding);
    println!("   间距: {}", settings.spacing);

    // 计算可用区域
    let available_width = image_width.saturating_sub(settings.padding * 2);
    let available_height = image_height.saturating_sub(settings.padding * 2);

    // 计算单元格步长（单元格尺寸 + 间距）
    let cell_step_width = settings.cell_width + settings.spacing;
    let cell_step_height = settings.cell_height + settings.spacing;

    // 计算行列数
    let cols = if cell_step_width > 0 { available_width / cell_step_width } else { 0 };
    let rows = if cell_step_height > 0 { available_height / cell_step_height } else { 0 };

    println!("   网格尺寸: {}×{} ({}个单元格)", cols, rows, cols * rows);

    // 生成所有网格单元格
    for row in 0..rows {
        for col in 0..cols {
            let x = settings.padding + col * cell_step_width;
            let y = settings.padding + row * cell_step_height;

            // 确保不超出图片边界
            if x + settings.cell_width <= image_width && y + settings.cell_height <= image_height {
                areas.push(CropArea {
                    id: format!("cell_{}_{}", row, col),
                    x,
                    y,
                    width: settings.cell_width,
                    height: settings.cell_height,
                    name: format!("{}_{}_{}_{}", col, row, settings.cell_width, settings.cell_height),
                });
            }
        }
    }

    println!("✅ ImageProcessor: 生成了 {} 个裁切区域", areas.len());
    areas
}

/// 🎯 批量处理图片 - 新增的主要功能
#[tauri::command]
pub async fn batch_process_images(
    images: Vec<ImageInput>,
    options: ProcessOptions,
    window: tauri::Window,
) -> Result<BatchProcessResponse, String> {
    let start_time = std::time::Instant::now();

    println!("🚀 ImageProcessor: 开始批量处理图片");
    println!("   图片数量: {}", images.len());
    println!("   选项: {:?}", options);

    // 并行处理所有图片
    let results: Vec<ProcessedImageResult> = images
        .par_iter()
        .enumerate()
        .map(|(index, image_input)| {
            // 生成唯一ID
            let unique_id = format!("img_{}_{}",
                chrono::Utc::now().timestamp_millis(),
                index
            );

            match process_single_image(image_input, &options, &unique_id) {
                Ok(processed_data) => {
                    // 发送进度更新（每10个或最后一个）
                    if (index + 1) % 10 == 0 || index + 1 == images.len() {
                        let progress = (index + 1) as f32 / images.len() as f32 * 100.0;
                        let _ = window.emit("batch_process_progress", serde_json::json!({
                            "completed": index + 1,
                            "total": images.len(),
                            "percentage": progress,
                            "currentImage": image_input.file_name
                        }));
                    }

                    ProcessedImageResult {
                        id: image_input.id.clone(),
                        unique_id,
                        success: true,
                        file_name: image_input.file_name.clone(),
                        original_type: image_input.original_type.clone(),
                        processed_data: Some(processed_data),
                        error: None,
                    }
                },
                Err(e) => {
                    println!("❌ ImageProcessor: 处理图片失败: {} - {}", image_input.file_name, e);
                    ProcessedImageResult {
                        id: image_input.id.clone(),
                        unique_id,
                        success: false,
                        file_name: image_input.file_name.clone(),
                        original_type: image_input.original_type.clone(),
                        processed_data: None,
                        error: Some(e),
                    }
                }
            }
        })
        .collect();

    let processing_time = start_time.elapsed().as_millis() as u64;
    let successful_count = results.iter().filter(|r| r.success).count();
    let failed_count = results.len() - successful_count;

    println!("✅ ImageProcessor: 批量处理完成");
    println!("   成功: {} 个", successful_count);
    println!("   失败: {} 个", failed_count);
    println!("   总耗时: {} ms", processing_time);
    println!("   平均耗时: {:.2} ms/图", processing_time as f64 / images.len() as f64);

    Ok(BatchProcessResponse {
        success: failed_count == 0,
        results,
        total_processed: successful_count,
        processing_time_ms: processing_time,
        error: if failed_count > 0 {
            Some(format!("部分图片处理失败: {} 个", failed_count))
        } else {
            None
        },
    })
}

/// 🎯 处理单个图片
fn process_single_image(
    image_input: &ImageInput,
    options: &ProcessOptions,
    _unique_id: &str,
) -> Result<ProcessedImageData, String> {
    // 1. 解码图片
    let img = image::load_from_memory(&image_input.buffer)
        .map_err(|e| format!("Failed to decode image {}: {}", image_input.file_name, e))?;

    let original_width = img.width();
    let original_height = img.height();

    println!("🔄 ImageProcessor: 处理图片 {} ({}×{})",
        image_input.file_name, original_width, original_height);

    // 2. 确定输出格式和质量
    let output_format = options.format.as_deref().unwrap_or("webp");
    let quality = options.quality.unwrap_or(0.9);

    let image_format = match output_format {
        "webp" => ImageFormat::WebP,
        "jpeg" | "jpg" => ImageFormat::Jpeg,
        _ => ImageFormat::Png,
    };

    // 3. 生成原图DataURL
    let original_data = encode_image_to_bytes(&img, image_format, quality)?;
    let original_data_url = generate_data_url(&original_data, output_format);

    let mut processed_data = ProcessedImageData {
        original: Some(ImageData {
            width: original_width,
            height: original_height,
            data_url: original_data_url,
            size: None,
        }),
        thumbnail: None,
        preview: None,
        processed_at: chrono::Utc::now().to_rfc3339(),
    };

    // 4. 生成缩略图
    if options.generate_thumbnail.unwrap_or(true) {
        let thumbnail_size = options.thumbnail_size.unwrap_or(128);
        if let Ok(thumbnail_data) = generate_resized_image(&img, thumbnail_size, image_format, quality) {
            let thumbnail_data_url = generate_data_url(&thumbnail_data.data, output_format);
            processed_data.thumbnail = Some(ImageData {
                width: thumbnail_data.width,
                height: thumbnail_data.height,
                data_url: thumbnail_data_url,
                size: Some(thumbnail_size),
            });
        }
    }

    // 5. 生成预览图
    if options.generate_preview.unwrap_or(true) {
        let preview_size = options.preview_size.unwrap_or(256);
        if let Ok(preview_data) = generate_resized_image(&img, preview_size, image_format, quality) {
            let preview_data_url = generate_data_url(&preview_data.data, output_format);
            processed_data.preview = Some(ImageData {
                width: preview_data.width,
                height: preview_data.height,
                data_url: preview_data_url,
                size: Some(preview_size),
            });
        }
    }

    Ok(processed_data)
}

/// 🎯 生成调整尺寸的图片
struct ResizedImageData {
    data: Vec<u8>,
    width: u32,
    height: u32,
}

fn generate_resized_image(
    img: &image::DynamicImage,
    max_size: u32,
    format: ImageFormat,
    quality: f32,
) -> Result<ResizedImageData, String> {
    let (original_width, original_height) = (img.width(), img.height());

    // 计算新尺寸，保持宽高比
    let (new_width, new_height) = if original_width > original_height {
        let ratio = max_size as f32 / original_width as f32;
        (max_size, (original_height as f32 * ratio) as u32)
    } else {
        let ratio = max_size as f32 / original_height as f32;
        ((original_width as f32 * ratio) as u32, max_size)
    };

    // 调整图片尺寸
    let resized = img.resize(new_width, new_height, image::imageops::FilterType::Lanczos3);

    // 编码为字节
    let data = encode_image_to_bytes(&resized, format, quality)?;

    Ok(ResizedImageData {
        data,
        width: new_width,
        height: new_height,
    })
}

/// 🎯 将图片编码为字节数组
fn encode_image_to_bytes(
    img: &image::DynamicImage,
    format: ImageFormat,
    _quality: f32,
) -> Result<Vec<u8>, String> {
    let mut buffer = Vec::new();
    img.write_to(&mut Cursor::new(&mut buffer), format)
        .map_err(|e| format!("Failed to encode image: {}", e))?;
    Ok(buffer)
}

/// 🎯 生成DataURL
fn generate_data_url(image_data: &[u8], format: &str) -> String {
    use base64::{Engine as _, engine::general_purpose};

    let mime_type = match format {
        "webp" => "image/webp",
        "jpeg" | "jpg" => "image/jpeg",
        _ => "image/png",
    };

    let base64_data = general_purpose::STANDARD.encode(image_data);
    format!("data:{};base64,{}", mime_type, base64_data)
}
