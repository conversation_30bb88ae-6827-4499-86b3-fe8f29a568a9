/**
 * plist导出器 - 导出游戏引擎兼容的plist格式
 */

import { save, open } from '@tauri-apps/plugin-dialog';
import { invoke } from '@tauri-apps/api/core';
import { BaseExporter, sanitizeFileName, ensureExtension, createImageFromBuffer, canvasToBlob, getMimeType } from '../exportUtils';
import type { ExportItem, ExportConfig, ExportResult, PlistData, PlistFrame } from '../exportTypes';

export class PlistExporter extends BaseExporter {
  async export(items: ExportItem[], config: ExportConfig): Promise<ExportResult> {
    const startTime = Date.now();
    const exportedFiles: ExportResult['files'] = [];
    let totalSize = 0;

    this.reportProgress(0, 0, '', 'preparing');

    // 计算总任务数
    const totalTasks = items.length;

    if (totalTasks === 0) {
      throw new Error('没有可导出的内容');
    }

    // 让用户选择导出文件夹
    let selectedFolder = config.outputPath;

    if (!selectedFolder) {
      const selectedPath = await open({
        title: '选择导出文件夹',
        directory: true,
        multiple: false,
        defaultPath: await (async () => {
          try {
            const { documentDir } = await import('@tauri-apps/api/path');
            return await documentDir();
          } catch {
            return undefined;
          }
        })()
      });

      if (!selectedPath || typeof selectedPath !== 'string') {
        throw new Error('用户取消了导出');
      }

      selectedFolder = selectedPath;
      console.log('📁 PlistExporter: 选择的导出文件夹', { selectedFolder });
    }

    // 处理每个导出项
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      this.reportProgress(i + 1, totalTasks, `${item.name}.plist`, 'processing');

      if (!item.cropAreas || item.cropAreas.length === 0) {
        console.warn(`跳过没有裁切数据的资源: ${item.name}`);
        continue;
      }

      // 导出原始图片（如果需要）
      if (config.includeOriginal && item.resource.buffer) {
        try {
          const mimeType = getMimeType(item.resource.path);
          const originalImg = await createImageFromBuffer(item.resource.buffer, mimeType);

          // 创建原图blob
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (!ctx) throw new Error('无法创建Canvas上下文');

          canvas.width = originalImg.width;
          canvas.height = originalImg.height;
          ctx.drawImage(originalImg, 0, 0);

          const originalBlob = await canvasToBlob(canvas, 'image/png');
          const arrayBuffer = await originalBlob.arrayBuffer();
          const uint8Array = new Uint8Array(arrayBuffer);

          // 生成原图文件名
          const originalFileName = this.generateImageFileName(item.name);

          console.log('🚀 PlistExporter: 导出原始图片', {
            targetDirectory: selectedFolder,
            fileName: originalFileName
          });

          const originalPath = await invoke<string>('export_single_file', {
            filePath: `${selectedFolder}/${originalFileName}`,
            fileData: Array.from(uint8Array)
          });

          console.log('✅ PlistExporter: 原始图片导出成功', { originalPath });

          exportedFiles.push({
            name: originalFileName,
            path: originalPath,
            size: originalBlob.size,
            type: 'image'
          });
          totalSize += originalBlob.size;

        } catch (error) {
          console.error('❌ PlistExporter: 原始图片导出失败', error);
          // 不中断plist导出，只是警告
        }
      }

      // 生成plist数据
      const plistData = this.generatePlistData(item);

      // 生成XML格式的plist内容
      const fileContent = this.generateXmlPlist(plistData);

      // 调试输出
      console.log('🔍 PlistExporter: 生成的plist内容预览:', {
        itemName: item.name,
        frameCount: Object.keys(plistData.frames).length,
        frameNames: Object.keys(plistData.frames),
        metadata: plistData.metadata,
        contentPreview: fileContent.substring(0, 800)
      });

      // 完整内容输出用于调试
      console.log('📄 PlistExporter: 完整plist内容:\n', fileContent);

      // 生成文件名
      const fileName = this.generateFileName(item.name, config);

      // 使用Rust后端保存文件
      try {
        const encoder = new TextEncoder();
        const fileData = encoder.encode(fileContent);

        console.log('🚀 PlistExporter: 调用Rust后端导出', {
          targetDirectory: selectedFolder,
          fileName: fileName
        });

        const exportedPath = await invoke<string>('export_single_file', {
          filePath: `${selectedFolder}/${fileName}`,
          fileData: Array.from(fileData)
        });

        console.log('✅ PlistExporter: Rust后端导出成功', { exportedPath });

        const fileSize = fileData.length;
        exportedFiles.push({
          name: fileName,
          path: exportedPath,
          size: fileSize,
          type: 'data'
        });
        totalSize += fileSize;

      } catch (error) {
        console.error('❌ PlistExporter: Rust后端导出失败', error);
        throw new Error(`导出plist文件失败: ${error}`);
      }
    }

    this.reportProgress(totalTasks, totalTasks, '', 'completed');

    return {
      success: true,
      files: exportedFiles,
      totalSize,
      duration: Date.now() - startTime
    };
  }

  private generatePlistData(item: ExportItem): PlistData {
    const frames: Record<string, PlistFrame> = {};

    // 为每个裁切区域生成帧数据
    item.cropAreas?.forEach((area, index) => {
      // 使用.png扩展名的命名规则，与TexturePacker一致
      let frameName: string;
      if (area.name) {
        // 如果area.name已经有扩展名，直接使用；否则添加.png
        frameName = area.name.includes('.') ? area.name : `${area.name}.png`;
      } else {
        frameName = `${item.name.replace(/\.[^/.]+$/, '')}-${index}.png`;
      }

      frames[frameName] = {
        aliases: [],
        spriteOffset: "{0,0}",
        spriteSize: `{${area.width},${area.height}}`,
        spriteSourceSize: `{${area.width},${area.height}}`,
        textureRect: `{{${area.x},${area.y}},{${area.width},${area.height}}}`,
        textureRotated: false
      };
    });

    const imageFileName = this.generateImageFileName(item.name);

    return {
      frames,
      metadata: {
        format: 3,
        textureFileName: imageFileName,
        size: `{${item.resource.width || 0},${item.resource.height || 0}}`,

        premultiplyAlpha: false
      }
    };
  }



  private generateXmlPlist(data: PlistData): string {
    // 使用真实TexturePacker格式
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
${this.generateFramesXml(data.frames)}
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>${data.metadata.format}</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <${data.metadata.premultiplyAlpha ? 'true' : 'false'}/>
            <key>realTextureFileName</key>
            <string>${data.metadata.textureFileName}</string>
            <key>size</key>
            <string>${data.metadata.size}</string>

            <key>textureFileName</key>
            <string>${data.metadata.textureFileName}</string>
        </dict>
    </dict>
</plist>`;
    return xml;
  }

  private generateFramesXml(frames: Record<string, PlistFrame>): string {
    let xml = '';
    for (const [frameName, frameData] of Object.entries(frames)) {
      xml += `            <key>${frameName}</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>${frameData.spriteOffset}</string>
                <key>spriteSize</key>
                <string>${frameData.spriteSize}</string>
                <key>spriteSourceSize</key>
                <string>${frameData.spriteSourceSize}</string>
                <key>textureRect</key>
                <string>${frameData.textureRect}</string>
                <key>textureRotated</key>
                <${frameData.textureRotated ? 'true' : 'false'}/>
            </dict>
`;
    }
    return xml;
  }

  private generateFileName(baseName: string, config: ExportConfig): string {
    let fileName: string;

    // 🎯 如果用户设置了文件名，完整使用设置的文件名
    if (config.fileName && config.fileName.trim() !== '') {
      fileName = config.fileName.replace(/\.[^/.]+$/, ''); // 移除扩展名
    } else {
      fileName = baseName.replace(/\.[^/.]+$/, '');
    }

    return ensureExtension(sanitizeFileName(fileName), 'plist');
  }

  private generateImageFileName(baseName: string): string {
    const nameWithoutExt = baseName.replace(/\.[^/.]+$/, '');
    return ensureExtension(sanitizeFileName(nameWithoutExt), 'png');
  }




}
