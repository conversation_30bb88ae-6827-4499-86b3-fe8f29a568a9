/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { FileAsset } from "./FileAsset";

export class ImageAsset extends FileAsset {
  protected gifAssetUUID: string | null;

  /**
   * Valid MIME types for image assets.
   * @returns {string[]} Array of strings representing MIME types in the form image/filetype.
   */
  static getValidMIMETypes(): string[] {
    const jpgTypes = ["image/jpeg"];
    const pngTypes = ["image/png"];
    return jpgTypes.concat(pngTypes);
  }

  /**
   * Valid extensions for image assets.
   * @returns {string[]} Array of strings representing extensions.
   */
  static getValidExtensions(): string[] {
    return [".jpeg", ".jpg", ".png"];
  }

  /**
   * Create a new ImageAsset.
   * @param {FileAssetArgs} args - Asset constructor arguments
   */
  constructor(args: FileAssetArgs = {}) {
    super(args);
    this.gifAssetUUID = null;
  }

  protected _serialize(args?: SerializeArgs): Record<string, any> {
    const data = super._serialize(args);
    data.gifAssetUUID = this.gifAssetUUID;
    return data;
  }

  protected _deserialize(data: Record<string, any>): void {
    super._deserialize(data);
    this.gifAssetUUID = data.gifAssetUUID;
  }

  public get classname(): string {
    return "ImageAsset";
  }

  /**
   * A list of Wick Paths that use this image as their image source.
   * @returns {Wick.Path[]}
   */
  public getInstances(): Wick.Path[] {
    const paths: Wick.Path[] = [];
    this.project.getAllFrames().forEach((frame) => {
      frame.paths.forEach((path) => {
        if (path.getLinkedAssets().indexOf(this) !== -1) {
          paths.push(path);
        }
      });
    });
    return paths;
  }

  /**
   * Check if there are any objects in the project that use this asset.
   * @returns {boolean}
   */
  public hasInstances(): boolean {
    return this.getInstances().length > 0;
  }

  /**
   * Removes all paths using this asset as their image source from the project.
   */
  public removeAllInstances(): void {
    this.getInstances().forEach((path) => {
      path.remove();
    });
  }

  /**
   * Load data in the asset
   * @param {function} callback - function to call when the data is done being loaded.
   */
  public load(callback: () => void): void {
    // Try to get paper.js to cache the image src.
    const img = new Image();
    img.src = this.src || "";
    img.onload = () => {
      const raster = new paper.Raster(img);
      raster.remove();
      callback();
    };

    img.onerror = () => {
      this.project.errorOccured(
        "Error loading image " +
          this.filename +
          ". Check that this is loaded properly."
      );
      callback();
    };
  }

  /**
   * Creates a new Wick Path that uses this asset's image data as it's image source.
   * @param {function} callback - called when the path is done loading.
   */
  public createInstance(callback: (path: Wick.Path) => void): void {
    Wick.Path.createImagePath(this, (path) => {
      callback(path);
    });
  }

  /**
   * Is this image asset part of a GIF? (if this is set to true, this asset won't appear in the asset library GUI)
   * @type {boolean}
   */
  public get isGifImage(): boolean {
    return !!this.gifAssetUUID;
  }
}
