/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { PopupMenu } from "./PopupMenu";

type DocumentEvent = {
  event: string;
  fn: (e: Event) => void;
};

type CanvasEvent = {
  event: string;
  fn: (e: Event) => void;
};

export class Project extends GUIElement {
  protected _canvas: HTMLCanvasElement;
  protected _ctx: CanvasRenderingContext2D;
  protected _canvasContainer: HTMLDivElement;
  protected _drawnElements: any[];
  protected _mouse: { x: number; y: number };
  protected _mouseHoverTargets: any[];
  protected _scrollX: number;
  protected _scrollY: number;
  protected _popupMenu: PopupMenu | null;
  protected _onProjectModified: () => void;
  protected _onProjectSoftModified: () => void;
  protected _attachedDocumentEvents: DocumentEvent[];
  protected _attachedCanvasEvents: CanvasEvent[];

  constructor(model: Base) {
    super(model);

    this._canvas = document.createElement("canvas");
    this._ctx = this._canvas.getContext("2d") as CanvasRenderingContext2D;

    this._canvasContainer = document.createElement("div");
    this._canvasContainer.style.width = "100%";
    this._canvasContainer.style.height = "100%";
    this._canvasContainer.appendChild(this._canvas);

    this._drawnElements = [];

    this._mouse = { x: 0, y: 0 };
    this._mouseHoverTargets = [];

    this._scrollX = 0;
    this._scrollY = 0;

    this._popupMenu = null;

    this._onProjectModified = () => {};
    this._onProjectSoftModified = () => {};

    this._attachedDocumentEvents = [];
    this._attachedCanvasEvents = [];
  }

  createDocumentEvent(
    event: string,
    callback: (e: Event) => void,
    c?: boolean
  ): void {
    document.addEventListener(event, callback, c);

    this._attachedDocumentEvents.push({
      event,
      fn: callback,
    });
  }

  createCanvasEvent(
    event: string,
    callback: (e: Event) => void,
    c?: boolean
  ): void {
    this._canvas.addEventListener(event, callback, c);

    this._attachedCanvasEvents.push({
      event,
      fn: callback,
    });
  }

  removeAllEventListeners(): void {
    this._attachedDocumentEvents.forEach((evt) => {
      document.removeEventListener(evt.event, evt.fn);
    });

    this._attachedCanvasEvents.forEach((evt) => {
      this._canvas.removeEventListener(evt.event, evt.fn);
    });
  }

  get canvasContainer(): HTMLDivElement {
    return this._canvasContainer;
  }

  get canvas(): HTMLCanvasElement {
    return this._canvas;
  }

  get ctx(): CanvasRenderingContext2D {
    return this._ctx;
  }

  get mouse(): { x: number; y: number } {
    return this._mouse;
  }

  set mouse(pos: { x: number; y: number }) {
    this._mouse = pos;
  }

  get mouseHoverTargets(): any[] {
    return this._mouseHoverTargets;
  }

  set mouseHoverTargets(targets: any[]) {
    this._mouseHoverTargets = targets;
  }

  get scrollX(): number {
    return this._scrollX;
  }

  set scrollX(x: number) {
    this._scrollX = x;
  }

  get scrollY(): number {
    return this._scrollY;
  }

  set scrollY(y: number) {
    this._scrollY = y;
  }

  get popupMenu(): PopupMenu | null {
    return this._popupMenu;
  }

  set popupMenu(menu: PopupMenu | null) {
    this._popupMenu = menu;
  }

  get onProjectModified(): () => void {
    return this._onProjectModified;
  }

  set onProjectModified(fn: () => void) {
    this._onProjectModified = fn;
  }

  get onProjectSoftModified(): () => void {
    return this._onProjectSoftModified;
  }

  set onProjectSoftModified(fn: () => void) {
    this._onProjectSoftModified = fn;
  }
}
