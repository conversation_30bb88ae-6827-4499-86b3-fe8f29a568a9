<script lang="ts">
  /**
   * 主导出对话框组件
   */

  import { onMount } from 'svelte';
  import ExportPreview from './ExportPreview.svelte';
  import ExportOptions from './ExportOptions.svelte';
  import { registerExporters, exportManager } from './formats';
  import type { ExportItem, ExportUIState, ExportProgress, ExportResult, ExportConfig } from './exportTypes';

  interface Props {
    visible: boolean;
    items: ExportItem[];
    defaultType?: 'cropped-images' | 'plist';
    onClose: () => void;
    onExport?: (result: ExportResult) => void;
  }

  let {
    visible,
    items,
    defaultType = 'cropped-images',
    onClose,
    onExport
  }: Props = $props();

  // 状态管理
  let dialogElement = $state<HTMLElement>();
  let isExporting = $state(false);
  let exportProgress = $state<ExportProgress>();
  let selectedItemIndex = $state(0);

  // 导出选项状态
  let exportState = $state<ExportUIState>({
    selectedType: defaultType,
    outputPath: '',
    fileName: '',
    format: 'png',
    quality: 0.9,
    includeOriginal: false,
    namePrefix: '',
    nameSuffix: ''
  });

  // 组件挂载时注册导出器
  onMount(() => {
    registerExporters();
  });

  // 监听visible变化，重置状态
  $effect(() => {
    if (visible) {
      resetState();
    }
  });

  // 重置状态
  function resetState() {
    isExporting = false;
    exportProgress = undefined;
    selectedItemIndex = 0;
    exportState = {
      selectedType: defaultType,
      outputPath: '',
      fileName: '',
      format: 'png',
      quality: 0.9,
      includeOriginal: false,
      namePrefix: '',
      nameSuffix: ''
    };
  }

  // 更新导出状态
  function handleStateChange(updates: Partial<ExportUIState>) {
    exportState = { ...exportState, ...updates };
  }

  // 选择导出文件夹
  async function selectExportFolder(): Promise<string | null> {
    try {
      // 使用Tauri的前端对话框API
      const { open } = await import('@tauri-apps/plugin-dialog');
      const result = await open({
        directory: true,
        multiple: false,
        title: '选择导出文件夹',
        defaultPath: await (async () => {
          try {
            const { documentDir } = await import('@tauri-apps/api/path');
            return await documentDir();
          } catch {
            return undefined;
          }
        })()
      });

      if (result && typeof result === 'string') {
        return result;
      } else {
        return null;
      }
    } catch (error) {
      console.error('文件夹选择失败:', error);
      return null;
    }
  }

  // 开始导出
  async function handleExport() {
    if (isExporting || items.length === 0) return;

    try {
      isExporting = true;
      exportProgress = {
        current: 0,
        total: 0,
        currentFile: '',
        stage: 'preparing'
      };

      // 选择导出文件夹
      const selectedFolder = await selectExportFolder();
      if (!selectedFolder) {
        console.log('用户取消了文件夹选择');
        return;
      }

      // 构建导出配置
      const config: ExportConfig = {
        type: exportState.selectedType,
        outputPath: selectedFolder,
        fileName: exportState.fileName,
        format: exportState.format,
        quality: exportState.quality,
        includeOriginal: exportState.includeOriginal,
        namePrefix: exportState.namePrefix,
        nameSuffix: exportState.nameSuffix
      };

      console.log('🚀 开始导出:', { items, config, selectedFolder });

      // 执行导出
      const result = await exportManager.export(
        exportState.selectedType,
        items,
        config,
        selectedFolder,
        (progress: ExportProgress) => {
          exportProgress = progress;
        }
      );

      console.log('✅ 导出完成:', result);

      // 通知父组件
      if (onExport) {
        onExport(result);
      }

      // 显示结果
      if (result.success) {
        alert(`导出成功！\n共导出 ${result.files.length} 个文件\n总大小: ${formatFileSize(result.totalSize)}\n耗时: ${result.duration}ms`);
        onClose();
      } else {
        alert(`导出失败: ${result.error}`);
      }

    } catch (error) {
      console.error('❌ 导出失败:', error);
      alert(`导出失败: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      isExporting = false;
      exportProgress = undefined;
    }
  }

  // 取消导出
  function handleCancel() {
    if (isExporting) {
      // TODO: 实现导出取消逻辑
      return;
    }
    onClose();
  }

  // 格式化文件大小
  function formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 处理ESC键关闭
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape' && !isExporting) {
      onClose();
    }
  }

  // 处理背景点击关闭
  function handleBackdropClick(event: MouseEvent) {
    if (event.target === dialogElement && !isExporting) {
      onClose();
    }
  }
</script>

{#if visible}
  <!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
  <div
    class="export-dialog-backdrop"
    bind:this={dialogElement}
    onclick={handleBackdropClick}
    onkeydown={handleKeydown}
    role="dialog"
    aria-modal="true"
    aria-labelledby="export-dialog-title"
    tabindex="-1"
  >
    <div class="export-dialog">
      <div class="dialog-header">
        <h3 id="export-dialog-title">导出资源</h3>
        <button
          class="close-btn"
          onclick={handleCancel}
          disabled={isExporting}
          aria-label="关闭对话框"
        >
          ✕
        </button>
      </div>

      <div class="dialog-content">
        <!-- 左侧预览 -->
        <div class="preview-panel">
          <ExportPreview
            {items}
            bind:selectedItemIndex
          />
        </div>

        <!-- 右侧选项 -->
        <div class="options-panel">
          <ExportOptions
            state={exportState}
            progress={exportProgress}
            {isExporting}
            onStateChange={handleStateChange}
            onExport={handleExport}
            onCancel={handleCancel}
          />
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .export-dialog-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .export-dialog {
    width: 90vw;
    max-width: 1200px;
    height: 80vh;
    max-height: 800px;
    background: var(--theme-surface);
    border-radius: var(--border-radius-large);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    animation: dialogSlideIn 0.3s ease-out;
  }

  @keyframes dialogSlideIn {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  .dialog-header {
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--theme-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--theme-surface-light);
  }

  .dialog-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--theme-text);
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--theme-text-secondary);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--border-radius);
    transition: var(--transition-base);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn:hover:not(:disabled) {
    background: var(--theme-surface);
    color: var(--theme-text);
  }

  .close-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .dialog-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .preview-panel {
    flex: 1;
    min-width: 0;
    padding: var(--spacing-4);
  }

  .options-panel {
    width: 350px;
    flex-shrink: 0;
    padding: var(--spacing-4);
    border-left: 1px solid var(--theme-border);
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .export-dialog {
      width: 95vw;
      height: 90vh;
    }

    .dialog-content {
      flex-direction: column;
    }

    .preview-panel {
      flex: 1;
      min-height: 300px;
    }

    .options-panel {
      width: auto;
      flex-shrink: 0;
      border-left: none;
      border-top: 1px solid var(--theme-border);
      max-height: 400px;
      overflow-y: auto;
    }
  }
</style>
