/**
 * 项目保存管理器
 * 负责收集、转换和保存项目数据
 */

import { invoke } from '@tauri-apps/api/core';
import { atlasStore } from '../stores/atlasStore';
import { resourceStore } from '../stores/resourceStore';
import { exportSettingsStore, exportSettingsActions } from '../stores/exportSettingsStore';
import { TauriAPI } from '../lib/tauriAPI';

// 保存结果接口
export interface SaveResult {
  success: boolean;
  file_path?: string;
  file_size?: number;
  error?: string;
}

// 加载结果接口
export interface LoadResult {
  success: boolean;
  data?: ProjectSaveData;
  error?: string;
  warnings?: string[];
}

// 项目保存数据接口
export interface ProjectSaveData {
  metadata: {
    version: string;
    project_name: string;
    created_at: string;
    updated_at: string;
    created_by?: string;
    description?: string;
    tags?: string[];
  };
  atlas_data: any;
  resource_data: any;
  export_settings: any;
  checksum: string;
}

/**
 * 项目保存管理器类
 */
export class ProjectSaveManager {
  private static instance: ProjectSaveManager;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): ProjectSaveManager {
    if (!ProjectSaveManager.instance) {
      ProjectSaveManager.instance = new ProjectSaveManager();
    }
    return ProjectSaveManager.instance;
  }

  /**
   * 收集当前项目数据
   */
  private async collectProjectData(): Promise<{
    atlasData: any;
    resourceData: any;
    exportSettings: any;
  }> {
    console.log('🔄 ProjectSave: 开始收集项目数据');

    // 收集图集数据
    const atlasData = atlasStore.exportAtlasData();
    console.log('📊 ProjectSave: 图集数据收集完成', {
      atlasCount: atlasData.atlases?.length || 0
    });

    // 收集资源数据
    const resourceData = await this.collectResourceData();
    console.log('📊 ProjectSave: 资源数据收集完成', {
      resourceCount: resourceData.resources?.length || 0
    });

    // 收集导出设置
    const exportSettings = await exportSettingsActions.getCurrentSettings();
    console.log('📊 ProjectSave: 导出设置收集完成');

    return {
      atlasData,
      resourceData,
      exportSettings
    };
  }

  /**
   * 收集资源数据
   */
  private async collectResourceData(): Promise<any> {
    return new Promise((resolve) => {
      let hasResolved = false; // 🎯 防止重复调用

      const unsubscribe = resourceStore.subscribe(state => {
        if (hasResolved) return; // 🎯 如果已经resolve过，直接返回

        // 🎯 使用新的单一数据结构：始终使用 rootResources
        const completeResources = state.rootResources || [];

        console.log('💾 ProjectSave: 收集资源数据', {
          currentFolder: state.currentFolder,
          rootResourcesCount: completeResources.length,
          navigationStack: state.navigationStack
        });

        // 🎯 递归序列化资源数据，将图片数据转换为 base64
        const serializedResources = this.serializeResourcesRecursively(completeResources);

        console.log('💾 ProjectSave: 收集资源数据完成', {
          currentFolder: state.currentFolder,
          rootResourcesCount: completeResources.length,
          serializedResourcesCount: serializedResources.length,
          navigationStackLength: state.navigationStack.length
        });

        hasResolved = true; // 🎯 标记已经resolve
        unsubscribe(); // 🎯 先取消订阅

        resolve({
          resources: serializedResources,
          // 🎯 保存导航状态（可选：如果需要恢复导航状态）
          currentFolder: null, // 始终重置为根目录
          navigationStack: [], // 始终重置导航栈
          exportedAt: new Date().toISOString()
        });
      });
    });
  }

  /**
   * 🎯 递归序列化资源数据
   */
  private serializeResourcesRecursively(resources: any[]): any[] {
    return resources.map(resource => {
      if (resource.type === 'image') {
        // 🎯 处理图片资源：无论是否有data都要处理
        let serializedResource = { ...resource };

        if ('data' in resource && resource.data) {
          // 有ArrayBuffer数据，转换为base64
          const base64Data = this.arrayBufferToBase64(resource.data);
          console.log('🔄 ProjectSave: 序列化图片资源（有data）', {
            name: resource.name,
            hasData: !!resource.data,
            base64Length: base64Data.length,
            hasProcessedData: !!resource.processedData
          });
          serializedResource = {
            ...serializedResource,
            dataBase64: base64Data,
            data: undefined, // 移除原始 ArrayBuffer
          };
        } else {
          console.log('🔄 ProjectSave: 序列化图片资源（无data）', {
            name: resource.name,
            hasDataBase64: !!resource.dataBase64,
            hasProcessedData: !!resource.processedData
          });
        }

        // 🎯 无论如何都要移除这些运行时数据
        return {
          ...serializedResource,
          blobUrl: undefined, // 移除 blob URL
          processedData: undefined // 🎯 移除processedData以节省空间
        };
      } else if (resource.type === 'folder' && resource.children && Array.isArray(resource.children)) {
        // 🎯 处理文件夹资源：递归序列化 children
        console.log('📁 ProjectSave: 序列化文件夹资源', {
          name: resource.name,
          childrenCount: resource.children.length,
          children: resource.children.map((c: any) => ({ name: c.name, type: c.type }))
        });
        return {
          ...resource,
          children: this.serializeResourcesRecursively(resource.children) // 🎯 递归处理
        };
      }
      return resource;
    });
  }

  /**
   * 将 ArrayBuffer 转换为 base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * 🎯 批量重新生成processedData
   */
  private async regenerateProcessedData(resources: any[]): Promise<void> {
    // 收集所有需要处理的图片资源
    const imageResources: any[] = [];

    const collectImages = (items: any[]) => {
      for (const item of items) {
        if (item.type === 'image' && item.data && !item.processedData) {
          console.log('🔍 ProjectSave: 找到需要重新生成processedData的图片', {
            name: item.name,
            hasData: !!item.data,
            hasProcessedData: !!item.processedData,
            dataSize: item.data?.byteLength || 0
          });
          imageResources.push(item);
        } else if (item.type === 'image') {
          console.log('🔍 ProjectSave: 跳过图片（不符合条件）', {
            name: item.name,
            hasData: !!item.data,
            hasProcessedData: !!item.processedData,
            reason: !item.data ? 'no data' : 'already has processedData'
          });
        } else if (item.type === 'folder' && item.children) {
          collectImages(item.children);
        }
      }
    };

    collectImages(resources);

    if (imageResources.length === 0) {
      console.log('📂 ProjectSave: 没有需要重新生成processedData的图片');
      return;
    }

    console.log('🔄 ProjectSave: 开始批量重新生成processedData', {
      imageCount: imageResources.length
    });

    try {
      // 准备批量处理的输入数据
      const imageInputs = imageResources.map(resource => ({
        id: resource.id,
        buffer: resource.data,
        originalType: resource.originalFile?.type || 'image/png',
        fileName: resource.name
      }));

      // 调用后端批量处理
      const result = await TauriAPI.Project.batchProcessImages(imageInputs, {
        generateThumbnail: true,
        thumbnailSize: 128,
        generatePreview: true,
        previewSize: 256,
        quality: 0.8,
        format: 'webp'
      });

      if (!result.success || !result.data) {
        throw new Error(result.error || '批量处理失败');
      }

      const batchResponse = result.data;

      // 将处理结果映射回原始资源
      for (const processedResult of batchResponse.results) {
        const originalResource = imageResources.find(r => r.id === processedResult.id);
        if (originalResource && processedResult.success && processedResult.processedData) {
          originalResource.processedData = {
            original: processedResult.processedData.original,
            thumbnail: processedResult.processedData.thumbnail,
            preview: processedResult.processedData.preview,
            processedAt: processedResult.processedData.processedAt
          };
        }
      }

      console.log('✅ ProjectSave: processedData重新生成完成', {
        totalImages: imageResources.length,
        successCount: batchResponse.results.filter((r: any) => r.success).length,
        processingTime: batchResponse.processingTimeMs
      });

    } catch (error) {
      console.error('❌ ProjectSave: 重新生成processedData失败', error);
      // 不抛出错误，允许项目继续加载
    }
  }

  /**
   * 将 base64 转换为 ArrayBuffer
   */
  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binaryString = atob(base64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes.buffer;
  }

  /**
   * 🎯 递归反序列化资源数据
   */
  private deserializeResourcesRecursively(resources: any[]): any[] {
    return resources.map((resource: any) => {
      // 🎯 处理Date字段的反序列化
      const processedResource = this.deserializeDateFields(resource);

      if (resource.type === 'image' && resource.dataBase64) {
        // 处理图片资源：将 base64 转换回 ArrayBuffer
        const arrayBuffer = this.base64ToArrayBuffer(resource.dataBase64);
        console.log('🔄 ProjectSave: 反序列化图片资源', {
          name: resource.name,
          hasBase64: !!resource.dataBase64,
          bufferSize: arrayBuffer.byteLength,
          hasCropData: !!resource.cropData,
          cropDataLastModified: resource.cropData?.lastModified
        });
        return {
          ...processedResource,
          data: arrayBuffer,
          dataBase64: undefined, // 移除 base64 数据
          isLoaded: true,
          processedData: undefined // 🎯 暂时设为undefined，稍后批量重新生成
        };
      } else if (resource.type === 'folder' && resource.children && Array.isArray(resource.children)) {
        // 🎯 处理文件夹资源：递归反序列化 children
        console.log('📁 ProjectSave: 反序列化文件夹资源', {
          name: resource.name,
          childrenCount: resource.children.length,
          children: resource.children.map((c: any) => ({ name: c.name, type: c.type }))
        });
        return {
          ...processedResource,
          children: this.deserializeResourcesRecursively(resource.children) // 🎯 递归处理
        };
      }
      return processedResource;
    });
  }

  /**
   * 🎯 反序列化Date字段
   */
  private deserializeDateFields(resource: any): any {
    const result = { ...resource };

    // 处理基础Date字段
    if (result.createdAt && typeof result.createdAt === 'string') {
      result.createdAt = new Date(result.createdAt);
    }
    if (result.updatedAt && typeof result.updatedAt === 'string') {
      result.updatedAt = new Date(result.updatedAt);
    }
    if (result.lastModified && typeof result.lastModified === 'string') {
      result.lastModified = new Date(result.lastModified);
    }

    // 🎯 处理cropData中的Date字段
    if (result.cropData && typeof result.cropData === 'object') {
      if (result.cropData.lastModified && typeof result.cropData.lastModified === 'string') {
        result.cropData.lastModified = new Date(result.cropData.lastModified);
        console.log('🔄 ProjectSave: 转换cropData.lastModified', {
          resourceName: result.name,
          originalValue: resource.cropData.lastModified,
          convertedValue: result.cropData.lastModified
        });
      }
    }

    // 🎯 处理splitSettings中的Date字段（如果有）
    if (result.splitSettings && typeof result.splitSettings === 'object') {
      if (result.splitSettings.lastModified && typeof result.splitSettings.lastModified === 'string') {
        result.splitSettings.lastModified = new Date(result.splitSettings.lastModified);
      }
    }

    // 🎯 处理originalFile中的Date字段
    if (result.originalFile && typeof result.originalFile === 'object') {
      if (result.originalFile.lastModified && typeof result.originalFile.lastModified === 'number') {
        // originalFile.lastModified是时间戳，不需要转换
        // 保持原样
      }
    }

    return result;
  }

  /**
   * 保存项目到文件
   */
  public async save(
    filePath: string,
    projectName: string,
    description?: string,
    tags?: string[]
  ): Promise<SaveResult> {
    try {
      console.log('💾 ProjectSave: 开始保存项目', { filePath, projectName });

      // 收集项目数据
      const { atlasData, resourceData, exportSettings } = await this.collectProjectData();

      // 调用后端保存
      const result = await invoke<SaveResult>('save_project_file', {
        filePath,
        atlasData,
        resourceData,
        exportSettings,
        projectName,
        description,
        tags
      });

      if (result.success) {
        console.log('✅ ProjectSave: 项目保存成功', {
          filePath: result.file_path,
          fileSize: result.file_size
        });
      } else {
        console.error('❌ ProjectSave: 项目保存失败', result.error);
      }

      return result;
    } catch (error) {
      console.error('❌ ProjectSave: 保存过程中发生错误', error);
      return {
        success: false,
        error: `保存失败: ${error}`
      };
    }
  }

  /**
   * 从文件加载项目
   */
  public async load(filePath: string): Promise<LoadResult> {
    try {
      console.log('📂 ProjectSave: 开始加载项目', { filePath });

      // 调用后端加载
      const result = await invoke<LoadResult>('load_project_file', {
        filePath
      });

      if (result.success && result.data) {
        console.log('✅ ProjectSave: 项目加载成功');

        // 恢复数据到各个 store
        await this.restoreProjectData(result.data);

        // 输出警告信息（如果有）
        if (result.warnings && result.warnings.length > 0) {
          console.warn('⚠️ ProjectSave: 加载警告', result.warnings);
        }
      } else {
        console.error('❌ ProjectSave: 项目加载失败', result.error);
      }

      return result;
    } catch (error) {
      console.error('❌ ProjectSave: 加载过程中发生错误', error);
      return {
        success: false,
        error: `加载失败: ${error}`
      };
    }
  }

  /**
   * 恢复项目数据到各个 store
   */
  private async restoreProjectData(data: ProjectSaveData): Promise<void> {
    console.log('🔄 ProjectSave: 开始恢复项目数据');

    try {
      // 恢复图集数据
      if (data.atlas_data) {
        atlasStore.importAtlasData(data.atlas_data);
        console.log('✅ ProjectSave: 图集数据恢复完成');
      }

      // 恢复资源数据
      if (data.resource_data) {
        await this.restoreResourceData(data.resource_data);
        console.log('✅ ProjectSave: 资源数据恢复完成');
      }

      // 恢复导出设置
      if (data.export_settings) {
        exportSettingsActions.updateSettings(data.export_settings);
        console.log('✅ ProjectSave: 导出设置恢复完成');
      }

      console.log('✅ ProjectSave: 所有数据恢复完成');
    } catch (error) {
      console.error('❌ ProjectSave: 数据恢复过程中发生错误', error);
      throw error;
    }
  }

  /**
   * 恢复资源数据
   */
  private async restoreResourceData(data: any): Promise<void> {
    if (!data.resources || !Array.isArray(data.resources)) {
      console.log('📂 ProjectSave: 没有资源数据需要恢复');
      return;
    }

    // 🎯 递归将序列化的资源数据转换回运行时格式
    const restoredResources = this.deserializeResourcesRecursively(data.resources);

    console.log('📂 ProjectSave: 恢复资源数据', {
      restoredResourcesCount: restoredResources.length,
      resourceTypes: restoredResources.map(r => ({ name: r.name, type: r.type }))
    });

    // 🎯 批量重新生成processedData
    await this.regenerateProcessedData(restoredResources);

    // 🎯 使用新的 ResourceStore 接口：只更新 rootResources
    resourceStore.update(state => ({
      ...state,
      rootResources: restoredResources, // 🎯 新接口：使用 rootResources 作为单一数据源
      // 🎯 始终重置为默认状态，不保持文件夹导航状态
      currentFolder: null,
      navigationStack: [], // 🎯 清空导航栈
      selectedResource: null // 清除选中状态
    }));

    console.log('✅ ProjectSave: 资源数据恢复到 rootResources 完成');
  }

  /**
   * 验证项目文件
   */
  public async validate(filePath: string): Promise<boolean> {
    try {
      const result = await invoke<boolean>('validate_project_file', {
        filePath
      });
      return result;
    } catch (error) {
      console.error('❌ ProjectSave: 验证项目文件失败', error);
      return false;
    }
  }
}

// 导出单例实例
export const projectSaveManager = ProjectSaveManager.getInstance();
