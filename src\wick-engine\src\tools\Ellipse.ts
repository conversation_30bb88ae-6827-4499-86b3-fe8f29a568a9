/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Tool } from "./Tool";

export class Ellipse extends Tool {
  protected name: string;
  protected path: any;
  protected topLeft: any;
  protected bottomRight: any;

  /**
   * Creates an instance of the ellipse tool.
   */
  constructor() {
    super();

    this.name = "ellipse";
    this.path = null;
    this.topLeft = null;
    this.bottomRight = null;
  }

  get doubleClickEnabled(): boolean {
    return false;
  }

  /**
   * A crosshair cursor.
   */
  get cursor(): string {
    return "crosshair";
  }

  get isDrawingTool(): boolean {
    return true;
  }

  onActivate(e: any): void {}

  onDeactivate(e: any): void {
    if (this.path) {
      this.path.remove();
      this.path = null;
    }
  }

  onMouseDown(e: any): void {
    this.topLeft = e.point;
    this.bottomRight = e.point;
  }

  onMouseDrag(e: any): void {
    if (this.path) this.path.remove();

    this.bottomRight = e.point;

    // Lock width and height if shift is held down
    if (e.modifiers.shift) {
      const d = this.bottomRight.subtract(this.topLeft);
      const max = Math.max(Math.abs(d.x), Math.abs(d.y));
      this.bottomRight.x = this.topLeft.x + max * (d.x < 0 ? -1 : 1);
      this.bottomRight.y = this.topLeft.y + max * (d.y < 0 ? -1 : 1);
    }

    const bounds = new (this.paper as any).Rectangle(
      new (this.paper as any).Point(this.topLeft.x, this.topLeft.y),
      new (this.paper as any).Point(this.bottomRight.x, this.bottomRight.y)
    );
    this.path = new (this.paper as any).Path.Ellipse(bounds);
    this.paper.project.activeLayer.addChild(this.path);

    this.path.fillColor = this.getSetting("fillColor").rgba;
    this.path.strokeColor = this.getSetting("strokeColor").rgba;
    this.path.strokeWidth = this.getSetting("strokeWidth");
    this.path.strokeCap = "round";
  }

  onMouseUp(e: any): void {
    if (!this.path) return;

    this.path.remove();
    this.addPathToProject(this.path);
    this.path = null;

    this.fire("canvasModified", {}, "ellipse");
  }

  // Helper methods (to be implemented)
  protected getSetting(name: string): any {
    return {};
  }
  protected addPathToProject(path: any): void {}
}
