/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Tool } from "./Tool";

export class PathCursor extends Tool {
  protected name: string;
  protected path: any;
  protected cursorSize: number | null;
  protected cachedCursor: string | null;

  /**
   * Creates an instance of the path cursor tool.
   */
  constructor() {
    super();

    this.name = "pathcursor";
    this.path = null;
    this.cursorSize = null;
    this.cachedCursor = null;
  }

  get doubleClickEnabled(): boolean {
    return false;
  }

  /**
   * The cursor style for the path cursor tool.
   */
  get cursor(): string {
    return this.cachedCursor || "crosshair";
  }

  get isDrawingTool(): boolean {
    return false;
  }

  onActivate(e: any): void {
    this.cursorSize = null;
  }

  onDeactivate(e: any): void {
    if (this.path) {
      this.path.remove();
      this.path = null;
    }
  }

  onMouseMove(e: any): void {
    // Don't render cursor after every mouse move, cache and only render when size changes
    const cursorNeedsRegen = this.getSetting("strokeWidth") !== this.cursorSize;

    if (cursorNeedsRegen) {
      this.cachedCursor = this.createDynamicCursor(
        "#000000",
        this.getSetting("strokeWidth") + 1
      );
      this.cursorSize = this.getSetting("strokeWidth");
      this.setCursor(this.cachedCursor);
    }
  }

  onMouseDown(e: any): void {
    // Implementation specific to path cursor
  }

  onMouseDrag(e: any): void {
    // Implementation specific to path cursor
  }

  onMouseUp(e: any): void {
    // Implementation specific to path cursor
  }

  // Helper methods (to be implemented)
  protected getSetting(name: string): any {
    return {};
  }
  protected createDynamicCursor(color: string, size: number): string {
    return "";
  }
  protected setCursor(cursor: string): void {}
}
