/**
 * 🎯 项目保存功能统一导出 - 二进制版本
 * 使用二进制格式直接存储，极大提升性能
 */

import { binaryProjectSave } from './projectSaveBinary';
import type { UnifiedSaveResult, UnifiedLoadResult } from './projectSaveBinary';

/**
 * 🎯 保存项目 - 使用二进制格式
 * @param filePath 保存文件路径
 * @param projectName 项目名称
 * @param description 项目描述（可选）
 * @param tags 项目标签（可选）
 * @returns 保存结果
 */
export async function saveProject(
  filePath: string,
  projectName: string,
  description?: string,
  tags?: string[]
): Promise<UnifiedSaveResult> {
  return await binaryProjectSave.save(filePath, projectName, description, tags);
}

/**
 * 🎯 加载项目 - 使用二进制格式
 * @param filePath 项目文件路径
 * @returns 加载结果
 */
export async function loadProject(filePath: string): Promise<UnifiedLoadResult> {
  return await binaryProjectSave.load(filePath);
}

// 🎯 保持向后兼容的类型导出
export type SaveResult = UnifiedSaveResult;
export type LoadResult = UnifiedLoadResult;

/**
 * 验证项目文件
 * @param filePath 项目文件路径
 * @returns 是否有效
 */
export async function validateProject(filePath: string): Promise<boolean> {
  return await projectSaveManager.validate(filePath);
}

// 导出类型
export type { SaveResult, LoadResult } from './projectSave';
