/**
 * Toast 提示管理器
 * 统一管理应用中的所有提示消息
 */
import { writable } from 'svelte/store';
import type { ToastType } from '../components/Toast.svelte';

export interface ToastConfig {
  id: string;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;
  showCloseButton?: boolean;
}

interface ToastState {
  toasts: ToastConfig[];
}

function createToastStore() {
  const { subscribe, update } = writable<ToastState>({
    toasts: []
  });

  let toastIdCounter = 0;

  return {
    subscribe,
    
    // 显示成功提示
    success(message: string, title?: string, duration = 3000) {
      this.show('success', message, title, duration);
    },

    // 显示错误提示
    error(message: string, title?: string, duration = 5000) {
      this.show('error', message, title, duration);
    },

    // 显示警告提示
    warning(message: string, title?: string, duration = 4000) {
      this.show('warning', message, title, duration);
    },

    // 显示信息提示
    info(message: string, title?: string, duration = 3000) {
      this.show('info', message, title, duration);
    },

    // 显示提示（通用方法）
    show(type: ToastType, message: string, title?: string, duration = 3000) {
      const id = `toast-${++toastIdCounter}`;
      const toast: ToastConfig = {
        id,
        type,
        message,
        title,
        duration,
        showCloseButton: true
      };

      update(state => ({
        ...state,
        toasts: [...state.toasts, toast]
      }));

      // 如果设置了自动关闭时间，则自动移除
      if (duration > 0) {
        setTimeout(() => {
          this.remove(id);
        }, duration + 300); // 加上动画时间
      }

      return id;
    },

    // 移除指定提示
    remove(id: string) {
      update(state => ({
        ...state,
        toasts: state.toasts.filter(toast => toast.id !== id)
      }));
    },

    // 清除所有提示
    clear() {
      update(state => ({
        ...state,
        toasts: []
      }));
    }
  };
}

export const toastStore = createToastStore();

// 便捷的全局方法
export const toast = {
  success: (message: string, title?: string) => toastStore.success(message, title),
  error: (message: string, title?: string) => toastStore.error(message, title),
  warning: (message: string, title?: string) => toastStore.warning(message, title),
  info: (message: string, title?: string) => toastStore.info(message, title)
};
