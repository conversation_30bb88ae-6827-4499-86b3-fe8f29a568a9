/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

declare global {
  interface Window {
    Wick: any;
  }
}

interface ClipboardData {
  objects: any[];
  playheadOffset: number;
}

/**
 * A clipboard utility class for copy/paste functionality.
 */
export class Clipboard {
  private _copyLocation: string | null;
  private _copyLayerIndex: number;
  private _originalObjects: any[];

  static get LOCALSTORAGE_KEY(): string {
    return "wick_engine_clipboard";
  }

  static get PASTE_OFFSET(): number {
    // how many pixels should we shift objects over when we paste (canvas only)
    return 20;
  }

  /**
   * Create a new Clipboard object.
   */
  constructor() {
    this._copyLocation = null;
    this._copyLayerIndex = 0;
    this._originalObjects = [];
  }

  /**
   * The data of copied objects, stored as JSON.
   */
  get clipboardData(): ClipboardData | null {
    const json = localStorage[Clipboard.LOCALSTORAGE_KEY];
    if (!json) return null;
    return JSON.parse(json);
  }

  set clipboardData(clipboardData: ClipboardData | null) {
    if (clipboardData === null) {
      localStorage.removeItem(Clipboard.LOCALSTORAGE_KEY);
    } else {
      localStorage[Clipboard.LOCALSTORAGE_KEY] = JSON.stringify(clipboardData);
    }
  }

  /**
   * Replace the current contents of the clipboard with new objects.
   * @param project - The Wick project instance
   * @param objects - The objects to copy to the clipboard
   */
  copyObjectsToClipboard(project: any, objects: any[]): void {
    if (!project || !(project instanceof window.Wick.Project)) {
      console.error("copyObjectsToClipboard(): project is required");
      return;
    }

    // Get the playhead position of the "first" frame in the list of objects
    let playheadCopyOffset: number | null = null;
    objects
      .filter((object) => object instanceof window.Wick.Frame)
      .forEach((frame) => {
        if (playheadCopyOffset === null || frame.start < playheadCopyOffset) {
          playheadCopyOffset = frame.start;
        }
      });

    // Keep track of where objects were originally copied from
    this._copyLocation = project.activeFrame && project.activeFrame.uuid;

    // Keep track of the topmost layer of the selection (we use this later to position frames)
    this._copyLayerIndex = Infinity;
    objects
      .filter((object) => {
        return (
          object instanceof window.Wick.Frame ||
          object instanceof window.Wick.Tween
        );
      })
      .map((frame) => frame.parentLayer.index)
      .forEach((i) => {
        this._copyLayerIndex = Math.min(this._copyLayerIndex, i);
      });

    // Make deep copies of every object
    const exportedData = objects.map((object) => object.export());

    // Save references to the original objects
    this._originalObjects = [...objects];

    // Store the clipboard data
    this.clipboardData = {
      objects: exportedData,
      playheadOffset: playheadCopyOffset || 0,
    };
  }
}

// Add to global Wick namespace
if (typeof window !== "undefined") {
  window.Wick = window.Wick || {};
  window.Wick.Clipboard = Clipboard;
}
