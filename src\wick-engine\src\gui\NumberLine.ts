/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { Playhead } from "./Playhead";
import { OnionSkinRange } from "./OnionSkinRange";
import { Bounds } from "./GUIElement";

export class NumberLine extends GUIElement {
  protected playhead: Playhead;
  protected onionSkinRangeLeft: OnionSkinRange;
  protected onionSkinRangeRight: OnionSkinRange;
  protected mousePlayheadPosition: number;

  constructor(model: Base) {
    super(model);

    this.cursor = "grab";
    this.canAutoScrollX = true;

    this.playhead = new Playhead(model);
    this.onionSkinRangeLeft = new OnionSkinRange(model, "left");
    this.onionSkinRangeRight = new OnionSkinRange(model, "right");
    this.mousePlayheadPosition = 0;
  }

  draw(): void {
    super.draw();

    const ctx = this.ctx;

    // Shift over 2px for some breathing room
    ctx.save();
    ctx.translate(2, 0);

    // Save where the mouse is if the user wants to drag the playhead around
    this.mousePlayheadPosition =
      Math.floor(this.localMouse.x / this.gridCellWidth) + 1;

    const width = this.canvas.width - GUIElement.LAYERS_CONTAINER_WIDTH;
    const height = GUIElement.NUMBER_LINE_HEIGHT;

    // Draw background cover
    ctx.fillStyle = GUIElement.TIMELINE_BACKGROUND_COLOR;
    ctx.beginPath();
    ctx.rect(this.project.scrollX - 2, 0, width, height);
    ctx.fill();

    // Draw number line cells
    for (let i = -1; i < width / this.gridCellWidth + 1; i++) {
      const skip = Math.round(this.project.scrollX / this.gridCellWidth);
      this._drawCell(i + skip);
    }

    // Draw onion skin range
    if (this.model.project.onionSkinEnabled) {
      ctx.save();
      ctx.translate(
        (this.model.playheadPosition - 1) * this.gridCellWidth +
          this.gridCellWidth / 2,
        0
      );
      this.onionSkinRangeLeft.draw();
      this.onionSkinRangeRight.draw();
      ctx.restore();
    }

    // Draw playhead
    this.playhead.draw();

    ctx.restore();
  }

  // Helper function for drawing each cell of the numberline (draws the border and the number)
  protected _drawCell(i: number): void {
    const ctx = this.ctx;

    const highlight = i === 0 || i % 5 === 4;

    // Draw cell number
    if (this.project.frameSizeMode !== "small" || highlight) {
      const fontSize = i >= 99 ? 13 : 16;
      const fontFamily = GUIElement.NUMBER_LINE_NUMBERS_FONT_FAMILY;
      ctx.font = `${fontSize}px ${fontFamily}`;
      if (highlight) {
        ctx.fillStyle = GUIElement.NUMBER_LINE_NUMBERS_HIGHLIGHT_COLOR;
      } else {
        ctx.fillStyle = GUIElement.NUMBER_LINE_NUMBERS_COMMON_COLOR;
      }
      const textContent = `${i + 1}`;
      const textWidth = ctx.measureText(textContent).width;
      ctx.fillText(
        textContent,
        i * this.gridCellWidth + this.gridCellWidth / 2 - textWidth / 2,
        GUIElement.NUMBER_LINE_HEIGHT - 5
      );
    }

    // Draw cell wall
    ctx.lineWidth = GUIElement.FRAMES_CONTAINER_VERTICAL_GRID_STROKE_WIDTH;
    if (highlight) {
      ctx.strokeStyle =
        GUIElement.FRAMES_CONTAINER_VERTICAL_GRID_HIGHLIGHT_STROKE_COLOR;
    } else {
      ctx.strokeStyle = GUIElement.FRAMES_CONTAINER_VERTICAL_GRID_STROKE_COLOR;
    }
    ctx.beginPath();
    const wallX = i * this.gridCellWidth;
    ctx.moveTo(wallX, 0);
    ctx.lineTo(wallX, GUIElement.NUMBER_LINE_HEIGHT);
    ctx.stroke();
  }

  onMouseDown(e: MouseEvent): void {
    this._movePlayhead();
  }

  onMouseDrag(e: MouseEvent): void {
    this._movePlayhead();
  }

  onMouseUp(e: MouseEvent): void {
    this.projectWasModified();
  }

  get bounds(): Bounds {
    return {
      x: this.project.scrollX,
      y: 0,
      width: this.canvas.width,
      height: GUIElement.NUMBER_LINE_HEIGHT,
    };
  }

  /* Helper function for dragging the playhead around */
  protected _movePlayhead(): void {
    const timeline = this.project.model.activeTimeline;
    if (timeline.playheadPosition !== this.mousePlayheadPosition) {
      timeline.playheadPosition = this.mousePlayheadPosition;
      this.projectWasSoftModified();
    }
  }
}
