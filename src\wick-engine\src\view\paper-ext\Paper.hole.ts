/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Paper.js-drawing-tools.
 *
 * Paper.js-drawing-tools is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Paper.js-drawing-tools is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Paper.js-drawing-tools.  If not, see <https://www.gnu.org/licenses/>.
 */

import * as paper from "paper";

interface HoleExtension {
  hole(item: paper.Item): paper.Path;
}

(paper as any).Path.inject({
  hole: function (item: paper.Item): paper.Path {
    if (!(item instanceof paper.Path)) {
      throw new Error("Paper.Path.hole: item must be a Path");
    }

    const result = this.subtract(item);
    if (result instanceof paper.Path) {
      return result;
    } else if (result instanceof paper.CompoundPath) {
      const children = result.children;
      result.remove();
      return children[0] as paper.Path;
    } else {
      throw new Error("Paper.Path.hole: unexpected result type");
    }
  },
} as HoleExtension);
