<script lang="ts">
  /**
   * Label 标签组件
   * 基于 Skeleton UI 和全局主题色彩
   */
  
  // Props
  export let text: string = '';
  export let htmlFor: string = '';
  export let required: boolean = false;
  export let size: 'sm' | 'md' | 'lg' = 'md';
  export let variant: 'default' | 'secondary' | 'success' | 'warning' | 'error' = 'default';
  export let weight: 'normal' | 'medium' | 'semibold' | 'bold' = 'medium';
  export let disabled: boolean = false;
  
  // 获取标签样式类
  function getLabelClass() {
    const baseClass = 'label';
    const sizeClass = `label-${size}`;
    const variantClass = `label-${variant}`;
    const weightClass = `label-${weight}`;
    const disabledClass = disabled ? 'label-disabled' : '';
    
    return [baseClass, sizeClass, variantClass, weightClass, disabledClass]
      .filter(Boolean)
      .join(' ');
  }
</script>

<label 
  class={getLabelClass()}
  for={htmlFor}
  aria-disabled={disabled}
>
  <span class="label-text">
    {text}
    <slot />
  </span>
  {#if required}
    <span class="label-required" aria-label="必填">*</span>
  {/if}
</label>

<style>
  .label {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-family: system-ui, -apple-system, sans-serif;
    color: #1a202c;
    cursor: default;
    transition: all 0.2s ease;
  }
  
  .label-disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .label-text {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .label-required {
    color: #ef4444;
    font-weight: bold;
    margin-left: 4px;
  }
  
  /* 尺寸变体 */
  .label-sm {
    font-size: 10px;
  }

  .label-md {
    font-size: 11px;
  }

  .label-lg {
    font-size: 12px;
  }
  
  /* 颜色变体 */
  .label-default {
    color: #1a202c;
  }

  .label-secondary {
    color: #ece9e9;
  }

  .label-success {
    color: #22c55e;
  }

  .label-warning {
    color: #f59e0b;
  }

  .label-error {
    color: #ef4444;
  }
  
  /* 字重变体 */
  .label-normal {
    font-weight: 400;
  }
  
  .label-medium {
    font-weight: 500;
  }
  
  .label-semibold {
    font-weight: 600;
  }
  
  .label-bold {
    font-weight: 700;
  }
  
  /* 焦点样式 */
  .label:focus-visible {
    outline: 2px solid rgba(64, 105, 240, 0.9);
    outline-offset: 2px;
    border-radius: 3px;
  }
</style>
