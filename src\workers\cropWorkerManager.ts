/**
 * 裁切Worker管理器
 * 管理裁切图片的Worker处理，避免阻塞UI线程
 */

import type { CropArea } from '../types/crop';

// Worker消息类型
interface WorkerMessage {
  id: string;
  type: 'generate-crop-areas' | 'generate-crop-canvas';
  payload: any;
}

interface WorkerResponse {
  id: string;
  type: 'success' | 'error' | 'progress';
  payload: any;
}

// 裁切任务配置
export interface CropAreasConfig {
  buffer: ArrayBuffer;
  originalType?: string;
  cropAreas: CropArea[];
  quality?: number;
}

export interface CropCanvasConfig {
  buffer: ArrayBuffer;
  originalType?: string;
  cropAreas: CropArea[];
  canvasWidth: number;
  canvasHeight: number;
  columns: number;
  itemWidth: number;
  itemHeight: number;
  gap: number;
}

// 结果类型
export interface CropAreaResult {
  areaId: string;
  blob: Blob;
  dataUrl: string;
  width: number;
  height: number;
}

export interface CropCanvasResult {
  canvasBlob: Blob;
  canvasDataUrl: string;
  canvasWidth: number;
  canvasHeight: number;
  areaPositions: Array<{
    areaId: string;
    x: number;
    y: number;
    width: number;
    height: number;
  }>;
}

// 进度回调类型
export type ProgressCallback = (progress: {
  completed: number;
  total: number;
  percentage: number;
  currentArea?: string;
}) => void;

/**
 * 裁切Worker管理器类
 */
export class CropWorkerManager {
  private worker: Worker | null = null;
  private taskCounter = 0;
  private pendingTasks = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
    onProgress?: ProgressCallback;
  }>();

  constructor() {
    this.initWorker();
  }

  /**
   * 初始化Worker
   */
  private initWorker() {
    try {
      this.worker = new Worker(
        new URL('../workers/imageProcessor.worker.ts', import.meta.url),
        { type: 'module' }
      );

      this.worker.onmessage = (event: MessageEvent<WorkerResponse>) => {
        this.handleWorkerMessage(event.data);
      };

      this.worker.onerror = (error) => {
        console.error('❌ CropWorkerManager: Worker错误', error);
        this.rejectAllTasks(new Error('Worker发生错误'));
      };

      console.log('✅ CropWorkerManager: Worker初始化成功');
    } catch (error) {
      console.error('❌ CropWorkerManager: Worker初始化失败', error);
    }
  }

  /**
   * 处理Worker消息
   */
  private handleWorkerMessage(response: WorkerResponse) {
    const task = this.pendingTasks.get(response.id);
    if (!task) {
      console.warn('⚠️ CropWorkerManager: 收到未知任务的响应', response.id);
      return;
    }

    switch (response.type) {
      case 'progress':
        if (task.onProgress) {
          task.onProgress(response.payload);
        }
        break;

      case 'success':
        this.pendingTasks.delete(response.id);
        task.resolve(response.payload);
        break;

      case 'error':
        this.pendingTasks.delete(response.id);
        task.reject(new Error(response.payload.message || '处理失败'));
        break;
    }
  }

  /**
   * 生成裁切区域图片（独立图片方案）
   */
  async generateCropAreas(
    config: CropAreasConfig,
    onProgress?: ProgressCallback
  ): Promise<{ cropResults: CropAreaResult[]; totalProcessed: number; totalAreas: number }> {
    if (!this.worker) {
      throw new Error('Worker未初始化');
    }

    const taskId = `crop-areas-${++this.taskCounter}`;
    
    return new Promise((resolve, reject) => {
      this.pendingTasks.set(taskId, { resolve, reject, onProgress });

      const message: WorkerMessage = {
        id: taskId,
        type: 'generate-crop-areas',
        payload: {
          id: taskId,
          buffer: config.buffer,
          originalType: config.originalType,
          cropAreas: config.cropAreas,
          quality: config.quality || 0.9
        }
      };

      this.worker!.postMessage(message);
      
      console.log('🚀 CropWorkerManager: 开始生成裁切区域图片', {
        taskId,
        areasCount: config.cropAreas.length
      });
    });
  }

  /**
   * 生成裁切Canvas（单个Canvas方案）
   */
  async generateCropCanvas(
    config: CropCanvasConfig,
    onProgress?: ProgressCallback
  ): Promise<CropCanvasResult> {
    if (!this.worker) {
      throw new Error('Worker未初始化');
    }

    const taskId = `crop-canvas-${++this.taskCounter}`;
    
    return new Promise((resolve, reject) => {
      this.pendingTasks.set(taskId, { resolve, reject, onProgress });

      const message: WorkerMessage = {
        id: taskId,
        type: 'generate-crop-canvas',
        payload: {
          id: taskId,
          buffer: config.buffer,
          originalType: config.originalType,
          cropAreas: config.cropAreas,
          canvasWidth: config.canvasWidth,
          canvasHeight: config.canvasHeight,
          columns: config.columns,
          itemWidth: config.itemWidth,
          itemHeight: config.itemHeight,
          gap: config.gap
        }
      };

      this.worker!.postMessage(message);
      
      console.log('🚀 CropWorkerManager: 开始生成裁切Canvas', {
        taskId,
        areasCount: config.cropAreas.length,
        canvasSize: `${config.canvasWidth}×${config.canvasHeight}`
      });
    });
  }

  /**
   * 取消所有任务
   */
  cancelAllTasks() {
    this.rejectAllTasks(new Error('任务被取消'));
  }

  /**
   * 拒绝所有待处理任务
   */
  private rejectAllTasks(error: Error) {
    for (const task of this.pendingTasks.values()) {
      task.reject(error);
    }
    this.pendingTasks.clear();
  }

  /**
   * 销毁Worker
   */
  destroy() {
    if (this.worker) {
      this.cancelAllTasks();
      this.worker.terminate();
      this.worker = null;
      console.log('🧹 CropWorkerManager: Worker已销毁');
    }
  }

  /**
   * 获取当前任务数量
   */
  get pendingTasksCount(): number {
    return this.pendingTasks.size;
  }

  /**
   * 检查Worker是否可用
   */
  get isReady(): boolean {
    return this.worker !== null;
  }
}

// 全局单例实例
export const cropWorkerManager = new CropWorkerManager();
