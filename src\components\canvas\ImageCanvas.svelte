<script lang="ts">
  /**
   * ImageCanvas 组件 - 专门显示单个资源图片，支持裁切功能
   * 参考 AtlasCanvas 和 cutSprite 实现
   */
  import { onMount, onDestroy } from 'svelte';
  import type { ImageResource, CropArea } from '../../types/imageType';
  // 移除已删除的 selectionStore 引用
  import {
    layoutSettingsStore,
    globalCropSettingsStore,
    type LayoutSettings,
    type GlobalCropSettings
  } from '../../stores/exportSettingsStore';
  import { imagePreviewCache } from '../../utils/imagePreviewCache';
  import { debounce } from '../../utils/debounce';
  import { performanceMonitor } from '../../utils/performanceMonitor';

  interface Props {
    showCropLines?: boolean;
    onCropAreaUpdate?: (area: CropArea) => void;
    onCropAreaCreate?: (area: CropArea) => void;
    onCropAreaSelect?: (areaId: string) => void;
  }

  let {
    showCropLines = true,
    onCropAreaUpdate,
    onCropAreaCreate,
    onCropAreaSelect
  }: Props = $props();

  // 🎯 直接从stores获取当前数据
  let currentResource = $state<ImageResource | null>(null);
  let currentCropAreas = $state<CropArea[]>([]);
  let lastResourceSignature = $state<string | undefined>(undefined);

  // 🎯 从selectionStore获取selectedCropAreaId
  let selectedCropAreaId = $state<string | undefined>(undefined);

  // 🎯 添加cropSettings监听状态
  let previousCropSettings = $state<any>(null);

  // 🎯 监听stores - 使用onMount避免无限循环
  let selectionUnsubscribe: (() => void) | null = null;
  let layoutSettingsUnsubscribe: (() => void) | null = null;
  let globalCropSettingsUnsubscribe: (() => void) | null = null;

  // 🎯 全局设置状态
  let currentLayoutSettings = $state<LayoutSettings | null>(null);
  let currentGlobalCropSettings = $state<GlobalCropSettings | null>(null);

  onMount(() => {
    // 监听选择状态变化 - 更敏感的监听
    selectionUnsubscribe = selectionStore.subscribe((state) => {
      console.log('🔍 ImageCanvas: 收到selectionStore变化', {
        type: state.type,
        resourceName: state.resource?.name,
        resourceType: state.resource?.type,
        cropAreasCount: state.cropAreas?.length || 0,
        cropSettings: state.cropSettings,
        fullState: state
      });

      // 🎯 检查cropSettings是否变化
      const cropSettingsChanged = JSON.stringify(state.cropSettings) !== JSON.stringify(previousCropSettings);
      if (cropSettingsChanged && previousCropSettings !== null) {
        console.log('🎯 ImageCanvas: 检测到cropSettings变化', {
          old: previousCropSettings,
          new: state.cropSettings,
          hasCanvas: !!isCanvasReady,
          hasImage: !!loadedImage,
          gridModeChanged: previousCropSettings.gridMode !== state.cropSettings.gridMode
        });

        if (isCanvasReady && loadedImage) {
          // 🎯 根据裁切模式决定渲染策略
          if (state.cropSettings.autoDetect === true) {
            console.log('🎯 ImageCanvas: 自动检测模式开启，开始检测透明区域');
            setTimeout(() => {
              generateAutoCropAreas();
            }, 0);
          } else if (state.cropSettings.gridMode === true) {
            console.log('🎯 ImageCanvas: 网格模式开启，生成网格');
            setTimeout(() => {
              generateGridCropAreas();
            }, 0);
          } else {
            console.log('🎯 ImageCanvas: 所有模式关闭，清空裁切区域');
            // 清空裁切区域
            currentCropAreas = [];
            // 重新渲染空白canvas
            setTimeout(() => {
              renderCanvas();
            }, 0);
          }
        }
      }
      previousCropSettings = { ...state.cropSettings };

      if (state.type === 'resource' && state.resource && state.resource.type === 'image') {
        const resource = state.resource as ImageResource;

        // 🎯 更详细的签名，包含所有可能影响显示的字段
        const cropDataSignature = resource.cropData ? JSON.stringify({
          lastModified: resource.cropData.lastModified instanceof Date
            ? resource.cropData.lastModified.getTime()
            : resource.cropData.lastModified,
          cellWidth: resource.cropData.cellWidth,
          cellHeight: resource.cropData.cellHeight,
          padding: resource.cropData.padding,
          spacing: resource.cropData.spacing,
          gridMode: resource.cropData.gridMode,
          areasCount: resource.cropData.areas?.length || 0
        }) : 'no-crop-data';

        // 🎯 包含selectionStore中的cropSettings，确保设置变化能被检测到
        const cropSettingsSignature = JSON.stringify(state.cropSettings);

        const newSignature = `${resource.id}_${resource.updatedAt}_${resource.width}_${resource.height}_${cropDataSignature}_${cropSettingsSignature}`;

        if (newSignature !== lastResourceSignature) {
          console.log('🔄 ImageCanvas: 检测到资源变化，重新加载', {
            resourceName: resource.name,
            resourceId: resource.id,
            size: `${resource.width}×${resource.height}`,
            hasCropData: !!resource.cropData,
            cropAreasCount: resource.cropData?.areas?.length || 0,
            cropSettings: resource.cropData ? {
              cellWidth: resource.cropData.cellWidth,
              cellHeight: resource.cropData.cellHeight,
              gridMode: resource.cropData.gridMode
            } : null,
            oldSignature: lastResourceSignature,
            newSignature
          });

          currentResource = resource;
          lastResourceSignature = newSignature;

          // 🎯 直接从resource.cropData获取裁切区域
          if (resource.cropData?.areas) {
            currentCropAreas = resource.cropData.areas;
            console.log('📋 ImageCanvas: 从resource.cropData加载裁切区域', {
              areasCount: resource.cropData.areas.length,
              gridMode: resource.cropData.gridMode,
              cellSize: `${resource.cropData.cellWidth}×${resource.cropData.cellHeight}`
            });
          } else {
            currentCropAreas = [];
          }

          // 🎯 同时更新selectedCropAreaId
          selectedCropAreaId = state.selectedCropAreaId || undefined;

          // 🎯 资源变化时，总是重新加载图片
          console.log('🎯 ImageCanvas: 资源变化，重新加载图片');

          // 清空当前图片，确保重新加载
          loadedImage = null;

          // 延迟加载避免循环
          setTimeout(() => {
            if (isCanvasReady) {
              loadImage();
            }
          }, 0);
        }
      } else if (state.type !== 'resource') {
        // 如果不是资源选择，清空当前资源
        if (currentResource) {
          console.log('🔄 ImageCanvas: 清空资源选择');
          currentResource = null;
          currentCropAreas = [];
          lastResourceSignature = undefined;
        }
      }
    });

    // 🎯 监听全局布局设置变化
    layoutSettingsUnsubscribe = layoutSettingsStore.subscribe((settings) => {
      console.log('🎯 ImageCanvas: 收到布局设置变化', settings);
      currentLayoutSettings = settings;

      // 如果有裁切信息，重新生成裁切预览
      if (currentResource?.cropData?.areas && currentResource.cropData.areas.length > 0) {
        console.log('🎯 ImageCanvas: 布局设置变化，重新生成裁切预览');
        setTimeout(() => {
          updateCropPreviews();
        }, 0);
      }
    });

    // 🎯 监听全局裁切设置变化
    globalCropSettingsUnsubscribe = globalCropSettingsStore.subscribe((settings) => {
      console.log('🎯 ImageCanvas: 收到裁切设置变化', settings);
      currentGlobalCropSettings = settings;

      // 如果有裁切信息，重新生成裁切预览
      if (currentResource?.cropData?.areas && currentResource.cropData.areas.length > 0) {
        console.log('🎯 ImageCanvas: 裁切设置变化，重新生成裁切预览');
        setTimeout(() => {
          updateCropPreviews();
        }, 0);
      }
    });
  });

  // 🎯 优化的状态管理 - 与其他组件保持一致
  let canvasElement = $state<HTMLCanvasElement>();
  let ctx = $state<CanvasRenderingContext2D | null>(null);
  let isLoading = $state(false);
  let error = $state<string | null>(null);

  // 图片和变换状态
  let loadedImage = $state<HTMLImageElement | null>(null);
  let transform = $state({
    scale: 1,
    translateX: 0,
    translateY: 0,
    imageWidth: 0,
    imageHeight: 0
  });

  // 交互状态
  let isDragging = $state(false);
  let isCreating = $state(false);
  let dragStart = $state({ x: 0, y: 0 });
  let dragMode = $state<'move' | 'resize' | 'create' | 'pan'>('pan');
  let resizeHandle = $state<string>('');
  let creatingArea = $state<CropArea | null>(null);

  // 🎯 操作状态管理 - 与其他组件保持一致
  let operationState = $state<{
    isProcessing: boolean;
    progress?: number;
    error?: string;
    operation?: 'loading' | 'rendering' | 'cropping';
  }>({ isProcessing: false });

  // 🎯 计算属性 - 减少重复计算
  const isCanvasReady = $derived(!!canvasElement && !!ctx);
  const isImageReady = $derived(!!loadedImage);
  const canRender = $derived(isCanvasReady && isImageReady && !isLoading);

  // 🎯 初始化Canvas
  function initCanvas() {
    if (!canvasElement) {
      console.error('❌ ImageCanvas: canvasElement未找到');
      return;
    }

    const context = canvasElement.getContext('2d');
    if (!context) {
      error = '无法获取Canvas上下文';
      console.error('❌ ImageCanvas: 无法获取Canvas上下文');
      return;
    }

    ctx = context;
    // 🎯 设置默认尺寸，图片加载后会调整为实际尺寸
    canvasElement.width = 800; // 临时默认尺寸
    canvasElement.height = 600; // 临时默认尺寸

    console.log('🎨 ImageCanvas: Canvas初始化完成', {
      defaultSize: '800×600',
      resourceName: currentResource?.name || '无资源'
    });

    // 初始化完成后加载图片
    loadImage();
  }

  // 🎯 优化的图片加载 - 集成缓存系统和性能监控
  async function loadImage() {
    if (!currentResource || currentResource.type !== 'image') return;

    // 🎯 开始性能监控
    performanceMonitor.startMeasure('image-canvas-load', {
      resourceId: currentResource.id,
      resourceName: currentResource.name,
      hasData: !!currentResource.data
    });

    isLoading = true;
    error = null;

    // 🎯 设置加载状态
    operationState = {
      isProcessing: true,
      progress: 10,
      operation: 'loading'
    };

    try {
      const img = new Image();

      // 🎯 优先使用缓存系统
      const cachedPreview = imagePreviewCache.getPreview(currentResource);
      if (cachedPreview) {
        console.log('✅ ImageCanvas: 使用缓存的图片预览', currentResource.name);
        operationState.progress = 50;
      }

      await new Promise((resolve, reject) => {
        img.onload = () => {
          if (!currentResource) return;

          console.log('✅ ImageCanvas: 图片加载完成', {
            name: currentResource.name,
            naturalSize: `${img.naturalWidth}×${img.naturalHeight}`,
            resourceSize: `${currentResource.width}×${currentResource.height}`
          });

          loadedImage = img;

          // 🎯 更新 currentResource 的尺寸信息（如果尚未设置或不正确）
          if (!currentResource.width || !currentResource.height ||
              currentResource.width !== img.naturalWidth || currentResource.height !== img.naturalHeight) {

            console.log('🔧 ImageCanvas: 检测到图片尺寸需要更新', {
              resourceName: currentResource.name,
              currentSize: `${currentResource.width}×${currentResource.height}`,
              naturalSize: `${img.naturalWidth}×${img.naturalHeight}`
            });

            // 🎯 通过resourceStore更新尺寸，这样会自动同步到selectionStore
            const resourceId = currentResource.id;
            const naturalWidth = img.naturalWidth;
            const naturalHeight = img.naturalHeight;

            import('../../stores/resourceStore').then(({ resourceActions }) => {
              resourceActions.updateResource(resourceId, {
                width: naturalWidth,
                height: naturalHeight
              });
            });

            console.log('✅ ImageCanvas: 图片尺寸已更新并同步', {
              resourceName: currentResource.name,
              width: img.naturalWidth,
              height: img.naturalHeight
            });
          }

          // 🎯 设置 canvas 尺寸与图片尺寸一致
          if (canvasElement) {
            canvasElement.width = img.naturalWidth;
            canvasElement.height = img.naturalHeight;

            console.log('🎯 ImageCanvas: Canvas尺寸已调整为图片尺寸', {
              canvasSize: `${canvasElement.width}×${canvasElement.height}`,
              imageSize: `${img.naturalWidth}×${img.naturalHeight}`
            });
          }

          // 更新变换状态 - 不需要缩放，1:1显示
          transform.imageWidth = img.naturalWidth;
          transform.imageHeight = img.naturalHeight;
          transform.scale = 1; // 1:1 显示，不缩放
          transform.translateX = 0; // 不需要偏移
          transform.translateY = 0; // 不需要偏移

          resolve(img);
        };

        img.onerror = () => {
          console.error('❌ ImageCanvas: 图片加载失败', currentResource?.name || '未知资源');
          reject(new Error(`图片加载失败: ${currentResource?.name || '未知资源'}`));
        };

        // 🎯 优先使用缓存，否则创建图片数据URL
        if (cachedPreview) {
          img.src = cachedPreview;
        } else if (currentResource?.data) {
          const mimeType = currentResource.originalFile?.type || 'image/png';
          const blob = new Blob([currentResource.data], { type: mimeType });
          img.src = URL.createObjectURL(blob);
        } else if (currentResource?.blobUrl) {
          img.src = currentResource.blobUrl;
        } else {
          reject(new Error(`资源缺少图片数据: ${currentResource?.name || '未知资源'}`));
        }
      });

      operationState.progress = 80;
      operationState.operation = 'rendering';

      // 渲染图片
      renderCanvas();

      operationState.progress = 90;

      // 🎯 图片加载完成后，检查是否需要生成网格
      console.log('🎯 ImageCanvas: 图片加载完成，检查网格状态');

      // 如果resource.cropData中有现有的areas，则加载它们
      const cropData = currentResource?.cropData;
      if (cropData?.areas && cropData.areas.length > 0) {
        console.log('🎯 ImageCanvas: 加载resource.cropData中的现有区域', cropData.areas.length);
        currentCropAreas = cropData.areas;
      } else {
        console.log('🎯 ImageCanvas: 没有现有区域，检查是否需要生成网格');
        currentCropAreas = [];

        // 🎯 检查当前的gridMode状态，如果开启则生成网格
        setTimeout(() => {
          checkAndGenerateGrid();
        }, 100); // 延迟一点确保状态同步
      }

      // 🎯 完成加载
      operationState = { isProcessing: false };

      // 🎯 结束性能监控
      performanceMonitor.endMeasure('image-canvas-load');

    } catch (err) {
      console.error('❌ ImageCanvas: 图片加载失败', err);
      error = err instanceof Error ? err.message : '图片加载失败';
      operationState = {
        isProcessing: false,
        error: `图片加载失败: ${err instanceof Error ? err.message : '未知错误'}`
      };

      performanceMonitor.endMeasure('image-canvas-load');
    } finally {
      isLoading = false;
    }
  }

  // 🎯 渲染Canvas
  function renderCanvas() {
    if (!ctx || !loadedImage || !canvasElement) return;

    // 清空画布
    ctx.clearRect(0, 0, canvasElement.width, canvasElement.height);

    // 绘制背景（深色背景）
    ctx.fillStyle = '#2a2a2a';
    ctx.fillRect(0, 0, canvasElement.width, canvasElement.height);

    // 🎯 绘制图片 - 1:1 尺寸，无缩放无偏移
    ctx.drawImage(loadedImage, 0, 0);

    // 绘制裁切区域
    if (showCropLines) {
      drawCropAreas();
    }

    // 绘制正在创建的区域
    if (isCreating && creatingArea) {
      drawCreatingArea();
    }
  }

  // 🎯 绘制裁切区域
  function drawCropAreas() {
    if (!ctx) return;

    console.log('🎨 ImageCanvas: 绘制裁切区域', {
      areasCount: currentCropAreas.length,
      showCropLines,
      areas: currentCropAreas.map(area => ({ id: area.id, name: area.name, size: `${area.width}×${area.height}` }))
    });

    const context = ctx; // 保存引用避免 TypeScript 警告
    context.save();

    currentCropAreas.forEach((area: CropArea) => {
      // 🎯 直接使用区域坐标，不需要变换
      const x = area.x;
      const y = area.y;
      const w = area.width;
      const h = area.height;

      // 绘制裁切框
      const isSelected = selectedCropAreaId === area.id;
      context.strokeStyle = isSelected ? '#ff6b35' : '#0080ff';
      context.lineWidth = isSelected ? 3 : 2;
      context.setLineDash([]);
      context.strokeRect(x, y, w, h);

      // 绘制半透明遮罩
      context.fillStyle = isSelected ? 'rgba(255, 107, 53, 0.1)' : 'rgba(0, 128, 255, 0.1)';
      context.fillRect(x, y, w, h);

      // 绘制区域名称
      if (area.name) {
        context.fillStyle = '#ffffff';
        context.font = '12px Arial';
        context.fillText(area.name, x + 4, y + 16);
      }

      // 绘制调整手柄（仅选中的区域）
      if (isSelected) {
        drawResizeHandles(x, y, w, h);
      }
    });

    context.restore();
  }

  // 🎯 绘制调整手柄
  function drawResizeHandles(x: number, y: number, w: number, h: number) {
    if (!ctx) return;

    const context = ctx; // 保存引用避免 TypeScript 警告
    const handleSize = 6;
    const handles = [
      { id: 'nw', x: x - handleSize/2, y: y - handleSize/2 },
      { id: 'ne', x: x + w - handleSize/2, y: y - handleSize/2 },
      { id: 'sw', x: x - handleSize/2, y: y + h - handleSize/2 },
      { id: 'se', x: x + w - handleSize/2, y: y + h - handleSize/2 }
    ];

    context.fillStyle = '#ffffff';
    context.strokeStyle = '#000000';
    context.lineWidth = 1;

    handles.forEach(handle => {
      context.fillRect(handle.x, handle.y, handleSize, handleSize);
      context.strokeRect(handle.x, handle.y, handleSize, handleSize);
    });
  }

  // 🎯 绘制正在创建的区域
  function drawCreatingArea() {
    if (!ctx || !creatingArea) return;

    ctx.save();
    // 🎯 直接使用区域坐标，不需要变换
    const x = creatingArea.x;
    const y = creatingArea.y;
    const w = creatingArea.width;
    const h = creatingArea.height;

    ctx.strokeStyle = '#ffff00';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    ctx.strokeRect(x, y, w, h);

    ctx.fillStyle = 'rgba(255, 255, 0, 0.1)';
    ctx.fillRect(x, y, w, h);

    ctx.restore();
  }

  // 🎯 自动生成裁切区域（参考 cutSprite 实现）
  function generateAutoCropAreas() {
    if (!loadedImage || !ctx) return;

    console.log('🎯 ImageCanvas: 开始自动生成裁切区域');

    // 创建临时canvas来分析图像
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d');
    if (!tempCtx) return;

    tempCanvas.width = loadedImage.naturalWidth;
    tempCanvas.height = loadedImage.naturalHeight;
    tempCtx.drawImage(loadedImage, 0, 0);

    // 获取图像数据
    const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
    const data = imageData.data;

    // 🎯 智能裁切算法：先分析图片特征，再检测非透明区域
    const imageStats = analyzeImageTransparency(data, tempCanvas.width, tempCanvas.height);
    console.log('📊 ImageCanvas: 图片透明度分析', imageStats);

    // 🎯 根据图片特征调整检测策略
    let detectedAreas: Array<{x: number, y: number, width: number, height: number}> = [];

    if (imageStats.isSuitableForAutoDetect) {
      // 图片适合自动检测
      console.log('🎯 ImageCanvas: 图片适合自动检测，开始检测');
      detectedAreas = detectNonTransparentAreas(data, tempCanvas.width, tempCanvas.height);
    } else {
      // 图片不适合自动检测，回退到网格模式
      console.log('🎯 ImageCanvas: 图片不适合自动检测，回退到网格模式', {
        reason: imageStats.transparentRatio <= 0.3 ? '透明度不足' : '不透明区域太少'
      });
      // 🎯 强制生成网格作为回退
      forceGenerateGridAsFallback();
      return; // 提前返回，避免执行后续的检测逻辑
    }

    if (detectedAreas.length > 0) {
      console.log('🎯 ImageCanvas: 检测到非透明区域', detectedAreas.length);
      detectedAreas.forEach((area, index) => {
        onCropAreaCreate?.({
          id: `auto_${Date.now()}_${index}`,
          name: `Auto_${index + 1}`,
          x: area.x,
          y: area.y,
          width: area.width,
          height: area.height,
          selected: false
        });
      });
    } else {
      console.log('🎯 ImageCanvas: 未检测到合适区域，回退到网格模式');
      // 🎯 强制回退到网格模式：临时启用gridMode并生成网格
      forceGenerateGridAsFallback();
    }
  }

  // 🎯 强制生成网格作为自动检测的回退方案
  function forceGenerateGridAsFallback() {
    if (!loadedImage || !currentResource) return;

    console.log('🔄 ImageCanvas: 强制生成网格作为回退方案');

    // 🎯 使用默认的网格设置
    const defaultCellWidth = 64;
    const defaultCellHeight = 64;
    const defaultPadding = 0;
    const defaultSpacing = 0;

    // 计算实际的单元格尺寸（包含间距）
    const actualCellWidth = defaultCellWidth + defaultSpacing;
    const actualCellHeight = defaultCellHeight + defaultSpacing;

    const cols = Math.floor((loadedImage.naturalWidth - defaultPadding * 2) / actualCellWidth);
    const rows = Math.floor((loadedImage.naturalHeight - defaultPadding * 2) / actualCellHeight);

    console.log('🎯 ImageCanvas: 使用默认设置生成回退网格', {
      cellWidth: defaultCellWidth,
      cellHeight: defaultCellHeight,
      padding: defaultPadding,
      spacing: defaultSpacing,
      cols,
      rows,
      total: cols * rows,
      imageSize: `${loadedImage.naturalWidth}×${loadedImage.naturalHeight}`
    });

    // 🎯 生成网格区域
    const newAreas: CropArea[] = [];
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const x = defaultPadding + col * actualCellWidth;
        const y = defaultPadding + row * actualCellHeight;

        newAreas.push({
          id: `fallback_grid_${row}_${col}`,
          name: `Grid_${row + 1}_${col + 1}`,
          x,
          y,
          width: defaultCellWidth,
          height: defaultCellHeight,
          selected: false
        });
      }
    }

    // 🎯 通过回调创建区域，而不是直接修改currentCropAreas
    console.log('🎯 ImageCanvas: 创建回退网格区域', newAreas.length);
    newAreas.forEach(area => {
      onCropAreaCreate?.(area);
    });
  }

  // 🎯 分析图片透明度特征
  function analyzeImageTransparency(data: Uint8ClampedArray, width: number, height: number) {
    const totalPixels = width * height;
    let transparentPixels = 0;
    let semiTransparentPixels = 0;
    let opaquePixels = 0;

    const alphaHistogram = new Array(256).fill(0);

    for (let i = 0; i < totalPixels; i++) {
      const alpha = data[i * 4 + 3];
      alphaHistogram[alpha]++;

      if (alpha === 0) {
        transparentPixels++;
      } else if (alpha < 200) {
        semiTransparentPixels++;
      } else {
        opaquePixels++;
      }
    }

    const transparentRatio = transparentPixels / totalPixels;
    const semiTransparentRatio = semiTransparentPixels / totalPixels;
    const opaqueRatio = opaquePixels / totalPixels;

    return {
      totalPixels,
      transparentPixels,
      semiTransparentPixels,
      opaquePixels,
      transparentRatio,
      semiTransparentRatio,
      opaqueRatio,
      alphaHistogram,
      // 判断是否适合自动检测
      isSuitableForAutoDetect: transparentRatio > 0.3 && opaqueRatio > 0.1
    };
  }

  // 🎯 检测非透明区域的算法 - 优化版本
  function detectNonTransparentAreas(data: Uint8ClampedArray, width: number, height: number) {
    const areas: Array<{x: number, y: number, width: number, height: number}> = [];
    const visited = new Array(width * height).fill(false);

    // 🎯 动态调整参数
    const imageArea = width * height;
    const minAreaSize = Math.max(16, Math.min(64, Math.sqrt(imageArea) / 10)); // 动态最小尺寸
    const alphaThreshold = 200; // 透明度阈值
    const maxAreaRatio = 0.7; // 最大区域比例
    const minDensity = 0.3; // 最小密度（实际像素/边界框像素）

    console.log('🔍 ImageCanvas: 开始检测非透明区域', {
      imageSize: `${width}×${height}`,
      alphaThreshold,
      minAreaSize: Math.round(minAreaSize),
      maxAreaRatio,
      minDensity
    });

    // 🎯 使用更智能的扫描策略：跳跃式扫描减少重复
    const scanStep = Math.max(1, Math.floor(minAreaSize / 4));

    for (let y = 0; y < height; y += scanStep) {
      for (let x = 0; x < width; x += scanStep) {
        const index = (y * width + x) * 4;
        const alpha = data[index + 3];

        // 🎯 使用更严格的透明度检测
        if (alpha >= alphaThreshold && !visited[y * width + x]) {
          const area = floodFillArea(data, width, height, x, y, visited, alphaThreshold);

          // 🎯 多重过滤条件
          const areaRatio = (area.width * area.height) / imageArea;
          const density = area.pixelCount / (area.width * area.height);
          const isValidSize = area.width >= minAreaSize && area.height >= minAreaSize;
          const isNotTooLarge = areaRatio <= maxAreaRatio;
          const hasGoodDensity = density >= minDensity;
          const isNotTooThin = area.width >= 8 && area.height >= 8; // 避免线条

          console.log('🔍 ImageCanvas: 检测到区域', {
            position: `(${area.x},${area.y})`,
            size: `${area.width}×${area.height}`,
            ratio: `${(areaRatio * 100).toFixed(1)}%`,
            density: `${(density * 100).toFixed(1)}%`,
            pixelCount: area.pixelCount,
            isValidSize,
            isNotTooLarge,
            hasGoodDensity,
            isNotTooThin
          });

          if (isValidSize && isNotTooLarge && hasGoodDensity && isNotTooThin) {
            areas.push(area);
          } else {
            const reasons = [];
            if (!isValidSize) reasons.push('尺寸太小');
            if (!isNotTooLarge) reasons.push('区域太大');
            if (!hasGoodDensity) reasons.push('密度太低');
            if (!isNotTooThin) reasons.push('太细长');

            console.log('🚫 ImageCanvas: 区域被过滤', {
              reason: reasons.join(', ')
            });
          }
        }
      }
    }

    console.log('✅ ImageCanvas: 非透明区域检测完成', {
      totalAreas: areas.length,
      areas: areas.map(a => `${a.width}×${a.height}@(${a.x},${a.y})`)
    });

    return areas;
  }

  // 🎯 洪水填充算法找到连通区域 - 优化版本
  function floodFillArea(
    data: Uint8ClampedArray,
    width: number,
    height: number,
    startX: number,
    startY: number,
    visited: boolean[],
    alphaThreshold: number = 200
  ) {
    const stack = [{x: startX, y: startY}];
    let minX = startX, maxX = startX;
    let minY = startY, maxY = startY;
    let pixelCount = 0;

    while (stack.length > 0) {
      const {x, y} = stack.pop()!;

      if (x < 0 || x >= width || y < 0 || y >= height) continue;
      if (visited[y * width + x]) continue;

      const index = (y * width + x) * 4;
      const alpha = data[index + 3];

      // 🎯 使用传入的透明度阈值
      if (alpha < alphaThreshold) continue;

      visited[y * width + x] = true;
      pixelCount++;

      // 更新边界
      minX = Math.min(minX, x);
      maxX = Math.max(maxX, x);
      minY = Math.min(minY, y);
      maxY = Math.max(maxY, y);

      // 添加相邻像素到栈中
      stack.push({x: x + 1, y});
      stack.push({x: x - 1, y});
      stack.push({x, y: y + 1});
      stack.push({x, y: y - 1});
    }

    const area = {
      x: minX,
      y: minY,
      width: maxX - minX + 1,
      height: maxY - minY + 1,
      pixelCount // 🎯 添加像素计数用于调试
    };

    console.log('🔍 ImageCanvas: 洪水填充完成', {
      startPoint: `(${startX},${startY})`,
      boundingBox: `${area.width}×${area.height}@(${area.x},${area.y})`,
      pixelCount,
      density: `${((pixelCount / (area.width * area.height)) * 100).toFixed(1)}%`
    });

    return area;
  }

  // 🎯 检查并生成网格或自动检测（图片加载完成后调用）
  function checkAndGenerateGrid() {
    if (!loadedImage || !currentResource) return;

    // 🎯 获取当前的selectionStore状态
    let currentCropSettings: any = null;
    selectionStore.subscribe(state => {
      currentCropSettings = state.cropSettings;
    })();

    if (!currentCropSettings) {
      console.log('🎯 ImageCanvas: 无法获取cropSettings，跳过检查');
      return;
    }

    console.log('🎯 ImageCanvas: 检查裁切模式状态', {
      gridMode: currentCropSettings.gridMode,
      autoDetect: currentCropSettings.autoDetect,
      hasExistingAreas: currentCropAreas.length > 0
    });

    // 🎯 如果有现有区域，跳过自动生成
    if (currentCropAreas.length > 0) {
      console.log('🎯 ImageCanvas: 已有现有区域，跳过自动生成');
      return;
    }

    // 🎯 优先级：自动检测 > 网格模式
    if (currentCropSettings.autoDetect === true) {
      console.log('🎯 ImageCanvas: 自动检测模式开启，开始检测透明区域');
      generateAutoCropAreas();
    } else if (currentCropSettings.gridMode === true) {
      console.log('🎯 ImageCanvas: 网格模式开启，生成网格');
      generateGridCropAreas();
    }
  }

  // 🎯 网格模式生成裁切区域 - 使用selectionStore中的cropSettings
  function generateGridCropAreas() {
    if (!loadedImage || !currentResource) return;

    // 🎯 获取当前的selectionStore状态
    let currentCropSettings: any = null;
    selectionStore.subscribe(state => {
      currentCropSettings = state.cropSettings;
    })();

    if (!currentCropSettings) {
      console.log('🎯 ImageCanvas: 无法获取cropSettings，跳过生成');
      return;
    }

    // 🎯 检查gridMode是否开启（且autoDetect未开启）
    if (currentCropSettings.gridMode !== true) {
      console.log('🎯 ImageCanvas: gridMode未开启，跳过生成');
      return;
    }

    // 🎯 如果autoDetect开启，优先使用自动检测
    if (currentCropSettings.autoDetect === true) {
      console.log('🎯 ImageCanvas: autoDetect开启，切换到自动检测模式');
      generateAutoCropAreas();
      return;
    }

    // 🎯 使用全局外边距设置，其他设置使用selectionStore
    const cellWidth = currentCropSettings.cellWidth || 64;
    const cellHeight = currentCropSettings.cellHeight || 64;
    const padding = currentGlobalCropSettings?.cropPadding ?? 0; // 使用全局外边距
    const spacing = 0; // 🎯 固定间距为0，spacing已迁移到ExportPanel

    // 计算实际的单元格尺寸（包含间距）
    const actualCellWidth = cellWidth + spacing;
    const actualCellHeight = cellHeight + spacing;

    const cols = Math.floor((loadedImage.naturalWidth - padding * 2) / actualCellWidth);
    const rows = Math.floor((loadedImage.naturalHeight - padding * 2) / actualCellHeight);

    console.log('🎯 ImageCanvas: 使用全局外边距生成网格裁切区域', {
      cellWidth,
      cellHeight,
      padding: `${padding} (全局设置)`,
      spacing,
      actualCellWidth,
      actualCellHeight,
      cols,
      rows,
      total: cols * rows,
      imageSize: `${loadedImage.naturalWidth}×${loadedImage.naturalHeight}`,
      globalCropSettings: currentGlobalCropSettings
    });

    // 🎯 生成新的网格区域（只在没有现有区域时）
    const newAreas: CropArea[] = [];
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const x = padding + col * actualCellWidth;
        const y = padding + row * actualCellHeight;

        newAreas.push({
          id: `grid_${row}_${col}`,
          name: `Grid_${row + 1}_${col + 1}`,
          x,
          y,
          width: cellWidth,
          height: cellHeight,
          selected: false
        });
      }
    }

    // 🎯 直接更新currentCropAreas并重新渲染
    currentCropAreas = newAreas;
    console.log('🎯 ImageCanvas: 生成了新的网格区域', newAreas.length);

    // 立即重新渲染以显示网格线
    setTimeout(() => {
      renderCanvas();
    }, 0);
  }

  // 🎯 暴露给父组件的方法：获取canvas图片数据
  export function getCanvasImageData(): Promise<{ blob: Blob; width: number; height: number } | null> {
    return new Promise((resolve) => {
      if (!canvasElement || !ctx) {
        console.warn('❌ ImageCanvas: Canvas 或 Context 不可用');
        resolve(null);
        return;
      }

      try {
        canvasElement.toBlob((blob) => {
          if (blob && canvasElement) {
            console.log('✅ ImageCanvas: 成功获取Canvas图片数据', {
              size: blob.size,
              type: blob.type,
              canvasSize: `${canvasElement.width}×${canvasElement.height}`
            });
            resolve({
              blob,
              width: canvasElement.width,
              height: canvasElement.height
            });
          } else {
            console.error('❌ ImageCanvas: Canvas.toBlob 返回 null');
            resolve(null);
          }
        }, 'image/png', 1.0);
      } catch (error) {
        console.error('❌ ImageCanvas: 获取Canvas图片数据失败', error);
        resolve(null);
      }
    });
  }

  // 🎯 防抖渲染函数
  const debouncedRenderCanvas = debounce(() => {
    if (canRender) {
      performanceMonitor.startMeasure('image-canvas-render');
      renderCanvas();
      performanceMonitor.endMeasure('image-canvas-render');
    }
  }, 16); // 60fps

  // 🎯 修复的状态管理 - 避免无限循环
  let lastCanvasElement: HTMLCanvasElement | undefined;

  $effect(() => {
    // 避免重复初始化相同的Canvas元素
    if (canvasElement && canvasElement !== lastCanvasElement) {
      console.log('🔄 ImageCanvas: Canvas元素变化，重新初始化');
      lastCanvasElement = canvasElement;

      // 异步初始化避免循环
      setTimeout(() => {
        initCanvas();
      }, 0);
    }
  });



  // 🎯 使用防抖渲染响应cropAreas变化
  $effect(() => {
    if (isCanvasReady && isImageReady) {
      debouncedRenderCanvas();
    }
  });

  // 🎯 自动清理错误状态
  $effect(() => {
    if (operationState.error) {
      const timer = setTimeout(() => {
        operationState = { isProcessing: false };
      }, 5000); // 5秒后自动清理错误状态

      return () => clearTimeout(timer);
    }
  });

  // 🎯 组件销毁时清理资源
  $effect(() => {
    return () => {
      // 清理操作状态
      operationState = { isProcessing: false };

      // 清理图片URL
      if (loadedImage && loadedImage.src.startsWith('blob:')) {
        URL.revokeObjectURL(loadedImage.src);
      }

      console.log('🧹 ImageCanvas: 组件销毁，清理资源');
    };
  });

  onMount(() => {
    console.log('🎨 ImageCanvas: 组件挂载', currentResource?.name || '无资源');
  });

  // 🎯 鼠标事件处理
  function handleMouseDown(event: MouseEvent) {
    if (!canvasElement || !ctx) return;

    const rect = canvasElement.getBoundingClientRect();
    const canvasX = event.clientX - rect.left;
    const canvasY = event.clientY - rect.top;

    // 🎯 Canvas坐标就是图片坐标（1:1显示）
    const imageX = canvasX;
    const imageY = canvasY;

    dragStart = { x: canvasX, y: canvasY };
    isDragging = true;

    // 检查是否点击了调整手柄
    const selectedArea = currentCropAreas.find((area: CropArea) => area.id === selectedCropAreaId);
    if (selectedArea) {
      const handle = getResizeHandle(canvasX, canvasY, selectedArea);
      if (handle) {
        dragMode = 'resize';
        resizeHandle = handle;
        return;
      }
    }

    // 检查是否点击了裁切区域
    const clickedArea = getAreaAtPoint(canvasX, canvasY);
    if (clickedArea) {
      onCropAreaSelect?.(clickedArea.id);
      dragMode = 'move';
      return;
    }

    // 开始创建新的裁切区域
    dragMode = 'create';
    isCreating = true;
    creatingArea = {
      id: `area_${Date.now()}`,
      x: Math.max(0, Math.min(imageX, transform.imageWidth)),
      y: Math.max(0, Math.min(imageY, transform.imageHeight)),
      width: 0,
      height: 0,
      name: `Area_${currentCropAreas.length + 1}`,
      selected: false
    };
  }

  function handleMouseMove(event: MouseEvent) {
    if (!isDragging || !canvasElement) return;

    const rect = canvasElement.getBoundingClientRect();
    const canvasX = event.clientX - rect.left;
    const canvasY = event.clientY - rect.top;

    const deltaX = canvasX - dragStart.x;
    const deltaY = canvasY - dragStart.y;

    if (dragMode === 'create' && creatingArea) {
      // 🎯 更新正在创建的区域 - 直接使用canvas坐标
      const imageX = canvasX;
      const imageY = canvasY;

      creatingArea.width = Math.max(1, Math.min(imageX - creatingArea.x, transform.imageWidth - creatingArea.x));
      creatingArea.height = Math.max(1, Math.min(imageY - creatingArea.y, transform.imageHeight - creatingArea.y));

      renderCanvas();
    } else if (dragMode === 'move') {
      // 🎯 移动选中的区域 - 直接使用像素差值
      moveSelectedArea(deltaX, deltaY);
    } else if (dragMode === 'resize') {
      // 🎯 调整选中区域的大小 - 直接使用像素差值
      resizeSelectedArea(deltaX, deltaY);
    }

    dragStart = { x: canvasX, y: canvasY };
  }

  function handleMouseUp() {
    if (isCreating && dragMode === 'create' && creatingArea) {
      // 完成创建区域
      if (creatingArea.width > 5 && creatingArea.height > 5) {
        onCropAreaCreate?.(creatingArea);
      }
    }

    isDragging = false;
    isCreating = false;
    dragMode = 'pan';
    resizeHandle = '';
    creatingArea = null;
  }

  // 🎯 辅助函数
  function getAreaAtPoint(x: number, y: number): CropArea | null {
    for (const area of currentCropAreas) {
      // 🎯 直接使用区域坐标，不需要变换
      const areaX = area.x;
      const areaY = area.y;
      const areaW = area.width;
      const areaH = area.height;

      if (x >= areaX && x <= areaX + areaW && y >= areaY && y <= areaY + areaH) {
        return area;
      }
    }
    return null;
  }

  function getResizeHandle(x: number, y: number, area: CropArea): string | null {
    // 🎯 直接使用区域坐标，不需要变换
    const areaX = area.x;
    const areaY = area.y;
    const areaW = area.width;
    const areaH = area.height;
    const handleSize = 6;

    const handles = [
      { id: 'nw', x: areaX - handleSize/2, y: areaY - handleSize/2 },
      { id: 'ne', x: areaX + areaW - handleSize/2, y: areaY - handleSize/2 },
      { id: 'sw', x: areaX - handleSize/2, y: areaY + areaH - handleSize/2 },
      { id: 'se', x: areaX + areaW - handleSize/2, y: areaY + areaH - handleSize/2 }
    ];

    for (const handle of handles) {
      if (x >= handle.x && x <= handle.x + handleSize &&
          y >= handle.y && y <= handle.y + handleSize) {
        return handle.id;
      }
    }
    return null;
  }

  function moveSelectedArea(deltaX: number, deltaY: number) {
    const selectedArea = currentCropAreas.find((area: CropArea) => area.id === selectedCropAreaId);
    if (!selectedArea) return;

    const updatedArea = {
      ...selectedArea,
      x: Math.max(0, Math.min(selectedArea.x + deltaX, transform.imageWidth - selectedArea.width)),
      y: Math.max(0, Math.min(selectedArea.y + deltaY, transform.imageHeight - selectedArea.height))
    };

    onCropAreaUpdate?.(updatedArea);
  }

  function resizeSelectedArea(deltaX: number, deltaY: number) {
    const selectedArea = currentCropAreas.find((area: CropArea) => area.id === selectedCropAreaId);
    if (!selectedArea) return;

    let updatedArea = { ...selectedArea };

    switch (resizeHandle) {
      case 'se':
        updatedArea.width = Math.max(1, Math.min(selectedArea.width + deltaX, transform.imageWidth - selectedArea.x));
        updatedArea.height = Math.max(1, Math.min(selectedArea.height + deltaY, transform.imageHeight - selectedArea.y));
        break;
      case 'nw':
        const newX = Math.max(0, selectedArea.x + deltaX);
        const newY = Math.max(0, selectedArea.y + deltaY);
        updatedArea.x = newX;
        updatedArea.y = newY;
        updatedArea.width = Math.max(1, selectedArea.width - (newX - selectedArea.x));
        updatedArea.height = Math.max(1, selectedArea.height - (newY - selectedArea.y));
        break;
      case 'ne':
        const newY2 = Math.max(0, selectedArea.y + deltaY);
        updatedArea.y = newY2;
        updatedArea.width = Math.max(1, Math.min(selectedArea.width + deltaX, transform.imageWidth - selectedArea.x));
        updatedArea.height = Math.max(1, selectedArea.height - (newY2 - selectedArea.y));
        break;
      case 'sw':
        const newX2 = Math.max(0, selectedArea.x + deltaX);
        updatedArea.x = newX2;
        updatedArea.width = Math.max(1, selectedArea.width - (newX2 - selectedArea.x));
        updatedArea.height = Math.max(1, Math.min(selectedArea.height + deltaY, transform.imageHeight - selectedArea.y));
        break;
    }

    onCropAreaUpdate?.(updatedArea);
  }

  // 🎯 更新裁切预览 - 当全局设置变化时重新生成小图片预览
  function updateCropPreviews() {
    if (!currentResource || !isCanvasReady || !loadedImage) {
      console.log('🎯 ImageCanvas: updateCropPreviews - 条件不满足', {
        hasResource: !!currentResource,
        isCanvasReady,
        hasImage: !!loadedImage
      });
      return;
    }

    console.log('🎯 ImageCanvas: 更新裁切预览', {
      areasCount: currentResource.cropData?.areas?.length || 0,
      layoutSettings: currentLayoutSettings,
      cropSettings: currentGlobalCropSettings,
      hasExistingAreas: !!(currentResource.cropData?.areas && currentResource.cropData.areas.length > 0)
    });

    // 🎯 如果有现有的裁切区域，需要重新生成以应用新的设置
    if (currentResource.cropData?.areas && currentResource.cropData.areas.length > 0) {
      console.log('🎯 ImageCanvas: 检测到现有裁切区域，重新生成以应用新设置');

      // 检查当前的裁切模式并重新生成
      const unsubscribe = selectionStore.subscribe((state) => {
        const cropSettings = state.cropSettings;

        if (cropSettings.gridMode === true) {
          console.log('🎯 ImageCanvas: 网格模式开启，重新生成网格');
          generateGridCropAreas();
        } else if (cropSettings.autoDetect === true) {
          console.log('🎯 ImageCanvas: 自动检测模式开启，重新检测透明区域');
          generateAutoCropAreas();
        } else {
          console.log('🎯 ImageCanvas: 无特定模式，仅重新渲染现有区域');
          renderCanvas();
        }
      });

      // 立即取消订阅，避免内存泄漏
      unsubscribe();
    } else {
      // 没有现有区域，只是重新渲染
      console.log('🎯 ImageCanvas: 无现有裁切区域，仅重新渲染');
      renderCanvas();
    }
  }

  onDestroy(() => {
    console.log('🧹 ImageCanvas: 组件销毁，清理资源');

    // 清理store订阅
    if (selectionUnsubscribe) {
      selectionUnsubscribe();
      selectionUnsubscribe = null;
    }

    if (layoutSettingsUnsubscribe) {
      layoutSettingsUnsubscribe();
      layoutSettingsUnsubscribe = null;
    }

    if (globalCropSettingsUnsubscribe) {
      globalCropSettingsUnsubscribe();
      globalCropSettingsUnsubscribe = null;
    }

    // 清理创建的URL对象
    if (loadedImage?.src.startsWith('blob:')) {
      URL.revokeObjectURL(loadedImage.src);
    }

    // 清理图片资源
    if (loadedImage) {
      loadedImage = null;
    }

    // 清理Canvas上下文
    if (ctx) {
      ctx = null;
    }
  });
</script>

<div class="image-canvas-container">
  <!-- Canvas画布 -->
  <div class="canvas-wrapper">
    {#if isLoading}
      <div class="loading-overlay">
        <div class="loading-spinner"></div>
        <span>加载中...</span>
      </div>
    {/if}

    {#if error}
      <div class="error-overlay">
        <div class="error-icon">❌</div>
        <span>{error}</span>
      </div>
    {/if}

    <canvas
      bind:this={canvasElement}
      class="image-canvas"
      class:loading={isLoading}
      class:error={!!error}
      onmousedown={handleMouseDown}
      onmousemove={handleMouseMove}
      onmouseup={handleMouseUp}
      onmouseleave={handleMouseUp}
    ></canvas>
  </div>
</div>

<style>
  .image-canvas-container {
    display: flex;
    flex-direction: column;
    background: #1a1a1a; /* 🎯 与AtlasCanvas背景色保持一致 */
    overflow: hidden; /* 🎯 删除滚动条 */
    width: fit-content; /* 🎯 容器大小等于canvas大小 */
    height: fit-content; /* 🎯 容器大小等于canvas大小 */
    /* 🎯 删除边框和圆角 */
  }



  .canvas-wrapper {
    position: relative;
    overflow: hidden; /* 🎯 删除滚动条 */
    background: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    width: fit-content; /* 🎯 容器大小等于canvas大小 */
    height: fit-content; /* 🎯 容器大小等于canvas大小 */
  }

  .image-canvas {
    display: block;
    cursor: crosshair;
    background: #2a2a2a; /* 🎯 与AtlasCanvas保持一致的canvas背景 */
    /* 🎯 删除边框、过渡效果和尺寸限制 */
  }

  .image-canvas.loading {
    opacity: 0.5;
  }

  .image-canvas.error {
    opacity: 0.3;
  }

  .loading-overlay,
  .error-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: var(--theme-text-secondary);
    z-index: 10;
  }

  .loading-spinner {
    width: 24px;
    height: 24px;
    border: 2px solid var(--theme-border);
    border-top: 2px solid var(--theme-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .error-icon {
    font-size: 2rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
