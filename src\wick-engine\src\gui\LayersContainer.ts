/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { LayerCreateLabel } from "./LayerCreateLabel";

export class LayersContainer extends GUIElement {
  protected layerCreateLabel: LayerCreateLabel;

  constructor(model: Base) {
    super(model);

    this.layerCreateLabel = new LayerCreateLabel(model);
  }

  draw(): void {
    const ctx = this.ctx;

    // Background
    ctx.fillStyle = GUIElement.TIMELINE_BACKGROUND_COLOR;
    ctx.beginPath();
    ctx.rect(
      0,
      this.project.scrollY,
      GUIElement.LAYERS_CONTAINER_WIDTH,
      this.canvas.height
    );
    ctx.fill();

    // Draw layers
    this.model.layers.forEach((layer) => {
      ctx.save();
      ctx.translate(0, layer.index * this.gridCellHeight);
      layer.guiElement.draw();
      ctx.restore();
    });

    // New layer creation label
    ctx.save();
    ctx.translate(0, this.model.layers.length * this.gridCellHeight);
    this.layerCreateLabel.draw();
    ctx.restore();
  }
}
