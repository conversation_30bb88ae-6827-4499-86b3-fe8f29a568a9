{"name": "GameSprite Stuio", "version": "0.1.0", "description": "", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "tauri": "tauri", "build:steam": "node scripts/build-for-steam.cjs", "tauri:build": "tauri build", "tauri:dev": "tauri dev"}, "license": "MIT", "dependencies": {"@mapbox/shelf-pack": "^3.2.0", "@skeletonlabs/skeleton": "^3.1.3", "@tauri-apps/api": "^2", "@tauri-apps/plugin-dialog": "^2.2.2", "@tauri-apps/plugin-fs": "^2.3.0", "@tauri-apps/plugin-opener": "^2", "maxrects-packer": "^2.7.3", "paneforge": "^0.0.6", "pixi.js": "^8.10.1", "potpack": "^2.0.0", "svelte-dnd-action": "^0.9.61", "svelte-i18n": "^4.0.1"}, "devDependencies": {"@sveltejs/adapter-static": "^3.0.6", "@sveltejs/kit": "^2.9.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tauri-apps/cli": "^2", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript": "~5.6.2", "vite": "^6.0.3"}}