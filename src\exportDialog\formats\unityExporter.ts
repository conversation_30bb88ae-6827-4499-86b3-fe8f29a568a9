import { invoke } from '@tauri-apps/api/core';
import type { ExportConfig, ExportItem, ExportResult, ExportProgress } from '../exportTypes';
import { ensureExtension, sanitizeFileName, getMimeType, createImageFromBuffer, canvasToBlob } from '../exportUtils';

export class UnityExporter {
  private progressCallback?: (progress: ExportProgress) => void;

  constructor(progressCallback?: (progress: ExportProgress) => void) {
    this.progressCallback = progressCallback;
  }

  private reportProgress(current: number, total: number, fileName: string, status: 'processing' | 'completed' | 'error') {
    if (this.progressCallback) {
      this.progressCallback({
        current,
        total,
        fileName,
        status
      });
    }
  }

  async export(items: ExportItem[], config: ExportConfig, selectedFolder: string): Promise<ExportResult> {
    console.log('🚀 UnityExporter: 开始导出', { items, config, selectedFolder });

    const exportedFiles: Array<{ name: string; path: string; size: number; type: 'data' | 'image' }> = [];
    let totalSize = 0;
    const totalTasks = items.length;

    // 处理每个导出项
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      this.reportProgress(i + 1, totalTasks, `${item.name}.tpsheet`, 'processing');

      if (!item.cropAreas || item.cropAreas.length === 0) {
        console.warn(`跳过没有裁切数据的资源: ${item.name}`);
        continue;
      }

      // 导出原始图片（如果需要）
      if (config.includeOriginal && item.resource.buffer) {
        try {
          const mimeType = getMimeType(item.resource.path);
          const originalImg = await createImageFromBuffer(item.resource.buffer, mimeType);

          // 创建原图blob
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (!ctx) throw new Error('无法创建Canvas上下文');

          canvas.width = originalImg.width;
          canvas.height = originalImg.height;
          ctx.drawImage(originalImg, 0, 0);

          const originalBlob = await canvasToBlob(canvas, 'image/png');
          const arrayBuffer = await originalBlob.arrayBuffer();
          const uint8Array = new Uint8Array(arrayBuffer);

          // 生成原图文件名
          const originalFileName = this.generateImageFileName(item.name);

          console.log('🚀 UnityExporter: 导出原始图片', {
            targetDirectory: selectedFolder,
            fileName: originalFileName
          });

          const originalPath = await invoke<string>('export_single_file', {
            filePath: `${selectedFolder}/${originalFileName}`,
            fileData: Array.from(uint8Array)
          });

          console.log('✅ UnityExporter: 原始图片导出成功', { originalPath });

          exportedFiles.push({
            name: originalFileName,
            path: originalPath,
            size: originalBlob.size,
            type: 'image'
          });
          totalSize += originalBlob.size;

        } catch (error) {
          console.error('❌ UnityExporter: 原始图片导出失败', error);
          // 不中断Unity导出，只是警告
        }
      }

      // 生成Unity .tpsheet数据
      const tpsheetContent = this.generateUnityTpsheet(item);

      // 生成文件名
      const fileName = this.generateFileName(item.name, config);

      // 使用Rust后端保存文件
      try {
        const encoder = new TextEncoder();
        const fileData = encoder.encode(tpsheetContent);

        console.log('🚀 UnityExporter: 调用Rust后端导出', {
          targetDirectory: selectedFolder,
          fileName: fileName
        });

        const exportedPath = await invoke<string>('export_single_file', {
          filePath: `${selectedFolder}/${fileName}`,
          fileData: Array.from(fileData)
        });

        console.log('✅ UnityExporter: Rust后端导出成功', { exportedPath });

        const fileSize = fileData.length;
        exportedFiles.push({
          name: fileName,
          path: exportedPath,
          size: fileSize,
          type: 'data'
        });
        totalSize += fileSize;

      } catch (error) {
        console.error('❌ UnityExporter: Rust后端导出失败', error);
        throw new Error(`导出Unity .tpsheet文件失败: ${error}`);
      }
    }

    this.reportProgress(totalTasks, totalTasks, '导出完成', 'completed');

    return {
      success: true,
      message: `成功导出 ${exportedFiles.length} 个文件`,
      files: exportedFiles,
      totalSize
    };
  }

  private generateUnityTpsheet(item: ExportItem): string {
    const imageFileName = this.generateImageFileName(item.name);

    let content = `#
# Sprite sheet data for Unity.
# Generated by GameSprite Studio
#
:format=40300
:texture=${imageFileName}
:size=${item.resource.width || 0}x${item.resource.height || 0}
:pivotpoints=enabled
:borders=disabled
:alphahandling=ClearTransparentPixels

`;

    // 为每个裁切区域生成精灵数据
    item.cropAreas?.forEach((area, index) => {
      const spriteName = area.name || `${item.name.replace(/\.[^/.]+$/, '')}-${index}`;

      // Unity完整格式：name;x;y;w;h;px;py;border_l;border_r;border_t;border_b;mesh_type;vertices;...
      // 简化版本，包含必要的字段
      content += `${spriteName};${area.x};${area.y};${area.width};${area.height}; 0.5;0.5; 0;0;0;0; 4;${area.width};0;0;0;0;${area.width};${area.height};${area.height}; 2;1;2;3;0;1;3\n`;
    });

    return content;
  }

  private generateFileName(baseName: string, config: ExportConfig): string {
    let fileName: string;

    // 🎯 如果用户设置了文件名，完整使用设置的文件名
    if (config.fileName && config.fileName.trim() !== '') {
      fileName = config.fileName.replace(/\.[^/.]+$/, ''); // 移除扩展名
    } else {
      fileName = baseName.replace(/\.[^/.]+$/, '');
    }

    return ensureExtension(sanitizeFileName(fileName), 'tpsheet');
  }

  private generateImageFileName(baseName: string): string {
    const nameWithoutExt = baseName.replace(/\.[^/.]+$/, '');
    return ensureExtension(sanitizeFileName(nameWithoutExt), 'png');
  }


}
