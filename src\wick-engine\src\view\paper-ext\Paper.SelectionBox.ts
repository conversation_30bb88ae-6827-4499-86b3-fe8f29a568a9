/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Paper.js-drawing-tools.
 *
 * Paper.js-drawing-tools is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Paper.js-drawing-tools is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Paper.js-drawing-tools.  If not, see <https://www.gnu.org/licenses/>.
 */

import * as paper from "paper";

interface SelectionBoxMode {
  mode: "contains" | "intersects";
}

class SelectionBox {
  private paper: any;
  private _start: paper.Point;
  private _end: paper.Point;
  private _items: paper.Item[];
  private _active: boolean;
  private _box: paper.Path.Rectangle;
  private _mode: "contains" | "intersects";

  constructor(paperContext: any) {
    this.paper = paperContext;

    this._start = new this.paper.Point();
    this._end = new this.paper.Point();
    this._items = [];
    this._active = false;
    this._box = new this.paper.Path.Rectangle({ insert: false });
    this._mode = "intersects";
  }

  start(point: paper.Point): void {
    this._active = true;
    this._start = point;
    this._end = point;
    this._rebuildBox();
  }

  drag(point: paper.Point): void {
    this._end = point;
    this._rebuildBox();
  }

  end(point: paper.Point): void {
    this._end = point;
    this._active = false;

    this._rebuildBox();
    this._box.remove();

    this._items = this._itemsInBox(this._box);
  }

  get items(): paper.Item[] {
    return this._items;
  }

  get active(): boolean {
    return this._active;
  }

  get mode(): "contains" | "intersects" {
    return this._mode;
  }

  set mode(mode: "contains" | "intersects") {
    if (mode !== "contains" && mode !== "intersects") {
      throw new Error("SelectionBox.mode: invalid mode");
    }
    this._mode = mode;
  }

  private _rebuildBox(): void {
    this._box.remove();
    this._box = new this.paper.Path.Rectangle({
      from: this._start,
      to: this._end,
      strokeWidth: 1,
      strokeColor: "black",
    });
  }

  private _itemsInBox(box: paper.Path.Rectangle): paper.Item[] {
    const checkItems: paper.Item[] = [];
    this._getSelectableLayers().forEach((layer) => {
      layer.children.forEach((child) => {
        checkItems.push(child);
      });
    });

    const items: paper.Item[] = [];
    checkItems.forEach((item) => {
      if (this.mode === "contains") {
        if (this._box.bounds.contains(item.bounds)) {
          items.push(item);
        }
      } else if (this.mode === "intersects") {
        if (this._shapesIntersect(item, this._box)) {
          items.push(item);
        }
      }
    });

    return items;
  }

  private _shapesIntersect(itemA: paper.Item, itemB: paper.Item): boolean {
    if (itemA instanceof this.paper.Group) {
      let intersects = false;
      const itemBClone = itemB.clone();
      itemBClone.transform(itemA.matrix.inverted());
      itemA.children.forEach((child) => {
        if (!intersects && this._shapesIntersect(child, itemBClone)) {
          intersects = true;
        }
      });
      return intersects;
    } else {
      const shapesDoIntersect = itemB.intersects(itemA);
      const boundsContain = itemB.bounds.contains(itemA.bounds);
      if (shapesDoIntersect || boundsContain) {
        return true;
      }
      return false;
    }
  }

  private _getSelectableLayers(): paper.Layer[] {
    return this.paper.project.layers.filter((layer: paper.Layer) => {
      return !layer.locked;
    });
  }
}

(paper as any).PaperScope.inject({
  SelectionBox: SelectionBox,
});
