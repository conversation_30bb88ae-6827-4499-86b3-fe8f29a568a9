/**
 * 图片裁剪处理器
 * 使用Tauri Rust后端进行高性能图片裁剪
 */

import { invoke } from '@tauri-apps/api/core';
import { listen, type UnlistenFn } from '@tauri-apps/api/event';
import type { ImageResource, SplitSettings, CropArea, CropData } from '../types/imageType';
import { resourceStore } from '../stores/resourceStore';

// Tauri命令接口定义
export interface CropImageRequest {
  imageId: string;
  imageName: string;
  imageData: number[];           // ArrayBuffer转换为number[]
  imageWidth: number;
  imageHeight: number;
  splitSettings: {
    cellWidth: number;
    cellHeight: number;
    gridMode: boolean;
    padding: number;
    spacing: number;
    autoDetect: boolean;
    minWidth: number;
    minHeight: number;
  };
  cropAreas?: CropArea[];        // 可选：前端生成的裁切区域
  outputFormat: 'png' | 'webp' | 'jpeg';
  quality: number;
}

export interface CroppedImageResult {
  id: string;
  name: string;
  data: number[];               // Vec<u8>转换为number[]
  width: number;
  height: number;
  format: string;
  sizeBytes: number;
  cellIndex?: number;
  // 🎯 新增：ProcessedImageData支持
  dataUrl?: string;
  thumbnailUrl?: string;
  previewUrl?: string;
}

export interface CropImageResponse {
  success: boolean;
  imageId: string;
  totalCropped: number;
  croppedImages: CroppedImageResult[];
  processingTimeMs: number;
  error?: string;
  // 🎯 新增：返回生成的cropAreas信息
  generatedCropAreas?: CropArea[];
}

// 进度回调接口
export interface CropProgress {
  completed: number;
  total: number;
  percentage: number;
  currentArea: string;
}

export type CropProgressCallback = (progress: CropProgress) => void;

// 裁剪状态
export interface CropState {
  isProcessing: boolean;
  progress: number;
  currentArea: string;
  totalAreas: number;
  completedAreas: number;
  error?: string;
}

/**
 * 图片裁剪器类
 */
export class ImageCropper {
  private progressUnlisten?: UnlistenFn;
  private currentState: CropState = {
    isProcessing: false,
    progress: 0,
    currentArea: '',
    totalAreas: 0,
    completedAreas: 0
  };

  /**
   * 获取当前裁剪状态
   */
  get state(): CropState {
    return { ...this.currentState };
  }

  /**
   * 裁剪图片
   * @param imageResource 图片资源
   * @param options 裁剪选项
   * @param onProgress 进度回调
   * @returns Promise<ImageResource[]> 裁剪后的图片数组
   */
  async cropImage(
    imageResource: ImageResource,
    options: {
      outputFormat?: 'png' | 'webp' | 'jpeg';
      quality?: number;
      useFrontendCropAreas?: boolean;
      cropAreas?: CropArea[];
    } = {},
    onProgress?: CropProgressCallback
  ): Promise<ImageResource[]> {

    // 验证输入
    if (!imageResource.data) {
      throw new Error('图片数据不存在');
    }

    if (!imageResource.splitSettings) {
      throw new Error('拆分设置不存在');
    }

    if (!imageResource.width || !imageResource.height) {
      throw new Error('图片尺寸信息不存在');
    }

    console.log('🎯 ImageCropper: 开始裁剪图片', {
      imageId: imageResource.id,
      imageName: imageResource.name,
      imageSize: `${imageResource.width}×${imageResource.height}`,
      splitSettings: imageResource.splitSettings
    });

    try {
      // 设置处理状态
      this.currentState = {
        isProcessing: true,
        progress: 0,
        currentArea: '准备中...',
        totalAreas: 0,
        completedAreas: 0
      };

      // 监听进度事件
      await this.setupProgressListener(onProgress);

      // 准备请求数据
      const request: CropImageRequest = {
        imageId: imageResource.id,
        imageName: imageResource.name,
        imageData: Array.from(new Uint8Array(imageResource.data)), // ArrayBuffer → number[]
        imageWidth: imageResource.width,
        imageHeight: imageResource.height,
        splitSettings: {
          cellWidth: imageResource.splitSettings.cellWidth,
          cellHeight: imageResource.splitSettings.cellHeight,
          gridMode: imageResource.splitSettings.gridMode,
          padding: imageResource.splitSettings.padding,
          spacing: imageResource.splitSettings.spacing,
          autoDetect: imageResource.splitSettings.autoDetect,
          minWidth: imageResource.splitSettings.minWidth,
          minHeight: imageResource.splitSettings.minHeight,
        },
        cropAreas: options.useFrontendCropAreas ? options.cropAreas : undefined,
        outputFormat: options.outputFormat || 'png',
        quality: options.quality || 0.9,
      };

      console.log('🚀 ImageCropper: 调用Rust裁剪命令', {
        dataSize: request.imageData.length,
        outputFormat: request.outputFormat,
        quality: request.quality
      });

      // 调用Rust裁剪命令
      const response = await invoke<CropImageResponse>('crop_image_batch', { request });

      if (!response.success) {
        throw new Error(response.error || '裁剪失败');
      }

      console.log('✅ ImageCropper: Rust裁剪完成', {
        totalCropped: response.totalCropped,
        processingTime: `${response.processingTimeMs}ms`,
        averageTimePerImage: `${(response.processingTimeMs / response.totalCropped).toFixed(2)}ms`
      });

      // 转换结果为ImageResource[]
      const croppedImages = this.convertResultsToImageResources(
        response.croppedImages,
        imageResource
      );

      // 🎯 生成cropData
      const cropData = this.generateCropDataFromResponse(response, imageResource);

      // 更新原始资源的cropImage和cropData字段
      await this.updateResourceWithCroppedData(imageResource.id, croppedImages, cropData);

      // 更新最终状态
      this.currentState = {
        isProcessing: false,
        progress: 100,
        currentArea: '完成',
        totalAreas: response.totalCropped,
        completedAreas: response.totalCropped
      };

      return croppedImages;

    } catch (error) {
      console.error('❌ ImageCropper: 裁剪失败', error);

      this.currentState = {
        isProcessing: false,
        progress: 0,
        currentArea: '失败',
        totalAreas: 0,
        completedAreas: 0,
        error: error instanceof Error ? error.message : '未知错误'
      };

      throw error;
    } finally {
      // 清理进度监听器
      await this.cleanupProgressListener();
    }
  }

  /**
   * 设置进度监听器
   */
  private async setupProgressListener(onProgress?: CropProgressCallback): Promise<void> {
    this.progressUnlisten = await listen('crop_progress', (event) => {
      const progress = event.payload as CropProgress;

      // 更新内部状态
      this.currentState = {
        ...this.currentState,
        progress: progress.percentage,
        currentArea: progress.currentArea,
        totalAreas: progress.total,
        completedAreas: progress.completed
      };

      // 调用外部回调
      onProgress?.(progress);

      console.log(`🎯 ImageCropper: 裁剪进度 ${progress.completed}/${progress.total} (${progress.percentage.toFixed(1)}%) - ${progress.currentArea}`);
    });
  }

  /**
   * 清理进度监听器
   */
  private async cleanupProgressListener(): Promise<void> {
    if (this.progressUnlisten) {
      this.progressUnlisten();
      this.progressUnlisten = undefined;
    }
  }

  /**
   * 转换Rust结果为ImageResource数组
   */
  private convertResultsToImageResources(
    results: CroppedImageResult[],
    originalResource: ImageResource
  ): ImageResource[] {
    return results.map((result, index) => {
      const imageResource: ImageResource = {
        id: `${originalResource.id}_crop_${result.id}`,
        name: result.name,
        type: 'image',
        // 🎯 优先使用dataUrl，如果没有则使用ArrayBuffer
        data: result.dataUrl ? undefined : new Uint8Array(result.data).buffer,
        blobUrl: result.dataUrl, // 🎯 直接使用Rust生成的dataUrl
        width: result.width,
        height: result.height,
        isLoaded: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isGridSplit: false, // 裁剪后的图片不再需要网格

        // 🎯 ProcessedImageData支持
        processedData: result.dataUrl ? {
          original: {
            width: result.width,
            height: result.height,
            dataUrl: result.dataUrl
          },
          thumbnail: result.thumbnailUrl ? {
            width: result.width,
            height: result.height,
            dataUrl: result.thumbnailUrl,
            size: 128
          } : undefined,
          preview: result.previewUrl ? {
            width: result.width,
            height: result.height,
            dataUrl: result.previewUrl,
            size: 256
          } : undefined,
          processedAt: new Date().toISOString()
        } : undefined,

        // 继承部分原始资源信息
        originalFile: {
          name: result.name,
          size: result.sizeBytes,
          lastModified: Date.now(),
          type: `image/${result.format}`,
        }
      };

      console.log(`🎨 ImageCropper: 转换裁剪结果 ${index + 1}/${results.length}`, {
        id: imageResource.id,
        name: imageResource.name,
        size: `${result.width}×${result.height}`,
        format: result.format,
        sizeBytes: result.sizeBytes,
        hasDataUrl: !!result.dataUrl,
        hasProcessedData: !!imageResource.processedData
      });

      return imageResource;
    });
  }

  /**
   * 生成cropData从响应结果
   */
  private generateCropDataFromResponse(
    response: CropImageResponse,
    imageResource: ImageResource
  ): CropData {
    // 🎯 从裁剪结果生成cropAreas
    const cropAreas: CropArea[] = response.croppedImages.map((result) => {
      // 🎯 根据cellIndex计算坐标位置
      const settings = imageResource.splitSettings!;
      const cellIndex = result.cellIndex || 0;
      const cols = Math.floor((imageResource.width! - settings.padding * 2) / (settings.cellWidth + settings.spacing));
      const row = Math.floor(cellIndex / cols);
      const col = cellIndex % cols;

      const x = settings.padding + col * (settings.cellWidth + settings.spacing);
      const y = settings.padding + row * (settings.cellHeight + settings.spacing);

      return {
        id: result.id,
        x: x,
        y: y,
        width: result.width,
        height: result.height,
        name: result.name,
        selected: false
      };
    });

    // 🎯 计算网格信息
    const settings = imageResource.splitSettings!;
    const availableWidth = imageResource.width! - settings.padding * 2;
    const availableHeight = imageResource.height! - settings.padding * 2;
    const cellStepWidth = settings.cellWidth + settings.spacing;
    const cellStepHeight = settings.cellHeight + settings.spacing;
    const cols = Math.floor(availableWidth / cellStepWidth);
    const rows = Math.floor(availableHeight / cellStepHeight);

    const cropData: CropData = {
      areas: cropAreas,
      cellWidth: settings.cellWidth,
      cellHeight: settings.cellHeight,
      gridMode: settings.gridMode,
      gridRows: rows,
      gridCols: cols,
      padding: settings.padding,
      spacing: settings.spacing,
      autoTrim: settings.autoDetect,
      lastModified: new Date()
    };

    console.log('🎯 ImageCropper: 生成cropData', {
      areasCount: cropAreas.length,
      gridSize: `${cols}×${rows}`,
      settings: settings
    });

    return cropData;
  }

  /**
   * 更新资源的cropImage和cropData字段
   */
  private async updateResourceWithCroppedData(
    resourceId: string,
    croppedImages: ImageResource[],
    cropData: CropData
  ): Promise<void> {
    // 参考SplitPanel中的更新方式
    resourceStore.update(state => {
      if (state.selectedResource?.id === resourceId && state.selectedResource.type === 'image') {
        (state.selectedResource as ImageResource).cropImage = croppedImages;
        (state.selectedResource as ImageResource).cropData = cropData;
        state.selectedResource.updatedAt = new Date().toISOString();
        return {
          ...state,
        };
      }
      return state;
    });

    console.log('💾 ImageCropper: 已更新资源cropImage和cropData字段', {
      resourceId,
      croppedCount: croppedImages.length,
      cropAreasCount: cropData.areas.length
    });
  }

  /**
   * 取消当前裁剪操作
   */
  async cancel(): Promise<void> {
    if (this.currentState.isProcessing) {
      await this.cleanupProgressListener();

      this.currentState = {
        isProcessing: false,
        progress: 0,
        currentArea: '已取消',
        totalAreas: 0,
        completedAreas: 0,
        error: '用户取消操作'
      };

      console.log('🛑 ImageCropper: 裁剪操作已取消');
    }
  }

  /**
   * 重置状态
   */
  reset(): void {
    this.currentState = {
      isProcessing: false,
      progress: 0,
      currentArea: '',
      totalAreas: 0,
      completedAreas: 0
    };
  }
}

// 全局单例实例
export const imageCropper = new ImageCropper();
