/**
 * 项目相关的类型定义
 */

import type { ResourceItem, SplitSettings, CropData } from './imageType';

// 项目文件的根结构
export interface ProjectFile {
    // 项目元信息
    metadata: {
        version: string;                    // 项目文件版本
        projectName: string;                // 项目名称
        createdAt: string;                  // 创建时间
        updatedAt: string;                  // 最后更新时间
        createdBy?: string;                 // 创建者
        description?: string;               // 项目描述
        tags?: string[];                    // 项目标签
    };

    // 项目设置
    settings: {
        // 默认合并设置
        defaultMergeSettings: {
            algorithm: 'maxrects' | 'shelf' | 'potpack' | 'guillotine';
            padding: number;
            allowRotation: boolean;
            powerOfTwo: boolean;
        };

        // UI设置
        uiSettings?: {
            theme?: 'light' | 'dark';
            panelLayout?: any;
            lastActiveAtlasId?: string;
        };

        // 导出设置
        exportSettings?: {
            format: 'png' | 'jpg' | 'webp';
            quality?: number;
            includeMetadata?: boolean;
        };
    };

    // 资源数据（核心内容）
    resources: {
        // 根级资源列表
        items: SerializableResourceItem[];

        // 资源索引（用于快速查找）
        index: {
            [id: string]: {
                type: 'image' | 'folder' | 'atlas';
                parentId?: string;
                path: string[];  // 从根到该资源的路径
            };
        };

        // 资源统计
        statistics: {
            totalImages: number;
            totalFolders: number;
            totalAtlases: number;
            totalSize: number;  // 总数据大小（字节）
        };
    };

    // 工作区状态
    workspace?: {
        openAtlases: string[];              // 当前打开的图集ID列表
        activeAtlasId?: string;             // 当前活动的图集ID
        selectedResourceIds: string[];      // 当前选中的资源ID列表
        expandedFolderIds: string[];        // 展开的文件夹ID列表
    };
}

// 序列化后的资源项（用于保存）
export type SerializableResourceItem =
    | SerializableImageResource
    | SerializableFolderResource
    | SerializableAtlasResource;

// 序列化的图片资源
export interface SerializableImageResource {
    id: string;
    name: string;
    type: 'image';
    path?: string;
    createdAt: string;
    updatedAt: string;

    // 图片数据（base64编码）
    dataBase64: string;
    width: number;
    height: number;

    // 原始文件信息
    originalFile: {
        name: string;
        size: number;
        lastModified: number;
        type: string;
        webkitRelativePath?: string;
    };

    // 拆分信息
    splitInfo?: {
        parentId?: string;
        region?: {
            x: number;
            y: number;
            width: number;
            height: number;
        };
        splitMethod?: 'manual' | 'auto' | 'grid';
    };

    // 🎯 SplitPanel 设置 - 修复：添加缺失的splitSettings字段
    splitSettings?: SplitSettings;

    // 🎯 SpriteCutDialog 保存的裁切数据 - 修复：添加缺失的cropData字段
    cropData?: CropData;

    // 子图片（拆分后的）
    children?: SerializableImageResource[];

    // 数据校验
    checksum?: string;  // 数据完整性校验
}

// 序列化的文件夹资源
export interface SerializableFolderResource {
    id: string;
    name: string;
    type: 'folder';
    path?: string;
    createdAt: string;
    updatedAt: string;

    // 子资源
    children: SerializableResourceItem[];

    // 文件夹元信息
    metadata?: {
        description?: string;
        color?: string;  // 文件夹颜色标记
        icon?: string;   // 文件夹图标
    };
}

// 序列化的图集资源
export interface SerializableAtlasResource {
    id: string;
    name: string;
    type: 'atlas';
    createdAt: string;
    updatedAt: string;

    // 图集包含的图片引用（只保存ID，不重复保存数据）
    children: {
        resourceId: string;     // 引用的图片资源ID
        resourcePath: string[]; // 资源在项目中的路径
    }[];

    // 图集设置
    settings: {
        algorithm: string;
        padding: number;
        allowRotation: boolean;
        powerOfTwo: boolean;
    };

    // 布局结果缓存
    layoutCache?: {
        algorithm: string;
        result: any;
        timestamp: number;
        hash: string;  // 基于children和settings的哈希值
    };

    // 导出历史
    exportHistory?: {
        timestamp: string;
        format: string;
        size: { width: number; height: number };
        efficiency: number;
    }[];
}

// 运行时项目数据结构
export interface ProjectData {
    metadata: ProjectFile['metadata'];
    settings: ProjectFile['settings'];
    resources: ResourceItem[];
    workspace?: ProjectFile['workspace'];
}

// 项目错误类型
export interface ProjectError {
    code: string;
    message: string;
    details?: any;
    timestamp?: string;
}

// 加载结果
export interface LoadResult<T = any> {
    success: boolean;
    data?: T;
    error?: ProjectError;
    warnings?: string[];
}

// 保存结果
export interface SaveResult {
    success: boolean;
    filePath?: string;
    size?: number;
    error?: ProjectError;
}

// 版本迁移接口
export interface VersionMigration {
    fromVersion: string;
    toVersion: string;
    description: string;
    migrate(data: any): Promise<any>;
}

// 项目验证结果
export interface ValidationResult {
    isValid: boolean;
    errors: ProjectError[];
    warnings: string[];
    statistics: {
        totalResources: number;
        corruptedResources: number;
        missingReferences: number;
    };
}
