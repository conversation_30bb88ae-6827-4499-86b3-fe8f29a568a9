<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- GameSprite Studio Menu Logo -->
  <defs>
    <linearGradient id="menuPrimaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#718096;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4a5568;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="menuSecondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景网格 -->
  <g opacity="0.3">
    <rect x="2" y="2" width="20" height="20" fill="none" stroke="currentColor" stroke-width="0.5"/>
    <line x1="8" y1="2" x2="8" y2="22" stroke="currentColor" stroke-width="0.3"/>
    <line x1="14" y1="2" x2="14" y2="22" stroke="currentColor" stroke-width="0.3"/>
    <line x1="2" y1="8" x2="22" y2="8" stroke="currentColor" stroke-width="0.3"/>
    <line x1="2" y1="14" x2="22" y2="14" stroke="currentColor" stroke-width="0.3"/>
  </g>
  
  <!-- 像素化精灵块 -->
  <rect x="3" y="3" width="5" height="5" fill="url(#menuPrimaryGradient)" opacity="0.8" rx="0.5"/>
  <rect x="9" y="3" width="5" height="5" fill="url(#menuSecondaryGradient)" opacity="0.9" rx="0.5"/>
  <rect x="15" y="3" width="6" height="5" fill="#22c55e" opacity="0.7" rx="0.5"/>
  
  <rect x="3" y="9" width="5" height="5" fill="url(#menuSecondaryGradient)" opacity="0.8" rx="0.5"/>
  <rect x="9" y="9" width="5" height="5" fill="url(#menuPrimaryGradient)" opacity="0.9" rx="0.5"/>
  <rect x="15" y="9" width="6" height="5" fill="#fbbf24" opacity="0.6" rx="0.5"/>
  
  <rect x="3" y="15" width="5" height="6" fill="#3b82f6" opacity="0.7" rx="0.5"/>
  <rect x="9" y="15" width="5" height="6" fill="url(#menuPrimaryGradient)" opacity="0.8" rx="0.5"/>
  <rect x="15" y="15" width="6" height="6" fill="url(#menuSecondaryGradient)" opacity="0.9" rx="0.5"/>
  
  <!-- 高亮边框 -->
  <rect x="2" y="2" width="20" height="20" fill="none" stroke="currentColor" stroke-width="1" opacity="0.6" rx="1"/>
  
  <!-- 工具指示器 -->
  <circle cx="20" cy="4" r="2" fill="url(#menuSecondaryGradient)" opacity="0.8"/>
  <circle cx="20" cy="4" r="1" fill="white" opacity="0.9"/>
</svg>
