/**
 * GameSprite Engine - 入口文件
 */

// 导出核心类型
export * from './types.js';

// 导出核心类
export { GameObject } from './GameObject.js';
export { Scene } from './Scene.js';
export { Project, type ProjectSettings } from './Project.js';
export { Viewport } from './Viewport.js';
export { GameSpriteEngine } from './GameSpriteEngine.js';
export { PixiRenderer } from './PixiRenderer.js';

// 导出系统类
export { EventSystem, ENGINE_EVENTS, type EventCallback, type EngineEventName } from './EventSystem.js';
export { HotkeySystem, COMMON_HOTKEYS, type HotkeyConfig, type HotkeyEvent } from './HotkeySystem.js';
export { System, getSystem, createEngine as createEngineFromSystem } from './System.js';

// 版本信息
export const VERSION = '1.0.0-simple';

// 创建引擎实例的便捷函数
export async function createEngine(
  canvas: HTMLCanvasElement,
  options?: import('./types.js').EngineOptions
): Promise<import('./GameSpriteEngine.js').GameSpriteEngine> {
  const { GameSpriteEngine } = await import('./GameSpriteEngine.js');
  const engine = new GameSpriteEngine();
  await engine.initialize(canvas, options);
  return engine;
}

// 默认导出
export { GameSpriteEngine as default } from './GameSpriteEngine.js';
