/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";

export class Icons extends GUIElement {
  protected _iconType: string;
  protected _iconSize: number;

  constructor(model: Base, iconType: string, iconSize: number) {
    super(model);
    this._iconType = iconType;
    this._iconSize = iconSize;
  }

  draw(): void {
    super.draw();

    const ctx = this.ctx;

    // Draw icon background
    ctx.fillStyle = GUIElement.ICON_BACKGROUND_COLOR;
    ctx.fillRect(0, 0, this._iconSize, this._iconSize);

    // Draw icon border
    ctx.strokeStyle = GUIElement.ICON_BORDER_COLOR;
    ctx.lineWidth = 1;
    ctx.strokeRect(0, 0, this._iconSize, this._iconSize);

    // Draw icon symbol
    ctx.fillStyle = GUIElement.ICON_SYMBOL_COLOR;
    ctx.font = `${this._iconSize * 0.6}px FontAwesome`;
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    ctx.fillText(this._iconType, this._iconSize / 2, this._iconSize / 2);
  }
}
