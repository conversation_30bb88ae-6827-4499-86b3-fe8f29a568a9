use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use std::sync::Mutex;

// 当前项目信息结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CurrentProjectInfo {
    pub project_path: String,
    pub project_file_path: String,
    pub timestamp: u64,
}

// 全局项目状态容器
static CURRENT_PROJECT: Mutex<Option<CurrentProjectInfo>> = Mutex::new(None);

#[derive(Debug, Serialize, Deserialize)]
pub struct ProjectConfig {
    pub name: String,
    pub path: String,
    pub engine_version: String,
    pub script_files: Vec<String>,
    pub data_path: String,
    pub img_path: String,
    pub audio_path: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ScriptInfo {
    pub url: String,
    pub local_path: String,
    pub is_required: bool,
}

/// 获取项目配置
#[tauri::command]
pub async fn get_project_config(project_path: String) -> Result<ProjectConfig, String> {
    let config_path = PathBuf::from(&project_path).join("project.json");

    if !config_path.exists() {
        return Err("项目配置文件不存在".to_string());
    }

    let config_content =
        fs::read_to_string(config_path).map_err(|e| format!("读取配置文件失败: {}", e))?;

    let config: ProjectConfig =
        serde_json::from_str(&config_content).map_err(|e| format!("解析配置文件失败: {}", e))?;

    Ok(config)
}

/// 扫描项目目录获取脚本文件列表
#[tauri::command]
pub async fn scan_project_scripts(project_path: String) -> Result<Vec<ScriptInfo>, String> {
    let project_dir = PathBuf::from(&project_path);

    if !project_dir.exists() {
        return Err("项目目录不存在".to_string());
    }

    let mut scripts = Vec::new();

    // 扫描 js 目录
    let js_dir = project_dir.join("js");
    if js_dir.exists() {
        scan_directory(&js_dir, &project_path, &mut scripts)?;
    }

    // 扫描 plugins 目录
    let plugins_dir = project_dir.join("js").join("plugins");
    if plugins_dir.exists() {
        scan_directory(&plugins_dir, &project_path, &mut scripts)?;
    }

    Ok(scripts)
}

fn scan_directory(
    dir: &PathBuf,
    project_root: &str,
    scripts: &mut Vec<ScriptInfo>,
) -> Result<(), String> {
    let entries = fs::read_dir(dir).map_err(|e| format!("读取目录失败: {}", e))?;

    for entry in entries {
        let entry = entry.map_err(|e| format!("读取目录项失败: {}", e))?;
        let path = entry.path();

        if path.is_file() && path.extension().map_or(false, |ext| ext == "js") {
            let relative_path = path
                .strip_prefix(project_root)
                .map_err(|_| "无法获取相对路径".to_string())?;

            let script_info = ScriptInfo {
                url: format!("file://{}", path.to_string_lossy()),
                local_path: relative_path.to_string_lossy().to_string(),
                is_required: is_required_script(&path),
            };

            scripts.push(script_info);
        } else if path.is_dir() {
            scan_directory(&path, project_root, scripts)?;
        }
    }

    Ok(())
}

fn is_required_script(path: &PathBuf) -> bool {
    if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
        // 定义必需的脚本文件
        matches!(
            filename,
            "plugins.js"
                | "main.js"
                | "rmmz_core.js"
                | "rmmz_managers.js"
                | "rmmz_objects.js"
                | "rmmz_scenes.js"
                | "rmmz_sprites.js"
                | "rmmz_windows.js"
        )
    } else {
        false
    }
}

/// 读取项目文件内容（文本文件）
#[tauri::command]
pub async fn read_project_file(file_path: String) -> Result<String, String> {
    fs::read_to_string(&file_path).map_err(|e| format!("读取文件失败: {}", e))
}

/// 读取项目二进制文件内容
#[tauri::command]
pub async fn read_project_binary_file(file_path: String) -> Result<Vec<u8>, String> {
    fs::read(&file_path).map_err(|e| format!("读取二进制文件失败: {}", e))
}

/// 检查项目是否有效
#[tauri::command]
pub async fn validate_project(project_path: String) -> Result<bool, String> {
    let project_dir = PathBuf::from(&project_path);

    // 检查基本目录结构
    let required_dirs = ["js", "data", "img"];
    for dir_name in &required_dirs {
        let dir_path = project_dir.join(dir_name);
        if !dir_path.exists() {
            return Ok(false);
        }
    }

    // 检查是否有 System.json（RPG Maker 项目的标志）
    let system_json = project_dir.join("data").join("System.json");
    if !system_json.exists() {
        return Ok(false);
    }

    Ok(true)
}

/// 获取项目列表（从最近打开的项目或指定目录）
#[tauri::command]
pub async fn get_recent_projects() -> Result<Vec<ProjectConfig>, String> {
    // 这里可以从配置文件或注册表读取最近的项目
    // 暂时返回空列表
    Ok(Vec::new())
}

/// 选择项目文件 (.rmmzproject)
#[tauri::command]
pub async fn select_project_file() -> Result<Option<String>, String> {
    // 注意：在新版本的 Tauri 中，文件对话框需要在前端处理
    // 这里返回一个提示，实际的文件选择应该在前端使用 @tauri-apps/api/dialog
    Err("请在前端使用 @tauri-apps/api/dialog 来选择 .rmmzproject 文件".to_string())
}

/// 从项目文件路径获取项目目录
#[tauri::command]
pub async fn get_project_directory_from_file(project_file_path: String) -> Result<String, String> {
    let project_file = PathBuf::from(&project_file_path);

    // 检查文件是否存在
    if !project_file.exists() {
        return Err("项目文件不存在".to_string());
    }

    // 检查文件扩展名
    if project_file
        .extension()
        .map_or(true, |ext| ext != "rmmzproject")
    {
        return Err("不是有效的 .rmmzproject 文件".to_string());
    }

    // 获取项目目录（文件所在的目录）
    let project_dir = project_file
        .parent()
        .ok_or("无法获取项目目录".to_string())?;

    Ok(project_dir.to_string_lossy().to_string())
}

/// 保存当前项目信息到全局容器
#[tauri::command]
pub async fn save_current_project_info(
    project_path: String,
    project_file_path: String,
) -> Result<(), String> {
    let current_time = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .map_err(|e| format!("获取时间戳失败: {}", e))?
        .as_secs();

    let project_info = CurrentProjectInfo {
        project_path,
        project_file_path,
        timestamp: current_time,
    };

    let mut current_project = CURRENT_PROJECT
        .lock()
        .map_err(|e| format!("获取全局项目状态锁失败: {}", e))?;

    *current_project = Some(project_info.clone());

    println!("项目信息已保存到全局容器: {:?}", project_info);
    Ok(())
}

/// 从全局容器获取当前项目信息
#[tauri::command]
pub async fn get_current_project_info() -> Result<Option<CurrentProjectInfo>, String> {
    let current_project = CURRENT_PROJECT
        .lock()
        .map_err(|e| format!("获取全局项目状态锁失败: {}", e))?;

    let project_info = current_project.clone();

    if let Some(ref info) = project_info {
        println!("从全局容器获取项目信息: {:?}", info);
    } else {
        println!("全局容器中没有项目信息");
    }

    Ok(project_info)
}

/// 清除全局容器中的当前项目信息
#[tauri::command]
pub async fn clear_current_project_info() -> Result<(), String> {
    let mut current_project = CURRENT_PROJECT
        .lock()
        .map_err(|e| format!("获取全局项目状态锁失败: {}", e))?;

    *current_project = None;
    println!("全局容器中的项目信息已清除");
    Ok(())
}
