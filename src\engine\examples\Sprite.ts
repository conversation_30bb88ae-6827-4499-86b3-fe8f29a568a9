/**
 * GameSprite Engine - Sprite 示例实现
 * 展示如何继承 GameObject 创建具体的显示对象
 */

import * as PIXI from 'pixi.js';
import { GameObject } from '../GameObject.js';
import { Resource } from '../Resource.js';
import type { Renderer } from '../types.js';

// 图片资源实现
export class ImageResource extends Resource {
  private image: HTMLImageElement | null = null;
  private texture: PIXI.Texture | null = null;
  
  constructor(name: string) {
    super('image', name);
  }
  
  async load(source: string | File | ArrayBuffer): Promise<void> {
    this.setState(import('../Resource.js').then(m => m.ResourceState.LOADING));
    this.setProgress(0);
    
    try {
      let imageUrl: string;
      
      if (typeof source === 'string') {
        imageUrl = source;
      } else if (source instanceof File) {
        imageUrl = URL.createObjectURL(source);
      } else {
        const blob = new Blob([source]);
        imageUrl = URL.createObjectURL(blob);
      }
      
      // 加载图片
      this.image = new Image();
      this.image.crossOrigin = 'anonymous';
      
      await new Promise<void>((resolve, reject) => {
        this.image!.onload = () => {
          this.setProgress(0.5);
          resolve();
        };
        this.image!.onerror = () => reject(new Error('Failed to load image'));
        this.image!.src = imageUrl;
      });
      
      // 创建 PIXI 纹理
      this.texture = PIXI.Texture.from(this.image);
      await this.texture.baseTexture.resource.load();
      
      this.setData(this.texture);
      this.setMetadata('width', this.image.width);
      this.setMetadata('height', this.image.height);
      this.setMetadata('source', source);
      
      this.setProgress(1);
      this.setState(import('../Resource.js').then(m => m.ResourceState.LOADED));
      
      // 清理临时 URL
      if (source instanceof File || source instanceof ArrayBuffer) {
        URL.revokeObjectURL(imageUrl);
      }
      
    } catch (error) {
      this.setError(error.message);
    }
  }
  
  unload(): void {
    if (this.texture) {
      this.texture.destroy(true);
      this.texture = null;
    }
    
    this.image = null;
    this.setData(null);
    this.setState(import('../Resource.js').then(m => m.ResourceState.UNLOADED));
  }
  
  clone(): ImageResource {
    const cloned = new ImageResource(this.name + '_copy');
    if (this.loaded && this.texture) {
      cloned.texture = this.texture.clone();
      cloned.setData(cloned.texture);
      cloned.setState(import('../Resource.js').then(m => m.ResourceState.LOADED));
    }
    return cloned;
  }
  
  getSize(): number {
    if (this.image) {
      return this.image.width * this.image.height * 4; // RGBA
    }
    return 0;
  }
  
  getTexture(): PIXI.Texture | null {
    return this.texture;
  }
  
  serialize(): object {
    return {
      ...super.serialize(),
      width: this.getMetadata('width'),
      height: this.getMetadata('height')
    };
  }
}

// Sprite 显示对象实现
export class Sprite extends GameObject {
  private pixiSprite: PIXI.Sprite;
  private imageResource: ImageResource | null = null;
  
  constructor(resource?: ImageResource, name: string = 'Sprite') {
    super(name);
    
    // 创建 PIXI 精灵
    this.pixiSprite = new PIXI.Sprite();
    this.adapter.pixiObject.addChild(this.pixiSprite);
    
    if (resource) {
      this.setTexture(resource);
    }
  }
  
  // === 纹理管理 ===
  setTexture(resource: ImageResource): void {
    this.imageResource = resource;
    
    if (resource.loaded) {
      const texture = resource.getTexture();
      if (texture) {
        this.pixiSprite.texture = texture;
        this.setSize(texture.width, texture.height);
      }
    } else {
      // 监听资源加载完成
      resource.on('stateChanged', (data) => {
        if (data.newState === 'loaded') {
          const texture = resource.getTexture();
          if (texture) {
            this.pixiSprite.texture = texture;
            this.setSize(texture.width, texture.height);
          }
        }
      });
    }
  }
  
  getTexture(): PIXI.Texture | null {
    return this.pixiSprite.texture;
  }
  
  getImageResource(): ImageResource | null {
    return this.imageResource;
  }
  
  // === 精灵特有属性 ===
  setAnchor(x: number, y: number = x): void {
    this.pixiSprite.anchor.set(x, y);
  }
  
  getAnchor(): { x: number; y: number } {
    return {
      x: this.pixiSprite.anchor.x,
      y: this.pixiSprite.anchor.y
    };
  }
  
  // === 渲染实现 ===
  protected renderContent(renderer: Renderer): void {
    // PIXI 适配器会自动处理渲染
    // 这里可以添加自定义渲染逻辑
  }
  
  // === 克隆 ===
  clone(): Sprite {
    const cloned = new Sprite(this.imageResource, this.name + '_copy');
    
    // 复制基础属性
    cloned.setPosition(this.x, this.y, this.z);
    cloned.setSize(this.width, this.height);
    cloned.setScale(this.scaleX, this.scaleY);
    cloned.setSkew(this.skewX, this.skewY);
    cloned.rotation = this.rotation;
    cloned.alpha = this.alpha;
    cloned.visible = this.visible;
    cloned.tint = this.tint;
    cloned.blendMode = this.blendMode;
    
    // 复制精灵特有属性
    const anchor = this.getAnchor();
    cloned.setAnchor(anchor.x, anchor.y);
    
    return cloned;
  }
  
  // === 序列化 ===
  serialize(): object {
    const anchor = this.getAnchor();
    return {
      ...super.serialize(),
      type: 'Sprite',
      imageResourceId: this.imageResource?.id,
      anchor: { x: anchor.x, y: anchor.y }
    };
  }
  
  deserialize(data: any): void {
    super.deserialize(data);
    
    if (data.anchor) {
      this.setAnchor(data.anchor.x, data.anchor.y);
    }
    
    // 注意：imageResource 需要在反序列化时从资源管理器中获取
  }
}

// 工厂函数
export async function createSpriteFromFile(file: File, name?: string): Promise<Sprite> {
  const resource = new ImageResource(name || file.name);
  await resource.load(file);
  return new Sprite(resource, name);
}

export async function createSpriteFromUrl(url: string, name?: string): Promise<Sprite> {
  const resource = new ImageResource(name || 'sprite');
  await resource.load(url);
  return new Sprite(resource, name);
}
