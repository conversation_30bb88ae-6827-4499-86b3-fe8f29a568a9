<script lang="ts">
  /**
   * 删除确认对话框组件
   * 可复用的删除确认弹窗，支持自定义标题、内容和警告信息
   */

  // 导入国际化
  import { _ } from '../../lib/i18n';

  interface Props {
    show: boolean;
    title?: string;
    message: string;
    warning?: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm: () => void;
    onCancel: () => void;
  }

  let {
    show = false,
    title = $_('dialog.confirmDelete'),
    message,
    warning = $_('dialog.deleteWarning'),
    confirmText = $_('actions.confirmDelete'),
    cancelText = $_('actions.cancel'),
    onConfirm,
    onCancel
  }: Props = $props();

  // 处理键盘事件
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      onCancel();
    }
  }

  // 处理遮罩点击
  function handleOverlayClick() {
    onCancel();
  }

  // 阻止对话框内部点击事件冒泡
  function handleDialogClick(event: MouseEvent) {
    event.stopPropagation();
  }
</script>

<!-- 删除确认对话框 -->
{#if show}
  <div
    class="delete-confirm-overlay"
    onclick={handleOverlayClick}
    onkeydown={handleKeydown}
    role="presentation"
    aria-label="删除确认对话框遮罩"
  >
    <div
      class="delete-confirm-dialog"
      onclick={handleDialogClick}
      onkeydown={handleKeydown}
      role="dialog"
      aria-modal="true"
      aria-labelledby="delete-confirm-title"
      tabindex="-1"
    >
      <div class="delete-confirm-header">
        <h3 id="delete-confirm-title">{title}</h3>
      </div>
      <div class="delete-confirm-content">
        <p>{@html message}</p>
        {#if warning}
          <div class="delete-warning">{warning}</div>
        {/if}
      </div>
      <div class="delete-confirm-actions">
        <button class="cancel-btn" onclick={onCancel}>{cancelText}</button>
        <button class="confirm-btn" onclick={onConfirm}>{confirmText}</button>
      </div>
    </div>
  </div>
{/if}

<style>
  /* 🎯 删除确认对话框 - 参考项目弹窗设计风格 */
  .delete-confirm-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  .delete-confirm-dialog {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 8px;
    padding: 0;
    width: 90%;
    max-width: 420px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease-out;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* 🎯 头部样式 - 参考项目modal-header设计 */
  .delete-confirm-header {
    padding: 16px 20px;
    background: var(--theme-surface);
    border-bottom: 1px solid var(--theme-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .delete-confirm-header h3 {
    margin: 0;
    color: var(--theme-text);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .delete-confirm-header h3::before {
    content: '🗑️';
    font-size: 1.1em;
  }

  /* 🎯 内容区域 - 参考项目modal-content设计 */
  .delete-confirm-content {
    padding: 16px 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .delete-confirm-content p {
    margin: 0;
    color: var(--theme-text);
    line-height: 1.5;
    font-size: 14px;
  }

  /* 🎯 警告信息样式 - 参考项目warning-section设计 */
  .delete-warning {
    padding: 8px 12px;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: 4px;
    color: #dc2626;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .delete-warning::before {
    content: '⚠️';
    font-size: 1em;
    flex-shrink: 0;
  }

  /* 🎯 底部操作区域 - 参考项目modal-footer设计 */
  .delete-confirm-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 20px;
    background: var(--theme-surface);
    border-top: 1px solid var(--theme-border);
  }

  /* 🎯 按钮样式 - 参考项目按钮设计 */
  .cancel-btn,
  .confirm-btn {
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
    min-width: 70px;
  }

  .cancel-btn {
    background: var(--theme-background);
    color: var(--theme-text);
    border-color: var(--theme-border);
  }

  .cancel-btn:hover {
    background: var(--theme-surface-hover, var(--theme-surface));
    border-color: var(--theme-primary);
  }

  .confirm-btn {
    background: #dc2626;
    color: white;
    border-color: #dc2626;
  }

  .confirm-btn:hover {
    background: #b91c1c;
    border-color: #b91c1c;
  }

  .confirm-btn:active,
  .cancel-btn:active {
    transform: translateY(1px);
  }
</style>
