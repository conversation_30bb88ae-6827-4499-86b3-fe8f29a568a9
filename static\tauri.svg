<svg width="256" height="256" viewBox="0 0 256 256" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- GameSprite Studio Logo -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4a5568;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2d3748;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#718096;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4a5568;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- 背景圆形 -->
  <circle cx="128" cy="128" r="120" fill="url(#bgGradient)" stroke="#718096" stroke-width="4"/>

  <!-- 主网格背景 -->
  <g opacity="0.3">
    <rect x="32" y="32" width="192" height="192" fill="none" stroke="#ffffff" stroke-width="1"/>
    <!-- 垂直网格线 -->
    <line x1="96" y1="32" x2="96" y2="224" stroke="#ffffff" stroke-width="0.8"/>
    <line x1="160" y1="32" x2="160" y2="224" stroke="#ffffff" stroke-width="0.8"/>
    <!-- 水平网格线 -->
    <line x1="32" y1="96" x2="224" y2="96" stroke="#ffffff" stroke-width="0.8"/>
    <line x1="32" y1="160" x2="224" y2="160" stroke="#ffffff" stroke-width="0.8"/>
  </g>

  <!-- 像素化精灵块 - 第一行 -->
  <rect x="40" y="40" width="48" height="48" fill="url(#primaryGradient)" rx="4"/>
  <rect x="104" y="40" width="48" height="48" fill="url(#secondaryGradient)" rx="4"/>
  <rect x="168" y="40" width="48" height="48" fill="#22c55e" opacity="0.8" rx="4"/>

  <!-- 像素化精灵块 - 第二行 -->
  <rect x="40" y="104" width="48" height="48" fill="url(#secondaryGradient)" rx="4"/>
  <rect x="104" y="104" width="48" height="48" fill="url(#primaryGradient)" rx="4"/>
  <rect x="168" y="104" width="48" height="48" fill="#fbbf24" opacity="0.9" rx="4"/>

  <!-- 像素化精灵块 - 第三行 -->
  <rect x="40" y="168" width="48" height="48" fill="#3b82f6" opacity="0.8" rx="4"/>
  <rect x="104" y="168" width="48" height="48" fill="url(#primaryGradient)" rx="4"/>
  <rect x="168" y="168" width="48" height="48" fill="url(#secondaryGradient)" rx="4"/>

  <!-- 高亮边框 -->
  <rect x="32" y="32" width="192" height="192" fill="none" stroke="#718096" stroke-width="2" rx="8"/>

  <!-- 工具指示器 -->
  <circle cx="200" cy="56" r="16" fill="url(#secondaryGradient)"/>
  <circle cx="200" cy="56" r="8" fill="#ffffff"/>
  <circle cx="200" cy="56" r="4" fill="url(#secondaryGradient)"/>

  <!-- 中心装饰 -->
  <circle cx="128" cy="128" r="24" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.6"/>
  <circle cx="128" cy="128" r="12" fill="none" stroke="#718096" stroke-width="1" opacity="0.8"/>

  <!-- 角落装饰 -->
  <rect x="48" y="48" width="8" height="8" fill="#ffffff" opacity="0.6" rx="1"/>
  <rect x="200" y="48" width="8" height="8" fill="#ffffff" opacity="0.6" rx="1"/>
  <rect x="48" y="200" width="8" height="8" fill="#ffffff" opacity="0.6" rx="1"/>
  <rect x="200" y="200" width="8" height="8" fill="#ffffff" opacity="0.6" rx="1"/>
</svg>
