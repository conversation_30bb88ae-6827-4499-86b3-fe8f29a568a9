/**
 * GameSprite Engine - 简化版 Scene
 */

import { GameObject } from './GameObject.js';

export class Scene extends GameObject {
  width: number;
  height: number;
  backgroundColor: number = 0x2a2a2a;
  
  constructor(width: number = 800, height: number = 600, name: string = 'Scene') {
    super(name);
    this.width = width;
    this.height = height;
    this.setSize(width, height);
  }
  
  setBackgroundColor(color: number): void {
    this.backgroundColor = color;
  }
  
  resize(width: number, height: number): void {
    this.width = width;
    this.height = height;
    this.setSize(width, height);
  }
}
