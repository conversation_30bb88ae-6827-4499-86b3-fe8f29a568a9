/**
 * Constants used for path offset operations
 */

/** Used for floating point comparisons */
export const MY_EPSILON = 1e-15;

/** Used when splitting curves, and for logic to see if we think its a real path when using bool operations */
export const MIN_PATH_LENGTH = 1e-6;

/** Used for smoothing curves */
export const LINE_TOLERANCE = 1e-7;

/** Used for self intersecting paths, we make a slightly different path and check for intersects */
export const OFFSET_SHIFT = 1e-4;

/** One third constant for curve calculations */
export const ONE_THIRD = 1 / 3;

/** Two thirds constant for curve calculations */
export const TWO_THIRD = 2 / 3;
