/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>..
 */

import { Base } from "../base/Base";

export interface Translation {
  x: number;
  y: number;
}

export interface Bounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export class GUIElement {
  protected _model: Base;
  protected _root: GUIElement | null;
  protected _localTranslation: Translation | null;
  protected _canvas: HTMLCanvasElement;
  protected _ctx: CanvasRenderingContext2D;

  static GRID_DEFAULT_CELL_WIDTH = 20;
  static GRID_DEFAULT_CELL_HEIGHT = 20;

  canAutoScrollY: boolean;
  cursor: string;

  /**
   * Create a new GUIElement
   * @param {Base} model - The object containing the data to use to draw this GUIElement
   */
  constructor(model: Base) {
    this._model = model;
    this._root = null;
    this._localTranslation = null;
    this.canAutoScrollY = false;
    this.cursor = "default";
  }

  /**
   * The object to use the data from to create this GUIElement
   */
  set model(model: Base) {
    this._model = model;
  }

  get model(): Base {
    return this._model;
  }

  /**
   * The root GUIElement.
   */
  get project(): GUIElement {
    if (!this._root) {
      this._root = this.model.project.guiElement;
    }
    return this._root;
  }

  /**
   * The canvas that this GUIElement belongs to.
   */
  get canvas(): HTMLCanvasElement {
    return this.project._canvas;
  }

  /**
   * The context of the canvas that this GUIElement belongs to.
   */
  get ctx(): CanvasRenderingContext2D {
    return this.model.project.guiElement._ctx;
  }

  /**
   * The current translation of the canvas. NOTE: This won't work without the following polyfill:
   * https://github.com/goessner/canvas-currentTransform
   */
  get currentTranslation(): Translation {
    const transform = this.ctx.currentTransform;
    return {
      x: transform.e,
      y: transform.f,
    };
  }

  /**
   * A copy of the transformation of the canvas when this object was drawn.
   */
  get localTranslation(): Translation | null {
    return this._localTranslation;
  }

  /**
   * The current grid cell width that all GUIElements are based off of.
   */
  get gridCellWidth(): number {
    return GUIElement.GRID_DEFAULT_CELL_WIDTH;
  }

  /**
   * The current grid cell height that all GUIElements are based off of.
   */
  get gridCellHeight(): number {
    return GUIElement.GRID_DEFAULT_CELL_HEIGHT;
  }

  /**
   * The bounding box of the hit area for mouse interactions.
   */
  get bounds(): Bounds | null {
    // Implemented by subclasses
    return null;
  }

  /**
   * The position of the mouse relative to this elements translation.
   */
  get localMouse(): Translation {
    const translation = this.localTranslation || { x: 0, y: 0 };
    return {
      x: this.project._mouse.x - translation.x,
      y: this.project._mouse.y - translation.y,
    };
  }

  /**
   * Checks if this object is touching the mouse.
   */
  mouseInBounds(): boolean {
    if (!this.bounds) return false;

    const localMouse = this.localMouse;
    const bounds = this.bounds;
    return (
      localMouse.x > bounds.x &&
      localMouse.y > bounds.y &&
      localMouse.x < bounds.x + bounds.width &&
      localMouse.y < bounds.y + bounds.height
    );
  }

  /**
   * Check if the mouse is hovering or clicking this element.
   */
  get mouseState(): "down" | "over" | "out" {
    if (this === this.project._getTopMouseTarget()) {
      if (this.project._isDragging) {
        return "down";
      } else {
        return "over";
      }
    } else {
      return "out";
    }
  }

  /**
   * Draw this GUIElement
   */
  draw(): void {
    this._localTranslation = this.currentTranslation;
    this.project.markElementAsDrawn(this);
  }

  /**
   * The function to call when the mouse clicks this element.
   */
  onMouseDown(e: MouseEvent): void {
    // Implemented by subclasses.
  }

  /**
   * The function to call when the mouse drags this element.
   */
  onMouseDrag(e: MouseEvent): void {
    // Implemented by subclasses.
  }

  /**
   * The function to call when the mouse finishes a click on this element.
   */
  onMouseUp(e: MouseEvent): void {
    // Implemented by subclasses.
  }

  /**
   * Causes the project to call it's onProjectModified function. Call this after modifying the project.
   */
  projectWasModified(): void {
    this.project._onProjectModified();
  }

  /**
   * Causes the project to call it's onProjectSoftModified function. Call this after modifying the project.
   */
  projectWasSoftModified(): void {
    this.project._onProjectSoftModified();
  }
}
