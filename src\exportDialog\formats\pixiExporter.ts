/**
 * Pixi.js 导出器 - 生成 Pixi.js 兼容的 JSON 格式
 */

import { invoke } from '@tauri-apps/api/core';
import { BaseExporter, sanitizeFileName, ensureExtension } from '../exportUtils';
import type { ExportItem, ExportConfig, ExportResult } from '../exportTypes';

// Pixi.js 帧数据接口
interface PixiFrame {
  frame: { x: number; y: number; w: number; h: number };
  rotated: boolean;
  trimmed: boolean;
  spriteSourceSize: { x: number; y: number; w: number; h: number };
  sourceSize: { w: number; h: number };
}

// Pixi.js 数据结构
interface PixiData {
  frames: Record<string, PixiFrame>;
  meta: {
    app: string;
    version: string;
    image: string;
    format: string;
    size: { w: number; h: number };
    scale: string;
  };
}

export class PixiExporter extends BaseExporter {
  async export(items: ExportItem[], config: ExportConfig, selectedFolder: string): Promise<ExportResult> {
    const startTime = Date.now();
    const exportedFiles: ExportResult['files'] = [];
    let totalSize = 0;
    const totalTasks = items.length;

    this.reportProgress(0, totalTasks, '准备导出...', 'preparing');

    // 处理每个导出项
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      this.reportProgress(i + 1, totalTasks, `${item.name}.json`, 'processing');

      if (!item.cropAreas || item.cropAreas.length === 0) {
        console.warn(`跳过没有裁切数据的资源: ${item.name}`);
        continue;
      }

      // 导出原始图片（如果需要）
      if (config.includeOriginal) {
        try {
          const imageData = item.resource.buffer || item.resource.data;
          if (imageData) {
            const originalBlob = new Blob([imageData], { type: 'image/png' });
            const uint8Array = new Uint8Array(await originalBlob.arrayBuffer());

            const originalFileName = this.generateImageFileName(item.name);

            console.log('🚀 PixiExporter: 导出原始图片', {
              targetDirectory: selectedFolder,
              fileName: originalFileName
            });

            const originalPath = await invoke<string>('export_single_file', {
              filePath: `${selectedFolder}/${originalFileName}`,
              fileData: Array.from(uint8Array)
            });

            console.log('✅ PixiExporter: 原始图片导出成功', { originalPath });

            exportedFiles.push({
              name: originalFileName,
              path: originalPath,
              size: originalBlob.size,
              type: 'image'
            });
            totalSize += originalBlob.size;
          }
        } catch (error) {
          console.error('❌ PixiExporter: 原始图片导出失败', error);
          // 不中断 Pixi.js 导出，只是警告
        }
      }

      // 生成 Pixi.js 数据
      const pixiData = this.generatePixiData(item);

      // 生成 JSON 内容
      const fileContent = JSON.stringify(pixiData, null, '\t');

      // 生成文件名
      const fileName = this.generateFileName(item.name, config);

      // 使用 Rust 后端保存文件
      try {
        const encoder = new TextEncoder();
        const fileData = encoder.encode(fileContent);

        console.log('🚀 PixiExporter: 调用Rust后端导出', {
          targetDirectory: selectedFolder,
          fileName: fileName
        });

        const exportedPath = await invoke<string>('export_single_file', {
          filePath: `${selectedFolder}/${fileName}`,
          fileData: Array.from(fileData)
        });

        console.log('✅ PixiExporter: Rust后端导出成功', { exportedPath });

        const fileSize = fileData.length;
        exportedFiles.push({
          name: fileName,
          path: exportedPath,
          size: fileSize,
          type: 'data'
        });
        totalSize += fileSize;

      } catch (error) {
        console.error('❌ PixiExporter: Rust后端导出失败', error);
        throw new Error(`导出Pixi.js文件失败: ${error}`);
      }
    }

    this.reportProgress(totalTasks, totalTasks, '导出完成', 'completed');

    return {
      success: true,
      files: exportedFiles,
      totalSize,
      duration: Date.now() - startTime
    };
  }

  private generatePixiData(item: ExportItem): PixiData {
    const frames: Record<string, PixiFrame> = {};

    // 为每个裁切区域生成帧数据
    item.cropAreas?.forEach((area, index) => {
      // 生成帧名称，参考 TexturePacker 的命名规则
      let frameName: string;
      if (area.name && area.name.trim() !== '') {
        // 如果有自定义名称，使用自定义名称
        frameName = area.name.endsWith('.png') ? area.name : `${area.name}.png`;
      } else {
        // 如果没有名称，使用 TexturePacker 风格的命名：资源名_x_y_w_h.png
        const baseName = item.name.replace(/\.[^/.]+$/, '');
        frameName = `${baseName}_${area.x}_${area.y}_${area.width}_${area.height}.png`;
      }

      frames[frameName] = {
        frame: {
          x: area.x,
          y: area.y,
          w: area.width,
          h: area.height
        },
        rotated: false,
        trimmed: false,
        spriteSourceSize: {
          x: 0,
          y: 0,
          w: area.width,
          h: area.height
        },
        sourceSize: {
          w: area.width,
          h: area.height
        }
      };
    });

    const imageFileName = this.generateImageFileName(item.name);

    return {
      frames,
      meta: {
        app: "GameSprite Studio",
        version: "1.0",
        image: imageFileName,
        format: "RGBA8888",
        size: {
          w: item.resource.width || 0,
          h: item.resource.height || 0
        },
        scale: "1"
      }
    };
  }

  private generateFileName(baseName: string, config: ExportConfig): string {
    let fileName: string;

    // 🎯 如果用户设置了文件名，完整使用设置的文件名
    if (config.fileName && config.fileName.trim() !== '') {
      fileName = config.fileName.replace(/\.[^/.]+$/, ''); // 移除扩展名
    } else {
      fileName = baseName.replace(/\.[^/.]+$/, '');
    }

    return ensureExtension(sanitizeFileName(fileName), 'json');
  }

  private generateImageFileName(baseName: string): string {
    const nameWithoutExt = baseName.replace(/\.[^/.]+$/, '');
    return ensureExtension(sanitizeFileName(nameWithoutExt), 'png');
  }


}
