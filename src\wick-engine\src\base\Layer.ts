/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "./Base";
import { Frame } from "./Frame";

/**
 * 表示一个Wick图层
 */
export class Layer extends Base {
  protected _locked: boolean;
  protected _hidden: boolean;

  /**
   * 创建一个Wick图层
   * @param args - 图层参数
   * @param args.locked - 图层是否锁定
   * @param args.hidden - 图层是否隐藏
   * @param args.name - 图层名称
   */
  constructor(
    args: { locked?: boolean; hidden?: boolean; name?: string } = {}
  ) {
    super(args);

    this._locked = args.locked ?? false;
    this._hidden = args.hidden ?? false;
    this.name = args.name || null;
  }

  protected _serialize(args: any): any {
    const data = super._serialize(args);

    data.locked = this._locked;
    data.hidden = this._hidden;

    return data;
  }

  protected _deserialize(data: any): void {
    super._deserialize(data);

    this._locked = data.locked;
    this._hidden = data.hidden;
  }

  get classname(): string {
    return "Layer";
  }

  /**
   * 获取图层中的帧列表
   */
  get frames(): Frame[] {
    return this.getChildren("Frame") as Frame[];
  }

  /**
   * 获取图层在时间轴中的顺序
   */
  get index(): number | undefined {
    return this.parent && this.parent.layers.indexOf(this);
  }

  /**
   * 激活此图层，使其成为时间轴中的活动图层
   */
  activate(): void {
    if (this.parent) {
      this.parent.activeLayerIndex = this.index as number;
    }
  }

  /**
   * 判断此图层是否为时间轴中的活动图层
   */
  get isActive(): boolean {
    return this.parent ? this === this.parent.activeLayer : false;
  }

  /**
   * 获取图层的帧长度
   */
  get length(): number {
    let end = 0;
    this.frames.forEach((frame) => {
      if (frame.end > end) {
        end = frame.end;
      }
    });
    return end;
  }

  /**
   * 获取图层是否锁定
   */
  get locked(): boolean {
    return this._locked;
  }

  /**
   * 设置图层是否锁定
   */
  set locked(value: boolean) {
    this._locked = value;
  }

  /**
   * 获取图层是否隐藏
   */
  get hidden(): boolean {
    return this._hidden;
  }

  /**
   * 设置图层是否隐藏
   */
  set hidden(value: boolean) {
    this._hidden = value;
  }
}
