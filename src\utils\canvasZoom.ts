/**
 * 通用Canvas缩放功能模块
 * 提供缩放、平移、坐标转换等功能，可在多个组件中复用
 */

// 基础类型定义
export interface Point {
  x: number;
  y: number;
}

export interface Transform {
  scale: number;
  translateX: number;
  translateY: number;
}

export interface ViewportInfo {
  canvasWidth: number;
  canvasHeight: number;
  imageWidth: number;
  imageHeight: number;
}

export interface ZoomConfig {
  minScale?: number;
  maxScale?: number;
  zoomFactor?: number;
  enableKeyboard?: boolean;
  enableWheel?: boolean;
  enableDrag?: boolean;
}

export interface ZoomCallbacks {
  onTransformChange?: (transform: Transform) => void;
  onZoomChange?: (scale: number) => void;
  onPanChange?: (translateX: number, translateY: number) => void;
  onRender?: () => void;
}

// 缩放管理器类
export class CanvasZoomManager {
  private canvas: HTMLCanvasElement;
  private transform: Transform;
  private viewport: ViewportInfo;
  private config: Required<ZoomConfig>;
  private callbacks: ZoomCallbacks;
  
  // 拖拽状态
  private isDragging = false;
  private dragStart: Point = { x: 0, y: 0 };
  private lastTranslate: Point = { x: 0, y: 0 };
  
  // 事件监听器
  private eventListeners: Array<{ element: Element | Document, event: string, handler: EventListener }> = [];

  constructor(
    canvas: HTMLCanvasElement,
    viewport: ViewportInfo,
    config: ZoomConfig = {},
    callbacks: ZoomCallbacks = {}
  ) {
    this.canvas = canvas;
    this.viewport = viewport;
    this.callbacks = callbacks;
    
    // 默认配置
    this.config = {
      minScale: config.minScale ?? 0.1,
      maxScale: config.maxScale ?? 10,
      zoomFactor: config.zoomFactor ?? 1.1,
      enableKeyboard: config.enableKeyboard ?? true,
      enableWheel: config.enableWheel ?? true,
      enableDrag: config.enableDrag ?? true
    };
    
    // 初始变换状态
    this.transform = {
      scale: 1,
      translateX: 0,
      translateY: 0
    };
    
    this.initEventListeners();
  }

  /**
   * 初始化事件监听器
   */
  private initEventListeners() {
    if (this.config.enableWheel) {
      this.addEventListener(this.canvas, 'wheel', this.handleWheel.bind(this));
    }
    
    if (this.config.enableDrag) {
      this.addEventListener(this.canvas, 'mousedown', this.handleMouseDown.bind(this));
      this.addEventListener(document, 'mousemove', this.handleMouseMove.bind(this));
      this.addEventListener(document, 'mouseup', this.handleMouseUp.bind(this));
    }
    
    if (this.config.enableKeyboard) {
      this.addEventListener(this.canvas, 'keydown', this.handleKeyDown.bind(this));
      // 确保canvas可以接收键盘事件
      this.canvas.tabIndex = 0;
    }
  }

  /**
   * 添加事件监听器并记录，便于清理
   */
  private addEventListener(element: Element | Document, event: string, handler: EventListener) {
    element.addEventListener(event, handler);
    this.eventListeners.push({ element, event, handler });
  }

  /**
   * 获取Canvas坐标
   */
  private getCanvasPosition(event: MouseEvent): Point {
    const rect = this.canvas.getBoundingClientRect();
    return {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };
  }

  /**
   * Canvas坐标转图片坐标
   */
  public canvasToImagePosition(canvasPos: Point): Point {
    return {
      x: (canvasPos.x - this.transform.translateX) / this.transform.scale,
      y: (canvasPos.y - this.transform.translateY) / this.transform.scale
    };
  }

  /**
   * 图片坐标转Canvas坐标
   */
  public imageToCanvasPosition(imagePos: Point): Point {
    return {
      x: imagePos.x * this.transform.scale + this.transform.translateX,
      y: imagePos.y * this.transform.scale + this.transform.translateY
    };
  }

  /**
   * 滚轮事件处理
   */
  private handleWheel(event: WheelEvent) {
    event.preventDefault();
    
    const canvasPos = this.getCanvasPosition(event);
    const zoomFactor = event.deltaY > 0 ? (1 / this.config.zoomFactor) : this.config.zoomFactor;
    
    this.zoomAtPoint(canvasPos, zoomFactor);
  }

  /**
   * 在指定点进行缩放
   */
  public zoomAtPoint(point: Point, factor: number) {
    const newScale = Math.max(
      this.config.minScale,
      Math.min(this.config.maxScale, this.transform.scale * factor)
    );
    
    if (newScale === this.transform.scale) return;
    
    const scaleChange = newScale / this.transform.scale;
    
    // 以指定点为中心缩放
    this.transform.translateX = point.x - (point.x - this.transform.translateX) * scaleChange;
    this.transform.translateY = point.y - (point.y - this.transform.translateY) * scaleChange;
    this.transform.scale = newScale;
    
    this.notifyTransformChange();
  }

  /**
   * 鼠标按下事件处理
   */
  private handleMouseDown(event: MouseEvent) {
    if (event.button !== 0) return; // 只处理左键
    
    const canvasPos = this.getCanvasPosition(event);
    this.isDragging = true;
    this.dragStart = canvasPos;
    this.lastTranslate = { x: this.transform.translateX, y: this.transform.translateY };
    
    this.canvas.style.cursor = 'grabbing';
    event.preventDefault();
  }

  /**
   * 鼠标移动事件处理
   */
  private handleMouseMove(event: MouseEvent) {
    if (!this.isDragging) return;
    
    const canvasPos = this.getCanvasPosition(event);
    
    this.transform.translateX = this.lastTranslate.x + (canvasPos.x - this.dragStart.x);
    this.transform.translateY = this.lastTranslate.y + (canvasPos.y - this.dragStart.y);
    
    this.notifyTransformChange();
  }

  /**
   * 鼠标释放事件处理
   */
  private handleMouseUp(_event: MouseEvent) {
    if (!this.isDragging) return;
    
    this.isDragging = false;
    this.canvas.style.cursor = 'grab';
  }

  /**
   * 键盘事件处理
   */
  private handleKeyDown(event: KeyboardEvent) {
    switch (event.key) {
      case '+':
      case '=':
        event.preventDefault();
        this.zoomIn();
        break;
      case '-':
        event.preventDefault();
        this.zoomOut();
        break;
      case '0':
        event.preventDefault();
        this.resetView();
        break;
      case 'f':
      case 'F':
        event.preventDefault();
        this.fitToView();
        break;
    }
  }

  /**
   * 放大
   */
  public zoomIn() {
    const centerPoint = {
      x: this.viewport.canvasWidth / 2,
      y: this.viewport.canvasHeight / 2
    };
    this.zoomAtPoint(centerPoint, this.config.zoomFactor);
  }

  /**
   * 缩小
   */
  public zoomOut() {
    const centerPoint = {
      x: this.viewport.canvasWidth / 2,
      y: this.viewport.canvasHeight / 2
    };
    this.zoomAtPoint(centerPoint, 1 / this.config.zoomFactor);
  }

  /**
   * 重置视图
   */
  public resetView() {
    this.transform.scale = 1;
    this.transform.translateX = 0;
    this.transform.translateY = 0;
    this.notifyTransformChange();
  }

  /**
   * 适应视图
   */
  public fitToView() {
    if (this.viewport.imageWidth === 0 || this.viewport.imageHeight === 0) return;
    
    const scaleX = this.viewport.canvasWidth / this.viewport.imageWidth;
    const scaleY = this.viewport.canvasHeight / this.viewport.imageHeight;
    
    this.transform.scale = Math.min(scaleX, scaleY, 1); // 不超过原始尺寸
    
    // 居中显示
    this.transform.translateX = (this.viewport.canvasWidth - this.viewport.imageWidth * this.transform.scale) / 2;
    this.transform.translateY = (this.viewport.canvasHeight - this.viewport.imageHeight * this.transform.scale) / 2;
    
    this.notifyTransformChange();
  }

  /**
   * 通知变换状态改变
   */
  private notifyTransformChange() {
    this.callbacks.onTransformChange?.(this.transform);
    this.callbacks.onZoomChange?.(this.transform.scale);
    this.callbacks.onPanChange?.(this.transform.translateX, this.transform.translateY);
    this.callbacks.onRender?.();
  }

  /**
   * 获取当前变换状态
   */
  public getTransform(): Transform {
    return { ...this.transform };
  }

  /**
   * 设置变换状态
   */
  public setTransform(transform: Partial<Transform>) {
    if (transform.scale !== undefined) {
      this.transform.scale = Math.max(
        this.config.minScale,
        Math.min(this.config.maxScale, transform.scale)
      );
    }
    if (transform.translateX !== undefined) {
      this.transform.translateX = transform.translateX;
    }
    if (transform.translateY !== undefined) {
      this.transform.translateY = transform.translateY;
    }
    this.notifyTransformChange();
  }

  /**
   * 更新视口信息
   */
  public updateViewport(viewport: Partial<ViewportInfo>) {
    Object.assign(this.viewport, viewport);
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<ZoomConfig>) {
    Object.assign(this.config, config);
  }

  /**
   * 销毁管理器，清理事件监听器
   */
  public destroy() {
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.eventListeners = [];
    
    // 重置canvas样式
    this.canvas.style.cursor = '';
  }
}
