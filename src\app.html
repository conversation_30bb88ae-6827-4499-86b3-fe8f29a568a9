<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%sveltekit.assets%/favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>RPG Maker MZL</title>
  %sveltekit.head%
    <!-- 🎯 内联样式确保滚动条样式立即生效 -->
    <style>
      /* 🎯 强制滚动条样式 - 最高优先级 */
      *::-webkit-scrollbar {
        width: 16px !important;
        height: 16px !important;
        background: transparent !important;
      }
      *::-webkit-scrollbar-track {
        background: #1a202c !important;
        border-radius: 10px !important;
        border: 2px solid #2d3748 !important;
        margin: 2px !important;
      }
      *::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #4a5568 0%, #667eea 30%, #4a5568 100%) !important;
        border-radius: 10px !important;
        border: 2px solid #1a202c !important;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2), 0 2px 4px rgba(0, 0, 0, 0.3) !important;
        transition: all 0.3s ease !important;
      }
      *::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #667eea 0%, #7c3aed 30%, #667eea 100%) !important;
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.3), 0 4px 8px rgba(0, 0, 0, 0.4) !important;
        transform: scale(1.05) !important;
      }
      *::-webkit-scrollbar-thumb:active {
        background: #2d3748 !important;
        transform: scale(0.95) !important;
      }
      *::-webkit-scrollbar-corner {
        background: #1a202c !important;
        border-radius: 10px !important;
      }
  
      /* Firefox滚动条 */
      * {
        scrollbar-width: auto !important;
        scrollbar-color: #4a5568 #1a202c !important;
      }
  
      /* 🎯 禁用输入框自动完成 */
      input, textarea, select {
        autocomplete: off !important;
        autocorrect: off !important;
        autocapitalize: off !important;
        spellcheck: false !important;
        data-form-type: "other" !important;
        data-lpignore: "true" !important;
      }
  
      input::-webkit-credentials-auto-fill-button,
      input::-webkit-contacts-auto-fill-button,
      input::-webkit-list-button,
      input::-webkit-calendar-picker-indicator {
        display: none !important;
        visibility: hidden !important;
        pointer-events: none !important;
        height: 0 !important;
        width: 0 !important;
        margin: 0 !important;
      }
  
      /* 禁用自动填充样式 */
      input:-webkit-autofill,
      input:-webkit-autofill:hover,
      input:-webkit-autofill:focus,
      input:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 30px #2d3748 inset !important;
        -webkit-text-fill-color: #ffffff !important;
        transition: background-color 5000s ease-in-out 0s !important;
      }
  
      /* 强制表单禁用自动完成 */
      form {
        autocomplete: off !important;
      }
    </style>
</head>

<body data-sveltekit-preload-data="hover" class="dark">
  <div id="app" style="display: contents">%sveltekit.body%</div>
</body>

</html>