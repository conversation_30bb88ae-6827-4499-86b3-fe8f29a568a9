/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

interface AssetArgs {
  name?: string;
}

interface SerializeArgs {
  includeOriginalSource?: boolean;
}

export class Asset extends Wick.Base {
  protected name: string;

  /**
   * Creates a new Wick Asset.
   * @param {AssetArgs} args - Asset constructor arguments
   */
  constructor(args: AssetArgs = {}) {
    super(args);
    this.name = args.name || "";
  }

  protected _serialize(args?: SerializeArgs): Record<string, any> {
    const data = super._serialize(args);
    data.name = this.name;
    return data;
  }

  protected _deserialize(data: Record<string, any>): void {
    super._deserialize(data);
    this.name = data.name;
  }

  /**
   * A list of all objects using this asset.
   */
  public getInstances(): any[] {
    // Implemented by subclasses
    return [];
  }

  /**
   * Check if there are any objects in the project that use this asset.
   * @returns {boolean}
   */
  public hasInstances(): boolean {
    // Implemented by sublasses
    return false;
  }

  /**
   * Remove all instances of this asset from the project. (Implemented by ClipAsset, ImageAsset, and SoundAsset)
   */
  public removeAllInstances(): void {
    // Implemented by sublasses
  }

  public get classname(): string {
    return "Asset";
  }
}
