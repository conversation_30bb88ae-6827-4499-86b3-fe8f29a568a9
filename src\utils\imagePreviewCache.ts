/**
 * 图片预览缓存系统
 * 用于缓存图片的base64预览，避免重复转换
 */

import type { ResourceItem } from '../types/imageType';
import { memoryManager } from './memoryManager';
import { LRUCache } from './lruCache';

interface PreviewCacheEntry {
  preview: string;
  timestamp: number;
  size: number;
  resourceId: string;
}

export class ImagePreviewCache {
  private lruCache: LRUCache<PreviewCacheEntry>;
  private readonly MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB限制
  private readonly MAX_CACHE_COUNT = 300; // 最大缓存数量
  private readonly CACHE_EXPIRE_TIME = 15 * 60 * 1000; // 15分钟过期
  private cleanupInterval: number;

  constructor() {
    // 🎯 初始化LRU缓存
    this.lruCache = new LRUCache<PreviewCacheEntry>(this.MAX_CACHE_SIZE, this.MAX_CACHE_COUNT);

    // 🎯 注册内存清理回调
    memoryManager.registerCleanupCallback(() => this.handleMemoryCleanup());

    // 🎯 监听内存清理事件
    window.addEventListener('memory-cleanup', (event: any) => {
      this.handleMemoryCleanupEvent(event.detail.type);
    });

    // 🎯 定期清理过期缓存
    this.cleanupInterval = setInterval(() => {
      this.lruCache.cleanupExpired(this.CACHE_EXPIRE_TIME);
    }, 5 * 60 * 1000); // 每5分钟清理一次
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(resource: ResourceItem): string {
    return `${resource.id}_${resource.updatedAt || resource.createdAt}`;
  }

  /**
   * 估算预览大小
   */
  private estimatePreviewSize(preview: string): number {
    // base64字符串大小估算
    return preview.length * 0.75; // base64编码大约比原始数据大33%
  }



  /**
   * 从ArrayBuffer生成base64预览
   */
  private generatePreviewFromBuffer(buffer: ArrayBuffer, mimeType?: string): string {
    try {
      let binary = '';
      const bytes = new Uint8Array(buffer);
      const len = bytes.byteLength;

      // 优化：分块处理大文件
      const chunkSize = 8192;
      for (let i = 0; i < len; i += chunkSize) {
        const chunk = bytes.subarray(i, Math.min(i + chunkSize, len));
        binary += String.fromCharCode.apply(null, Array.from(chunk));
      }

      const base64 = btoa(binary);
      const actualMimeType = mimeType || 'image/png';
      return `data:${actualMimeType};base64,${base64}`;
    } catch (error) {
      console.error('❌ ImagePreviewCache: 生成预览失败', error);
      return '';
    }
  }

  /**
   * 获取图片预览 - 使用LRU缓存
   */
  getPreview(resource: ResourceItem): string | null {
    if (resource.type !== 'image' || !resource.data) {
      return null;
    }

    const cacheKey = this.generateCacheKey(resource);

    // 🎯 从LRU缓存获取
    const cached = this.lruCache.get(cacheKey);
    if (cached) {
      return cached.preview;
    }

    // 生成新预览
    const preview = this.generatePreviewFromBuffer(
      resource.data,
      resource.originalFile?.type
    );

    if (preview) {
      const size = this.estimatePreviewSize(preview);

      // 如果预览太大，不缓存但仍返回
      if (size > this.MAX_CACHE_SIZE * 0.2) {
        console.warn('⚠️ ImagePreviewCache: 预览过大，跳过缓存', {
          resourceId: resource.id,
          size: `${(size / 1024 / 1024).toFixed(2)}MB`
        });
        return preview;
      }

      // 🎯 使用LRU缓存存储
      const cacheEntry: PreviewCacheEntry = {
        preview,
        timestamp: Date.now(),
        size,
        resourceId: resource.id
      };

      this.lruCache.set(cacheKey, cacheEntry, size);

      console.log('💾 ImagePreviewCache: 缓存新预览', {
        resourceId: resource.id,
        resourceName: resource.name,
        size: `${(size / 1024).toFixed(2)}KB`,
        cacheStats: this.lruCache.getStats()
      });
    }

    return preview;
  }

  /**
   * 🎯 优化的预加载预览（使用Web Worker）
   */
  async preloadPreview(resource: ResourceItem): Promise<void> {
    // 使用requestIdleCallback在浏览器空闲时处理
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => {
        this.getPreview(resource);
      }, { timeout: 1000 });
    } else {
      // 降级到setTimeout
      setTimeout(() => {
        this.getPreview(resource);
      }, 0);
    }
  }

  /**
   * 🎯 批量预加载预览（智能调度）
   */
  async batchPreloadPreviews(resources: ResourceItem[], maxConcurrent = 3): Promise<void> {
    const imageResources = resources.filter(r => r.type === 'image' && r.data);

    // 分批处理，避免一次性处理太多
    for (let i = 0; i < imageResources.length; i += maxConcurrent) {
      const batch = imageResources.slice(i, i + maxConcurrent);

      // 并行处理当前批次
      await Promise.all(
        batch.map(resource => this.preloadPreview(resource))
      );

      // 在批次之间添加小延迟，避免阻塞UI
      if (i + maxConcurrent < imageResources.length) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    }
  }

  /**
   * 移除特定资源的缓存
   */
  removePreview(resourceId: string): void {
    const keys = this.lruCache.keys();
    for (const key of keys) {
      const entry = this.lruCache.get(key);
      if (entry && entry.resourceId === resourceId) {
        this.lruCache.delete(key);
        console.log('🗑️ ImagePreviewCache: 移除资源缓存', { resourceId });
        break;
      }
    }
  }

  /**
   * 清空所有缓存
   */
  clearAll(): void {
    this.lruCache.clear();
    console.log('🧹 ImagePreviewCache: 清空所有缓存');
  }

  /**
   * 🎯 处理内存清理回调
   */
  private handleMemoryCleanup(): void {
    const beforeStats = this.lruCache.getStats();

    // 清理过期缓存
    this.lruCache.cleanupExpired(this.CACHE_EXPIRE_TIME);

    // 如果内存压力大，减少缓存大小限制
    if (beforeStats.count > 50) {
      const newMaxSize = Math.max(this.MAX_CACHE_SIZE * 0.5, 20 * 1024 * 1024); // 至少保留20MB
      this.lruCache.setMaxSize(newMaxSize);

      setTimeout(() => {
        // 5分钟后恢复正常大小
        this.lruCache.setMaxSize(this.MAX_CACHE_SIZE);
      }, 5 * 60 * 1000);
    }

    const afterStats = this.lruCache.getStats();
    const freedMemory = beforeStats.size - afterStats.size;

    if (freedMemory > 0) {
      console.log('🧹 ImagePreviewCache: 内存清理完成', {
        freedMemory: `${(freedMemory / 1024 / 1024).toFixed(2)}MB`,
        beforeCount: beforeStats.count,
        afterCount: afterStats.count,
        hitRate: `${afterStats.hitRate.toFixed(1)}%`
      });
    }
  }

  /**
   * 🎯 处理内存清理事件
   */
  private handleMemoryCleanupEvent(type: string): void {
    switch (type) {
      case 'cache-expired':
        this.lruCache.cleanupExpired(this.CACHE_EXPIRE_TIME);
        break;
      case 'cache-non-critical':
        // 减少缓存大小到70%
        this.lruCache.setMaxSize(this.MAX_CACHE_SIZE * 0.7);
        break;
      case 'cache-all':
        // 减少缓存大小到50%
        this.lruCache.setMaxSize(this.MAX_CACHE_SIZE * 0.5);
        break;
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const lruStats = this.lruCache.getStats();
    return {
      count: lruStats.count,
      totalSize: lruStats.size,
      maxSize: lruStats.maxSize,
      maxCount: lruStats.maxCount,
      usage: (lruStats.size / lruStats.maxSize) * 100,
      hitRate: lruStats.hitRate,
      memoryUsage: lruStats.memoryUsage
    };
  }

  /**
   * 销毁缓存实例
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.lruCache.clear();
    console.log('🧹 ImagePreviewCache: 实例已销毁');
  }
}

// 全局缓存实例
export const imagePreviewCache = new ImagePreviewCache();
