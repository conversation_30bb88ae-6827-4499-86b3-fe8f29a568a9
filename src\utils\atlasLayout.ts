/**
 * 图集布局算法
 * 使用专业的打包库：MaxRects、<PERSON><PERSON>、Potpack
 */

import { MaxRectsPacker } from 'maxrects-packer';
import ShelfPack from '@mapbox/shelf-pack';
import potpack from 'potpack';
import type { ImageResource, LayoutItem, LayoutResult, LayoutOptions } from '../types';

/**
 * 主要的布局函数
 */
export function calculateAtlasLayout(
  resources: ImageResource[],
  options: LayoutOptions
): LayoutResult {
  console.log('🔄 AtlasLayout: 开始计算布局', {
    resourceCount: resources.length,
    algorithm: options.algorithm,
    maxSize: `${options.maxWidth}x${options.maxHeight}`,
    padding: options.padding
  });

  if (resources.length === 0) {
    return {
      items: [],
      width: 0,
      height: 0,
      efficiency: 0
    };
  }

  let result: LayoutResult;

  // 预处理：分析图片尺寸分布
  const areas = resources.map(r => (r.width || 64) * (r.height || 64));
  const avgArea = areas.reduce((sum, area) => sum + area, 0) / areas.length;
  const largeImages = resources.filter(r => ((r.width || 64) * (r.height || 64)) > avgArea * 2);
  const smallImages = resources.filter(r => ((r.width || 64) * (r.height || 64)) <= avgArea * 2);

  console.log('📊 AtlasLayout: 图片分析', {
    total: resources.length,
    large: largeImages.length,
    small: smallImages.length,
    avgArea: Math.round(avgArea)
  });

  switch (options.algorithm) {
    case 'none':
      result = originalOrderLayout(resources, options);
      break;
    case 'maxrects':
      result = maxRectsLayout(resources, options);
      break;
    case 'shelf':
      result = shelfLayout(resources, options);
      break;
    case 'potpack':
      result = potpackLayout(resources, options);
      break;
    case 'guillotine':
      result = guillotineLayout(resources, options);
      break;
    default:
      result = maxRectsLayout(resources, options);
  }

  // 保存原始计算的尺寸
  const originalWidth = result.width;
  const originalHeight = result.height;

  // 调整最终尺寸为2的幂（如果启用）
  if (options.powerOfTwo) {
    result.width = nextPowerOfTwo(result.width);
    result.height = nextPowerOfTwo(result.height);
  }

  // 注意：移除maxWidth/maxHeight限制，允许动态尺寸

  // 重新计算效率
  const totalArea = result.items.reduce((sum, item) =>
    sum + item.width * item.height, 0
  );
  result.efficiency = totalArea / (result.width * result.height);

  console.log('🔄 AtlasLayout: 尺寸处理', {
    original: `${originalWidth}×${originalHeight}`,
    powerOfTwo: options.powerOfTwo,
    final: `${result.width}×${result.height}`
  });

  console.log('✅ AtlasLayout: 布局计算完成', {
    algorithm: options.algorithm,
    finalSize: `${result.width}x${result.height}`,
    efficiency: `${(result.efficiency * 100).toFixed(1)}%`,
    itemCount: result.items.length
  });

  return result;
}

/**
 * MaxRects算法 - 使用专业库
 */
function maxRectsLayout(resources: ImageResource[], options: LayoutOptions): LayoutResult {
  const packer = new MaxRectsPacker(
    options.maxWidth,
    options.maxHeight,
    options.padding, // padding参数
    {
      smart: true,
      pot: options.powerOfTwo,
      square: false,
      allowRotation: options.allowRotation,
      border: 0 // 边框间距，我们用padding处理
    }
  );

  console.log('🔄 MaxRects: 创建packer', {
    maxSize: `${options.maxWidth}×${options.maxHeight}`,
    padding: options.padding,
    allowRotation: options.allowRotation,
    powerOfTwo: options.powerOfTwo
  });

  // 准备输入数据并按面积从大到小排序（大图片优先）
  const rects = resources.map(resource => ({
    width: resource.width || 64,
    height: resource.height || 64,
    data: resource
  })).sort((a, b) => (b.width * b.height) - (a.width * a.height));

  console.log('🔄 MaxRects: 排序后的图片', rects.map(r => `${r.data.name}(${r.width}×${r.height})`));

  // 执行打包
  packer.addArray(rects as any);

  // 转换结果 - 只使用第一个bin，忽略oversized的bin
  const items: LayoutItem[] = [];

  if (packer.bins.length > 0) {
    const firstBin = packer.bins[0];
    console.log('🔄 MaxRects: 使用第一个bin', {
      binCount: packer.bins.length,
      firstBinRects: firstBin.rects.length,
      binSize: `${firstBin.width}×${firstBin.height}`
    });

    for (const rect of firstBin.rects) {
      // 检查坐标是否有效
      if (rect.x !== undefined && rect.y !== undefined) {
        const isRotated = rect.rot || false;
        console.log('🔄 MaxRects: 添加矩形', {
          name: rect.data.name,
          position: `(${rect.x},${rect.y})`,
          size: `${rect.width}×${rect.height}`,
          rotated: isRotated,
          originalSize: `${rect.data.width}×${rect.data.height}`,
          rotationBenefit: isRotated ? '🔄 已旋转优化空间' : '➡️ 未旋转'
        });
        items.push({
          x: rect.x,
          y: rect.y,
          width: rect.width,
          height: rect.height,
          resource: rect.data,
          rotated: rect.rot || false
        });
      } else {
        console.warn('🔄 MaxRects: 跳过无效坐标的矩形', rect.data.name);
      }
    }
  }

  console.log('🔄 MaxRects: 最终结果', { itemCount: items.length, items: items.map(i => `${i.resource.name}@(${i.x},${i.y})`) });

  // 计算实际使用的尺寸（包含padding）
  const maxX = Math.max(...items.map(item => item.x + item.width + options.padding), 0);
  const maxY = Math.max(...items.map(item => item.y + item.height + options.padding), 0);

  const totalArea = items.reduce((sum, item) => sum + item.width * item.height, 0);
  const efficiency = maxX > 0 && maxY > 0 ? totalArea / (maxX * maxY) : 0;

  console.log('🔄 MaxRects: 布局完成', {
    originalSize: `${options.maxWidth}×${options.maxHeight}`,
    actualSize: `${maxX}×${maxY}`,
    efficiency: `${(efficiency * 100).toFixed(1)}%`,
    itemCount: items.length,
    itemPositions: items.map(item => `${item.resource.name}@(${item.x},${item.y})+${item.width}×${item.height}`)
  });

  return {
    items,
    width: maxX,
    height: maxY,
    efficiency
  };
}

/**
 * Shelf算法 - 使用Mapbox库，支持旋转优化
 */
function shelfLayout(resources: ImageResource[], options: LayoutOptions): LayoutResult {
  console.log('🔄 Shelf: 开始布局', {
    count: resources.length,
    allowRotation: options.allowRotation
  });

  const shelf = new ShelfPack(options.maxWidth, options.maxHeight, {
    autoResize: false
  });

  // 🎯 如果启用旋转，预处理资源
  const processedResources = resources.map((resource, index) => {
    const originalW = resource.width || 64;
    const originalH = resource.height || 64;
    let finalW = originalW;
    let finalH = originalH;
    let shouldRotate = false;

    // 🎯 Shelf算法的旋转策略：优化高度利用率
    if (options.allowRotation && originalW > originalH) {
      // 如果宽度大于高度，考虑旋转以减少shelf的高度浪费
      const aspectRatio = originalW / originalH;
      if (aspectRatio > 1.5) { // 宽高比大于1.5时考虑旋转
        finalW = originalH;
        finalH = originalW;
        shouldRotate = true;
      }
    }

    return {
      resource,
      index,
      w: finalW,
      h: finalH,
      rotated: shouldRotate
    };
  });

  // 按高度从大到小排序（高图片优先，更好的shelf布局）
  processedResources.sort((a, b) => b.h - a.h);

  const bins = processedResources.map(item => ({
    w: item.w,
    h: item.h,
    id: item.index,
    rotated: item.rotated
  }));

  console.log('🔄 Shelf: 按高度排序后的图片', bins.map(b =>
    `${resources[b.id].name}(${b.w}×${b.h})${b.rotated ? ' 🔄' : ''}`
  ));

  // 执行打包
  const results = shelf.pack(bins);

  console.log('🔄 Shelf: 打包结果', results.map((r: any) =>
    `${resources[r.id].name}@(${r.x},${r.y})`
  ));

  // 转换结果，应用padding作为间距
  const items: LayoutItem[] = results.map((result: any) => {
    const processedItem = processedResources.find(item => item.index === result.id);
    return {
      x: result.x + options.padding,
      y: result.y + options.padding,
      width: result.w,  // 使用处理后的尺寸
      height: result.h, // 使用处理后的尺寸
      resource: resources[result.id],
      rotated: processedItem?.rotated || false
    };
  });

  // 计算实际使用的尺寸（包含padding）
  const maxX = Math.max(...items.map(item => item.x + item.width + options.padding), 0);
  const maxY = Math.max(...items.map(item => item.y + item.height + options.padding), 0);

  const totalArea = items.reduce((sum, item) => sum + item.width * item.height, 0);
  const efficiency = maxX > 0 && maxY > 0 ? totalArea / (maxX * maxY) : 0;

  console.log('🔄 Shelf: 布局完成', {
    finalSize: `${maxX}×${maxY}`,
    efficiency: `${(efficiency * 100).toFixed(1)}%`,
    rotatedItems: items.filter(item => item.rotated).length
  });

  return {
    items,
    width: maxX,
    height: maxY,
    efficiency
  };
}

/**
 * Potpack算法 - 简单高效，动态调整canvas尺寸，支持旋转优化
 */
function potpackLayout(resources: ImageResource[], options: LayoutOptions): LayoutResult {
  console.log('🔄 Potpack: 开始布局', {
    count: resources.length,
    allowRotation: options.allowRotation
  });

  // 🎯 如果启用旋转，预处理资源以找到最佳方向
  const processedResources = resources.map((resource, index) => {
    const originalW = resource.width || 64;
    const originalH = resource.height || 64;
    let finalW = originalW;
    let finalH = originalH;
    let shouldRotate = false;

    // 🎯 旋转优化：如果高度大于宽度且启用旋转，考虑旋转
    if (options.allowRotation && originalH > originalW) {
      // 简单启发式：如果图片是竖直的，尝试旋转看是否能更好地利用空间
      const aspectRatio = originalH / originalW;
      if (aspectRatio > 1.5) { // 高宽比大于1.5时考虑旋转
        finalW = originalH;
        finalH = originalW;
        shouldRotate = true;
      }
    }

    return {
      resource,
      index,
      w: finalW,
      h: finalH,
      rotated: shouldRotate,
      originalW,
      originalH
    };
  });

  // 准备potpack输入数据
  const boxes = processedResources.map(item => ({
    w: item.w,
    h: item.h,
    index: item.index,
    rotated: item.rotated
  }));

  // 按面积排序（potpack要求）
  boxes.sort((a, b) => (b.w * b.h) - (a.w * a.h));

  console.log('🔄 Potpack: 处理图片', {
    count: boxes.length,
    largest: `${boxes[0]?.w}×${boxes[0]?.h}`,
    smallest: `${boxes[boxes.length-1]?.w}×${boxes[boxes.length-1]?.h}`,
    rotatedCount: boxes.filter(b => b.rotated).length
  });

  // 执行打包
  const { w, h, fill } = potpack(boxes);

  // 转换结果，应用padding作为间距
  const items: LayoutItem[] = boxes.map(box => {
    const processedItem = processedResources[box.index];
    return {
      x: (box as any).x + options.padding,
      y: (box as any).y + options.padding,
      width: box.w,  // 使用处理后的尺寸
      height: box.h, // 使用处理后的尺寸
      resource: resources[box.index],
      rotated: processedItem.rotated
    };
  });

  // 计算实际需要的canvas尺寸（包含padding）
  const finalWidth = w + options.padding * 2;
  const finalHeight = h + options.padding * 2;

  // 重新计算效率
  const totalArea = items.reduce((sum, item) => sum + item.width * item.height, 0);
  const efficiency = totalArea / (finalWidth * finalHeight);

  console.log('🔄 Potpack: 布局完成', {
    originalSize: `${options.maxWidth}×${options.maxHeight}`,
    actualSize: `${finalWidth}×${finalHeight}`,
    efficiency: `${(efficiency * 100).toFixed(1)}%`,
    rotatedItems: items.filter(item => item.rotated).length
  });

  return {
    items,
    width: finalWidth,
    height: finalHeight,
    efficiency
  };
}

/**
 * Guillotine算法 - 真正的实现
 */
function guillotineLayout(resources: ImageResource[], options: LayoutOptions): LayoutResult {
  console.log('🔄 Guillotine: 开始布局', {
    resourceCount: resources.length,
    maxSize: `${options.maxWidth}×${options.maxHeight}`,
    allowRotation: options.allowRotation
  });

  if (resources.length === 0) {
    return { items: [], width: 0, height: 0, efficiency: 0 };
  }

  // 按面积从大到小排序
  const sortedResources = [...resources].sort((a, b) =>
    ((b.width || 64) * (b.height || 64)) - ((a.width || 64) * (a.height || 64))
  );

  const items: LayoutItem[] = [];
  const freeRects: Rectangle[] = [{ x: 0, y: 0, width: options.maxWidth, height: options.maxHeight }];

  for (const resource of sortedResources) {
    const resourceWidth = resource.width || 64;
    const resourceHeight = resource.height || 64;

    let bestRect: Rectangle | null = null;
    let bestRotated = false;
    let bestScore = Infinity;

    // 尝试找到最佳的矩形位置
    for (const rect of freeRects) {
      // 尝试不旋转
      if (rect.width >= resourceWidth + options.padding &&
          rect.height >= resourceHeight + options.padding) {
        const score = calculateGuillotineScore(rect, resourceWidth, resourceHeight);
        if (score < bestScore) {
          bestScore = score;
          bestRect = rect;
          bestRotated = false;
        }
      }

      // 尝试旋转（如果允许且有益）
      if (options.allowRotation &&
          resourceWidth !== resourceHeight && // 只有非正方形才需要旋转
          rect.width >= resourceHeight + options.padding &&
          rect.height >= resourceWidth + options.padding) {
        const score = calculateGuillotineScore(rect, resourceHeight, resourceWidth);
        if (score < bestScore) {
          bestScore = score;
          bestRect = rect;
          bestRotated = true;
        }
      }
    }

    if (bestRect) {
      const finalWidth = bestRotated ? resourceHeight : resourceWidth;
      const finalHeight = bestRotated ? resourceWidth : resourceHeight;

      // 添加到结果
      items.push({
        x: bestRect.x + options.padding,
        y: bestRect.y + options.padding,
        width: finalWidth,
        height: finalHeight,
        resource,
        rotated: bestRotated
      });

      console.log('🔄 Guillotine: 放置图片', {
        name: resource.name,
        position: `(${bestRect.x + options.padding},${bestRect.y + options.padding})`,
        size: `${finalWidth}×${finalHeight}`,
        rotated: bestRotated
      });

      // 分割矩形
      splitRectangle(freeRects, bestRect, finalWidth + options.padding * 2, finalHeight + options.padding * 2);
    } else {
      console.warn('🔄 Guillotine: 无法放置图片', resource.name);
    }
  }

  // 计算实际使用的尺寸
  const maxX = Math.max(...items.map(item => item.x + item.width + options.padding), 0);
  const maxY = Math.max(...items.map(item => item.y + item.height + options.padding), 0);

  const totalArea = items.reduce((sum, item) => sum + item.width * item.height, 0);
  const efficiency = maxX > 0 && maxY > 0 ? totalArea / (maxX * maxY) : 0;

  console.log('🔄 Guillotine: 布局完成', {
    finalSize: `${maxX}×${maxY}`,
    efficiency: `${(efficiency * 100).toFixed(1)}%`,
    itemCount: items.length
  });

  return {
    items,
    width: maxX,
    height: maxY,
    efficiency
  };
}

// Guillotine 算法辅助接口和函数
interface Rectangle {
  x: number;
  y: number;
  width: number;
  height: number;
}

function calculateGuillotineScore(rect: Rectangle, itemWidth: number, itemHeight: number): number {
  // 使用最佳短边适应 (Best Short Side Fit) 启发式
  const leftoverHorizontal = rect.width - itemWidth;
  const leftoverVertical = rect.height - itemHeight;
  const shortSide = Math.min(leftoverHorizontal, leftoverVertical);
  const longSide = Math.max(leftoverHorizontal, leftoverVertical);

  return shortSide * 1000 + longSide; // 优先选择短边剩余最小的
}

function splitRectangle(freeRects: Rectangle[], usedRect: Rectangle, itemWidth: number, itemHeight: number) {
  // 移除使用的矩形
  const index = freeRects.indexOf(usedRect);
  if (index > -1) {
    freeRects.splice(index, 1);
  }

  // 生成新的自由矩形
  const x = usedRect.x;
  const y = usedRect.y;
  const w = usedRect.width;
  const h = usedRect.height;

  // 右侧剩余矩形
  if (x + itemWidth < x + w) {
    freeRects.push({
      x: x + itemWidth,
      y: y,
      width: w - itemWidth,
      height: itemHeight
    });
  }

  // 下方剩余矩形
  if (y + itemHeight < y + h) {
    freeRects.push({
      x: x,
      y: y + itemHeight,
      width: itemWidth,
      height: h - itemHeight
    });
  }

  // 右下角剩余矩形
  if (x + itemWidth < x + w && y + itemHeight < y + h) {
    freeRects.push({
      x: x + itemWidth,
      y: y + itemHeight,
      width: w - itemWidth,
      height: h - itemHeight
    });
  }

  // 清理重叠的矩形
  cleanupOverlappingRects(freeRects);
}

function cleanupOverlappingRects(freeRects: Rectangle[]) {
  for (let i = 0; i < freeRects.length; i++) {
    for (let j = i + 1; j < freeRects.length; j++) {
      if (isRectangleInside(freeRects[i], freeRects[j])) {
        freeRects.splice(i, 1);
        i--;
        break;
      } else if (isRectangleInside(freeRects[j], freeRects[i])) {
        freeRects.splice(j, 1);
        j--;
      }
    }
  }
}

function isRectangleInside(inner: Rectangle, outer: Rectangle): boolean {
  return inner.x >= outer.x &&
         inner.y >= outer.y &&
         inner.x + inner.width <= outer.x + outer.width &&
         inner.y + inner.height <= outer.y + outer.height;
}

/**
 * 原始顺序布局 - 保持图片的原始网格位置，不重新排列
 * 🎯 专门用于裁切后的小图片，完全按照原始网格布局
 */
function originalOrderLayout(resources: ImageResource[], options: LayoutOptions): LayoutResult {
  console.log('🔄 OriginalOrder: 开始布局（保持原始网格位置）', {
    resourceCount: resources.length,
    padding: options.padding
  });

  if (resources.length === 0) {
    return { items: [], width: 0, height: 0, efficiency: 0 };
  }

  // 🎯 尝试解析裁切图片的网格位置信息
  const gridItems: Array<{
    resource: ImageResource;
    col: number;
    row: number;
    width: number;
    height: number;
    parsed: boolean;
  }> = [];

  let maxCol = 0;
  let maxRow = 0;
  let cellWidth = 0;
  let cellHeight = 0;

  // 解析每个图片的网格位置
  for (const resource of resources) {
    const parsed = parseGridPosition(resource.name);
    if (parsed) {
      gridItems.push({
        resource,
        col: parsed.col,
        row: parsed.row,
        width: parsed.width,
        height: parsed.height,
        parsed: true
      });
      maxCol = Math.max(maxCol, parsed.col);
      maxRow = Math.max(maxRow, parsed.row);
      cellWidth = parsed.width;
      cellHeight = parsed.height;
    } else {
      // 如果无法解析，使用默认位置
      gridItems.push({
        resource,
        col: gridItems.length % 10, // 默认每行10个
        row: Math.floor(gridItems.length / 10),
        width: resource.width || 64,
        height: resource.height || 64,
        parsed: false
      });
    }
  }

  const hasGridInfo = gridItems.some(item => item.parsed);

  if (hasGridInfo) {
    console.log('🎯 OriginalOrder: 检测到网格信息，使用真实网格布局', {
      gridSize: `${maxCol + 1}×${maxRow + 1}`,
      cellSize: `${cellWidth}×${cellHeight}`,
      parsedCount: gridItems.filter(item => item.parsed).length
    });

    // 🎯 按照真实的网格位置布局
    const boundingBoxPadding = options.boundingBoxPadding || 0;
    const items: LayoutItem[] = gridItems.map(item => ({
      x: boundingBoxPadding + item.col * (item.width + options.padding),
      y: boundingBoxPadding + item.row * (item.height + options.padding),
      width: item.width,
      height: item.height,
      resource: item.resource,
      rotated: false
    }));

    // 计算最终尺寸（包含包围盒边距）
    const finalWidth = (maxCol + 1) * cellWidth + maxCol * options.padding + boundingBoxPadding * 2;
    const finalHeight = (maxRow + 1) * cellHeight + maxRow * options.padding + boundingBoxPadding * 2;

    // 计算效率
    const totalArea = items.reduce((sum, item) => sum + item.width * item.height, 0);
    const efficiency = finalWidth > 0 && finalHeight > 0 ? totalArea / (finalWidth * finalHeight) : 0;

    console.log('🔄 OriginalOrder: 网格布局完成', {
      finalSize: `${finalWidth}×${finalHeight}`,
      efficiency: `${(efficiency * 100).toFixed(1)}%`,
      itemCount: items.length,
      gridLayout: '✅ 真实网格位置'
    });

    return {
      items,
      width: finalWidth,
      height: finalHeight,
      efficiency
    };

  } else {
    console.log('🎯 OriginalOrder: 未检测到网格信息，使用流式布局');

    // 🎯 回退到流式布局（保持原始顺序）
    const boundingBoxPadding = options.boundingBoxPadding || 0;
    const items: LayoutItem[] = [];
    let currentX = boundingBoxPadding;
    let currentY = boundingBoxPadding;
    let rowHeight = 0;
    let maxWidth = 0;

    for (let i = 0; i < resources.length; i++) {
      const resource = resources[i];
      const itemWidth = resource.width || 64;
      const itemHeight = resource.height || 64;

      // 检查是否需要换行
      if (currentX + itemWidth + boundingBoxPadding > options.maxWidth && currentX > boundingBoxPadding) {
        currentX = boundingBoxPadding;
        currentY += rowHeight + options.padding;
        rowHeight = 0;
      }

      items.push({
        x: currentX,
        y: currentY,
        width: itemWidth,
        height: itemHeight,
        resource,
        rotated: false
      });

      currentX += itemWidth + options.padding;
      rowHeight = Math.max(rowHeight, itemHeight);
      maxWidth = Math.max(maxWidth, currentX - options.padding + boundingBoxPadding);
    }

    const finalWidth = maxWidth;
    const finalHeight = currentY + rowHeight + boundingBoxPadding;
    const totalArea = items.reduce((sum, item) => sum + item.width * item.height, 0);
    const efficiency = finalWidth > 0 && finalHeight > 0 ? totalArea / (finalWidth * finalHeight) : 0;

    console.log('🔄 OriginalOrder: 流式布局完成', {
      finalSize: `${finalWidth}×${finalHeight}`,
      efficiency: `${(efficiency * 100).toFixed(1)}%`,
      itemCount: items.length,
      fallbackLayout: '✅ 保持输入顺序'
    });

    return {
      items,
      width: finalWidth,
      height: finalHeight,
      efficiency
    };
  }
}

/**
 * 解析裁切图片名称中的网格位置信息
 * 格式：{col}_{row}_{width}_{height}
 */
function parseGridPosition(imageName: string): {
  col: number;
  row: number;
  width: number;
  height: number;
} | null {
  // 匹配格式：数字_数字_数字_数字
  const match = imageName.match(/^(\d+)_(\d+)_(\d+)_(\d+)$/);
  if (match) {
    return {
      col: parseInt(match[1], 10),
      row: parseInt(match[2], 10),
      width: parseInt(match[3], 10),
      height: parseInt(match[4], 10)
    };
  }
  return null;
}

/**
 * 计算下一个2的幂
 */
function nextPowerOfTwo(value: number): number {
  return Math.pow(2, Math.ceil(Math.log2(value)));
}
