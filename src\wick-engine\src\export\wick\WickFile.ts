/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

declare global {
  interface Window {
    Wick: any;
  }
}

interface WickFileOptions {
  width?: number;
  height?: number;
  onProgress?: (frame: number, maxFrames: number) => void;
  onFinish: (wickFileData: string) => void;
  format?: "base64" | "json";
}

export class WickFile {
  /**
   * Create a Wick file from a project.
   * @param project - The project to create a Wick file from
   * @param options - Configuration options for Wick file generation
   */
  static toWickFile(project: any, options: WickFileOptions): void {
    const { onProgress, onFinish, format = "json" } = options;

    project.generateWickFile({
      width: options.width,
      height: options.height,
      onFinish: (wickData: string) => {
        onFinish(format === "base64" ? btoa(wickData) : wickData);
      },
      onProgress: onProgress,
    });
  }

  /**
   * Load a project from a Wick file.
   * @param wickFileData - The Wick file data to load
   * @param callback - Function that receives the loaded project
   */
  static fromWickFile(
    wickFileData: string,
    callback: (project: any) => void
  ): void {
    try {
      const projectData = JSON.parse(wickFileData);
      const project = new (window.Wick.Project as any)(projectData);
      callback(project);
    } catch (e) {
      console.error("WickFile: Error loading project from Wick file");
      console.error(e);
      callback(null);
    }
  }
}

// Add to global Wick namespace
if (typeof window !== "undefined") {
  window.Wick = window.Wick || {};
  window.Wick.WickFile = WickFile;
}
