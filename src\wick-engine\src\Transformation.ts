/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

interface TransformationArgs {
  x?: number;
  y?: number;
  scaleX?: number;
  scaleY?: number;
  rotation?: number;
  opacity?: number;
}

interface TransformationValues {
  x: number;
  y: number;
  scaleX: number;
  scaleY: number;
  rotation: number;
  opacity: number;
}

/** 表示一个变换的类 */
export class Transformation {
  protected x: number;
  protected y: number;
  protected scaleX: number;
  protected scaleY: number;
  protected rotation: number;
  protected opacity: number;

  /**
   * 创建一个变换
   * @param args - 变换参数
   */
  constructor(args: TransformationArgs = {}) {
    this.x = args.x === undefined ? 0 : args.x;
    this.y = args.y === undefined ? 0 : args.y;
    this.scaleX = args.scaleX === undefined ? 1 : args.scaleX;
    this.scaleY = args.scaleY === undefined ? 1 : args.scaleY;
    this.rotation = args.rotation === undefined ? 0 : args.rotation;
    this.opacity = args.opacity === undefined ? 1 : args.opacity;
  }

  /**
   * 包含此变换值的对象
   */
  get values(): TransformationValues {
    return {
      x: this.x,
      y: this.y,
      scaleX: this.scaleX,
      scaleY: this.scaleY,
      rotation: this.rotation,
      opacity: this.opacity,
    };
  }

  /**
   * 创建此变换的副本
   * @returns 复制的变换
   */
  copy(): Transformation {
    return new Transformation(this.values);
  }
}
