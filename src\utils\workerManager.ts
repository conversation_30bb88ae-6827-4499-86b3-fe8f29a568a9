/**
 * Web Worker管理器
 * 管理图片处理Worker的生命周期、任务分发和负载均衡
 */

import type { WorkerMessage, WorkerResponse } from '../workers/imageProcessor.worker';

interface WorkerTask {
  id: string;
  resolve: (result: any) => void;
  reject: (error: Error) => void;
  onProgress?: (progress: any) => void;
  timeout?: number;
}

interface WorkerInstance {
  worker: Worker;
  busy: boolean;
  taskCount: number;
  lastUsed: number;
}

export class WorkerManager {
  private workers: WorkerInstance[] = [];
  private tasks = new Map<string, WorkerTask>();
  private taskQueue: (() => void)[] = [];
  private maxWorkers: number;
  private workerTimeout = 30000; // 30秒超时

  constructor(maxWorkers?: number) {
    // 根据CPU核心数确定Worker数量
    this.maxWorkers = maxWorkers || Math.min(navigator.hardwareConcurrency || 2, 4);
    console.log('🏭 WorkerManager: 初始化', { maxWorkers: this.maxWorkers });
  }

  /**
   * 获取可用的Worker
   */
  private async getAvailableWorker(): Promise<WorkerInstance> {
    // 查找空闲的Worker
    let availableWorker = this.workers.find(w => !w.busy);

    if (!availableWorker && this.workers.length < this.maxWorkers) {
      // 创建新Worker
      availableWorker = await this.createWorker();
    }

    if (!availableWorker) {
      // 选择任务最少的Worker
      availableWorker = this.workers.reduce((min, current) => 
        current.taskCount < min.taskCount ? current : min
      );
    }

    return availableWorker;
  }

  /**
   * 创建新的Worker实例
   */
  private async createWorker(): Promise<WorkerInstance> {
    const worker = new Worker(
      new URL('../workers/imageProcessor.worker.ts', import.meta.url),
      { type: 'module' }
    );

    const workerInstance: WorkerInstance = {
      worker,
      busy: false,
      taskCount: 0,
      lastUsed: Date.now()
    };

    // 设置消息处理器
    worker.onmessage = (event: MessageEvent<WorkerResponse>) => {
      this.handleWorkerMessage(workerInstance, event.data);
    };

    worker.onerror = (error) => {
      console.error('🚨 WorkerManager: Worker错误', error);
      this.handleWorkerError(workerInstance, error);
    };

    this.workers.push(workerInstance);
    console.log('👷 WorkerManager: 创建新Worker', { 
      totalWorkers: this.workers.length 
    });

    return workerInstance;
  }

  /**
   * 处理Worker消息
   */
  private handleWorkerMessage(workerInstance: WorkerInstance, response: WorkerResponse) {
    const { id, type, payload } = response;
    const task = this.tasks.get(id);

    if (!task) {
      console.warn('⚠️ WorkerManager: 收到未知任务的响应', { id, type });
      return;
    }

    switch (type) {
      case 'success':
        this.completeTask(workerInstance, id, payload);
        break;
      case 'error':
        this.failTask(workerInstance, id, new Error(payload.message || '处理失败'));
        break;
      case 'progress':
        if (task.onProgress) {
          task.onProgress(payload);
        }
        break;
    }
  }

  /**
   * 处理Worker错误
   */
  private handleWorkerError(workerInstance: WorkerInstance, error: ErrorEvent) {
    console.error('🚨 WorkerManager: Worker发生错误', error);
    
    // 找到该Worker的所有任务并标记为失败
    for (const [taskId, task] of this.tasks.entries()) {
      task.reject(new Error(`Worker错误: ${error.message}`));
      this.tasks.delete(taskId);
    }

    // 重启Worker
    this.restartWorker(workerInstance);
  }

  /**
   * 完成任务
   */
  private completeTask(workerInstance: WorkerInstance, taskId: string, result: any) {
    const task = this.tasks.get(taskId);
    if (task) {
      task.resolve(result);
      this.tasks.delete(taskId);
    }

    workerInstance.busy = false;
    workerInstance.taskCount--;
    workerInstance.lastUsed = Date.now();

    // 处理队列中的下一个任务
    this.processQueue();
  }

  /**
   * 任务失败
   */
  private failTask(workerInstance: WorkerInstance, taskId: string, error: Error) {
    const task = this.tasks.get(taskId);
    if (task) {
      task.reject(error);
      this.tasks.delete(taskId);
    }

    workerInstance.busy = false;
    workerInstance.taskCount--;
    workerInstance.lastUsed = Date.now();

    // 处理队列中的下一个任务
    this.processQueue();
  }

  /**
   * 重启Worker
   */
  private async restartWorker(workerInstance: WorkerInstance) {
    try {
      workerInstance.worker.terminate();
      const index = this.workers.indexOf(workerInstance);
      if (index !== -1) {
        this.workers.splice(index, 1);
      }
      
      // 创建新的Worker替换
      await this.createWorker();
      console.log('🔄 WorkerManager: Worker已重启');
    } catch (error) {
      console.error('❌ WorkerManager: 重启Worker失败', error);
    }
  }

  /**
   * 处理任务队列
   */
  private processQueue() {
    if (this.taskQueue.length > 0) {
      const nextTask = this.taskQueue.shift();
      if (nextTask) {
        nextTask();
      }
    }
  }

  /**
   * 执行任务
   */
  async executeTask<T>(
    type: string,
    payload: any,
    options: {
      onProgress?: (progress: any) => void;
      timeout?: number;
    } = {}
  ): Promise<T> {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    return new Promise<T>((resolve, reject) => {
      const task: WorkerTask = {
        id: taskId,
        resolve,
        reject,
        onProgress: options.onProgress,
        timeout: options.timeout || this.workerTimeout
      };

      this.tasks.set(taskId, task);

      // 设置超时
      const timeoutId = setTimeout(() => {
        if (this.tasks.has(taskId)) {
          this.tasks.delete(taskId);
          reject(new Error('任务超时'));
        }
      }, task.timeout);

      // 包装resolve以清除超时
      const originalResolve = task.resolve;
      task.resolve = (result: any) => {
        clearTimeout(timeoutId);
        originalResolve(result);
      };

      const originalReject = task.reject;
      task.reject = (error: Error) => {
        clearTimeout(timeoutId);
        originalReject(error);
      };

      // 尝试立即执行或加入队列
      this.scheduleTask(taskId, type, payload);
    });
  }

  /**
   * 调度任务
   */
  private async scheduleTask(taskId: string, type: string, payload: any) {
    try {
      const worker = await this.getAvailableWorker();
      
      worker.busy = true;
      worker.taskCount++;
      worker.lastUsed = Date.now();

      const message: WorkerMessage = {
        id: taskId,
        type: type as any,
        payload
      };

      worker.worker.postMessage(message);

      console.log('📤 WorkerManager: 任务已发送', {
        taskId,
        type,
        workerIndex: this.workers.indexOf(worker)
      });

    } catch (error) {
      const task = this.tasks.get(taskId);
      if (task) {
        task.reject(error instanceof Error ? error : new Error('调度任务失败'));
        this.tasks.delete(taskId);
      }
    }
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      totalWorkers: this.workers.length,
      busyWorkers: this.workers.filter(w => w.busy).length,
      pendingTasks: this.tasks.size,
      queuedTasks: this.taskQueue.length,
      workers: this.workers.map((w, index) => ({
        index,
        busy: w.busy,
        taskCount: w.taskCount,
        lastUsed: w.lastUsed
      }))
    };
  }

  /**
   * 清理空闲的Worker
   */
  cleanup() {
    const now = Date.now();
    const maxIdleTime = 5 * 60 * 1000; // 5分钟

    this.workers = this.workers.filter(worker => {
      if (!worker.busy && now - worker.lastUsed > maxIdleTime) {
        worker.worker.terminate();
        console.log('🧹 WorkerManager: 清理空闲Worker');
        return false;
      }
      return true;
    });
  }

  /**
   * 销毁所有Worker
   */
  destroy() {
    this.workers.forEach(worker => {
      worker.worker.terminate();
    });
    this.workers = [];
    this.tasks.clear();
    this.taskQueue = [];
    console.log('🧹 WorkerManager: 已销毁所有Worker');
  }
}

// 全局实例
export const workerManager = new WorkerManager();

// 定期清理空闲Worker
setInterval(() => {
  workerManager.cleanup();
}, 60000); // 每分钟检查一次
