/**
 * 二进制项目保存和加载管理器
 * 使用二进制格式直接存储项目数据，无需数据转换，极大提升性能
 */

import { TauriAPI } from '../lib/tauriAPI';
import { atlasStore } from '../stores/atlasStore';
import { resourceStore } from '../stores/resourceStore';
import { exportSettingsActions } from '../stores/exportSettingsStore';
import { get } from 'svelte/store';

/// 二进制保存结果
interface BinarySaveResult {
  success: boolean;
  filePath?: string;
  fileSize?: number;
  error?: string;
  format: string;
}

/// 二进制加载结果
interface BinaryLoadResult {
  success: boolean;
  data?: any;
  error?: string;
  warnings?: string[];
  format: string;
}

/// 统一保存结果
interface UnifiedSaveResult {
  success: boolean;
  filePath?: string;
  fileSize?: number;
  error?: string;
  format: string;
}

/// 统一加载结果
interface UnifiedLoadResult {
  success: boolean;
  data?: any;
  error?: string;
  warnings?: string[];
  format: string;
}

/**
 * 🎯 二进制项目保存管理器
 * 核心优势：零数据转换，极速保存和加载
 */
export class BinaryProjectSave {

  /**
   * 🎯 保存项目 - 二进制格式
   */
  public async save(
    filePath: string,
    projectName: string,
    description?: string,
    tags?: string[]
  ): Promise<UnifiedSaveResult> {
    try {
      console.log('🚀 BinaryProjectSave: 开始二进制保存项目', { filePath, projectName });

      // 1. 收集所有数据 - 直接使用原始格式，无任何处理
      const imageResources = this.collectImageResources();
      const atlasData = this.collectAtlasData();
      const exportSettings = await this.collectExportSettings();

      console.log('📊 BinaryProjectSave: 数据收集完成', {
        imageResourceCount: imageResources?.resources?.length || 0,
        atlasDataSize: JSON.stringify(atlasData).length,
        exportSettingsSize: JSON.stringify(exportSettings).length
      });

      // 2. 调用后端二进制保存API
      const result = await TauriAPI.Project.saveProjectBinary(
        filePath,
        projectName,
        description,
        tags,
        imageResources,
        atlasData,
        exportSettings
      );

      if (result.success && result.data) {
        console.log('✅ BinaryProjectSave: 二进制保存成功', {
          filePath: result.data.filePath,
          fileSize: result.data.fileSize,
          format: result.data.format
        });

        return {
          success: true,
          filePath: result.data.filePath,
          fileSize: result.data.fileSize,
          format: result.data.format
        };
      } else {
        console.error('❌ BinaryProjectSave: 二进制保存失败', result.error);
        return {
          success: false,
          error: result.error,
          format: 'binary'
        };
      }
    } catch (error) {
      console.error('❌ BinaryProjectSave: 保存过程中发生错误', error);
      return {
        success: false,
        error: `保存失败: ${error}`,
        format: 'binary'
      };
    }
  }

  /**
   * 🎯 加载项目 - 二进制格式
   */
  public async load(filePath: string): Promise<UnifiedLoadResult> {
    try {
      console.log('🚀 BinaryProjectSave: 开始二进制加载项目', { filePath });

      // 调用后端二进制加载API
      const result = await TauriAPI.Project.loadProjectBinary(filePath);

      if (result.success && result.data) {
        console.log('✅ BinaryProjectSave: 二进制加载成功', {
          format: result.data.format,
          hasWarnings: result.data.warnings?.length > 0
        });

        // 直接恢复项目数据，无需任何处理
        await this.restoreProjectData(result.data.data);

        return {
          success: true,
          data: result.data.data,
          warnings: result.data.warnings,
          format: result.data.format
        };
      } else {
        console.error('❌ BinaryProjectSave: 二进制加载失败', result.error);
        return {
          success: false,
          error: result.error,
          format: 'binary'
        };
      }
    } catch (error) {
      console.error('❌ BinaryProjectSave: 加载过程中发生错误', error);
      return {
        success: false,
        error: `加载失败: ${error}`,
        format: 'binary'
      };
    }
  }

  /**
   * 🎯 验证二进制项目文件
   */
  public async validate(filePath: string): Promise<boolean> {
    try {
      const result = await TauriAPI.Project.validateProjectBinary(filePath);
      return result.success && result.data === true;
    } catch (error) {
      console.error('❌ BinaryProjectSave: 验证失败', error);
      return false;
    }
  }

  /**
   * 🎯 收集图片资源数据 - 直接使用原始格式
   */
  private collectImageResources(): any {
    const state = get(resourceStore);
    const resources = state.rootResources;

    console.log('📂 BinaryProjectSave: 收集图片资源', {
      resourceCount: resources.length
    });

    // 🎯 关键：直接返回原始数据，包含 ArrayBuffer 和 processedData
    return {
      resources: resources,
      metadata: {
        totalCount: resources.length,
        collectedAt: new Date().toISOString()
      }
    };
  }

  /**
   * 🎯 收集图集数据 - 直接使用原始格式
   */
  private collectAtlasData(): any {
    return atlasStore.exportAtlasData();
  }

  /**
   * 🎯 收集导出设置 - 直接使用原始格式
   */
  private async collectExportSettings(): Promise<any> {
    return await exportSettingsActions.getCurrentSettings();
  }

  /**
   * 🎯 恢复项目数据到各个store - 处理字符串格式的数据
   */
  private async restoreProjectData(data: any): Promise<void> {
    console.log('🔄 BinaryProjectSave: 开始恢复项目数据');

    try {
      // 恢复图集数据 - 从字符串解析
      if (data.atlas_data) {
        const atlasData = typeof data.atlas_data === 'string'
          ? JSON.parse(data.atlas_data)
          : data.atlas_data;
        atlasStore.importAtlasData(atlasData);
        console.log('✅ BinaryProjectSave: 图集数据恢复完成');
      }

      // 恢复资源数据 - 从字符串解析
      if (data.resource_data) {
        const resourceData = typeof data.resource_data === 'string'
          ? JSON.parse(data.resource_data)
          : data.resource_data;
        await this.restoreResourceData(resourceData);
        console.log('✅ BinaryProjectSave: 资源数据恢复完成');
      }

      // 恢复导出设置 - 从字符串解析
      if (data.export_settings) {
        const exportSettings = typeof data.export_settings === 'string'
          ? JSON.parse(data.export_settings)
          : data.export_settings;
        exportSettingsActions.updateSettings(exportSettings);
        console.log('✅ BinaryProjectSave: 导出设置恢复完成');
      }

      console.log('✅ BinaryProjectSave: 所有数据恢复完成');
    } catch (error) {
      console.error('❌ BinaryProjectSave: 数据恢复过程中发生错误', error);
      throw error;
    }
  }

  /**
   * 🎯 恢复资源数据 - 直接使用，无需转换
   */
  private async restoreResourceData(resourceData: any): Promise<void> {
    if (!resourceData || !resourceData.resources) {
      console.log('📂 BinaryProjectSave: 没有资源数据需要恢复');
      return;
    }

    console.log('🔄 BinaryProjectSave: 开始恢复资源数据', {
      resourceCount: resourceData.resources.length
    });

    // 🎯 关键：直接恢复资源，数据已经包含 ArrayBuffer 和 processedData
    const restoredResources = this.restoreArrayBuffersInResources(resourceData.resources);

    // 🎯 使用正确的 resourceStore API：直接更新 rootResources
    resourceStore.update(state => ({
      ...state,
      rootResources: restoredResources,
      currentFolder: null,
      navigationStack: [],
      selectedResource: null
    }));

    console.log('✅ BinaryProjectSave: 资源数据恢复完成');
  }

  /**
   * 🎯 恢复资源中的ArrayBuffer - 从数组格式转换回ArrayBuffer
   */
  private restoreArrayBuffersInResources(resources: any[]): any[] {
    return resources.map(resource => {
      const restoredResource = { ...resource };

      // 处理图片资源的data字段
      if (restoredResource.type === 'image' && restoredResource.data && Array.isArray(restoredResource.data)) {
        // 将数字数组转换回ArrayBuffer
        const uint8Array = new Uint8Array(restoredResource.data);
        restoredResource.data = uint8Array.buffer;

        console.log('🔄 BinaryProjectSave: 恢复ArrayBuffer', {
          name: restoredResource.name,
          size: uint8Array.length
        });
      }

      // 递归处理子资源
      if (restoredResource.children && Array.isArray(restoredResource.children)) {
        restoredResource.children = this.restoreArrayBuffersInResources(restoredResource.children);
      }

      return restoredResource;
    });
  }
}

// 导出单例实例
export const binaryProjectSave = new BinaryProjectSave();
