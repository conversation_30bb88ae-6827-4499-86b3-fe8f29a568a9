/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { Button } from "./Button";
import { GUIElement } from "./GUIElement";
import { Bounds } from "./GUIElement";

export class BreadcrumbsButton extends Button {
  buttonWidth: number;

  constructor(model: Base) {
    super(model, {
      clickFn: () => {
        this.model.project.focus = model;
        this.projectWasModified();
      },
    });
  }

  draw(): void {
    super.draw();

    const ctx = this.ctx;

    // Button label settings
    ctx.font = "14px Nunito Sans";
    const textContent = this.model.identifier || "Clip";
    const textWidth = ctx.measureText(textContent).width;
    const textX = GUIElement.BREADCRUMBS_PADDING;
    const textY =
      GUIElement.BREADCRUMBS_HEIGHT / 2 + GUIElement.BREADCRUMBS_PADDING;

    // Fill color based on mouse interactions
    let buttonBodyColor = "red";
    if (this.model === this.model.project.focus) {
      buttonBodyColor = GUIElement.BREADCRUMBS_ACTIVE_BUTTON_FILL_COLOR;
    } else if (this.mouseState === "down") {
      buttonBodyColor = GUIElement.BREADCRUMBS_INACTIVE_BUTTON_FILL_COLOR;
    } else if (this.mouseState === "over") {
      buttonBodyColor = GUIElement.BREADCRUMBS_HOVER_BUTTON_FILL_COLOR;
    } else {
      buttonBodyColor = GUIElement.BREADCRUMBS_INACTIVE_BUTTON_FILL_COLOR;
    }

    const buttonWidth = textWidth + GUIElement.BREADCRUMBS_PADDING * 2;
    this.buttonWidth = buttonWidth; // Save how large the button is to use in other places...

    // Button body
    ctx.fillStyle = buttonBodyColor;
    ctx.beginPath();
    ctx.roundRect(
      0,
      0,
      buttonWidth,
      GUIElement.BREADCRUMBS_HEIGHT,
      GUIElement.FRAME_BORDER_RADIUS
    );
    ctx.fill();

    ctx.beginPath();
    ctx.rect(
      0,
      GUIElement.BREADCRUMBS_HEIGHT / 2,
      buttonWidth,
      GUIElement.BREADCRUMBS_HEIGHT / 2
    );
    ctx.fill();

    // Add the active highlight to the tab if necessary.
    if (this.model === this.model.project.focus) {
      ctx.fillStyle = GUIElement.BREADCRUMBS_ACTIVE_BORDER_COLOR;
      ctx.beginPath();
      ctx.rect(
        0,
        GUIElement.BREADCRUMBS_HEIGHT - GUIElement.BREADCRUMBS_HIGHLIGHT_HEIGHT,
        buttonWidth,
        GUIElement.BREADCRUMBS_HIGHLIGHT_HEIGHT
      );
      ctx.fill();
    }

    // Button label text
    ctx.fillStyle = "#BBBBBB";
    ctx.fillText(textContent, textX, textY);
  }

  get bounds(): Bounds {
    return {
      x: 0,
      y: 0,
      width: this.buttonWidth,
      height: GUIElement.BREADCRUMBS_HEIGHT,
    };
  }
}
