/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { Tooltip } from "./Tooltip";

interface ButtonArgs {
  clickFn?: (e: MouseEvent) => void;
  tooltip?: string;
}

export class Button extends GUIElement {
  protected _clickFn: ((e: MouseEvent) => void) | undefined;
  protected _tooltip: string | undefined;
  protected tooltip: Tooltip;
  protected lastPressed: number;

  /**
   * Create a new button.
   * @param {Base} model - See GUIElement constructor
   * @param {ButtonArgs} args - Button configuration options
   * @param {function} args.clickFn - The function to call when the button is clicked
   * @param {string} args.tooltip - (Optional) The title of the tooltip
   */
  constructor(model: Base, args: ButtonArgs = {}) {
    super(model);

    this._clickFn = args.clickFn;
    this._tooltip = args.tooltip;

    this.tooltip = new Tooltip(this.model, this._tooltip);

    this.cursor = "pointer";

    this.lastPressed = 0;
  }

  draw(): void {
    super.draw();
  }

  onMouseDown(e: MouseEvent): void {
    const now = Date.now();
    const timeSince = now - this.lastPressed;

    // Require 100 ms between clicks.
    // This helps ensure that double events are not counted immediately.
    if (timeSince > 150 && this._clickFn) {
      this._clickFn(e);
      this.lastPressed = now;
    }
  }
}
