/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { FileAsset } from "./FileAsset";

export class SVGAsset extends FileAsset {
  /**
   * Returns all valid MIME types for files which can be converted to SVGAssets.
   * @return {string[]} Array of strings of MIME types in the form MediaType/Subtype.
   */
  static getValidMIMETypes(): string[] {
    return ["image/svg+xml"];
  }

  /**
   * Returns all valid extensions types for files which can be attempted to be
   * converted to SVGAssets.
   * @return  {string[]} Array of strings representing extensions.
   */
  static getValidExtensions(): string[] {
    return [".svg"];
  }

  /**
   * Create a new SVGAsset.
   * @param {FileAssetArgs} args - Asset constructor arguments
   */
  constructor(args: FileAssetArgs = {}) {
    super(args);
  }

  protected _serialize(args?: SerializeArgs): Record<string, any> {
    return super._serialize(args);
  }

  protected _deserialize(data: Record<string, any>): void {
    super._deserialize(data);
  }

  public get classname(): string {
    return "SVGAsset";
  }

  /**
   * A list of Wick Paths, Clips and Layers that use this SVGAsset as their image source.
   * I think this should return Assets not Paths
   * @returns {any[]}
   */
  public getInstances(): any[] {
    return []; // TODO
  }

  /**
   * Check if there are any objects in the project that use this asset.
   * @returns {boolean}
   */
  public hasInstances(): boolean {
    return false;
  }

  /**
   * Removes all Items using this asset as their source from the project.
   */
  public removeAllInstances(): void {
    // TODO
  }

  /**
   * Load data in the asset
   * @param {function} callback - function to call when the data is done being loaded.
   */
  public load(callback: () => void): void {
    // We don't need to do anything here, the data for SVGAssets is just SVG
    callback();
  }

  /**
   * Walks through the items tree creating the appropriate wick object for each node
   * @param {paper.Item} item - the item to process
   * @returns {Wick.Base}
   */
  static walkItems(item: paper.Item): Wick.Base {
    let wickItem: Wick.Base | null = null;

    if (
      item instanceof paper.Layer ||
      (item.name !== null &&
        item.name.startsWith("layer") &&
        item instanceof paper.Group)
    ) {
      wickItem = new Wick.Layer();

      const frame = new Wick.Frame();
      wickItem.addFrame(frame);
      const groupChildren = Array.from(item.children);
      groupChildren.forEach((childItem) => {
        const wickChildItem = SVGAsset.walkItems(childItem).copy();

        if (wickChildItem instanceof Wick.Clip) {
          frame.addClip(wickChildItem);
        } else if (wickChildItem instanceof Wick.Path) {
          frame.addPath(wickChildItem);
        } else if (wickChildItem instanceof Wick.Layer) {
          frame.addLayer(wickChildItem);
        } else {
          console.error(
            `SVG Import: Unknown item type.${wickChildItem.classname}`
          );
        }
      });
    } else if (item instanceof paper.Group) {
      wickItem = new Wick.Clip();
      const wickObjects: Wick.Base[] = [];
      const layers: Wick.Layer[] = [];
      const groupChildren = Array.from(item.children);
      groupChildren.forEach((childItem) => {
        const clipActiveLayer = (wickItem as Wick.Clip).activeLayer;
        const walkItem = SVGAsset.walkItems(childItem).copy();

        if (walkItem instanceof Wick.Layer) {
          layers.push(walkItem);
          clipActiveLayer.activate();
        } else {
          wickObjects.push(walkItem);
        }
      });
      (wickItem as Wick.Clip).addObjects(wickObjects);
      layers.forEach((layer) => {
        (wickItem as Wick.Clip).timeline.addLayer(layer);
      });
    } else if (item instanceof paper.Shape) {
      wickItem = new Wick.Path({});
    } else {
      wickItem = new Wick.Path({
        json: item.exportJSON(),
      });
    }

    return wickItem;
  }

  /**
   * Walks through the items tree converting shapes into paths.
   * @param {paper.Item} item - the item to process
   */
  static _breakAppartShapesRecursively(item: paper.Item): void {
    item.applyMatrix = true;
    if (item instanceof paper.Group || item instanceof paper.Layer) {
      const children = Array.from(item.children);
      children.forEach((childItem) => {
        SVGAsset._breakAppartShapesRecursively(childItem);
      });
    } else if (item instanceof paper.Shape) {
      const path = item.toPath();
      item.replaceWith(path);
    }
  }

  /**
   * Creates a new Wick SVG that uses this asset's data.
   * @param {function} callback - called when the SVG is done loading.
   */
  public createInstance(callback: (item: Wick.Base) => void): void {
    // Implementation needed
  }
}
