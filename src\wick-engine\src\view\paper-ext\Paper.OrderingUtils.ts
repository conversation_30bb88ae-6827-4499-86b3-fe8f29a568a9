/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Paper.js-drawing-tools.
 *
 * Paper.js-drawing-tools is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Paper.js-drawing-tools is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Paper.js-drawing-tools.  If not, see <https://www.gnu.org/licenses/>.
 */

import * as paper from "paper";

interface OrderingUtilsExtension {
  sendToBack(): void;
  bringToFront(): void;
  sendBackward(): void;
  bringForward(): void;
}

(paper as any).Item.inject({
  sendToBack: function (): void {
    if (this.parent) {
      const children = this.parent.children;
      this.insertBelow(children[0]);
    }
  },
  bringToFront: function (): void {
    if (this.parent) {
      const children = this.parent.children;
      this.insertAbove(children[children.length - 1]);
    }
  },
  sendBackward: function (): void {
    if (this.previousSibling) {
      this.insertBelow(this.previousSibling);
    }
  },
  bringForward: function (): void {
    if (this.nextSibling) {
      this.insertAbove(this.nextSibling);
    }
  },
} as OrderingUtilsExtension);
