/**
 * 可见性观察器工具
 * 用于检测元素是否在视口中，优化图片加载时机
 */

export interface VisibilityCallback {
  onVisible?: () => void;
  onHidden?: () => void;
  onNearVisible?: () => void; // 即将可见（提前加载）
}

export class VisibilityObserver {
  private observer: IntersectionObserver;
  private nearObserver: IntersectionObserver;
  private callbacks = new Map<Element, VisibilityCallback>();

  constructor() {
    // 主观察器：检测元素是否可见
    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          const callback = this.callbacks.get(entry.target);
          if (!callback) return;

          if (entry.isIntersecting) {
            callback.onVisible?.();
          } else {
            callback.onHidden?.();
          }
        });
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 0.1 // 10%可见时触发
      }
    );

    // 近距离观察器：提前检测即将可见的元素
    this.nearObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          const callback = this.callbacks.get(entry.target);
          if (!callback) return;

          if (entry.isIntersecting) {
            callback.onNearVisible?.();
          }
        });
      },
      {
        root: null,
        rootMargin: '200px', // 提前200px开始预加载
        threshold: 0
      }
    );
  }

  /**
   * 观察元素
   */
  observe(element: Element, callback: VisibilityCallback): void {
    this.callbacks.set(element, callback);
    this.observer.observe(element);
    this.nearObserver.observe(element);

    console.log('👁️ VisibilityObserver: 开始观察元素');
  }

  /**
   * 停止观察元素
   */
  unobserve(element: Element): void {
    this.callbacks.delete(element);
    this.observer.unobserve(element);
    this.nearObserver.unobserve(element);

    console.log('👁️ VisibilityObserver: 停止观察元素');
  }

  /**
   * 销毁观察器
   */
  destroy(): void {
    this.observer.disconnect();
    this.nearObserver.disconnect();
    this.callbacks.clear();

    console.log('🧹 VisibilityObserver: 销毁观察器');
  }

  /**
   * 获取当前观察的元素数量
   */
  getObservedCount(): number {
    return this.callbacks.size;
  }
}

// 全局实例
export const visibilityObserver = new VisibilityObserver();

// Svelte action：用于在组件中方便地使用可见性观察
export function observeVisibility(element: Element, callback: VisibilityCallback) {
  visibilityObserver.observe(element, callback);

  return {
    destroy() {
      visibilityObserver.unobserve(element);
    }
  };
}

/**
 * 批量可见性管理器
 * 用于管理大量元素的可见性检测，避免性能问题
 */
export class BatchVisibilityManager {
  private batchSize = 20; // 每批处理20个元素
  private processingQueue: Array<{element: Element, callback: VisibilityCallback}> = [];
  private isProcessing = false;

  /**
   * 批量添加观察
   */
  addToBatch(element: Element, callback: VisibilityCallback): void {
    this.processingQueue.push({ element, callback });
    this.processBatch();
  }

  /**
   * 处理批次
   */
  private async processBatch(): Promise<void> {
    if (this.isProcessing || this.processingQueue.length === 0) return;

    this.isProcessing = true;

    while (this.processingQueue.length > 0) {
      const batch = this.processingQueue.splice(0, this.batchSize);

      // 批量添加观察
      batch.forEach(({ element, callback }) => {
        visibilityObserver.observe(element, callback);
      });

      // 给浏览器一些时间处理
      await new Promise(resolve => setTimeout(resolve, 16)); // ~1帧的时间
    }

    this.isProcessing = false;
  }

  /**
   * 清空队列
   */
  clearBatch(): void {
    this.processingQueue = [];
    this.isProcessing = false;
  }
}

// 全局批量管理器实例
export const batchVisibilityManager = new BatchVisibilityManager();
