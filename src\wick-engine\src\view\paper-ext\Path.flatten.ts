/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Paper.js-drawing-tools.
 *
 * Paper.js-drawing-tools is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Paper.js-drawing-tools is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Paper.js-drawing-tools.  If not, see <https://www.gnu.org/licenses/>.
 */

declare module 'paper' {
  interface Path {
    flatten(): Path;
  }
}

declare const OffsetUtils: {
  offsetPath(path: paper.Path, offset: number, isUnion: boolean): paper.Path;
  joinOffsets(outerPath: paper.Path, innerPath: paper.Path, originalPath: paper.Path, offset: number): paper.Path;
};

paper.Path.inject({
  flatten: function(): paper.Path {
    const offset = this.strokeWidth / 2;

    const outerPath = OffsetUtils.offsetPath(this, offset, true);
    const innerPath = OffsetUtils.offsetPath(this, -offset, true);

    let flatPath = OffsetUtils.joinOffsets(outerPath.clone(), innerPath.clone(), this, offset);
    flatPath = flatPath.unite();

    return flatPath;
  }
}));