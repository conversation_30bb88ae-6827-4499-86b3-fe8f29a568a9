/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

// 注意：
// 这可能不应该是全局的，而是每个Wick.Project应该拥有一个ObjectCache。
// 如果在多个项目之间共享ObjectCache，测试会变得很困难。

interface ObjectMap {
  [key: string]: any;
}

/**
 * 用于存储和检索大型文件数据的全局工具类
 */
export class ObjectCache {
  protected _objects: ObjectMap;
  protected _objectsNeedAutosave: ObjectMap;

  /**
   * 创建一个ObjectCache
   */
  constructor() {
    this._objects = {};
    this._objectsNeedAutosave = {};
  }

  /**
   * 将对象添加到缓存中
   * @param object - 要添加的对象
   */
  addObject(object: any): void {
    this._objects[object.uuid] = object;
  }

  /**
   * 从缓存中移除对象
   * @param object - 要从缓存中移除的对象
   */
  removeObject(object: any): void {
    if (object.classname === "Project") {
      object.destroy();
      return; // TODO, 移除这个
    }
    delete this._objects[object.uuid];
  }

  /**
   * 通过UUID从缓存中移除对象
   * @param uuid - 要从缓存中移除的对象的uuid
   */
  removeObjectByUUID(uuid: string): void {
    delete this._objects[uuid];
  }

  /**
   * 从对象缓存中移除所有对象
   */
  clear(): void {
    this._objects = {};
    this._objectsNeedAutosave = {};
  }

  /**
   * 通过UUID获取对象
   * @param uuid - 对象的UUID
   * @returns 对象实例
   */
  getObjectByUUID(uuid: string): any | null {
    if (!uuid) {
      console.error("ObjectCache: getObjectByUUID: uuid is required.");
    }

    const object = this._objects[uuid];
    if (!object) {
      console.error(
        "Warning: object with uuid " + uuid + " was not found in the cache."
      );
      return null;
    } else {
      return object;
    }
  }

  /**
   * 获取缓存中的所有对象
   * @returns 所有对象的数组
   */
  getAllObjects(): any[] {
    const allObjects: any[] = [];

    for (const uuid in this._objects) {
      allObjects.push(this._objects[uuid]);
    }

    return allObjects;
  }

  /**
   * 移除项目中存在但不再链接到根对象的所有对象。
   * 这基本上是一个垃圾收集函数。此函数尝试保留在撤销/重做中引用的对象。
   * @param project - 用于确定哪些对象没有引用的项目
   */
  removeUnusedObjects(project: any): void {
    const activeObjects = this.getActiveObjects(project);
    const uuids = activeObjects.map((obj) => obj.uuid);
    uuids.push(project.uuid); // 不要忘记包含项目本身...

    let uuidSet = new Set(uuids);

    const historyIDs = project.history.getObjectUUIDs();

    uuidSet = new Set([...historyIDs, ...uuidSet]);

    this.getAllObjects().forEach((object) => {
      if (!uuidSet.has(object.uuid)) {
        this.removeObject(object);
      }
    });
  }

  /**
   * 移除所有temporary标志设置为true的对象
   */
  removeTemporaryObjects(): void {
    this.getAllObjects().forEach((obj) => {
      if (obj.temporary) {
        this.removeObject(obj);
      }
    });
  }

  /**
   * 获取在给定项目中引用的所有对象
   * @param project - 要检查子对象是否活动的项目
   * @returns 活动对象数组
   */
  getActiveObjects(project: any): any[] {
    // 这做同样的事情，但它快得多
    return project.getChildrenRecursive().map((object: any) => {
      return this.getObjectByUUID(object.uuid);
    });
  }

  /**
   * 保存一个对象以在下次自动保存时自动保存
   * @param object - 要保存的对象
   */
  markObjectToBeAutosaved(object: any): void {
    this._objectsNeedAutosave[object.uuid] = true;
  }

  /**
   * 从必须自动保存的对象列表中移除给定对象
   * @param object - 要从自动保存对象列表中移除的对象
   */
  clearObjectToBeAutosaved(object: any): void {
    delete this._objectsNeedAutosave[object.uuid];
  }

  /**
   * 如果给定对象在下次自动保存期间被标记为自动保存，则返回true
   * @param object - 要检查自动保存的对象
   */
  objectNeedsAutosave(object: any): boolean {
    return this._objectsNeedAutosave[object.uuid];
  }

  /**
   * 返回当前需要自动保存的对象数组
   * @returns 标记为自动保存的对象
   */
  getObjectsNeedAutosaved(): any[] {
    return Object.keys(this._objectsNeedAutosave).map((uuid) =>
      this.getObjectByUUID(uuid)
    );
  }
}

// 创建全局ObjectCache实例
export const objectCache = new ObjectCache();
