<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- GameSprite Studio High-Resolution Icon -->
  <defs>
    <linearGradient id="iconBgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4a5568;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2d3748;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a202c;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconPrimaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#718096;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4a5568;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconSecondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#667eea;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="iconAccentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16a34a;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="256" cy="256" r="240" fill="url(#iconBgGradient)" stroke="#718096" stroke-width="8"/>
  
  <!-- 主网格背景 -->
  <g opacity="0.2">
    <rect x="64" y="64" width="384" height="384" fill="none" stroke="#ffffff" stroke-width="2"/>
    <!-- 垂直网格线 -->
    <line x1="192" y1="64" x2="192" y2="448" stroke="#ffffff" stroke-width="1.5"/>
    <line x1="320" y1="64" x2="320" y2="448" stroke="#ffffff" stroke-width="1.5"/>
    <!-- 水平网格线 -->
    <line x1="64" y1="192" x2="448" y2="192" stroke="#ffffff" stroke-width="1.5"/>
    <line x1="64" y1="320" x2="448" y2="320" stroke="#ffffff" stroke-width="1.5"/>
  </g>
  
  <!-- 像素化精灵块 - 第一行 -->
  <rect x="80" y="80" width="96" height="96" fill="url(#iconPrimaryGradient)" rx="8" filter="url(#glow)"/>
  <rect x="208" y="80" width="96" height="96" fill="url(#iconSecondaryGradient)" rx="8" filter="url(#glow)"/>
  <rect x="336" y="80" width="96" height="96" fill="url(#iconAccentGradient)" rx="8" filter="url(#glow)"/>
  
  <!-- 像素化精灵块 - 第二行 -->
  <rect x="80" y="208" width="96" height="96" fill="url(#iconSecondaryGradient)" rx="8" filter="url(#glow)"/>
  <rect x="208" y="208" width="96" height="96" fill="url(#iconPrimaryGradient)" rx="8" filter="url(#glow)"/>
  <rect x="336" y="208" width="96" height="96" fill="#fbbf24" rx="8" filter="url(#glow)"/>
  
  <!-- 像素化精灵块 - 第三行 -->
  <rect x="80" y="336" width="96" height="96" fill="#3b82f6" rx="8" filter="url(#glow)"/>
  <rect x="208" y="336" width="96" height="96" fill="url(#iconPrimaryGradient)" rx="8" filter="url(#glow)"/>
  <rect x="336" y="336" width="96" height="96" fill="url(#iconSecondaryGradient)" rx="8" filter="url(#glow)"/>
  
  <!-- 高亮边框 -->
  <rect x="64" y="64" width="384" height="384" fill="none" stroke="#718096" stroke-width="4" rx="16"/>
  
  <!-- 工具指示器 -->
  <circle cx="400" cy="112" r="32" fill="url(#iconSecondaryGradient)" filter="url(#glow)"/>
  <circle cx="400" cy="112" r="16" fill="#ffffff"/>
  <circle cx="400" cy="112" r="8" fill="url(#iconSecondaryGradient)"/>
  
  <!-- 中心装饰 -->
  <circle cx="256" cy="256" r="48" fill="none" stroke="#ffffff" stroke-width="4" opacity="0.6"/>
  <circle cx="256" cy="256" r="24" fill="none" stroke="#718096" stroke-width="2" opacity="0.8"/>
  
  <!-- 角落装饰 -->
  <rect x="96" y="96" width="16" height="16" fill="#ffffff" opacity="0.6" rx="2"/>
  <rect x="400" y="96" width="16" height="16" fill="#ffffff" opacity="0.6" rx="2"/>
  <rect x="96" y="400" width="16" height="16" fill="#ffffff" opacity="0.6" rx="2"/>
  <rect x="400" y="400" width="16" height="16" fill="#ffffff" opacity="0.6" rx="2"/>
  
  <!-- 品牌标识 -->
  <text x="256" y="480" text-anchor="middle" fill="#718096" font-family="Arial, sans-serif" font-size="24" font-weight="bold" opacity="0.8">GameSprite Studio</text>
</svg>
