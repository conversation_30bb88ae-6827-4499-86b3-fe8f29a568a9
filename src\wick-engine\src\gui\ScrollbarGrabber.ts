/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";

export class ScrollbarGrabber extends GUIElement {
  protected _mouseStart: { x: number; y: number } | null;
  protected _mouseEnd: { x: number; y: number } | null;
  protected _mouseDiff: { x: number; y: number } | null;

  constructor(model: Base) {
    super(model);
    this._mouseStart = null;
    this._mouseEnd = null;
    this._mouseDiff = null;
  }

  draw(): void {
    super.draw();

    const ctx = this.ctx;

    this._mouseStart = this._mouseStart || {
      x: this.localMouse.x,
      y: this.localMouse.y,
    };
    this._mouseEnd = {
      x: this.localMouse.x,
      y: this.localMouse.y,
    };
    this._mouseDiff = {
      x: this._mouseEnd.x - this._mouseStart.x,
      y: this._mouseEnd.y - this._mouseStart.y,
    };

    // Draw scrollbar grabber
    ctx.fillStyle = GUIElement.SCROLLBAR_GRABBER_COLOR;
    ctx.fillRect(0, 0, this.gridWidth, this.gridCellHeight);

    // Draw scrollbar grabber border
    ctx.strokeStyle = GUIElement.SCROLLBAR_GRABBER_BORDER_COLOR;
    ctx.lineWidth = 1;
    ctx.strokeRect(0, 0, this.gridWidth, this.gridCellHeight);
  }

  finish(): void {
    const timeline = this.model.parentTimeline;
    const scrollX = this._mouseDiff.x / this.gridCellWidth;
    timeline.scroll += Math.round(scrollX);
  }
}
