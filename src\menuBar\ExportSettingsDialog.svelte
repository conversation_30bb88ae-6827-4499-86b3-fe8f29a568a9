<script lang="ts">
  /**
   * 导出设置弹窗组件
   * 可拖动的紧凑导出设置界面
   */

  import ExportPanel from './ExportPanel.svelte';

  // 导入国际化
  import { _ } from '../lib/i18n';

  // Props
  interface Props {
    show: boolean;
    onClose: () => void;
  }

  let { show, onClose }: Props = $props();

  // 拖拽相关状态
  let isDragging = $state(false);
  let dragOffset = $state({ x: 0, y: 0 });
  let dialogPosition = $state({ x: 100, y: 100 });
  let dialogElement: HTMLDivElement | undefined;

  // 拖拽开始
  function handleMouseDown(event: MouseEvent) {
    if (event.target === event.currentTarget || (event.target as HTMLElement).classList.contains('dialog-header')) {
      if (!dialogElement) return;

      isDragging = true;
      const rect = dialogElement.getBoundingClientRect();
      dragOffset.x = event.clientX - rect.left;
      dragOffset.y = event.clientY - rect.top;

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      event.preventDefault();
    }
  }

  // 拖拽移动
  function handleMouseMove(event: MouseEvent) {
    if (isDragging && dialogElement) {
      dialogPosition.x = event.clientX - dragOffset.x;
      dialogPosition.y = event.clientY - dragOffset.y;

      // 限制在窗口范围内
      const maxX = window.innerWidth - dialogElement.offsetWidth;
      const maxY = window.innerHeight - dialogElement.offsetHeight;

      dialogPosition.x = Math.max(0, Math.min(dialogPosition.x, maxX));
      dialogPosition.y = Math.max(0, Math.min(dialogPosition.y, maxY));
    }
  }

  // 拖拽结束
  function handleMouseUp() {
    isDragging = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  }

  // ESC键关闭
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      onClose();
    }
  }
</script>

{#if show}
  <!-- 透明遮罩层 - 不阻止下层交互 -->
  <div
    class="dialog-overlay"
    onkeydown={handleKeydown}
    role="dialog"
    aria-modal="false"
    tabindex="-1"
  >
    <!-- 弹窗主体 -->
    <div
      bind:this={dialogElement}
      class="export-dialog"
      class:dragging={isDragging}
      style="left: {dialogPosition.x}px; top: {dialogPosition.y}px;"
      onclick={(e) => e.stopPropagation()}
      onkeydown={(e) => e.stopPropagation()}
      role="none"
    >
      <!-- 可拖拽的头部 -->
      <div
        class="dialog-header"
        onmousedown={handleMouseDown}
        role="none"
      >
        <div class="dialog-title">
          <span class="title-icon">⚙️</span>
          <span class="title-text">{$_('export.settings')}</span>
        </div>
        <button
          class="close-button"
          onclick={onClose}
          title={$_('actions.close')}
          aria-label="{$_('actions.close')}{$_('export.settings')}"
        >
          ✕
        </button>
      </div>

      <!-- 内容区域 -->
      <div class="dialog-content">
        <ExportPanel />
      </div>
    </div>
  </div>
{/if}

<style>
  .dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    pointer-events: none; /* 🎯 允许点击穿透到下层 */
  }

  .export-dialog {
    position: absolute;
    width: 400px;
    max-width: 90vw;
    max-height: 80vh;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    user-select: none;
    pointer-events: auto; /* 🎯 弹窗本身可以接收点击事件 */
  }

  .export-dialog.dragging {
    cursor: grabbing;
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
  }

  .dialog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: linear-gradient(135deg, var(--theme-primary-dark) 0%, var(--theme-primary) 100%);
    color: white;
    cursor: grab;
    border-bottom: 1px solid var(--theme-border);
  }

  .dialog-header:active {
    cursor: grabbing;
  }

  .dialog-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 14px;
  }

  .title-icon {
    font-size: 16px;
  }

  .title-text {
    font-size: 14px;
  }

  .close-button {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
    line-height: 1;
  }

  .close-button:hover {
    background: rgba(255, 255, 255, 0.2);
  }

  .dialog-content {
    height: auto; /* 🎯 自动高度，适应内容 */
    max-height: 600px; /* 🎯 最大高度限制 */
    overflow: hidden; /* 🎯 移除滚动条 */
    background: var(--theme-background);
  }

  /* 紧凑样式调整 */
  .dialog-content :global(.export-panel) {
    height: 100%;
  }

  .dialog-content :global(.panel-content) {
    padding: 12px;
  }

  .dialog-content :global(.export-type-section) {
    margin-bottom: 16px;
  }

  .dialog-content :global(.settings-section) {
    margin-bottom: 16px;
  }

  .dialog-content :global(.setting-group) {
    margin-bottom: 12px;
  }

  .dialog-content :global(.type-card) {
    padding: 8px 12px;
  }

  .dialog-content :global(.type-name) {
    font-size: 13px;
  }

  .dialog-content :global(.type-desc) {
    font-size: 11px;
  }

  .dialog-content :global(label) {
    font-size: 13px;
    margin-bottom: 4px;
  }

  .dialog-content :global(select),
  .dialog-content :global(input[type="text"]) {
    padding: 6px 8px;
    font-size: 13px;
  }

  .dialog-content :global(.checkbox-label) {
    font-size: 13px;
  }

  /* 🎯 移除弹窗滚动条样式，改为在内部容器中使用滚动条 */
</style>
