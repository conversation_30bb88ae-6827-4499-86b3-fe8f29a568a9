import { generateId } from '../utils/id.js';
import { GameSpriteEngine } from './GameSpriteEngine.js';
import type { Project } from './Project.js';
import type { EngineOptions } from './types.js';

/**
 * 系统管理类
 * 统一管理运行时初始化、快捷键、项目设置等
 */
export class System {
  private static instance: System | null = null;

  // 系统标识
  public readonly id: string;

  // 引擎实例
  private engine: GameSpriteEngine | null = null;

  // 当前项目
  private currentProject: Project | null = null;

  // 运行时状态
  private isInitialized: boolean = false;
  private isRunning: boolean = false;

  // 快捷键映射
  private shortcuts: Map<string, () => void> = new Map();

  // 键盘事件处理器
  private keyDownHandler: ((event: KeyboardEvent) => void) | null = null;

  private constructor() {
    this.id = generateId();
    console.log('🔧 System: 系统管理器创建', { id: this.id });
  }

  /**
   * 获取系统单例实例
   */
  static getInstance(): System {
    if (!System.instance) {
      System.instance = new System();
    }
    return System.instance;
  }

  /**
   * 初始化系统和引擎
   */
  async initialize(canvas: HTMLCanvasElement, options?: EngineOptions): Promise<GameSpriteEngine> {
    if (this.isInitialized) {
      console.warn('⚠️ System: 系统已经初始化');
      if (!this.engine) {
        throw new Error('系统已初始化但引擎实例不存在');
      }
      return this.engine;
    }

    try {
      console.log('🚀 System: 开始初始化系统和引擎...');

      // 1. 初始化快捷键系统
      this.initializeShortcuts();

      // 2. 创建并初始化引擎
      this.engine = new GameSpriteEngine();
      await this.engine.initialize(canvas, options);

      // 3. 设置当前项目
      const project = this.engine.getProject();
      if (project) {
        this.setCurrentProject(project);
      }

      this.isInitialized = true;
      console.log('✅ System: 系统和引擎初始化完成');

      return this.engine;

    } catch (error) {
      console.error('❌ System: 系统初始化失败', error);
      throw error;
    }
  }

  /**
   * 启动系统
   */
  start(): void {
    if (!this.isInitialized) {
      throw new Error('系统未初始化，请先调用 initialize()');
    }

    if (this.isRunning) {
      console.warn('⚠️ System: 系统已经在运行');
      return;
    }

    this.isRunning = true;
    console.log('▶️ System: 系统已启动');
  }

  /**
   * 停止系统
   */
  stop(): void {
    if (!this.isRunning) {
      console.warn('⚠️ System: 系统未在运行');
      return;
    }

    this.isRunning = false;
    console.log('⏹️ System: 系统已停止');
  }

  /**
   * 初始化快捷键系统
   */
  private initializeShortcuts(): void {
    // 注册默认快捷键
    this.registerShortcut('Ctrl+S', () => {
      console.log('💾 System: 保存项目 (Ctrl+S)');
      this.saveCurrentProject();
    });

    this.registerShortcut('Ctrl+N', () => {
      console.log('📄 System: 新建项目 (Ctrl+N)');
      // TODO: 实现新建项目逻辑
    });

    this.registerShortcut('Ctrl+O', () => {
      console.log('📂 System: 打开项目 (Ctrl+O)');
      // TODO: 实现打开项目逻辑
    });

    // 设置键盘事件监听
    this.keyDownHandler = (event: KeyboardEvent) => {
      const shortcut = this.getShortcutKey(event);
      const handler = this.shortcuts.get(shortcut);

      if (handler) {
        event.preventDefault();
        handler();
      }
    };

    document.addEventListener('keydown', this.keyDownHandler);

    console.log('⌨️ System: 快捷键系统已初始化', {
      shortcuts: Array.from(this.shortcuts.keys())
    });
  }

  /**
   * 注册快捷键
   */
  registerShortcut(shortcut: string, handler: () => void): void {
    this.shortcuts.set(shortcut, handler);
    console.log(`⌨️ System: 注册快捷键 ${shortcut}`);
  }

  /**
   * 取消注册快捷键
   */
  unregisterShortcut(shortcut: string): void {
    if (this.shortcuts.delete(shortcut)) {
      console.log(`⌨️ System: 取消快捷键 ${shortcut}`);
    }
  }

  /**
   * 获取快捷键字符串
   */
  private getShortcutKey(event: KeyboardEvent): string {
    const parts: string[] = [];

    if (event.ctrlKey) parts.push('Ctrl');
    if (event.altKey) parts.push('Alt');
    if (event.shiftKey) parts.push('Shift');
    if (event.metaKey) parts.push('Meta');

    parts.push(event.key);

    return parts.join('+');
  }

  /**
   * 设置当前项目
   */
  setCurrentProject(project: Project | null): void {
    const oldProject = this.currentProject;
    this.currentProject = project;

    console.log('📋 System: 设置当前项目', {
      oldProject: oldProject?.id,
      newProject: project?.id,
      projectName: project?.name
    });
  }

  /**
   * 获取当前项目
   */
  getCurrentProject(): Project | null {
    return this.currentProject;
  }

  /**
   * 获取引擎实例
   */
  getEngine(): GameSpriteEngine | null {
    return this.engine;
  }

  /**
   * 保存当前项目
   */
  private saveCurrentProject(): void {
    if (!this.currentProject) {
      console.warn('⚠️ System: 没有当前项目可保存');
      return;
    }

    // TODO: 实现项目保存逻辑
    console.log('💾 System: 保存项目', {
      projectId: this.currentProject.id,
      projectName: this.currentProject.name
    });
  }

  /**
   * 获取系统状态
   */
  getStatus() {
    return {
      id: this.id,
      isInitialized: this.isInitialized,
      isRunning: this.isRunning,
      currentProject: this.currentProject?.id || null,
      shortcutsCount: this.shortcuts.size
    };
  }

  /**
   * 销毁系统
   */
  destroy(): void {
    // 销毁引擎
    if (this.engine) {
      this.engine.destroy();
      this.engine = null;
    }

    // 清理快捷键监听
    if (this.keyDownHandler) {
      document.removeEventListener('keydown', this.keyDownHandler);
      this.keyDownHandler = null;
    }

    // 清理快捷键映射
    this.shortcuts.clear();

    // 重置状态
    this.currentProject = null;
    this.isInitialized = false;
    this.isRunning = false;

    console.log('🧹 System: 系统已销毁');
  }
}

// 导出单例获取函数
export function getSystem(): System {
  return System.getInstance();
}

/**
 * 创建引擎的便捷函数 - 通过System统一管理
 */
export async function createEngine(
  canvas: HTMLCanvasElement,
  options?: EngineOptions
): Promise<GameSpriteEngine> {
  const system = getSystem();
  return await system.initialize(canvas, options);
}
