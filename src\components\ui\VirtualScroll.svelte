/**
 * 虚拟滚动组件
 * 用于高效渲染大量列表项，只渲染可见区域的元素
 */

<script lang="ts">
  import { onMount } from 'svelte';

  interface Props<T> {
    items: T[];
    itemHeight: number;
    containerHeight: number;
    overscan?: number; // 额外渲染的项目数量
    gap?: number; // 项目间距
    onItemRender?: (item: T, index: number) => void;
  }

  let {
    items,
    itemHeight,
    containerHeight,
    overscan = 5,
    gap = 0,
    onItemRender
  }: Props<any> = $props();

  // 状态管理
  let scrollContainer: HTMLDivElement;
  let scrollTop = $state(0);
  let isScrolling = $state(false);
  let scrollTimeout: number;

  // 计算属性
  const totalHeight = $derived(items.length * (itemHeight + gap) - gap);
  const visibleCount = $derived(Math.ceil(containerHeight / (itemHeight + gap)));
  const startIndex = $derived(Math.max(0, Math.floor(scrollTop / (itemHeight + gap)) - overscan));
  const endIndex = $derived(Math.min(items.length - 1, startIndex + visibleCount + overscan * 2));
  const visibleItems = $derived(items.slice(startIndex, endIndex + 1));
  const offsetY = $derived(startIndex * (itemHeight + gap));

  // 滚动事件处理
  function handleScroll(event: Event) {
    const target = event.target as HTMLDivElement;
    scrollTop = target.scrollTop;
    isScrolling = true;

    // 清除之前的定时器
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }

    // 设置滚动结束检测
    scrollTimeout = setTimeout(() => {
      isScrolling = false;
    }, 150);

    // 触发项目渲染回调
    if (onItemRender) {
      visibleItems.forEach((item, index) => {
        onItemRender(item, startIndex + index);
      });
    }
  }

  // 滚动到指定索引
  function scrollToIndex(index: number, behavior: ScrollBehavior = 'smooth') {
    if (!scrollContainer) return;

    const targetScrollTop = index * (itemHeight + gap);
    scrollContainer.scrollTo({
      top: targetScrollTop,
      behavior
    });
  }

  // 滚动到指定项目
  function scrollToItem(item: any, behavior: ScrollBehavior = 'smooth') {
    const index = items.indexOf(item);
    if (index !== -1) {
      scrollToIndex(index, behavior);
    }
  }

  // 获取当前可见范围
  function getVisibleRange() {
    return {
      start: startIndex,
      end: endIndex,
      items: visibleItems
    };
  }

  // 组件销毁时清理
  $effect(() => {
    return () => {
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  });

  // 暴露方法给父组件
  export { scrollToIndex, scrollToItem, getVisibleRange };
</script>

<div
  bind:this={scrollContainer}
  class="virtual-scroll-container"
  style="height: {containerHeight}px;"
  onscroll={handleScroll}
>
  <!-- 总高度占位符 -->
  <div class="virtual-scroll-spacer" style="height: {totalHeight}px;">
    <!-- 可见项目容器 -->
    <div
      class="virtual-scroll-content"
      class:scrolling={isScrolling}
      style="transform: translateY({offsetY}px);"
    >
      {#each visibleItems as item, index (startIndex + index)}
        <div
          class="virtual-scroll-item"
          style="height: {itemHeight}px; margin-bottom: {gap}px;"
          data-index={startIndex + index}
        >
          <slot {item} index={startIndex + index} />
        </div>
      {/each}
    </div>
  </div>
</div>

<style>
  .virtual-scroll-container {
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    width: 100%;
  }

  .virtual-scroll-spacer {
    position: relative;
    width: 100%;
  }

  .virtual-scroll-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    will-change: transform;
    transition: none;
  }

  .virtual-scroll-content.scrolling {
    pointer-events: none; /* 滚动时禁用交互，提升性能 */
  }

  .virtual-scroll-item {
    width: 100%;
    box-sizing: border-box;
  }

  /* 优化滚动性能 */
  .virtual-scroll-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 自定义滚动条 */
  .virtual-scroll-container::-webkit-scrollbar {
    width: 8px;
  }

  .virtual-scroll-container::-webkit-scrollbar-track {
    background: var(--theme-surface-light, #f1f5f9);
    border-radius: 4px;
  }

  .virtual-scroll-container::-webkit-scrollbar-thumb {
    background: var(--theme-border-dark, #cbd5e1);
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  .virtual-scroll-container::-webkit-scrollbar-thumb:hover {
    background: var(--theme-primary, #3b82f6);
  }
</style>
