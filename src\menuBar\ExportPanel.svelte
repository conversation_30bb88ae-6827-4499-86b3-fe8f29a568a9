<script lang="ts">
  /**
   * 导出面板组件
   * 显示当前对象的导出属性设置
   */

  // 移除已删除的 selectionStore 引用
  import {
    exportSettingsStore,
    exportSettingsActions,
    type ExportSettings
  } from '../stores/exportSettingsStore';


  import { EXPORT_TYPES, type ExportTypeKey } from '../exportDialog/formats';

  // 导入国际化
  import { _ } from '../lib/i18n';

  // 🎯 状态管理 - 从全局store获取
  let exportSettings = $state<ExportSettings>();



  // 🎯 初始化和监听全局设置变化
  $effect(() => {
    // 订阅导出设置
    const unsubscribeExport = exportSettingsStore.subscribe(settings => {
      exportSettings = settings;
    });

    return () => {
      unsubscribeExport();
    };
  });

  // 获取当前导出类型配置
  const currentTypeConfig = $derived(() => {
    return exportSettings ? EXPORT_TYPES[exportSettings.targetEngine] : EXPORT_TYPES['cropped-images'];
  });

  // 获取国际化的导出类型配置
  const getLocalizedExportTypes = $derived(() => {
    return Object.entries(EXPORT_TYPES).map(([key, config]) => ({
      key: key as ExportTypeKey,
      ...config,
      name: $_(`exportTypes.${key}.name`),
      description: $_(`exportTypes.${key}.description`)
    }));
  });

  // 更新导出设置的辅助函数
  function updateSettings(updates: Partial<ExportSettings>) {
    exportSettingsActions.updateSettings(updates);
  }

  // 更新导出类型
  function updateExportType(type: ExportTypeKey) {
    exportSettingsActions.updateExportType(type);
  }

  // 布局设置已移动到各自的组件中

  // 当格式改变时，确保质量设置的可见性
  const showQualitySlider = $derived(() => {
    // 🎯 修复：PNG也应该有质量控制（压缩级别）
    return exportSettings?.format === 'jpg' || exportSettings?.format === 'webp' || exportSettings?.format === 'png';
  });
</script>

<div class="export-panel">
  <div class="panel-content">
    <!-- 🎯 导出类型选择 - 带滚动条的容器 -->
    <div class="export-type-section">
      <h4>🎯 {$_('export.type')}</h4>
      <div class="type-container">
        <div class="type-grid">
          {#each getLocalizedExportTypes() as config}
            <button
              class="type-card"
              class:selected={exportSettings?.targetEngine === config.key}
              onclick={() => updateExportType(config.key)}
            >
              <div class="type-icon">{config.icon}</div>
              <div class="type-info">
                <div class="type-name">{config.name}</div>
                <div class="type-desc">{config.description}</div>
              </div>
            </button>
          {/each}
        </div>
      </div>
    </div>

    <!-- 🎯 导出属性设置 - 始终显示 -->
    <div class="settings-section">
      <h4>⚙️ {$_('export.settings')}</h4>

      <div class="setting-row">
        <div class="setting-group">
          <label for="output-format">{$_('export.format')}:</label>
          <select
            id="output-format"
            value={exportSettings?.format || 'png'}
            onchange={(e) => updateSettings({ format: (e.target as HTMLSelectElement).value as any })}
          >
            {#each currentTypeConfig().formats as format}
              <option value={format}>{format.toUpperCase()}</option>
            {/each}
          </select>
        </div>
        <div class="setting-group">
          <label for="file-name">{$_('export.filename')}:</label>
          <input
            id="file-name"
            type="text"
            value={exportSettings?.fileName || ''}
            autocomplete="off"
            oninput={(e) => updateSettings({ fileName: (e.target as HTMLInputElement).value })}
            placeholder={$_('ui.placeholder')}
          />
        </div>
      </div>

      {#if showQualitySlider()}
        <div class="setting-group">
          <label for="quality">
            {#if exportSettings?.format === 'png'}
              {$_('export.compressionLevel')}: {Math.round((exportSettings?.quality || 0.9) * 100)}%
            {:else}
              {$_('export.quality')}: {Math.round((exportSettings?.quality || 0.9) * 100)}%
            {/if}
          </label>
          <input
            id="quality"
            type="range"
            min="0.1"
            max="1"
            step="0.01"
            value={exportSettings?.quality || 0.9}
            oninput={(e) => updateSettings({ quality: parseFloat((e.target as HTMLInputElement).value) })}
          />
        </div>
      {/if}



      <div class="checkbox-group">
        <label class="checkbox-label">
          <input
            type="checkbox"
            checked={exportSettings?.includeOriginal || false}
            onchange={(e) => updateSettings({ includeOriginal: (e.target as HTMLInputElement).checked })}
          />
          {$_('export.includeOriginal')}
        </label>

        <label class="checkbox-label">
          <input
            type="checkbox"
            checked={exportSettings?.optimizeSize || false}
            onchange={(e) => updateSettings({ optimizeSize: (e.target as HTMLInputElement).checked })}
          />
          {$_('export.optimizeSize')}
        </label>

      </div>
    </div>

    <!-- 布局设置已移动到各自的组件中 -->
  </div>
</div>

<style>
  .export-panel {
    /* 🎯 移除固定高度，适应滚动布局 */
    display: flex;
    flex-direction: column;
    background: var(--theme-surface);
  }



  .panel-content {
    /* 🎯 移除flex和固定高度，让内容自然流动 */
    padding: 12px;
    display: flex;
    flex-direction: column;
  }



  .export-type-section {
    margin-bottom: 12px; /* 🎯 减少间距 */
    flex-shrink: 0; /* 🎯 防止被压缩 */
  }

  .export-type-section h4 {
    margin: 0 0 8px 0;
    color: var(--theme-text);
    font-size: 14px;
    font-weight: 600;
  }

  .type-container {
    height: 200px; /* 🎯 进一步减少高度 */
    overflow-y: auto; /* 🎯 添加垂直滚动条 */
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    padding: 6px; /* 🎯 减少内边距 */
    background: var(--theme-background);
  }

  .type-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .type-card {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 8px;
    background: var(--theme-background);
  }

  .type-card:hover {
    background: var(--theme-surface-hover, var(--theme-surface));
  }

  .type-card.selected {
    border-color: var(--theme-primary);
    background: var(--theme-primary-light, rgba(59, 130, 246, 0.1));
  }

  .type-icon {
    font-size: 18px;
    flex-shrink: 0;
  }

  .type-info {
    flex: 1;
  }

  .type-name {
    font-weight: 600;
    color: var(--theme-text);
    font-size: 13px;
  }

  .type-desc {
    color: var(--theme-text-secondary);
    font-size: 11px;
    margin-top: 2px;
  }

  .settings-section {
    margin-bottom: 8px; /* 🎯 减少底部间距 */
    flex-shrink: 0; /* 🎯 防止被压缩，保持完整显示 */
  }

  .settings-section h4 {
    margin: 0 0 8px 0;
    color: var(--theme-text);
    font-size: 14px;
    font-weight: 600;
  }

  .setting-group {
    margin-bottom: 8px; /* 🎯 减少设置组间距 */
  }

  .setting-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .setting-group label {
    display: block;
    margin-bottom: 4px;
    color: var(--theme-text);
    font-size: 13px;
    font-weight: 500;
  }

  .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 4px; /* 🎯 减少复选框间距 */
    margin-bottom: 8px; /* 🎯 减少底部间距 */
  }

  .checkbox-label {
    display: flex !important;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    margin-bottom: 0 !important;
    font-size: 13px;
  }

  select, input[type="text"], input[type="range"] {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    background: var(--theme-background);
    color: var(--theme-text);
    font-size: 13px;
  }

  /* 🎯 调小输入框宽度 */
  input[type="text"] {
    width: 80%; /* 🎯 输入框宽度改为80% */
    max-width: 150px; /* 🎯 最大宽度限制 */
  }

  input[type="range"] {
    padding: 0;
  }

  /* 🎯 导出类型容器滚动条样式 */
  .type-container::-webkit-scrollbar {
    width: 6px;
  }

  .type-container::-webkit-scrollbar-track {
    background: var(--theme-surface);
    border-radius: 3px;
  }

  .type-container::-webkit-scrollbar-thumb {
    background: var(--theme-border);
    border-radius: 3px;
  }

  .type-container::-webkit-scrollbar-thumb:hover {
    background: var(--theme-text-secondary);
  }


</style>
