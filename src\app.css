/**
 * 全局样式文件
 * 包含主题变量、基础样式和Skeleton UI集成
 */

/* 导入Skeleton UI样式 */
@import '@skeletonlabs/skeleton/themes/theme-skeleton.css';
@import '@skeletonlabs/skeleton/styles/skeleton.css';

/* 全局CSS变量定义 */
:root {
  /* 主题色彩变量 - 深色主题（基于图片风格） */
  --theme-primary: #007acc;
  --theme-primary-dark: #005a9e;
  --theme-primary-light: #3399dd;

  --theme-secondary: #007acc;
  --theme-secondary-dark: #005a9e;
  --theme-secondary-light: #3399dd;

  --theme-background: #1e1e1e;
  --theme-background-dark: #181818;
  --theme-background-light: #252526;

  --theme-surface: #2d2d30;
  --theme-surface-dark: #252526;
  --theme-surface-light: #3c3c3c;

  --theme-text: #cccccc;
  --theme-text-secondary: #969696;
  --theme-text-inverse: #1e1e1e;

  --theme-border: #3c3c3c;
  --theme-border-light: #2d2d30;
  --theme-border-dark: #464647;

  --theme-success: #4ec9b0;
  --theme-warning: #ffcc02;
  --theme-error: #f44747;
  --theme-info: #007acc;

  --theme-shadow: rgba(0, 0, 0, 0.6);
  --theme-shadow-light: rgba(0, 0, 0, 0.3);
  --theme-shadow-dark: rgba(0, 0, 0, 0.8);

  /* 布局变量 */
  --title-bar-height: 32px;
  --status-bar-height: 24px;
  --border-radius: 4px;
  --border-radius-small: 2px;
  --border-radius-large: 8px;

  /* 字体变量 */
  --font-family-base: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;

  /* 间距变量 */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;

  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--theme-text);
  background-color: var(--theme-background);
  overflow: hidden;
}

/* 应用程序根容器 */
#app {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--theme-surface-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--theme-border);
  border-radius: var(--border-radius-small);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-border-dark);
}

/* 选择文本样式 */
::selection {
  background-color: var(--theme-primary);
  color: var(--theme-text-inverse);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* 按钮基础样式 */
button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  background: none;
  transition: var(--transition-base);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 输入框基础样式 */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  color: var(--theme-text);
  background-color: var(--theme-surface);
  border: 1px solid var(--theme-border);
  border-radius: var(--border-radius);
  padding: var(--spacing-2) var(--spacing-3);
  transition: var(--transition-base);
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px rgba(74, 85, 104, 0.2);
}

/* 链接样式 */
a {
  color: var(--theme-primary-light);
  text-decoration: none;
  transition: var(--transition-base);
}

a:hover {
  color: var(--theme-primary);
  text-decoration: underline;
}

/* 工具类 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* 主题特定样式 */
.dark {
  color-scheme: dark;
}

.light {
  color-scheme: light;
}

/* Skeleton UI 主题覆盖 */
.dark .card {
  background-color: var(--theme-surface);
  border-color: var(--theme-border);
}

.dark .btn {
  background-color: var(--theme-primary);
  color: var(--theme-text-inverse);
}

.dark .btn:hover {
  background-color: var(--theme-primary-dark);
}

.dark .input-group input {
  background-color: var(--theme-surface);
  border-color: var(--theme-border);
  color: var(--theme-text);
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --font-size-base: 0.9rem;
    --spacing-4: 0.75rem;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --theme-border: rgba(255, 255, 255, 0.5);
    --theme-border-dark: rgba(255, 255, 255, 0.7);
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}


