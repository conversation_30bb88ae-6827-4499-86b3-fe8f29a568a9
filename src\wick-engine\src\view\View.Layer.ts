/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { View } from "./View";
import { Layer } from "../base/Layer";
import * as paper from "paper";

export class LayerView extends View {
  protected static readonly BASE_ONION_OPACITY = 0.35;

  protected activeFrameLayers: paper.Layer[];
  protected onionSkinnedFramesLayers: paper.Layer[];
  protected activeFrameContainers: paper.Group[];
  protected _model: Layer;

  constructor(model: Layer) {
    super(model);

    this.activeFrameLayers = [];
    this.onionSkinnedFramesLayers = [];
    this.activeFrameContainers = [];
  }

  render(): void {
    // Add active frame layers
    this.activeFrameLayers = [];
    const frame = this.model.activeFrame;
    if (frame) {
      frame.view.render();

      this.activeFrameLayers.push(frame.view.objectsLayer);

      frame.view.objectsLayer.locked = false;
      frame.view.objectsLayer.opacity = 1.0;
    }

    // Disable mouse events on layers if they are locked.
    // (However, this is ignored while the project is playing so the interact tool always works.)
    // (This is also ignored for layers which are inside clips and not the current focus.)
    this.activeFrameLayers.forEach((layer) => {
      if (this.model.project.playing || !this.model.parentClip.isFocus) {
        layer.locked = false;
      } else {
        layer.locked = this.model.locked;
      }
    });

    // Add onion skinning, if necessary.
    this.onionSkinnedFramesLayers = [];

    if (
      this.model.project &&
      this.model.project.onionSkinEnabled &&
      !this.model.project.playing &&
      this.model.parentClip.isFocus
    ) {
      this.addOnionSkin();
    }
  }

  protected addOnionSkin(): void {
    this.model.frames
      .filter((frame) => frame.onionSkinned)
      .forEach((frame) => {
        this.onionSkinFrame(frame);
      });
  }

  protected onionSkinFrame(frame: any): void {
    const onionSkinSeekBackwards = this.model.project.onionSkinSeekBackwards;
    const onionSkinSeekForwards = this.model.project.onionSkinSeekForwards;
    const playheadPosition = this.model.project.focus.timeline.playheadPosition;

    frame.view.render();

    this.onionSkinnedFramesLayers.push(frame.view.objectsLayer);

    let seek = 1;
    if (frame.midpoint < playheadPosition) {
      seek = onionSkinSeekBackwards;
    } else if (frame.midpoint > playheadPosition) {
      seek = onionSkinSeekForwards;
    }

    const dist = frame.distanceFrom(playheadPosition);
    let onionMult = (seek - dist + 1) / seek;
    onionMult = Math.min(1, Math.max(0, onionMult));
    const opacity = onionMult * LayerView.BASE_ONION_OPACITY;

    frame.view.objectsLayer.locked = true;
    frame.view.objectsLayer.opacity = opacity;
  }
}
