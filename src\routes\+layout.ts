// <PERSON><PERSON> doesn't have a Node.js server to do proper SSR
// so we will use adapter-static to prerender the app (SSG)
// See: https://v2.tauri.app/start/frontend/sveltekit/ for more info

import { initI18n } from '../lib/i18n';
import { initLocaleStore } from '../stores/localeStore';

export const prerender = true;
export const ssr = false;

// 初始化国际化
export async function load() {
  // 初始化i18n
  await initI18n();

  // 初始化语言store
  initLocaleStore();

  return {};
}
