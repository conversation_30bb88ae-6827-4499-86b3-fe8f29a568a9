/**
 * GameSprite Engine - 快捷键系统
 * 监听键盘事件并通过全局事件系统派发
 */

import { EventSystem, ENGINE_EVENTS } from './EventSystem.js';

export interface HotkeyConfig {
  key: string;           // 主键，如 'a', 'Enter', 'Space'
  ctrl?: boolean;        // 是否需要Ctrl键
  alt?: boolean;         // 是否需要Alt键
  shift?: boolean;       // 是否需要Shift键
  meta?: boolean;        // 是否需要Meta键(Cmd/Win)
  preventDefault?: boolean; // 是否阻止默认行为
  description?: string;  // 快捷键描述
}

export interface HotkeyEvent {
  hotkey: string;        // 快捷键组合字符串
  config: HotkeyConfig;  // 快捷键配置
  originalEvent: KeyboardEvent; // 原始键盘事件
}

export class HotkeySystem {
  private static hotkeyMap: Map<string, HotkeyConfig> = new Map();
  private static isInitialized: boolean = false;
  private static pressedKeys: Set<string> = new Set();

  /**
   * 初始化快捷键系统
   */
  static initialize(): void {
    if (this.isInitialized) {
      console.warn('⌨️ HotkeySystem: 已经初始化过了');
      return;
    }

    // 监听键盘事件
    document.addEventListener('keydown', this.handleKeyDown.bind(this));
    document.addEventListener('keyup', this.handleKeyUp.bind(this));
    
    // 监听窗口失焦，清除按键状态
    window.addEventListener('blur', this.clearPressedKeys.bind(this));

    this.isInitialized = true;
    console.log('⌨️ HotkeySystem: 快捷键系统已初始化');
  }

  /**
   * 销毁快捷键系统
   */
  static destroy(): void {
    if (!this.isInitialized) return;

    document.removeEventListener('keydown', this.handleKeyDown.bind(this));
    document.removeEventListener('keyup', this.handleKeyUp.bind(this));
    window.removeEventListener('blur', this.clearPressedKeys.bind(this));

    this.hotkeyMap.clear();
    this.pressedKeys.clear();
    this.isInitialized = false;
    
    console.log('⌨️ HotkeySystem: 快捷键系统已销毁');
  }

  /**
   * 注册快捷键
   * @param config 快捷键配置
   * @param eventName 要派发的事件名称
   */
  static register(config: HotkeyConfig, eventName: string): void {
    const hotkeyString = this.configToString(config);
    
    if (this.hotkeyMap.has(hotkeyString)) {
      console.warn(`⌨️ HotkeySystem: 快捷键 [${hotkeyString}] 已存在，将被覆盖`);
    }

    this.hotkeyMap.set(hotkeyString, { ...config, eventName } as any);
    console.log(`⌨️ HotkeySystem: 注册快捷键 [${hotkeyString}] -> ${eventName}`);
  }

  /**
   * 取消注册快捷键
   * @param config 快捷键配置
   */
  static unregister(config: HotkeyConfig): boolean {
    const hotkeyString = this.configToString(config);
    const result = this.hotkeyMap.delete(hotkeyString);
    
    if (result) {
      console.log(`⌨️ HotkeySystem: 取消注册快捷键 [${hotkeyString}]`);
    }
    
    return result;
  }

  /**
   * 处理键盘按下事件
   */
  private static handleKeyDown(event: KeyboardEvent): void {
    // 记录按下的键
    this.pressedKeys.add(event.code);

    // 检查是否匹配已注册的快捷键
    const matchedHotkey = this.findMatchingHotkey(event);
    if (matchedHotkey) {
      const [hotkeyString, config] = matchedHotkey;
      
      // 阻止默认行为
      if (config.preventDefault !== false) {
        event.preventDefault();
        event.stopPropagation();
      }

      // 派发快捷键事件
      const hotkeyEvent: HotkeyEvent = {
        hotkey: hotkeyString,
        config,
        originalEvent: event
      };

      // 派发到全局事件系统
      EventSystem.emit(ENGINE_EVENTS.HOTKEY_PRESSED, hotkeyEvent);
      
      // 如果配置了特定事件名称，也派发该事件
      if ((config as any).eventName) {
        EventSystem.emit((config as any).eventName, hotkeyEvent);
      }

      console.log(`⌨️ HotkeySystem: 触发快捷键 [${hotkeyString}]`);
    }
  }

  /**
   * 处理键盘释放事件
   */
  private static handleKeyUp(event: KeyboardEvent): void {
    this.pressedKeys.delete(event.code);
  }

  /**
   * 清除按键状态
   */
  private static clearPressedKeys(): void {
    this.pressedKeys.clear();
  }

  /**
   * 查找匹配的快捷键
   */
  private static findMatchingHotkey(event: KeyboardEvent): [string, HotkeyConfig] | null {
    for (const [hotkeyString, config] of this.hotkeyMap) {
      if (this.isEventMatching(event, config)) {
        return [hotkeyString, config];
      }
    }
    return null;
  }

  /**
   * 检查事件是否匹配配置
   */
  private static isEventMatching(event: KeyboardEvent, config: HotkeyConfig): boolean {
    // 检查主键
    if (event.key.toLowerCase() !== config.key.toLowerCase()) {
      return false;
    }

    // 检查修饰键
    if (!!config.ctrl !== event.ctrlKey) return false;
    if (!!config.alt !== event.altKey) return false;
    if (!!config.shift !== event.shiftKey) return false;
    if (!!config.meta !== event.metaKey) return false;

    return true;
  }

  /**
   * 将配置转换为字符串
   */
  private static configToString(config: HotkeyConfig): string {
    const parts: string[] = [];
    
    if (config.ctrl) parts.push('Ctrl');
    if (config.alt) parts.push('Alt');
    if (config.shift) parts.push('Shift');
    if (config.meta) parts.push('Meta');
    
    parts.push(config.key);
    
    return parts.join('+');
  }

  /**
   * 获取所有已注册的快捷键
   */
  static getAllHotkeys(): { [hotkey: string]: HotkeyConfig } {
    const result: { [hotkey: string]: HotkeyConfig } = {};
    this.hotkeyMap.forEach((config, hotkey) => {
      result[hotkey] = config;
    });
    return result;
  }

  /**
   * 获取当前按下的键
   */
  static getPressedKeys(): string[] {
    return Array.from(this.pressedKeys);
  }

  /**
   * 检查快捷键系统是否已初始化
   */
  static isReady(): boolean {
    return this.isInitialized;
  }
}

// 预定义的常用快捷键配置
export const COMMON_HOTKEYS = {
  // 文件操作
  NEW_PROJECT: { key: 'n', ctrl: true, description: '新建项目' },
  OPEN_PROJECT: { key: 'o', ctrl: true, description: '打开项目' },
  SAVE_PROJECT: { key: 's', ctrl: true, description: '保存项目' },
  SAVE_AS: { key: 's', ctrl: true, shift: true, description: '另存为' },
  
  // 编辑操作
  UNDO: { key: 'z', ctrl: true, description: '撤销' },
  REDO: { key: 'y', ctrl: true, description: '重做' },
  COPY: { key: 'c', ctrl: true, description: '复制' },
  PASTE: { key: 'v', ctrl: true, description: '粘贴' },
  CUT: { key: 'x', ctrl: true, description: '剪切' },
  DELETE: { key: 'Delete', description: '删除' },
  
  // 视图操作
  ZOOM_IN: { key: '=', ctrl: true, description: '放大' },
  ZOOM_OUT: { key: '-', ctrl: true, description: '缩小' },
  ZOOM_RESET: { key: '0', ctrl: true, description: '重置缩放' },
  FIT_TO_SCREEN: { key: 'f', ctrl: true, description: '适应屏幕' },
  
  // 工具切换
  SELECT_TOOL: { key: 'v', description: '选择工具' },
  MOVE_TOOL: { key: 'm', description: '移动工具' },
  ROTATE_TOOL: { key: 'r', description: '旋转工具' },
  SCALE_TOOL: { key: 's', description: '缩放工具' },
  
  // 播放控制
  PLAY_PAUSE: { key: ' ', description: '播放/暂停' },
  STOP: { key: 'Escape', description: '停止' },
  
  // 其他
  FULLSCREEN: { key: 'F11', description: '全屏' },
  HELP: { key: 'F1', description: '帮助' }
} as const;
