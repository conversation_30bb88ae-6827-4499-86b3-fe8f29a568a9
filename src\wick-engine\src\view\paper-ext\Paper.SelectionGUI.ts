/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import * as paper from "paper";

interface SelectionGUIConstructorArgs {
  items?: paper.Item[];
  rotation?: number;
  originX?: number;
  originY?: number;
  layer?: paper.Layer;
}

interface HandleArgs {
  name: string;
  type: "scale" | "pivot" | "rotation";
  center: paper.Point;
  fillColor: string;
  strokeColor: string;
}

class SelectionGUI {
  static get BOX_STROKE_WIDTH(): number {
    return 1;
  }

  static get BOX_STROKE_COLOR(): string {
    return "rgba(100,150,255,1.0)";
  }

  static get HANDLE_RADIUS(): number {
    return 5;
  }

  static get HANDLE_STROKE_WIDTH(): number {
    return SelectionGUI.BOX_STROKE_WIDTH;
  }

  static get HANDLE_STROKE_COLOR(): string {
    return SelectionGUI.BOX_STROKE_COLOR;
  }

  static get HANDLE_FILL_COLOR(): string {
    return "rgba(255,255,255,0.3)";
  }

  static get PIVOT_STROKE_WIDTH(): number {
    return SelectionGUI.BOX_STROKE_WIDTH;
  }

  static get PIVOT_FILL_COLOR(): string {
    return "rgba(255,255,255,0.5)";
  }

  static get PIVOT_STROKE_COLOR(): string {
    return "rgba(0,0,0,1)";
  }

  static get PIVOT_RADIUS(): number {
    return SelectionGUI.HANDLE_RADIUS;
  }

  static get ROTATION_HOTSPOT_RADIUS(): number {
    return 20;
  }

  static get ROTATION_HOTSPOT_FILLCOLOR(): string {
    return "rgba(100,150,255,0.5)";
  }

  private items: paper.Item[];
  private rotation: number;
  private originX: number;
  private originY: number;
  private matrix: paper.Matrix;
  private bounds: paper.Rectangle;
  private item: paper.Group;

  constructor(args: SelectionGUIConstructorArgs = {}) {
    this.items = args.items || [];
    this.rotation = args.rotation || 0;
    this.originX = args.originX || 0;
    this.originY = args.originY || 0;

    this.matrix = new paper.Matrix();
    this.bounds = this._getBoundsOfItems();
    this.matrix.translate(this.bounds.center.x, this.bounds.center.y);
    this.matrix.rotate(this.rotation);
    this.matrix.translate(
      new paper.Point(0, 0).subtract(
        new paper.Point(this.bounds.center.x, this.bounds.center.y)
      )
    );
    this.bounds = this._getBoundsOfItems();

    this.item = new paper.Group({
      applyMatrix: true,
    });
    const layer = args.layer || paper.project.activeLayer;
    layer.addChild(this.item);

    this.item.addChild(this._createBorder());

    if (this.items.length > 1) {
      this.item.addChildren(this._createItemOutlines());
    }

    this.item.addChild(this._createRotationHotspot("topLeft"));
    this.item.addChild(this._createRotationHotspot("topRight"));
    this.item.addChild(this._createRotationHotspot("bottomLeft"));
    this.item.addChild(this._createRotationHotspot("bottomRight"));

    this.item.addChild(this._createScalingHandle("topLeft"));
    this.item.addChild(this._createScalingHandle("topRight"));
    this.item.addChild(this._createScalingHandle("bottomLeft"));
    this.item.addChild(this._createScalingHandle("bottomRight"));
    this.item.addChild(this._createScalingHandle("topCenter"));
    this.item.addChild(this._createScalingHandle("bottomCenter"));
    this.item.addChild(this._createScalingHandle("leftCenter"));
    this.item.addChild(this._createScalingHandle("rightCenter"));

    this.item.addChild(this._createOriginPointHandle());

    this.item.children.forEach((child) => {
      child.data.isSelectionBoxGUI = true;
    });

    this.item.transform(this.matrix);
  }

  destroy(): void {
    this.item.remove();
  }

  moveHandleAndScale(handleName: string, position: paper.Point): void {
    // Implementation will be added later
  }

  moveHandleAndRotate(handleName: string, position: paper.Point): void {
    // Implementation will be added later
  }

  private _createBorder(): paper.Path.Rectangle {
    const border = new paper.Path.Rectangle({
      name: "border",
      from: this.bounds.topLeft,
      to: this.bounds.bottomRight,
      strokeWidth: SelectionGUI.BOX_STROKE_WIDTH,
      strokeColor: SelectionGUI.BOX_STROKE_COLOR,
      insert: false,
    });
    border.data.isBorder = true;
    return border;
  }

  private _createItemOutlines(): paper.Path.Rectangle[] {
    return this.items.map((item) => {
      const outline = new paper.Path.Rectangle(item.bounds);
      outline.fillColor = new paper.Color("rgba(0,0,0,0)");
      outline.strokeColor = new paper.Color(SelectionGUI.BOX_STROKE_COLOR);
      outline.strokeWidth = SelectionGUI.BOX_STROKE_WIDTH;
      outline.data.isBorder = true;
      return outline;
    });
  }

  private _createScalingHandle(edge: string): paper.Path.Circle {
    return this._createHandle({
      name: edge,
      type: "scale",
      center: this.bounds[edge],
      fillColor: SelectionGUI.HANDLE_FILL_COLOR,
      strokeColor: SelectionGUI.HANDLE_STROKE_COLOR,
    });
  }

  private _createOriginPointHandle(): paper.Path.Circle {
    return this._createHandle({
      name: "pivot",
      type: "pivot",
      center: new paper.Point(this.originX, this.originY),
      fillColor: SelectionGUI.PIVOT_FILL_COLOR,
      strokeColor: SelectionGUI.PIVOT_STROKE_COLOR,
    });
  }

  private _createHandle(args: HandleArgs): paper.Path.Circle {
    if (!args) console.error("_createHandle: args is required");
    if (!args.name) console.error("_createHandle: args.name is required");
    if (!args.type) console.error("_createHandle: args.type is required");
    if (!args.center) console.error("_createHandle: args.center is required");
    if (!args.fillColor)
      console.error("_createHandle: args.fillColor is required");
    if (!args.strokeColor)
      console.error("_createHandle: args.strokeColor is required");

    const circle = new paper.Path.Circle({
      center: args.center,
      radius: SelectionGUI.HANDLE_RADIUS / paper.view.zoom,
      strokeWidth: SelectionGUI.HANDLE_STROKE_WIDTH / paper.view.zoom,
      strokeColor: args.strokeColor,
      fillColor: args.fillColor,
      insert: false,
    });

    circle.applyMatrix = false;
    circle.data.handleType = args.type;
    circle.data.handleEdge = args.name;

    return circle;
  }

  private _createRotationHotspot(cornerName: string): paper.Path {
    const r = SelectionGUI.ROTATION_HOTSPOT_RADIUS / paper.view.zoom;
    const hotspot = new paper.Path([
      new paper.Point(0, 0),
      new paper.Point(0, r),
      new paper.Point(r, r),
      new paper.Point(r, -r),
      new paper.Point(-r, -r),
      new paper.Point(-r, 0),
    ]);
    hotspot.fillColor = new paper.Color(
      SelectionGUI.ROTATION_HOTSPOT_FILLCOLOR
    );
    hotspot.position.x = this.bounds[cornerName].x;
    hotspot.position.y = this.bounds[cornerName].y;

    const rotationAngles: { [key: string]: number } = {
      topRight: 0,
      bottomRight: 90,
      bottomLeft: 180,
      topLeft: 270,
    };
    hotspot.rotate(rotationAngles[cornerName]);

    hotspot.data.handleType = "rotation";
    hotspot.data.handleEdge = cornerName;

    return hotspot;
  }

  private _getBoundsOfItems(): paper.Rectangle {
    if (this.items.length === 0) {
      return new paper.Rectangle();
    }

    const itemsForBoundsCalc = this.items.map((item) => {
      const clone = item.clone();
      clone.transform(this.matrix);
      clone.remove();
      return clone;
    });

    let bounds: paper.Rectangle | null = null;
    itemsForBoundsCalc.forEach((item) => {
      bounds = bounds ? bounds.unite(item.bounds) : item.bounds;
    });

    return bounds!;
  }
}

(paper as any).PaperScope.inject({
  SelectionGUI: SelectionGUI,
});
