<script lang="ts">
  /**
   * Toast 提示组件 - 用于显示临时提示信息
   */

  import { _ } from '../../lib/i18n';

  interface Props {
    show?: boolean;
    type?: 'info' | 'success' | 'warning' | 'error';
    title?: string;
    message: string;
    duration?: number; // 自动关闭时间（毫秒），0表示不自动关闭
    closable?: boolean; // 是否显示关闭按钮
    onclose?: () => void; // 关闭回调
  }

  let {
    show = false,
    type = 'info',
    title,
    message,
    duration = 3000,
    closable = true,
    onclose
  }: Props = $props();

  let visible = $state(false);
  let timeoutId: number | null = null;

  // 监听show属性变化
  $effect(() => {
    if (show) {
      visible = true;
      startAutoClose();
    } else {
      visible = false;
      clearAutoClose();
    }
  });

  function startAutoClose() {
    if (duration > 0) {
      clearAutoClose();
      timeoutId = window.setTimeout(() => {
        close();
      }, duration);
    }
  }

  function clearAutoClose() {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  }

  function close() {
    visible = false;
    clearAutoClose();
    onclose?.();
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      close();
    }
  }

  // 获取图标
  function getIcon(type: string): string {
    switch (type) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return 'ℹ️';
    }
  }

  // 获取颜色类
  function getColorClass(type: string): string {
    switch (type) {
      case 'success': return 'toast-success';
      case 'warning': return 'toast-warning';
      case 'error': return 'toast-error';
      default: return 'toast-info';
    }
  }
</script>

{#if visible}
  <div
    class="toast-overlay"
    onclick={close}
    onkeydown={handleKeyDown}
    role="button"
    tabindex="0"
    aria-label={$_('actions.close')}
  >
    <div
      class="toast {getColorClass(type)}"
      onclick={(e) => e.stopPropagation()}
      role="alert"
      aria-live="polite"
    >
      <div class="toast-content">
        <div class="toast-icon">
          {getIcon(type)}
        </div>
        <div class="toast-text">
          {#if title}
            <div class="toast-title">{title}</div>
          {/if}
          <div class="toast-message">{message}</div>
        </div>
        {#if closable}
          <button
            class="toast-close"
            onclick={close}
            title={$_('actions.close')}
            aria-label={$_('actions.close')}
          >
            ✕
          </button>
        {/if}
      </div>
    </div>
  </div>
{/if}

<style>
  .toast-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.2s ease-out;
  }

  .toast {
    background: var(--theme-surface);
    border-radius: var(--border-radius-large);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 400px;
    min-width: 300px;
    margin: var(--spacing-4);
    animation: slideIn 0.3s ease-out;
    border: 2px solid;
  }

  .toast-info {
    border-color: var(--theme-primary);
  }

  .toast-success {
    border-color: #10b981;
  }

  .toast-warning {
    border-color: #f59e0b;
  }

  .toast-error {
    border-color: #ef4444;
  }

  .toast-content {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
  }

  .toast-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
    margin-top: 2px;
  }

  .toast-text {
    flex: 1;
    min-width: 0;
  }

  .toast-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--theme-text);
    margin-bottom: var(--spacing-1);
  }

  .toast-message {
    font-size: var(--font-size-sm);
    color: var(--theme-text-secondary);
    line-height: 1.5;
    word-wrap: break-word;
  }

  .toast-close {
    background: transparent;
    border: none;
    color: var(--theme-text-secondary);
    cursor: pointer;
    font-size: 1rem;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
  }

  .toast-close:hover {
    background: var(--theme-surface-light);
    color: var(--theme-text);
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
</style>
