/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

declare global {
  interface Window {
    Wick: any;
    AudioContext: typeof AudioContext;
    webkitAudioContext: typeof AudioContext;
  }
}

interface AudioTrackArgs {
  callback?: (buffer: AudioBuffer | null) => void;
  onProgress?: (frame: number, maxFrames: number) => void;
  soundInfo?: AudioInfo[];
}

interface AudioInfo {
  src: string;
  startTime: number;
  duration: number;
  volume: number;
}

export class AudioTrack {
  private _project: any; // Wick.Project type

  /**
   * @returns {Wick.Project} The project this audio track belongs to
   */
  get project(): any {
    return this._project;
  }

  set project(project: any) {
    this._project = project;
  }

  /**
   * Create a new AudioTrack
   * @param project - the project to use audio from
   */
  constructor(project: any) {
    this._project = project;
  }

  /**
   * Generate an AudioBuffer of all the project's sounds as one audio track.
   * Can take sound information from a generated sequence.
   */
  toAudioBuffer(args: AudioTrackArgs = {}): void {
    const callback = args.callback || (() => {});
    const onProgress =
      args.onProgress || ((frame: number, maxFrames: number) => {});

    const genBuffer = (audioInfo: AudioInfo[] | undefined) => {
      if (!audioInfo) {
        callback(null);
        return;
      }

      if (audioInfo.length === 0) {
        // No audio in the project, no AudioBuffer to create
        callback(null);
        return;
      }

      AudioTrack.generateProjectAudioBuffer(
        audioInfo,
        (audioArraybuffer: AudioBuffer | null) => {
          callback(audioArraybuffer);
        },
        onProgress
      );
    };

    // If audio information is passed in from a previous render, use that. Otherwise, render it again.
    if (args.soundInfo) {
      genBuffer(args.soundInfo);
    } else {
      this.project.generateAudioSequence({
        onFinish: genBuffer,
        onProgress: onProgress,
      });
    }
  }

  /**
   * Create an AudioBuffer from given sounds.
   * @param projectAudioInfo - info generated on sounds played in the project
   * @param callback - callback to receive the generated AudioBuffer
   * @param onProgress - A function which receives a message and progress
   */
  static generateProjectAudioBuffer(
    projectAudioInfo: AudioInfo[],
    callback: (buffer: AudioBuffer | null) => void,
    onProgress?: (message: string, progress?: number) => void
  ): void {
    window.AudioContext = window.AudioContext || window.webkitAudioContext;
    const ctx = new AudioContext();

    const audiobuffers: AudioBuffer[] = [];

    const mergeAudio = () => {
      onProgress && onProgress("Merging Audio");
      audiobuffers.sort((a, b) => a.duration - b.duration);

      let i = 0;

      const mergedAudioBuffer = audiobuffers.reduce((buffer1, buffer2) => {
        const buf = this.mergeBuffers([buffer1, buffer2], ctx, onProgress);
        i += 1;
        return buf;
      });

      callback(mergedAudioBuffer);
    };
  }

  /**
   * Merge multiple AudioBuffers into one
   */
  private static mergeBuffers(
    buffers: AudioBuffer[],
    ctx: AudioContext,
    onProgress?: (message: string, progress?: number) => void
  ): AudioBuffer {
    // Implementation details would go here
    return ctx.createBuffer(2, 44100, 44100); // Placeholder implementation
  }
}

// Add to global Wick namespace
if (typeof window !== "undefined") {
  window.Wick = window.Wick || {};
  window.Wick.AudioTrack = AudioTrack;
}
