/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Tool } from "./Tool";

export class Cursor extends Tool {
  protected name: string;
  protected SELECTION_TOLERANCE: number;
  protected CURSOR_DEFAULT: string;
  protected CURSOR_SCALE_TOP_RIGHT_BOTTOM_LEFT: string;
  protected CURSOR_SCALE_TOP_LEFT_BOTTOM_RIGHT: string;
  protected CURSOR_SCALE_VERTICAL: string;
  protected CURSOR_SCALE_HORIZONTAL: string;
  protected CURSOR_ROTATE_TOP: string;
  protected CURSOR_ROTATE_RIGHT: string;
  protected CURSOR_ROTATE_BOTTOM: string;
  protected CURSOR_ROTATE_LEFT: string;
  protected CURSOR_ROTATE_TOP_RIGHT: string;
  protected CURSOR_ROTATE_TOP_LEFT: string;
  protected CURSOR_ROTATE_BOTTOM_RIGHT: string;
  protected CURSOR_ROTATE_BOTTOM_LEFT: string;
  protected CURSOR_MOVE: string;
  protected hitResult: any;
  protected selectionBox: any;
  protected selectedItems: any[];
  protected currentCursorIcon: string;
  protected _selection: any;
  protected _widget: any;
  protected __isDragging: boolean;
  protected hoverPreview: any;
  protected project: any;

  /**
   * Creates a cursor tool.
   */
  constructor() {
    super();

    this.name = "cursor";

    this.SELECTION_TOLERANCE = 3;
    this.CURSOR_DEFAULT = "cursors/default.png";
    this.CURSOR_SCALE_TOP_RIGHT_BOTTOM_LEFT =
      "cursors/scale-top-right-bottom-left.png";
    this.CURSOR_SCALE_TOP_LEFT_BOTTOM_RIGHT =
      "cursors/scale-top-left-bottom-right.png";
    this.CURSOR_SCALE_VERTICAL = "cursors/scale-vertical.png";
    this.CURSOR_SCALE_HORIZONTAL = "cursors/scale-horizontal.png";
    this.CURSOR_ROTATE_TOP = "cursors/rotate-top-right.png";
    this.CURSOR_ROTATE_RIGHT = "cursors/rotate-bottom-right.png";
    this.CURSOR_ROTATE_BOTTOM = "cursors/rotate-bottom-left.png";
    this.CURSOR_ROTATE_LEFT = "cursors/rotate-top-left.png";
    this.CURSOR_ROTATE_TOP_RIGHT = "cursors/rotate-top-right.png";
    this.CURSOR_ROTATE_TOP_LEFT = "cursors/rotate-top-left.png";
    this.CURSOR_ROTATE_BOTTOM_RIGHT = "cursors/rotate-bottom-right.png";
    this.CURSOR_ROTATE_BOTTOM_LEFT = "cursors/rotate-bottom-left.png";
    this.CURSOR_MOVE = "cursors/move.png";

    this.hitResult = new (this.paper as any).HitResult();
    this.selectionBox = new (this.paper as any).SelectionBox(this.paper);

    this.selectedItems = [];

    this.currentCursorIcon = "";
    this.__isDragging = false;
  }

  /**
   * Generate the current cursor.
   */
  get cursor(): string {
    return 'url("' + this.currentCursorIcon + '") 32 32, auto';
  }

  onActivate(e: any): void {
    this.selectedItems = [];
  }

  onDeactivate(e: any): void {}

  onMouseMove(e: any): void {
    super.onMouseMove(e);

    // Find the thing that is currently under the cursor.
    this.hitResult = this._updateHitResult(e);

    // Update the image being used for the cursor
    this._setCursor(this._getCursor());
  }

  onMouseDown(e: any): void {
    super.onMouseDown(e);

    if (!e.modifiers) e.modifiers = {};

    this.hitResult = this._updateHitResult(e);

    if (this.hitResult.item && this.hitResult.item.data.isSelectionBoxGUI) {
      // Clicked the selection box GUI, do nothing
    } else if (
      this.hitResult.item &&
      this._isItemSelected(this.hitResult.item)
    ) {
      // We clicked something that was already selected.
      // Shift click: Deselect that item
      if (e.modifiers.shift) {
        this._deselectItem(this.hitResult.item);
        this.fire("canvasModified", {}, "cursorDeselect");
      }
    } else if (this.hitResult.item && this.hitResult.type === "fill") {
      if (!e.modifiers.shift) {
        // Shift click? Keep everything else selected.
        this._clearSelection();
      }
      // Clicked an item: select that item
      this._selectItem(this.hitResult.item);
      this.fire("canvasModified", {}, "cursorSelect");
    } else {
      // Nothing was clicked, so clear the selection and start a new selection box
      // (don't clear the selection if shift is held, though)
      if (this._selection.numObjects > 0 && !e.modifiers.shift) {
        this._clearSelection();
        this.fire("canvasModified", {}, "cursorClearSelect");
      }

      this.selectionBox.start(e.point);
    }
  }

  onDoubleClick(e: any): void {
    const selectedObject = this._selection.getSelectedObject();
    if (selectedObject && selectedObject instanceof (window as any).Wick.Clip) {
      // Double clicked a Clip, set the focus to that Clip.
      if (this.project.focusTimelineOfSelectedClip()) {
        this.fire("canvasModified", {}, "cursorFocusTimelineSelected");
      }
    } else if (
      selectedObject &&
      selectedObject instanceof (window as any).Wick.Path &&
      selectedObject.view.item instanceof (this.paper as any).PointText
    ) {
      // Double clicked text, switch to text tool and edit the text item.
      // TODO
    } else if (!selectedObject) {
      // Double clicked the canvas, leave the current focus.
      if (this.project.focusTimelineOfParentClip()) {
        this.fire("canvasModified", {}, "cursorFocusTimelineParent");
      }
    }
  }

  onMouseDrag(e: any): void {
    if (!e.modifiers) e.modifiers = {};

    this.__isDragging = true;

    if (this.hitResult.item && this.hitResult.item.data.isSelectionBoxGUI) {
      // Update selection drag
      if (!this._widget.currentTransformation) {
        this._widget.startTransformation(this.hitResult.item);
      }
      this._widget.updateTransformation(this.hitResult.item, e);
    } else if (this.selectionBox.active) {
      // Selection box is being used, update it with a new point
      this.selectionBox.drag(e.point);
    } else if (this.hitResult.item && this.hitResult.type === "fill") {
      // We're dragging the selection itself, so move the whole item.
      if (!this._widget.currentTransformation) {
        this._widget.startTransformation(this.hitResult.item);
      }
      this._widget.updateTransformation(this.hitResult.item, e);
    } else {
      this.__isDragging = false;
    }
  }

  onMouseUp(e: any): void {
    if (!e.modifiers) e.modifiers = {};

    if (this.selectionBox.active) {
      // Finish selection box and select objects touching box (or inside box, if alt is held)
      this.selectionBox.mode = e.modifiers.alt ? "contains" : "intersects";
      this.selectionBox.end(e.point);

      if (!e.modifiers.shift) {
        this._selection.clear();
      }

      const selectables = this.selectionBox.items.filter((item: any) => {
        return item.data.wickUUID;
      });

      this._selectItems(selectables);

      // Only modify the canvas if you actually selected something.
      if (this.selectionBox.items.length > 0) {
        this.fire("canvasModified", {}, "cursorSelectMultiple");
      }
    } else if (this._selection.numObjects > 0) {
      if (this.__isDragging) {
        this.__isDragging = false;
        this.project.tryToAutoCreateTween();
        this._widget.finishTransformation();
        this.fire("canvasModified", {}, "cursorDrag");
      }
    }
  }

  // Helper methods (to be implemented)
  protected _updateHitResult(e: any): any {
    return {};
  }
  protected _setCursor(cursor: string): void {}
  protected _getCursor(): string {
    return "";
  }
  protected _isItemSelected(item: any): boolean {
    return false;
  }
  protected _deselectItem(item: any): void {}
  protected _clearSelection(): void {}
  protected _selectItem(item: any): void {}
  protected _selectItems(items: any[]): void {}
}
