/**
 * GameSprite Engine - PIXI 渲染器
 */

import * as PIXI from 'pixi.js';
import type { Renderer } from './types.js';

export class Pixi<PERSON>enderer implements Renderer {
  private app: PIXI.Application;
  private resizeObserver: ResizeObserver | null = null;

  constructor() {
    this.app = new PIXI.Application();
  }

  async initialize(canvas: HTMLCanvasElement, options?: {
    width?: number;
    height?: number;
    backgroundColor?: number;
  }): Promise<void> {
    await this.app.init({
      canvas: canvas,
      width: options?.width || 800,
      height: options?.height || 600,
      background: options?.backgroundColor || 0x2a2a2a,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      resizeTo: canvas.parentElement || canvas, // PIXI内置自动调整到父容器大小
    });

    // 设置canvas样式为100%
    canvas.style.width = '100%';
    canvas.style.height = '100%';

    // 监听PIXI的resize事件
    this.setupPixiResize();
  }

  get width(): number {
    return this.app.screen.width;
  }

  get height(): number {
    return this.app.screen.height;
  }

  // === 尺寸监听 ===
  private setupPixiResize(): void {
    // 使用ResizeObserver监听canvas容器的尺寸变化
    // 这样可以监听到PaneResizer拖动导致的尺寸变化
    const container = this.app.canvas.parentElement;
    if (!container) {
      console.warn('⚠️ PixiRenderer: 找不到canvas容器，无法设置尺寸监听');
      return;
    }

    this.resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;

        // 调整渲染器尺寸
        this.app.renderer.resize(width, height);

        // 调用处理函数
        this.handleResize(width, height);
      }
    });

    // 监听canvas的容器
    this.resizeObserver.observe(container);

    console.log('👁️ PixiRenderer: ResizeObserver监听已设置，监听容器:', container.tagName);
  }

  private handleResize(width: number, height: number): void {
    // 当canvas尺寸变化时，保持stage内容尺寸不变
    // stage的scale和position保持固定，不随canvas尺寸变化

    // 重置stage的变换，确保内容尺寸保持不变
    this.app.stage.scale.set(1, 1);
    this.app.stage.position.set(0, 0);

    console.log('📐 PixiRenderer: 尺寸已更新，stage保持不变', {
      canvasSize: { width, height },
      stageScale: this.app.stage.scale,
      stagePosition: this.app.stage.position
    });
  }

  clear(): void {
    // PIXI 会自动清除
  }

  render(object: any): void {
    // PIXI 会自动渲染舞台
  }

  getStage(): PIXI.Container {
    return this.app.stage;
  }

  getApp(): PIXI.Application {
    return this.app;
  }

  destroy(): void {
    // 销毁PIXI应用
    this.app.destroy(true);
  }
}
