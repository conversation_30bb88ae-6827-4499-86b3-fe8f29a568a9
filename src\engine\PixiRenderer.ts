/**
 * GameSprite Engine - PIXI 渲染器
 */

import * as PIXI from 'pixi.js';
import type { Renderer } from './types.js';

export class PixiRenderer implements Renderer {
  private app: PIXI.Application;

  constructor() {
    this.app = new PIXI.Application();
  }

  async initialize(canvas: HTMLCanvasElement, options?: {
    width?: number;
    height?: number;
    backgroundColor?: number;
  }): Promise<void> {
    await this.app.init({
      canvas: canvas,
      width: options?.width || 800,
      height: options?.height || 600,
      background: options?.backgroundColor || 0x2a2a2a,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      resizeTo: canvas.parentElement || canvas, // PIXI内置自动调整到父容器大小
    });

    // 设置canvas样式为100%
    canvas.style.width = '100%';
    canvas.style.height = '100%';

    // 监听PIXI的resize事件
    this.setupPixiResize();
  }

  get width(): number {
    return this.app.screen.width;
  }

  get height(): number {
    return this.app.screen.height;
  }

  // === 尺寸监听 ===
  private setupPixiResize(): void {
    // PIXI内置的resize功能会自动处理canvas尺寸变化
    // 我们只需要监听resize事件来通知外部组件
    this.app.renderer.on('resize', (width: number, height: number) => {
      this.handleResize(width, height);
    });

    console.log('👁️ PixiRenderer: PIXI内置尺寸监听已设置');
  }

  private handleResize(width: number, height: number): void {
    // 在这里处理尺寸变化，保持内容尺寸不变
    // topContainer的内容会保持原有尺寸，只是在新的canvas尺寸中重新定位

    console.log('📐 PixiRenderer: 尺寸已更新', { width, height });
  }

  clear(): void {
    // PIXI 会自动清除
  }

  render(object: any): void {
    // PIXI 会自动渲染舞台
  }

  getStage(): PIXI.Container {
    return this.app.stage;
  }

  getApp(): PIXI.Application {
    return this.app;
  }

  destroy(): void {
    // 销毁PIXI应用
    this.app.destroy(true);
  }
}
