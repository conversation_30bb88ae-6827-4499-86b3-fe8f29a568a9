/**
 * GameSprite Engine - PIXI 渲染器
 */

import * as PIXI from 'pixi.js';
import type { Renderer } from './types.js';

export class PixiRenderer implements Renderer {
  private app: PIXI.Application;

  constructor() {
    this.app = new PIXI.Application();
  }

  async initialize(canvas: HTMLCanvasElement, options?: {
    width?: number;
    height?: number;
    backgroundColor?: number;
  }): Promise<void> {
    await this.app.init({
      canvas: canvas,
      width: options?.width || 800,
      height: options?.height || 600,
      background: options?.backgroundColor || 0x2a2a2a,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      // 移除resizeTo，让canvas保持固定尺寸
    });

    // 设置canvas样式为100%，脱离文档流
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';

    console.log('✅ PixiRenderer: Canvas设置为脱离文档流的100%');
  }

  get width(): number {
    return this.app.screen.width;
  }

  get height(): number {
    return this.app.screen.height;
  }



  clear(): void {
    // PIXI 会自动清除
  }

  render(object: any): void {
    // PIXI 会自动渲染舞台
  }

  getStage(): PIXI.Container {
    return this.app.stage;
  }

  getApp(): PIXI.Application {
    return this.app;
  }

  destroy(): void {
    // 销毁PIXI应用
    this.app.destroy(true);
  }
}
