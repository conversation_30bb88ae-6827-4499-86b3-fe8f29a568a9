/**
 * GameSprite Engine - PIXI 渲染器
 */

import * as PIXI from 'pixi.js';
import type { Renderer } from './types.js';

export class <PERSON><PERSON><PERSON><PERSON><PERSON> implements Renderer {
  private app: PIXI.Application;
  private canvas: HTMLCanvasElement | null = null;
  private resizeObserver: ResizeObserver | null = null;
  private onResizeCallback: ((width: number, height: number) => void) | null = null;

  constructor() {
    this.app = new PIXI.Application();
  }

  async initialize(canvas: HTMLCanvasElement, options?: {
    width?: number;
    height?: number;
    backgroundColor?: number;
  }): Promise<void> {
    this.canvas = canvas;

    await this.app.init({
      canvas: canvas,
      width: options?.width || 800,
      height: options?.height || 600,
      background: options?.backgroundColor || 0x2a2a2a,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      resizeTo: canvas.parentElement || canvas, // 自动调整到父容器大小
    });

    // 设置canvas样式为100%
    canvas.style.width = '100%';
    canvas.style.height = '100%';

    // 监听canvas尺寸变化
    this.setupResizeObserver();
  }

  get width(): number {
    return this.app.screen.width;
  }

  get height(): number {
    return this.app.screen.height;
  }

  // === 尺寸监听 ===
  private setupResizeObserver(): void {
    if (!this.canvas) return;

    // 使用ResizeObserver监听canvas容器尺寸变化
    this.resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        this.handleResize(width, height);
      }
    });

    // 监听canvas的父容器
    const container = this.canvas.parentElement;
    if (container) {
      this.resizeObserver.observe(container);
    }

    console.log('👁️ PixiRenderer: 尺寸监听已设置');
  }

  private handleResize(width: number, height: number): void {
    // 更新PIXI应用尺寸
    this.app.renderer.resize(width, height);

    // 通知外部监听器
    if (this.onResizeCallback) {
      this.onResizeCallback(width, height);
    }

    console.log('📐 PixiRenderer: 尺寸已更新', { width, height });
  }

  // 设置尺寸变化回调
  setResizeCallback(callback: (width: number, height: number) => void): void {
    this.onResizeCallback = callback;
  }

  clear(): void {
    // PIXI 会自动清除
  }

  render(object: any): void {
    // PIXI 会自动渲染舞台
  }

  getStage(): PIXI.Container {
    return this.app.stage;
  }

  getApp(): PIXI.Application {
    return this.app;
  }

  destroy(): void {
    // 清理尺寸监听
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }

    this.onResizeCallback = null;
    this.canvas = null;
    this.app.destroy(true);
  }
}
