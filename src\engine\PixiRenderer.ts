/**
 * GameSprite Engine - PIXI 渲染器
 */

import * as PIXI from 'pixi.js';
import type { Renderer } from './types.js';

export class Pixi<PERSON>enderer implements Renderer {
  private app: PIXI.Application;

  // Stage变换控制
  private stageZoom: number = 1;
  private stagePanX: number = 0;
  private stagePanY: number = 0;
  private minZoom: number = 0.1;
  private maxZoom: number = 5.0;
  private zoomStep: number = 0.1;

  // 事件状态
  private isSpacePressed: boolean = false;
  private isDragging: boolean = false;
  private lastMouseX: number = 0;
  private lastMouseY: number = 0;

  constructor() {
    this.app = new PIXI.Application();
  }

  async initialize(canvas: HTMLCanvasElement, options?: {
    width?: number;
    height?: number;
    backgroundColor?: number;
  }): Promise<void> {
    await this.app.init({
      canvas: canvas,
      width: options?.width || 800,
      height: options?.height || 600,
      background: options?.backgroundColor || 0x2a2a2a,
      antialias: true,
      resolution: window.devicePixelRatio || 1,
      // 移除resizeTo，让canvas保持固定尺寸
    });

    // 设置canvas样式为100%，脱离文档流
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';

    // 设置stage事件监听
    this.setupStageEvents();

    console.log('✅ PixiRenderer: Canvas设置为脱离文档流的100%，事件监听已设置');
  }

  get width(): number {
    return this.app.screen.width;
  }

  get height(): number {
    return this.app.screen.height;
  }



  // === 事件处理方法 ===
  private handleWheel = (event: WheelEvent) => {
    if (!event.ctrlKey) return;

    event.preventDefault();
    event.stopPropagation();

    const delta = event.deltaY;
    const zoomDirection = delta > 0 ? -1 : 1;
    this.zoom(zoomDirection * this.zoomStep);
  };

  private handleMouseDown = (event: MouseEvent) => {
    if (this.isSpacePressed && event.button === 0) {
      this.isDragging = true;
      this.lastMouseX = event.clientX;
      this.lastMouseY = event.clientY;
      this.app.canvas.style.cursor = 'grabbing';
      event.preventDefault();
    }
  };

  private handleMouseMove = (event: MouseEvent) => {
    if (this.isDragging && this.isSpacePressed) {
      const deltaX = event.clientX - this.lastMouseX;
      const deltaY = event.clientY - this.lastMouseY;

      this.pan(deltaX, deltaY);

      this.lastMouseX = event.clientX;
      this.lastMouseY = event.clientY;
    }
  };

  private handleMouseUp = (event: MouseEvent) => {
    if (event.button === 0) {
      this.isDragging = false;
      this.app.canvas.style.cursor = this.isSpacePressed ? 'grab' : 'default';
    }
  };

  private handleMouseLeave = () => {
    this.isDragging = false;
    this.app.canvas.style.cursor = this.isSpacePressed ? 'grab' : 'default';
  };

  private handleKeyDown = (event: KeyboardEvent) => {
    if (event.code === 'Space') {
      this.isSpacePressed = true;
      this.app.canvas.style.cursor = this.isDragging ? 'grabbing' : 'grab';
      event.preventDefault();
    }
  };

  private handleKeyUp = (event: KeyboardEvent) => {
    if (event.code === 'Space') {
      this.isSpacePressed = false;
      this.isDragging = false;
      this.app.canvas.style.cursor = 'default';
    }
  };

  // === Canvas事件监听 ===
  private setupStageEvents(): void {
    const canvas = this.app.canvas;

    // 添加canvas事件监听
    canvas.addEventListener('wheel', this.handleWheel);
    canvas.addEventListener('mousedown', this.handleMouseDown);
    canvas.addEventListener('mousemove', this.handleMouseMove);
    canvas.addEventListener('mouseup', this.handleMouseUp);
    canvas.addEventListener('mouseleave', this.handleMouseLeave);

    // 添加键盘事件监听
    document.addEventListener('keydown', this.handleKeyDown);
    document.addEventListener('keyup', this.handleKeyUp);

    console.log('🖱️ PixiRenderer: Canvas事件监听已设置');
  }

  // === Stage变换方法 ===
  private updateStageTransform(): void {
    const stage = this.app.stage;

    // 计算stage中心点
    const centerX = this.width / 2;
    const centerY = this.height / 2;

    // 应用缩放（以中心点为原点）
    stage.scale.set(this.stageZoom);

    // 应用平移（考虑缩放中心点）
    stage.position.set(
      centerX + this.stagePanX - centerX * this.stageZoom,
      centerY + this.stagePanY - centerY * this.stageZoom
    );
  }

  zoom(delta: number): void {
    const newZoom = this.stageZoom + delta;
    const clampedZoom = Math.max(this.minZoom, Math.min(this.maxZoom, newZoom));

    if (clampedZoom === this.stageZoom) return;

    this.stageZoom = clampedZoom;
    this.updateStageTransform();

    console.log('🔍 PixiRenderer: Stage缩放', { zoom: this.stageZoom });
  }

  pan(deltaX: number, deltaY: number): void {
    this.stagePanX += deltaX;
    this.stagePanY += deltaY;
    this.updateStageTransform();
  }

  resetTransform(): void {
    this.stageZoom = 1;
    this.stagePanX = 0;
    this.stagePanY = 0;
    this.updateStageTransform();

    console.log('🔄 PixiRenderer: Stage变换已重置');
  }

  // === 原有方法 ===
  clear(): void {
    // PIXI 会自动清除
  }

  render(): void {
    // PIXI 会自动渲染舞台
  }

  getStage(): PIXI.Container {
    return this.app.stage;
  }

  getApp(): PIXI.Application {
    return this.app;
  }

  destroy(): void {
    // 清理canvas事件监听
    const canvas = this.app.canvas;
    if (canvas) {
      canvas.removeEventListener('wheel', this.handleWheel);
      canvas.removeEventListener('mousedown', this.handleMouseDown);
      canvas.removeEventListener('mousemove', this.handleMouseMove);
      canvas.removeEventListener('mouseup', this.handleMouseUp);
      canvas.removeEventListener('mouseleave', this.handleMouseLeave);
    }

    // 清理键盘事件监听
    document.removeEventListener('keydown', this.handleKeyDown);
    document.removeEventListener('keyup', this.handleKeyUp);

    // 销毁PIXI应用
    this.app.destroy(true);
  }
}
