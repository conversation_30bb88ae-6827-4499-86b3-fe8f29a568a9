/**
 * 二进制项目保存和加载功能
 * 使用二进制格式直接存储项目数据，无需数据转换
 */

use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use tauri::command;

/// 二进制项目数据结构 - 使用字符串存储 JSON 数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BinaryProjectData {
    /// 项目元信息
    pub metadata: ProjectMetadata,
    /// 图集数据 - 存储为 JSON 字符串
    pub atlas_data: String,
    /// 资源数据 - 存储为 JSON 字符串
    pub resource_data: String,
    /// 导出设置数据 - 存储为 JSON 字符串
    pub export_settings: String,
    /// 数据校验和
    pub checksum: String,
}

/// 项目元信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectMetadata {
    /// 项目文件版本
    pub version: String,
    /// 项目名称
    pub project_name: String,
    /// 创建时间
    pub created_at: String,
    /// 最后更新时间
    pub updated_at: String,
    /// 创建者
    pub created_by: Option<String>,
    /// 项目描述
    pub description: Option<String>,
    /// 项目标签
    pub tags: Option<Vec<String>>,
    /// 存储格式标识
    pub storage_format: String,
}

/// 保存结果
#[derive(Debug, Serialize, Deserialize)]
pub struct BinarySaveResult {
    pub success: bool,
    pub file_path: Option<String>,
    pub file_size: Option<u64>,
    pub error: Option<String>,
    pub format: String,
}

/// 加载结果
#[derive(Debug, Serialize, Deserialize)]
pub struct BinaryLoadResult {
    pub success: bool,
    pub data: Option<BinaryProjectData>,
    pub error: Option<String>,
    pub warnings: Option<Vec<String>>,
    pub format: String,
}

/// 计算数据校验和
fn calculate_binary_checksum(data: &[u8]) -> String {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};

    let mut hasher = DefaultHasher::new();
    data.hash(&mut hasher);
    format!("{:x}", hasher.finish())
}

/// 🎯 二进制保存项目 - 核心新功能
#[command]
pub async fn save_project_binary(
    file_path: String,
    project_name: String,
    description: Option<String>,
    tags: Option<Vec<String>>,
    image_resources: serde_json::Value,
    atlas_data: serde_json::Value,
    export_settings: serde_json::Value,
) -> Result<BinarySaveResult, String> {
    println!("🚀 开始二进制保存项目: {}", file_path);

    let start_time = std::time::Instant::now();

    // 1. 构建项目数据 - 将 JSON 值转换为字符串
    let now = chrono::Utc::now().to_rfc3339();
    let project_data = BinaryProjectData {
        metadata: ProjectMetadata {
            version: "1.0.0".to_string(),
            project_name,
            created_at: now.clone(),
            updated_at: now,
            created_by: None,
            description,
            tags,
            storage_format: "binary".to_string(),
        },
        atlas_data: serde_json::to_string(&atlas_data)
            .map_err(|e| format!("序列化图集数据失败: {}", e))?,
        resource_data: serde_json::to_string(&image_resources)
            .map_err(|e| format!("序列化资源数据失败: {}", e))?,
        export_settings: serde_json::to_string(&export_settings)
            .map_err(|e| format!("序列化导出设置失败: {}", e))?,
        checksum: String::new(), // 稍后计算
    };

    // 2. 序列化为二进制数据
    let binary_data = bincode::serialize(&project_data)
        .map_err(|e| format!("二进制序列化失败: {}", e))?;

    // 3. 计算校验和
    let checksum = calculate_binary_checksum(&binary_data);

    // 4. 重新序列化包含校验和的数据
    let mut final_project_data = project_data;
    final_project_data.checksum = checksum.clone();

    let final_binary_data = bincode::serialize(&final_project_data)
        .map_err(|e| format!("最终二进制序列化失败: {}", e))?;

    // 5. 确保目录存在
    let path = PathBuf::from(&file_path);
    if let Some(parent) = path.parent() {
        if !parent.exists() {
            fs::create_dir_all(parent)
                .map_err(|e| format!("创建目录失败: {}", e))?;
        }
    }

    // 6. 写入二进制文件
    fs::write(&path, &final_binary_data)
        .map_err(|e| format!("写入二进制文件失败: {}", e))?;

    // 7. 获取文件大小
    let file_size = fs::metadata(&path)
        .map(|m| m.len())
        .unwrap_or(0);

    let processing_time = start_time.elapsed().as_millis() as u64;

    println!("✅ 二进制保存完成: {} ms, {} bytes, 校验和: {}",
        processing_time, file_size, checksum);

    Ok(BinarySaveResult {
        success: true,
        file_path: Some(file_path),
        file_size: Some(file_size),
        error: None,
        format: "binary".to_string(),
    })
}

/// 🎯 二进制加载项目 - 核心新功能
#[command]
pub async fn load_project_binary(
    file_path: String,
) -> Result<BinaryLoadResult, String> {
    println!("🚀 开始二进制加载项目: {}", file_path);

    let start_time = std::time::Instant::now();
    let path = PathBuf::from(&file_path);

    if !path.exists() {
        return Ok(BinaryLoadResult {
            success: false,
            data: None,
            error: Some("项目文件不存在".to_string()),
            warnings: None,
            format: "binary".to_string(),
        });
    }

    // 1. 读取二进制文件
    let binary_data = fs::read(&path)
        .map_err(|e| format!("读取二进制文件失败: {}", e))?;

    // 2. 反序列化项目数据
    let project_data: BinaryProjectData = bincode::deserialize(&binary_data)
        .map_err(|e| format!("二进制反序列化失败: {}", e))?;

    let mut warnings = Vec::new();

    // 3. 验证数据完整性
    let mut temp_data = project_data.clone();
    temp_data.checksum = String::new();

    if let Ok(temp_binary) = bincode::serialize(&temp_data) {
        let calculated_checksum = calculate_binary_checksum(&temp_binary);
        if calculated_checksum != project_data.checksum {
            warnings.push("数据校验和不匹配，文件可能已损坏".to_string());
        }
        println!("校验和验证: 存储={}, 计算={}", project_data.checksum, calculated_checksum);
    }

    // 4. 版本兼容性检查
    if project_data.metadata.version != "1.0.0" {
        warnings.push(format!(
            "项目文件版本 {} 可能不完全兼容当前版本",
            project_data.metadata.version
        ));
    }

    let processing_time = start_time.elapsed().as_millis() as u64;
    println!("✅ 二进制加载完成: {} ms", processing_time);

    Ok(BinaryLoadResult {
        success: true,
        data: Some(project_data),
        error: None,
        warnings: if warnings.is_empty() { None } else { Some(warnings) },
        format: "binary".to_string(),
    })
}

/// 🎯 验证二进制项目文件
#[command]
pub async fn validate_project_binary(file_path: String) -> Result<bool, String> {
    println!("验证二进制项目文件: {}", file_path);

    let path = PathBuf::from(&file_path);

    if !path.exists() {
        return Ok(false);
    }

    match fs::read(&path) {
        Ok(binary_data) => {
            match bincode::deserialize::<BinaryProjectData>(&binary_data) {
                Ok(data) => {
                    // 验证校验和
                    let mut temp_data = data.clone();
                    temp_data.checksum = String::new();

                    if let Ok(temp_binary) = bincode::serialize(&temp_data) {
                        let calculated_checksum = calculate_binary_checksum(&temp_binary);
                        let is_valid = calculated_checksum == data.checksum;
                        println!("二进制项目文件验证结果: {}", is_valid);
                        Ok(is_valid)
                    } else {
                        Ok(false)
                    }
                }
                Err(_) => Ok(false),
            }
        }
        Err(_) => Ok(false),
    }
}
