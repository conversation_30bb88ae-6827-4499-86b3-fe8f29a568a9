<script lang="ts">
  /**
   * ImageItem 组件 - 处理图片资源的加载、显示和交互
   */

  import type { ImageResource, FolderResource, ResourceItem } from '../types';
  import DeleteButton from '../components/ui/DeleteButton.svelte';
  import DeleteConfirmDialog from '../components/ui/DeleteConfirmDialog.svelte';

  import { resourceActions, resourceStore } from '../stores/resourceStore';
  import { atlasStore } from '../stores/atlasStore';
  import { dragStore } from '../stores/dragStore';

  // 导入国际化
  import { _ } from '../lib/i18n';

  interface Props {
    imageResource: ResourceItem;  // 🎯 接受ResourceItem，内部判断类型
  }

  let {
    imageResource
  }: Props = $props();

  // 🎯 为了兼容现有代码，创建resource别名
  const resource = $derived(imageResource);

  // 🎯 判断资源类型
  const isImage = $derived(resource.type === 'image');
  const isFolder = $derived(resource.type === 'folder');

  // 🎯 内部管理selectedResource状态 - 使用id比较避免代理对象问题
  let storeState = $state($resourceStore);
  const selectedResource = $derived(storeState.selectedResource);
  const isSelected = $derived(selectedResource?.id === resource.id);

  // 🎯 监听store变化
  $effect(() => {
    const unsubscribe = resourceStore.subscribe((state) => {
      storeState = state;
    });
    return () => unsubscribe();
  });

  // 🎯 点击处理 - 确保选中rootResources中的对象，同时清空图集选择
  async function handleClick() {
    // 🎯 先清空atlasStore的selectedAtlas，避免状态冲突
    await atlasStore.selectAtlas(null);

    resourceStore.update(state => {
      // 🎯 递归查找rootResources中的对应对象
      const findResourceInTree = (resources: any[], targetId: string): any => {
        for (const res of resources) {
          if (res.id === targetId) {
            return res;
          } else if (res.type === 'folder' && res.children) {
            const found = findResourceInTree(res.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };

      const targetResource = findResourceInTree(state.rootResources, resource.id);

      if (targetResource) {
        console.log('🎯 ImageItem: 点击选中资源，使用rootResources中的对象', {
          resourceName: targetResource.name,
          isSameObject: targetResource === resource
        });

        return {
          ...state,
          selectedResource: targetResource // 🎯 使用rootResources中的对象
        };
      } else {
        console.warn('⚠️ ImageItem: 在rootResources中找不到对应的资源', resource.id);
        return {
          ...state,
          selectedResource: resource // 🎯 fallback到原对象
        };
      }
    });
  }

  // 🎯 双击处理 - 根据资源类型处理
  function handleDoubleClick() {
    // 先设置选中状态
    handleClick();

    if (isFolder) {
      // 🎯 文件夹双击：查找rootResources中的对应文件夹并设置为currentFolder
      console.log('🎯 ImageItem: 双击文件夹，查找rootResources中的对应对象', {
        folderName: resource.name,
        folderPath: resource.path
      });

      resourceStore.update(state => {
        // 🎯 递归查找rootResources中的对应文件夹
        const findFolderInTree = (resources: any[], targetPath: string): any => {
          for (const res of resources) {
            if (res.type === 'folder' && res.path === targetPath) {
              return res;
            } else if (res.type === 'folder' && res.children) {
              const found = findFolderInTree(res.children, targetPath);
              if (found) return found;
            }
          }
          return null;
        };

        const targetFolder = findFolderInTree(state.rootResources, resource.path || '');

        if (targetFolder) {
          // 更新导航栈
          const newNavigationStack = [...state.navigationStack];
          if (state.currentFolder) {
            newNavigationStack.push(state.currentFolder.path || '');
          }

          console.log('✅ ImageItem: 找到rootResources中的文件夹对象', {
            folderName: targetFolder.name,
            folderPath: targetFolder.path,
            isSameObject: targetFolder === resource
          });

          return {
            ...state,
            currentFolder: targetFolder, // 🎯 使用rootResources中的对象
            navigationStack: newNavigationStack
          };
        } else {
          console.warn('⚠️ ImageItem: 在rootResources中找不到对应的文件夹', resource.path);
          return state;
        }
      });
    } else if (isImage) {
      // 🎯 图片双击：可以添加预览或编辑逻辑
      console.log('🎯 ImageItem: 双击图片资源', resource.name);
      // TODO: 可以在这里添加打开图片编辑器或预览的逻辑
    }
  }

  // 🎯 简化的状态管理 - 只保留必要状态
  let showDeleteConfirm = $state(false);

  // 🎯 获取图片显示源 - 只对图片资源有效
  const imageSrc = $derived(() => {
    if (!isImage || !('processedData' in resource) || !resource.processedData) return '';

    // 优先使用预览图，其次缩略图，最后原图
    if (resource.processedData.preview?.dataUrl) {
      return resource.processedData.preview.dataUrl;
    } else if (resource.processedData.thumbnail?.dataUrl) {
      return resource.processedData.thumbnail.dataUrl;
    } else if (resource.processedData.original?.dataUrl) {
      return resource.processedData.original.dataUrl;
    }

    return '';
  });




  // 🎯 图片尺寸信息 - 只对图片资源有效
  const imageInfo = $derived(() => {
    if (!isImage || !('processedData' in resource) || !resource.processedData) return null;

    // 优先使用原图尺寸信息
    if (resource.processedData.original) {
      return {
        width: resource.processedData.original.width,
        height: resource.processedData.original.height
      };
    } else if (resource.processedData.preview) {
      return {
        width: resource.processedData.preview.width,
        height: resource.processedData.preview.height
      };
    } else if (resource.processedData.thumbnail) {
      return {
        width: resource.processedData.thumbnail.width,
        height: resource.processedData.thumbnail.height
      };
    }

    return null;
  });





  // 🎯 头部点击处理 - 确保选中rootResources中的对象，同时清空图集选择
  async function handleHeaderClick(event: MouseEvent) {
    event.stopPropagation(); // 阻止事件冒泡

    // 🎯 先清空atlasStore的selectedAtlas，避免状态冲突
    await atlasStore.selectAtlas(null);

    resourceStore.update(state => {
      // 🎯 递归查找rootResources中的对应对象
      const findResourceInTree = (resources: any[], targetId: string): any => {
        for (const res of resources) {
          if (res.id === targetId) {
            return res;
          } else if (res.type === 'folder' && res.children) {
            const found = findResourceInTree(res.children, targetId);
            if (found) return found;
          }
        }
        return null;
      };

      const targetResource = findResourceInTree(state.rootResources, resource.id);

      if (targetResource) {
        console.log('🎯 ImageItem: 头部点击，使用rootResources中的对象', {
          resourceName: targetResource.name,
          isSameObject: targetResource === resource
        });

        return {
          ...state,
          selectedResource: targetResource // 🎯 使用rootResources中的对象
        };
      } else {
        console.warn('⚠️ ImageItem: 在rootResources中找不到对应的资源', resource.id);
        return {
          ...state,
          selectedResource: resource // 🎯 fallback到原对象
        };
      }
    });
  }

  // 🎯 头部键盘事件处理
  function handleHeaderKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleHeaderClick(event as any);
    }
  }





  // 键盘事件处理（用于无障碍支持）
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleClick();
    }
  }

  // 🎯 删除资源处理函数
  function handleDelete(event: MouseEvent) {
    event.stopPropagation(); // 防止触发父元素的点击事件

    console.log('🗑️ ImageItem: 删除资源', {
      resourceId: resource.id,
      resourceName: resource.name,
      resourceType: resource.type
    });

    // 显示删除确认对话框
    showDeleteConfirm = true;
  }

  // 确认删除
  function confirmDelete() {
    showDeleteConfirm = false;

    // 🎯 1. 先检查resourceStore，如果删除的是当前选中的资源，清除选择状态
    if (selectedResource?.id === resource.id) {
      resourceStore.update(state => ({
        ...state,
        selectedResource: null
      }));
      console.log('🔄 ImageItem: 删除当前选中的资源，清除选择状态');
    }

    // 🎯 2. 从所有图集中移除该资源的引用
    const atlasState = atlasStore.state;
    atlasState.atlases.forEach(atlas => {
      // 检查图集中是否包含该资源（通过原始ID或拷贝ID匹配）
      const hasResource = atlas.children.some(child =>
        child.id === resource.id ||
        child.id.startsWith(`${resource.id}_copy_`) ||
        child.splitInfo?.parentId === resource.id
      );

      if (hasResource) {
        // 移除所有相关的资源引用
        const childrenToRemove = atlas.children.filter(child =>
          child.id === resource.id ||
          child.id.startsWith(`${resource.id}_copy_`) ||
          child.splitInfo?.parentId === resource.id
        );

        childrenToRemove.forEach(child => {
          atlasStore.removeResourceFromAtlas(atlas.id, child.id);
          console.log('🔄 ImageItem: 从图集中移除资源引用', {
            atlasName: atlas.name,
            childId: child.id,
            childName: child.name
          });
        });
      }
    });

    // 🎯 3. 最后从resourceStore中删除资源
    resourceActions.removeResource(resource.id);

    console.log('✅ ImageItem: 资源删除成功，已清理所有相关引用', {
      resourceId: resource.id,
      resourceName: resource.name
    });
  }

  // 取消删除
  function cancelDelete() {
    showDeleteConfirm = false;
    console.log('❌ ImageItem: 用户取消删除操作');
  }

</script>

<div class="image-item-wrapper">
  <div
    class="image-item"
    class:selected={isSelected}
    onclick={handleClick}
    ondblclick={handleDoubleClick}
    onkeydown={handleKeydown}
    role="button"
    tabindex="0"
    aria-label="{$_('resource.title')}: {resource.name}"
  >
    <div class="image-container">
      {#if isFolder}
        <!-- 🎯 文件夹显示 -->
        <div class="folder-display">
          <div class="folder-icon">📁</div>
          <div class="folder-info">
            {#if 'children' in resource && resource.children}
              <span class="folder-count">{resource.children.length} {$_('ui.items')}</span>
            {/if}
          </div>
        </div>
      {:else if isImage && imageSrc()}
        <!-- 🎯 图片显示 -->
        <img
          src={imageSrc()}
          alt={resource.name}
          class="resource-image"
          loading="lazy"
        />
      {:else if isImage}
        <!-- 🎯 图片处理中占位符 -->
        <div class="image-placeholder">
          <div class="placeholder-icon">🖼️</div>
          <div class="placeholder-text">{$_('status.processing')}...</div>
        </div>
      {:else}
        <!-- 🎯 未知类型占位符 -->
        <div class="image-placeholder">
          <div class="placeholder-icon">❓</div>
          <div class="placeholder-text">{$_('ui.unknownType')}</div>
        </div>
      {/if}

      <!-- 🎯 拖拽覆盖层 - 只对图片启用拖拽 -->
      {#if isImage}
        <div
          class="drag-overlay"
          role="button"
          tabindex="-1"
          aria-label="{$_('ui.drag')} {resource.name}"
          onmousedown={(e) => {
          console.log('🎯 ImageItem: 开始拖拽', {
            resourceName: resource.name,
            resourceId: resource.id,
            hasImageSrc: !!imageSrc,
            hasResourceData: !!resource.data
          });

          const dragData = {
            type: 'image' as const,
            src: imageSrc() || '',
            alt: resource.name,
            name: resource.name,
            resourceId: resource.id,
            resource: resource,
            // 🎯 优先使用 resource 的宽高（原图实际尺寸）
            width: resource.width || imageInfo()?.width,
            height: resource.height || imageInfo()?.height,
            buffer: resource.data
          };

          try {
            // 启动拖拽状态
            dragStore.startDrag(dragData, {
              x: e.clientX,
              y: e.clientY
            });

            // 🎯 添加全局鼠标移动监听器来更新拖拽预览位置
            let lastUpdateTime = 0;
            const handleMouseMove = (event: MouseEvent) => {
              const now = Date.now();
              // 限制更新频率为60fps (16.67ms)
              if (now - lastUpdateTime > 16) {
                dragStore.updateMousePosition({
                  x: event.clientX,
                  y: event.clientY
                });
                lastUpdateTime = now;
              }
            };

            const handleMouseUp = () => {
              console.log('🎯 ImageItem: 结束拖拽');
              dragStore.endDrag();

              // 移除事件监听
              document.removeEventListener('mousemove', handleMouseMove);
              document.removeEventListener('mouseup', handleMouseUp);
            };

            // 添加全局事件监听
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            // 阻止默认行为和事件冒泡
            e.preventDefault();
            e.stopPropagation();

            console.log('✅ ImageItem: 拖拽数据设置成功，已添加鼠标监听器');
          } catch (error) {
            console.error('❌ ImageItem: 拖拽数据设置失败', error);
          }
        }}
      ></div>
      {/if}

      <!-- 🎯 右上角删除按钮 -->
      <DeleteButton
        onclick={handleDelete}
        size="small"
        variant="overlay"
        position="absolute"
        top="3px"
        right="3px"
        title={$_('actions.delete')}
        ariaLabel="{$_('actions.delete')} {resource.name}"
      />
    </div>

    <!-- 🎯 可点击的头部区域 -->
    <div
      class="image-header"
      onclick={handleHeaderClick}
      onkeydown={handleHeaderKeydown}
      role="button"
      tabindex="0"
      title={$_('ui.clickToShow')}
    >
      <div class="image-info">
        <span class="image-name" title={resource.name}>{resource.name}</span>
        {#if isImage && resource.width && resource.height}
          <span class="image-size">{resource.width}×{resource.height}</span>
        {:else if isFolder && 'children' in resource && resource.children}
          <span class="folder-size">{resource.children.length} {$_('ui.items')}</span>
        {/if}
      </div>
      <div class="header-icon">{isFolder ? '📁' : '🎯'}</div>
    </div>

  </div>
</div>

<!-- 删除确认对话框 -->
<DeleteConfirmDialog
  show={showDeleteConfirm}
  message={$_('resource.deleteConfirm', { values: { type: isFolder ? $_('resource.folder') : $_('resource.image'), name: resource.name } })}
  warning={$_('dialog.deleteWarning')}
  onConfirm={confirmDelete}
  onCancel={cancelDelete}
/>

<style>
  .image-item-wrapper {
    display: flex;
    transition: var(--transition-base);
    position: relative;
  }



  .image-item {
    display: flex;
    flex-direction: column;
    background: var(--theme-surface);
    border: 2px solid transparent;
    border-radius: var(--border-radius-large);
    overflow: hidden;
    cursor: pointer;
    transition: var(--transition-base);
    position: relative;
    flex-shrink: 0;
    width: 100%; /* 🎯 固定宽度 */
    max-width: 100%; /* 🎯 最大宽度限制 */
    box-sizing: border-box; /* 🎯 包含边框在内的盒模型 */
  }

  .image-item:hover {
    border-color: var(--theme-border-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--theme-shadow-light);
  }

  .image-item.selected {
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px var(--theme-primary-light);
  }

  .image-item:focus {
    outline: none;
    border-color: var(--theme-primary);
  }

  .image-container {
    position: relative;
    width: 100%;
    height: 100px !important; /* 🎯 缩小容器高度：120px -> 100px */
    min-height: 100px !important; /* 🎯 最小高度 */
    max-height: 100px !important; /* 🎯 最大高度 */
    background: var(--theme-surface-light);
    display: flex !important;
    align-items: center;
    justify-content: center;
    overflow: hidden; /* 🎯 防止图片溢出容器 */
    flex-shrink: 0; /* 🎯 防止被压缩 */
  }

  /* 🎯 强制约束Image组件尺寸，防止撑大容器 */
  .image-container :global(.image-wrapper) {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
    display: inline-block !important;
    position: relative !important;
  }

  .image-container :global(.image) {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    display: block !important;
  }

  /* 🎯 渐进式图片组件样式 */
  .image-container :global(.progressive-image-item) {
    width: 100%;
    height: 100%;
  }

  /* 🎯 拖拽覆盖层 */
  .drag-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    cursor: grab;
    background: transparent;
    user-select: none;
  }

  .drag-overlay:active {
    cursor: grabbing;
  }

  .drag-overlay:focus {
    outline: 2px solid var(--theme-primary);
    outline-offset: 2px;
  }

  /* 🎯 图片占位符 */
  .image-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--theme-text-secondary);
    font-size: 14px;
    height: 100%;
    width: 100%;
    background: var(--theme-bg-secondary);
    border-radius: 6px;
  }

  .placeholder-icon {
    font-size: 1.8rem; /* 🎯 缩小占位符图标：2rem -> 1.8rem */
    opacity: 0.5;
  }

  .placeholder-text {
    font-size: 11px; /* 🎯 缩小字体：12px -> 11px */
    opacity: 0.7;
  }

  /* 🎯 资源图片样式 */
  .resource-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 6px;
  }

  /* 🎯 文件夹显示样式 */
  .folder-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    height: 100%;
    width: 100%;
    background: var(--color-surface-secondary);
    border-radius: 6px;
  }

  .folder-icon {
    font-size: 2.5rem; /* 🎯 缩小文件夹图标：3rem -> 2.5rem */
    opacity: 0.8;
  }

  .folder-info {
    font-size: 11px; /* 🎯 缩小字体：12px -> 11px */
    color: var(--color-text-secondary);
    font-weight: 500;
  }

  .folder-count {
    background: var(--color-primary-light);
    color: var(--color-primary);
    padding: 2px 5px; /* 🎯 缩小内边距：2px 6px -> 2px 5px */
    border-radius: 3px; /* 🎯 缩小圆角：4px -> 3px */
    font-size: 10px; /* 🎯 缩小字体：11px -> 10px */
  }

  .folder-size {
    font-size: 10px; /* 🎯 缩小字体：11px -> 10px */
    color: var(--color-text-secondary);
    background: var(--color-surface-hover);
    padding: 2px 5px; /* 🎯 缩小内边距：2px 6px -> 2px 5px */
    border-radius: 3px; /* 🎯 缩小圆角：4px -> 3px */
  }

  /* 🎯 可点击头部样式 - 深色背景 */
  .image-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 10px; /* 🎯 缩小内边距：8px 12px -> 6px 10px */
    background: #1a202c; /* 使用深色背景 */
    border-top: 1px solid var(--theme-border);
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 32px; /* 🎯 缩小最小高度：36px -> 32px */
  }

  .image-header:hover {
    background: #2d3748; /* hover时使用稍浅的深色 */
    border-color: var(--theme-primary);
  }

  .image-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
    min-width: 0;
  }

  .image-name {
    font-size: 11px; /* 🎯 缩小字体：12px -> 11px */
    font-weight: 500;
    color: #ffffff; /* 白色文字，确保在深色背景上可见 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .image-size {
    font-size: 9px; /* 🎯 缩小字体：10px -> 9px */
    color: #cccccc; /* 浅灰色文字，确保在深色背景上可见 */
    font-family: monospace;
  }

  .header-icon {
    font-size: 14px; /* 🎯 缩小图标：16px -> 14px */
    opacity: 0.6;
    transition: all 0.2s ease;
    margin-left: 5px; /* 🎯 缩小边距：6px -> 5px */
    flex-shrink: 0;
  }

  .image-header:hover .header-icon {
    opacity: 1;
  }
</style>
