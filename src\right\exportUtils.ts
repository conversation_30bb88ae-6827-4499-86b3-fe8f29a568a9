/**
 * 导出工具函数
 * 参考 SplitPanelNew 封装统一的导出方法
 */

import { open } from '@tauri-apps/plugin-dialog';
import { exportManager } from '../exportDialog/exportUtils';
import { exportSettingsStore } from '../stores/exportSettingsStore';
import type { ImageResource, AtlasResource, CropArea } from '../types/imageType';
import type { ExportItem } from '../exportDialog/exportTypes';

/**
 * 导出单个图片资源（包含裁切区域）
 */
export async function exportImageResource(imageResource: ImageResource): Promise<boolean> {
  try {
    console.log('📤 开始导出图片资源:', imageResource.name);

    // 获取当前导出设置
    let currentSettings: any = null;
    const unsubscribe = exportSettingsStore.subscribe(settings => {
      currentSettings = settings;
    });
    unsubscribe();

    if (!currentSettings) {
      console.error('❌ 无法获取导出设置');
      return false;
    }

    // 选择导出文件夹
    const exportPath = await selectExportFolder();
    if (!exportPath) {
      console.log('🚫 用户取消了导出');
      return false;
    }

    // 准备导出项目
    const exportItems: ExportItem[] = [];

    // 检查是否有裁切数据
    if (imageResource.cropData?.areas && imageResource.cropData.areas.length > 0) {
      console.log('📋 发现裁切区域，导出裁切后的图片', imageResource.cropData.areas.length);

      // 创建单个导出项目，包含所有裁切区域
      exportItems.push({
        id: imageResource.id,
        name: imageResource.name,
        resource: {
          id: imageResource.id,
          name: imageResource.name,
          buffer: new Uint8Array(imageResource.data),
          path: imageResource.path || imageResource.name,
          width: imageResource.width || 0,
          height: imageResource.height || 0
        } as any,
        cropAreas: imageResource.cropData.areas
      });
    } else {
      console.log('📋 没有裁切区域，导出原图');

      // 导出原图
      exportItems.push({
        id: imageResource.id,
        name: imageResource.name,
        resource: {
          id: imageResource.id,
          name: imageResource.name,
          buffer: new Uint8Array(imageResource.data),
          path: imageResource.path || imageResource.name,
          width: imageResource.width || 0,
          height: imageResource.height || 0
        } as any,
        cropAreas: [] // 没有裁切区域
      });
    }

    // 🎯 确保导出器已注册
    const { registerExporters } = await import('../exportDialog/formats');
    registerExporters();

    // 执行导出
    const result = await exportManager.export(
      currentSettings.selectedType || 'cropped-images',
      exportItems,
      {
        type: currentSettings.selectedType || 'cropped-images',
        outputPath: exportPath,
        fileName: currentSettings.fileName || imageResource.name,
        format: currentSettings.format || 'png',
        quality: currentSettings.quality || 0.9,
        includeOriginal: currentSettings.includeOriginal || false,
        namePrefix: currentSettings.namePrefix || '',
        nameSuffix: currentSettings.nameSuffix || ''
      },
      exportPath,
      (progress) => {
        console.log('📊 导出进度', progress);
      }
    );

    if (result.success) {
      console.log('✅ 图片资源导出成功', result);
      return true;
    } else {
      console.error('❌ 图片资源导出失败', result);
      return false;
    }

  } catch (error) {
    console.error('❌ 导出图片资源时发生错误:', error);
    return false;
  }
}

/**
 * 导出图集资源
 */
export async function exportAtlasResource(atlasResource: AtlasResource): Promise<boolean> {
  try {
    console.log('📤 开始导出图集资源:', atlasResource.name);

    // 获取当前导出设置
    let currentSettings: any = null;
    const unsubscribe = exportSettingsStore.subscribe(settings => {
      currentSettings = settings;
    });
    unsubscribe();

    if (!currentSettings) {
      console.error('❌ 无法获取导出设置');
      return false;
    }

    // 选择导出文件夹
    const exportPath = await selectExportFolder();
    if (!exportPath) {
      console.log('🚫 用户取消了导出');
      return false;
    }

    console.log('📋 准备合并图集', {
      atlasName: atlasResource.name,
      childrenCount: atlasResource.children?.length || 0,
      layoutSettings: atlasResource.layoutSettings
    });

    // 检查图集中的图片
    if (!atlasResource.children || atlasResource.children.length === 0) {
      console.warn('⚠️ 图集中没有可导出的图片');
      return false;
    }

    // 🎯 过滤出有效的图片资源（有图片数据的）
    const validImages = atlasResource.children.filter(child => {
      const hasData = child.data ||
                     child.processedData?.preview?.dataUrl ||
                     child.processedData?.thumbnail?.dataUrl ||
                     child.processedData?.original?.dataUrl ||
                     child.blobUrl ||
                     child.dataBase64;

      if (!hasData) {
        console.warn('⚠️ 跳过没有图片数据的资源:', child.name);
      }

      return hasData;
    });

    if (validImages.length === 0) {
      console.warn('⚠️ 图集中没有有效的图片数据');
      return false;
    }

    // // 🎯 使用布局算法计算图片位置
    // const { calculateImageLayout } = await import('../center/imageLayoutCalculator');
    // const layoutSettings = atlasResource.layoutSettings || {
    //   padding: 10,
    //   spacing: 5,
    //   algorithm: 'maxrects',
    //   powerOfTwo: false,
    //   allowRotation: false,
    //   maxWidth: 1024,
    //   maxHeight: 1024
    // };

    // console.log('🎯 计算图集布局', { layoutSettings, imageCount: validImages.length });

    // const layoutResult = await calculateImageLayout(validImages, layoutSettings);

    // if (!layoutResult.success) {
    //   console.error('❌ 图集布局计算失败');
    //   return false;
    // }

    // console.log('✅ 图集布局计算成功', {
    //   boundingBox: layoutResult.boundingBox,
    //   imageCount: layoutResult.images.length
    // });

    // // 🎯 生成合并的图集图片
    // console.log('🎨 生成合并的图集图片');
    // const mergedImageBuffer = await generateMergedAtlasImage(layoutResult);

    // if (!mergedImageBuffer) {
    //   console.error('❌ 生成合并图集图片失败');
    //   return false;
    // }

    // // 🎯 将布局结果转换为 cropAreas 格式（用于生成坐标数据）
    // const cropAreas = layoutResult.images.map(imageLayout => ({
    //   id: imageLayout.resource.id,
    //   name: imageLayout.resource.name,
    //   x: imageLayout.x,
    //   y: imageLayout.y,
    //   width: imageLayout.width,
    //   height: imageLayout.height
    // }));

    // console.log('📋 生成的cropAreas', {
    //   count: cropAreas.length,
    //   areas: cropAreas.map(area => ({
    //     name: area.name,
    //     rect: `${area.x},${area.y},${area.width}×${area.height}`
    //   }))
    // });

    // // 🎯 创建图集导出项目
    // const exportItems: ExportItem[] = [{
    //   id: atlasResource.id,
    //   name: atlasResource.name,
    //   resource: {
    //     id: atlasResource.id,
    //     name: atlasResource.name,
    //     buffer: mergedImageBuffer, // 🎯 使用生成的合并图片
    //     path: atlasResource.path || atlasResource.name,
    //     width: layoutResult.boundingBox.width,
    //     height: layoutResult.boundingBox.height
    //   } as any,
    //   cropAreas: cropAreas // 🎯 用于生成坐标数据文件
    // }];

    // console.log('✅ 准备导出图集', {
    //   atlasName: atlasResource.name,
    //   mergedImageSize: `${layoutResult.boundingBox.width}×${layoutResult.boundingBox.height}`,
    //   childrenCount: cropAreas.length,
    //   bufferSize: `${(mergedImageBuffer.length / 1024).toFixed(2)}KB`
    // });

    // // 🎯 确保导出器已注册
    // const { registerExporters } = await import('../exportDialog/formats');
    // registerExporters();

    // // 执行导出
    // const result = await exportManager.export(
    //   currentSettings.selectedType || 'plist', // 🎯 图集默认导出为plist格式
    //   exportItems,
    //   {
    //     type: currentSettings.selectedType || 'plist',
    //     outputPath: exportPath,
    //     fileName: currentSettings.fileName || atlasResource.name,
    //     format: currentSettings.format || 'png',
    //     quality: currentSettings.quality || 0.9,
    //     includeOriginal: currentSettings.includeOriginal || false,
    //     namePrefix: currentSettings.namePrefix || '',
    //     nameSuffix: currentSettings.nameSuffix || ''
    //   },
    //   exportPath,
    //   (progress) => {
    //     console.log('📊 图集导出进度', progress);
    //   }
    // );

    // if (result.success) {
    //   console.log('✅ 图集资源导出成功', result);
    //   return true;
    // } else {
    //   console.error('❌ 图集资源导出失败', result);
    //   return false;
    // }

  } catch (error) {
    console.error('❌ 导出图集资源时发生错误:', error);
    return false;
  }
}

/**
 * 选择导出文件夹
 */
async function selectExportFolder(): Promise<string | null> {
  try {
    const result = await open({
      directory: true,
      multiple: false,
      title: '选择导出文件夹',
      defaultPath: await (async () => {
        try {
          const { documentDir } = await import('@tauri-apps/api/path');
          return await documentDir();
        } catch {
          return undefined;
        }
      })()
    });

    if (result && typeof result === 'string') {
      return result;
    } else {
      return null;
    }
  } catch (error) {
    console.error('文件夹选择失败:', error);
    return null;
  }
}

/**
 * 显示导出成功提示
 */
export function showExportSuccess(itemCount: number, exportPath: string) {
  console.log(`✅ 成功导出 ${itemCount} 个项目到: ${exportPath}`);
  // 这里可以添加 Toast 通知或其他 UI 反馈
}

/**
 * 显示导出失败提示
 */
export function showExportError(error: string) {
  console.error(`❌ 导出失败: ${error}`);
  // 这里可以添加 Toast 通知或其他 UI 反馈
}

/**
 * 生成合并的图集图片
 */
async function generateMergedAtlasImage(layoutResult: any): Promise<Uint8Array | null> {
  try {
    console.log('🎨 开始生成合并图集图片', {
      boundingBox: layoutResult.boundingBox,
      imageCount: layoutResult.images.length
    });

    // 创建Canvas
    const canvas = document.createElement('canvas');
    canvas.width = layoutResult.boundingBox.width;
    canvas.height = layoutResult.boundingBox.height;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      console.error('❌ 无法获取Canvas上下文');
      return null;
    }

    // 清空Canvas（透明背景）
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 加载并绘制每个图片
    const imagePromises = layoutResult.images.map(async (imageLayout: any) => {
      try {
        const imageResource = imageLayout.resource;

        // 🎯 从ImageResource加载图片
        // const { loadImageFromResource } = await import('../center/imageLoader');
        // const loadResult = await loadImageFromResource(imageResource);

        // if (!loadResult.success || !loadResult.image) {
        //   console.warn('⚠️ 加载图片失败:', imageResource.name);
        //   return false;
        // }

        // // 绘制到Canvas上
        // ctx.drawImage(
        //   loadResult.image,
        //   imageLayout.x,
        //   imageLayout.y,
        //   imageLayout.width,
        //   imageLayout.height
        // );

        // console.log('✅ 绘制图片成功:', {
        //   name: imageResource.name,
        //   position: `${imageLayout.x},${imageLayout.y}`,
        //   size: `${imageLayout.width}×${imageLayout.height}`
        // });

        return true;
      } catch (error) {
        console.error('❌ 绘制图片失败:', imageLayout.resource.name, error);
        return false;
      }
    });

    // 等待所有图片绘制完成
    const results = await Promise.all(imagePromises);
    const successCount = results.filter(Boolean).length;

    console.log('🎨 图片绘制完成', {
      成功: successCount,
      总数: layoutResult.images.length,
      Canvas尺寸: `${canvas.width}×${canvas.height}`
    });

    if (successCount === 0) {
      console.error('❌ 没有成功绘制任何图片');
      return null;
    }

    // 将Canvas转换为Blob
    const blob = await new Promise<Blob | null>((resolve) => {
      canvas.toBlob(resolve, 'image/png', 1.0);
    });

    if (!blob) {
      console.error('❌ Canvas转换为Blob失败');
      return null;
    }

    // 将Blob转换为ArrayBuffer
    const arrayBuffer = await blob.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);

    console.log('✅ 合并图集图片生成成功', {
      尺寸: `${canvas.width}×${canvas.height}`,
      文件大小: `${(uint8Array.length / 1024).toFixed(2)}KB`
    });

    return uint8Array;

  } catch (error) {
    console.error('❌ 生成合并图集图片时发生错误:', error);
    return null;
  }
}
