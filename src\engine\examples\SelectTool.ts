/**
 * GameSprite Engine - SelectTool 示例实现
 * 展示如何继承 Tool 创建具体的工具
 */

import { Tool } from '../Tool.js';
import { GameObject } from '../GameObject.js';
import type { ToolOption } from '../types.js';

export class SelectTool extends Tool {
  private selectedObjects: Set<GameObject> = new Set();
  private isDragging: boolean = false;
  private dragStartPos: { x: number; y: number } | null = null;
  private dragOffset: { x: number; y: number } | null = null;
  
  constructor() {
    super(
      'select',
      'Select Tool',
      '👆',
      'selection',
      'default',
      'V'
    );
  }
  
  // === 生命周期 ===
  protected onActivate(): void {
    console.log('Select tool activated');
    this.setupEventListeners();
  }
  
  protected onDeactivate(): void {
    console.log('Select tool deactivated');
    this.clearSelection();
    this.isDragging = false;
    this.dragStartPos = null;
    this.dragOffset = null;
  }
  
  // === 工具选项 ===
  getOptions(): ToolOption[] {
    return [
      {
        key: 'multiSelect',
        label: 'Multi Select',
        type: 'boolean',
        defaultValue: true
      },
      {
        key: 'snapToGrid',
        label: 'Snap to Grid',
        type: 'boolean',
        defaultValue: false
      },
      {
        key: 'gridSize',
        label: 'Grid Size',
        type: 'number',
        defaultValue: 10,
        min: 1,
        max: 100
      }
    ];
  }
  
  protected onOptionChanged(key: string, newValue: any, oldValue: any): void {
    console.log(`Select tool option changed: ${key} = ${newValue}`);
    
    if (key === 'snapToGrid' && newValue) {
      // 如果启用网格对齐，对当前选中的对象进行对齐
      this.snapSelectedObjectsToGrid();
    }
  }
  
  // === 事件处理 ===
  onMouseDown(event: MouseEvent, target?: GameObject): void {
    const multiSelect = this.getOption('multiSelect', true);
    
    if (target) {
      // 点击了对象
      if (multiSelect && event.ctrlKey) {
        // 多选模式
        this.toggleSelection(target);
      } else {
        // 单选模式
        if (!this.isSelected(target)) {
          this.clearSelection();
          this.addToSelection(target);
        }
      }
      
      // 开始拖拽
      this.startDrag(event, target);
    } else {
      // 点击了空白区域
      if (!multiSelect || !event.ctrlKey) {
        this.clearSelection();
      }
    }
  }
  
  onMouseMove(event: MouseEvent, target?: GameObject): void {
    if (this.isDragging && this.selectedObjects.size > 0) {
      this.updateDrag(event);
    }
  }
  
  onMouseUp(event: MouseEvent, target?: GameObject): void {
    if (this.isDragging) {
      this.endDrag(event);
    }
  }
  
  onKeyDown(event: KeyboardEvent): void {
    switch (event.key) {
      case 'Delete':
      case 'Backspace':
        this.deleteSelectedObjects();
        break;
        
      case 'a':
      case 'A':
        if (event.ctrlKey) {
          event.preventDefault();
          this.selectAll();
        }
        break;
        
      case 'Escape':
        this.clearSelection();
        break;
        
      case 'c':
      case 'C':
        if (event.ctrlKey) {
          event.preventDefault();
          this.copySelectedObjects();
        }
        break;
        
      case 'v':
      case 'V':
        if (event.ctrlKey) {
          event.preventDefault();
          this.pasteObjects();
        }
        break;
    }
  }
  
  // === 选择管理 ===
  addToSelection(object: GameObject): void {
    this.selectedObjects.add(object);
    this.highlightObject(object, true);
    this.emit('selectionChanged', Array.from(this.selectedObjects));
  }
  
  removeFromSelection(object: GameObject): void {
    this.selectedObjects.delete(object);
    this.highlightObject(object, false);
    this.emit('selectionChanged', Array.from(this.selectedObjects));
  }
  
  toggleSelection(object: GameObject): void {
    if (this.isSelected(object)) {
      this.removeFromSelection(object);
    } else {
      this.addToSelection(object);
    }
  }
  
  clearSelection(): void {
    for (const object of this.selectedObjects) {
      this.highlightObject(object, false);
    }
    this.selectedObjects.clear();
    this.emit('selectionChanged', []);
  }
  
  isSelected(object: GameObject): boolean {
    return this.selectedObjects.has(object);
  }
  
  getSelectedObjects(): GameObject[] {
    return Array.from(this.selectedObjects);
  }
  
  selectAll(): void {
    const scene = this.engine?.getScene();
    if (scene) {
      this.clearSelection();
      this.selectObjectsRecursively(scene);
    }
  }
  
  private selectObjectsRecursively(object: GameObject): void {
    for (const child of object.children) {
      this.addToSelection(child);
      this.selectObjectsRecursively(child);
    }
  }
  
  // === 拖拽功能 ===
  private startDrag(event: MouseEvent, target: GameObject): void {
    this.isDragging = true;
    this.dragStartPos = { x: event.clientX, y: event.clientY };
    this.dragOffset = {
      x: event.clientX - target.x,
      y: event.clientY - target.y
    };
    
    this.emit('dragStart', {
      objects: Array.from(this.selectedObjects),
      startPos: this.dragStartPos
    });
  }
  
  private updateDrag(event: MouseEvent): void {
    if (!this.dragStartPos || !this.dragOffset) return;
    
    const deltaX = event.clientX - this.dragStartPos.x;
    const deltaY = event.clientY - this.dragStartPos.y;
    
    for (const object of this.selectedObjects) {
      const newX = object.x + deltaX;
      const newY = object.y + deltaY;
      
      if (this.getOption('snapToGrid', false)) {
        const gridSize = this.getOption('gridSize', 10);
        object.setPosition(
          Math.round(newX / gridSize) * gridSize,
          Math.round(newY / gridSize) * gridSize
        );
      } else {
        object.setPosition(newX, newY);
      }
    }
    
    this.dragStartPos = { x: event.clientX, y: event.clientY };
  }
  
  private endDrag(event: MouseEvent): void {
    this.isDragging = false;
    
    this.emit('dragEnd', {
      objects: Array.from(this.selectedObjects)
    });
    
    this.dragStartPos = null;
    this.dragOffset = null;
  }
  
  // === 对象操作 ===
  private deleteSelectedObjects(): void {
    const objectsToDelete = Array.from(this.selectedObjects);
    this.clearSelection();
    
    for (const object of objectsToDelete) {
      object.destroy();
    }
    
    this.emit('objectsDeleted', objectsToDelete);
  }
  
  private copySelectedObjects(): void {
    const selectedData = Array.from(this.selectedObjects).map(obj => obj.serialize());
    
    // 存储到剪贴板（简化实现）
    this.setOption('clipboard', selectedData);
    
    this.emit('objectsCopied', Array.from(this.selectedObjects));
  }
  
  private pasteObjects(): void {
    const clipboardData = this.getOption('clipboard');
    if (!clipboardData || !Array.isArray(clipboardData)) return;
    
    this.clearSelection();
    
    // 这里需要根据实际的对象工厂来创建对象
    // 简化实现，实际项目中需要更完善的反序列化机制
    this.emit('objectsPasted', clipboardData);
  }
  
  // === 视觉反馈 ===
  private highlightObject(object: GameObject, highlight: boolean): void {
    // 简化实现：通过修改 tint 来高亮显示
    if (highlight) {
      object.tint = 0x00ff00; // 绿色高亮
    } else {
      object.tint = 0xffffff; // 恢复原色
    }
  }
  
  private snapSelectedObjectsToGrid(): void {
    const gridSize = this.getOption('gridSize', 10);
    
    for (const object of this.selectedObjects) {
      object.setPosition(
        Math.round(object.x / gridSize) * gridSize,
        Math.round(object.y / gridSize) * gridSize
      );
    }
  }
  
  // === 事件监听器设置 ===
  private setupEventListeners(): void {
    // 这里可以设置额外的事件监听器
    // 例如监听场景变化、对象创建/销毁等
  }
  
  // === 工具信息 ===
  getTooltip(): string {
    const count = this.selectedObjects.size;
    if (count === 0) {
      return 'Select Tool (V) - Click to select objects';
    } else if (count === 1) {
      return `Select Tool (V) - 1 object selected`;
    } else {
      return `Select Tool (V) - ${count} objects selected`;
    }
  }
  
  getHelpText(): string {
    return `Select Tool:
- Click to select objects
- Ctrl+Click for multi-select
- Drag to move selected objects
- Delete to remove selected objects
- Ctrl+A to select all
- Ctrl+C to copy, Ctrl+V to paste
- Escape to clear selection`;
  }
}
