/**
 * Blob URL管理器
 * 统一管理应用中的所有Blob URL，确保正确清理，避免内存泄漏
 */

interface BlobUrlEntry {
  url: string;
  blob: Blob;
  createdAt: number;
  lastAccessed: number;
  source: string; // 创建来源，用于调试
  size: number;
}

/**
 * Blob URL管理器类
 */
export class BlobUrlManager {
  private static instance: BlobUrlManager;
  private urlMap = new Map<string, BlobUrlEntry>();
  private readonly MAX_AGE = 30 * 60 * 1000; // 30分钟过期
  private readonly MAX_SIZE = 100 * 1024 * 1024; // 100MB限制
  private cleanupInterval: number | null = null;
  private totalSize = 0;

  static getInstance(): BlobUrlManager {
    if (!BlobUrlManager.instance) {
      BlobUrlManager.instance = new BlobUrlManager();
    }
    return BlobUrlManager.instance;
  }

  constructor() {
    this.startCleanupTimer();
    
    // 监听页面卸载，清理所有URL
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.cleanup();
      });
    }
  }

  /**
   * 🎯 创建Blob URL并注册管理
   */
  createObjectURL(blob: Blob, source = 'unknown'): string {
    const url = URL.createObjectURL(blob);
    const now = Date.now();

    const entry: BlobUrlEntry = {
      url,
      blob,
      createdAt: now,
      lastAccessed: now,
      source,
      size: blob.size
    };

    this.urlMap.set(url, entry);
    this.totalSize += blob.size;

    console.log('🔗 BlobUrlManager: 创建Blob URL', {
      url: url.substring(0, 50) + '...',
      source,
      size: `${(blob.size / 1024).toFixed(2)}KB`,
      totalUrls: this.urlMap.size,
      totalSize: `${(this.totalSize / 1024 / 1024).toFixed(2)}MB`
    });

    // 检查是否需要清理
    this.checkSizeLimits();

    return url;
  }

  /**
   * 🎯 撤销Blob URL并从管理中移除
   */
  revokeObjectURL(url: string): void {
    const entry = this.urlMap.get(url);
    
    if (entry) {
      try {
        URL.revokeObjectURL(url);
        this.urlMap.delete(url);
        this.totalSize -= entry.size;

        console.log('🗑️ BlobUrlManager: 撤销Blob URL', {
          url: url.substring(0, 50) + '...',
          source: entry.source,
          size: `${(entry.size / 1024).toFixed(2)}KB`,
          age: `${((Date.now() - entry.createdAt) / 1000).toFixed(1)}s`,
          remainingUrls: this.urlMap.size,
          remainingSize: `${(this.totalSize / 1024 / 1024).toFixed(2)}MB`
        });
      } catch (error) {
        console.warn('⚠️ BlobUrlManager: 撤销URL失败', error);
      }
    } else {
      // 即使不在管理中，也尝试撤销
      try {
        URL.revokeObjectURL(url);
        console.log('🗑️ BlobUrlManager: 撤销未管理的URL', url.substring(0, 50) + '...');
      } catch (error) {
        console.warn('⚠️ BlobUrlManager: 撤销未管理URL失败', error);
      }
    }
  }

  /**
   * 🎯 更新URL访问时间
   */
  accessUrl(url: string): void {
    const entry = this.urlMap.get(url);
    if (entry) {
      entry.lastAccessed = Date.now();
    }
  }

  /**
   * 🎯 检查大小限制并清理
   */
  private checkSizeLimits(): void {
    if (this.totalSize > this.MAX_SIZE) {
      console.warn('⚠️ BlobUrlManager: 超过大小限制，开始清理', {
        currentSize: `${(this.totalSize / 1024 / 1024).toFixed(2)}MB`,
        limit: `${(this.MAX_SIZE / 1024 / 1024).toFixed(2)}MB`
      });
      
      this.cleanupOldest(0.3); // 清理30%最旧的URL
    }
  }

  /**
   * 🎯 清理最旧的URL
   */
  private cleanupOldest(ratio: number): void {
    const entries = Array.from(this.urlMap.entries());
    entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
    
    const toRemoveCount = Math.floor(entries.length * ratio);
    const toRemove = entries.slice(0, toRemoveCount);
    
    let freedSize = 0;
    toRemove.forEach(([url, entry]) => {
      this.revokeObjectURL(url);
      freedSize += entry.size;
    });
    
    if (freedSize > 0) {
      console.log('🧹 BlobUrlManager: 清理完成', {
        removedCount: toRemoveCount,
        freedSize: `${(freedSize / 1024 / 1024).toFixed(2)}MB`,
        remainingUrls: this.urlMap.size
      });
    }
  }

  /**
   * 🎯 清理过期的URL
   */
  private cleanupExpired(): void {
    const now = Date.now();
    const expiredUrls: string[] = [];
    
    for (const [url, entry] of this.urlMap.entries()) {
      if (now - entry.lastAccessed > this.MAX_AGE) {
        expiredUrls.push(url);
      }
    }
    
    if (expiredUrls.length > 0) {
      console.log('🕒 BlobUrlManager: 清理过期URL', {
        expiredCount: expiredUrls.length,
        maxAge: `${this.MAX_AGE / 1000 / 60}分钟`
      });
      
      expiredUrls.forEach(url => this.revokeObjectURL(url));
    }
  }

  /**
   * 🎯 启动定时清理
   */
  private startCleanupTimer(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpired();
    }, 5 * 60 * 1000) as any; // 每5分钟清理一次
  }

  /**
   * 🎯 停止定时清理
   */
  stopCleanupTimer(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * 🎯 清理所有URL
   */
  cleanup(): void {
    console.log('🧹 BlobUrlManager: 清理所有Blob URL', {
      totalUrls: this.urlMap.size,
      totalSize: `${(this.totalSize / 1024 / 1024).toFixed(2)}MB`
    });
    
    for (const url of this.urlMap.keys()) {
      this.revokeObjectURL(url);
    }
    
    this.stopCleanupTimer();
  }

  /**
   * 🎯 按来源清理URL
   */
  cleanupBySource(source: string): void {
    const urlsToRemove: string[] = [];
    
    for (const [url, entry] of this.urlMap.entries()) {
      if (entry.source === source) {
        urlsToRemove.push(url);
      }
    }
    
    if (urlsToRemove.length > 0) {
      console.log('🧹 BlobUrlManager: 按来源清理URL', {
        source,
        count: urlsToRemove.length
      });
      
      urlsToRemove.forEach(url => this.revokeObjectURL(url));
    }
  }

  /**
   * 🎯 获取统计信息
   */
  getStats(): {
    totalUrls: number;
    totalSize: number;
    oldestAge: number;
    newestAge: number;
    sources: Record<string, number>;
  } {
    const now = Date.now();
    const sources: Record<string, number> = {};
    let oldestAge = 0;
    let newestAge = 0;
    
    for (const entry of this.urlMap.values()) {
      const age = now - entry.createdAt;
      if (oldestAge === 0 || age > oldestAge) oldestAge = age;
      if (newestAge === 0 || age < newestAge) newestAge = age;
      
      sources[entry.source] = (sources[entry.source] || 0) + 1;
    }
    
    return {
      totalUrls: this.urlMap.size,
      totalSize: this.totalSize,
      oldestAge,
      newestAge,
      sources
    };
  }

  /**
   * 🎯 打印统计报告
   */
  printReport(): void {
    const stats = this.getStats();
    
    console.log('📊 BlobUrlManager 统计报告', {
      总URL数: stats.totalUrls,
      总大小: `${(stats.totalSize / 1024 / 1024).toFixed(2)}MB`,
      最旧年龄: `${(stats.oldestAge / 1000 / 60).toFixed(1)}分钟`,
      最新年龄: `${(stats.newestAge / 1000).toFixed(1)}秒`,
      来源分布: stats.sources
    });
  }
}

// 导出单例实例
export const blobUrlManager = BlobUrlManager.getInstance();
