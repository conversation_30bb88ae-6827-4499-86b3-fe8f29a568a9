/**
 * 图片加载队列管理器
 * 用于控制图片加载的并发数量和优先级
 */

import type { ImageResource } from '../types';
import { imagePreviewCache } from './imagePreviewCache';
import { performanceMonitor } from './performanceMonitor';

export interface LoadQueueItem {
  resource: ImageResource;
  priority: number;
  callback?: (success: boolean, src?: string) => void;
}

export class ImageLoadQueue {
  private queue: LoadQueueItem[] = [];
  private loading = new Set<string>();
  private maxConcurrent: number;
  private isProcessing = false;

  constructor(maxConcurrent = 3) {
    this.maxConcurrent = maxConcurrent;
  }

  /**
   * 添加图片到加载队列
   * @param resource 图片资源
   * @param priority 优先级 (1=最高, 2=中等, 3=最低)
   * @param callback 加载完成回调
   */
  addToQueue(
    resource: ImageResource, 
    priority: number = 2, 
    callback?: (success: boolean, src?: string) => void
  ): void {
    // 检查是否已经在队列中或正在加载
    if (this.loading.has(resource.id) || 
        this.queue.some(item => item.resource.id === resource.id)) {
      return;
    }

    // 检查缓存
    const cachedPreview = imagePreviewCache.getPreview(resource);
    if (cachedPreview) {
      callback?.(true, cachedPreview);
      return;
    }

    // 添加到队列
    this.queue.push({ resource, priority, callback });
    
    // 按优先级排序（数字越小优先级越高）
    this.queue.sort((a, b) => a.priority - b.priority);

    console.log('📋 ImageLoadQueue: 添加到队列', {
      resourceName: resource.name,
      priority,
      queueLength: this.queue.length,
      loading: this.loading.size
    });

    // 开始处理队列
    this.processQueue();
  }

  /**
   * 处理加载队列
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) return;
    this.isProcessing = true;

    while (this.queue.length > 0 && this.loading.size < this.maxConcurrent) {
      const item = this.queue.shift();
      if (!item) break;

      // 再次检查是否已在加载中
      if (this.loading.has(item.resource.id)) {
        continue;
      }

      this.loading.add(item.resource.id);
      
      // 异步加载图片
      this.loadImage(item).finally(() => {
        this.loading.delete(item.resource.id);
        // 继续处理队列
        setTimeout(() => this.processQueue(), 10);
      });
    }

    this.isProcessing = false;
  }

  /**
   * 加载单个图片
   */
  private async loadImage(item: LoadQueueItem): Promise<void> {
    const { resource, callback } = item;

    try {
      performanceMonitor.startMeasure('queue-image-load', {
        resourceId: resource.id,
        resourceName: resource.name
      });

      console.log('🔄 ImageLoadQueue: 开始加载', resource.name);

      // 确保资源数据已加载
      if (!resource.data && !resource.isLoaded) {
        const { resourceActions } = await import('../stores/resourceStore');
        await resourceActions.loadResourceBuffer(resource.id);
        
        // 获取更新后的资源
        const updatedResource = await resourceActions.getResourceById(resource.id);
        if (updatedResource && updatedResource.type === 'image') {
          Object.assign(resource, updatedResource);
        }
      }

      if (!resource.data) {
        throw new Error('图片数据不可用');
      }

      // 生成预览并缓存
      const preview = imagePreviewCache.getPreview(resource);
      
      if (preview) {
        console.log('✅ ImageLoadQueue: 加载成功', resource.name);
        callback?.(true, preview);
      } else {
        throw new Error('无法生成图片预览');
      }

      performanceMonitor.endMeasure('queue-image-load');

    } catch (error) {
      console.error('❌ ImageLoadQueue: 加载失败', resource.name, error);
      callback?.(false);
      performanceMonitor.endMeasure('queue-image-load');
    }
  }

  /**
   * 清空队列
   */
  clearQueue(): void {
    this.queue = [];
    console.log('🧹 ImageLoadQueue: 清空队列');
  }

  /**
   * 获取队列状态
   */
  getStatus() {
    return {
      queueLength: this.queue.length,
      loading: this.loading.size,
      maxConcurrent: this.maxConcurrent
    };
  }

  /**
   * 设置最大并发数
   */
  setMaxConcurrent(max: number): void {
    this.maxConcurrent = max;
    this.processQueue(); // 重新处理队列
  }

  /**
   * 移除特定资源的加载任务
   */
  removeFromQueue(resourceId: string): void {
    this.queue = this.queue.filter(item => item.resource.id !== resourceId);
    this.loading.delete(resourceId);
  }
}

// 全局实例
export const imageLoadQueue = new ImageLoadQueue(3);

// 根据设备性能调整并发数
const adjustConcurrencyByPerformance = () => {
  const memory = (navigator as any).deviceMemory;
  const cores = navigator.hardwareConcurrency;
  
  let maxConcurrent = 3; // 默认值
  
  if (memory && cores) {
    if (memory >= 8 && cores >= 8) {
      maxConcurrent = 5; // 高性能设备
    } else if (memory >= 4 && cores >= 4) {
      maxConcurrent = 3; // 中等性能设备
    } else {
      maxConcurrent = 2; // 低性能设备
    }
  }
  
  imageLoadQueue.setMaxConcurrent(maxConcurrent);
  
  console.log('🎛️ ImageLoadQueue: 根据设备性能调整并发数', {
    memory: memory || 'unknown',
    cores: cores || 'unknown',
    maxConcurrent
  });
};

// 初始化时调整并发数
adjustConcurrencyByPerformance();
