{"app": {"title": "GameSprite Studio", "description": "Game Sprite Atlas Creation Tool"}, "menu": {"file": "File", "edit": "Edit", "view": "View", "settings": "Settings", "help": "Help", "language": "Language"}, "actions": {"open": "Open Project", "save": "Save Project", "saveAs": "Save As", "export": "Export", "exportSettings": "Export Settings", "import": "Import", "importFiles": "Import Files", "importFolder": "Import Folder", "delete": "Delete", "cancel": "Cancel", "confirm": "Confirm", "confirmDelete": "Confirm Delete", "close": "Close", "exit": "Exit", "create": "Create", "edit": "Edit", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "selectAll": "Select All", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "zoomReset": "Reset Zoom", "fitToWindow": "Fit to Window", "refresh": "Refresh", "reload": "Reload", "reset": "Reset to De<PERSON>ult"}, "dialog": {"confirmDelete": "Confirm Delete", "deleteWarning": "This action cannot be undone.", "exportResources": "Export Resources", "exportDialog": "Export Dialog", "selectFolder": "Select Folder", "selectFile": "Select File", "selectExportFolder": "Select Export Folder", "chooseLocation": "Choose Location", "fileDialog": "File Dialog", "folderDialog": "Folder Dialog", "about": "About", "settings": "Settings", "preferences": "Preferences"}, "status": {"loading": "Loading...", "exporting": "Exporting...", "importing": "Importing...", "processing": "Processing...", "saving": "Saving...", "preparing": "Preparing...", "completed": "Completed", "failed": "Failed", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "ready": "Ready", "idle": "Idle", "busy": "Busy", "loadingImages": "Loading Images"}, "panel": {"left": "Left Panel", "right": "Right Panel", "center": "Center Panel", "bottom": "Bottom Panel", "top": "Top Panel", "atlas": "Atlas Panel", "resource": "Resource Panel", "properties": "Properties Panel", "preview": "Preview Panel"}, "atlas": {"title": "Atlas", "create": "Create Atlas", "delete": "Delete Atlas", "merge": "<PERSON><PERSON>", "split": "Split Atlas", "settings": "Atlas Settings", "mergeSettings": "Atlas Merge Settings", "layout": "Layout", "canvas": "<PERSON><PERSON>", "size": "Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "background": "Background", "padding": "Padding", "spacing": "Spacing", "algorithm": "Algorithm", "powerOfTwo": "Power of Two", "allowRotation": "Allow Rotation", "maxWidth": "<PERSON>", "maxHeight": "Max Height", "efficiency": "Efficiency", "empty": "Empty Atlas", "noAtlas": "No Atlas", "selectAtlas": "Please select an atlas", "atlasName": "Atlas Name", "dragArea": "Atlas management panel drag area", "dragToCreate": "Drag images here to create atlas", "dropToCreate": "Drop images to create new atlas", "moreAtlases": "{count} more atlases", "itemDragArea": "Atlas item drag area", "dragToAdd": "Drag images here to add to atlas", "deleteConfirm": "Are you sure you want to delete atlas <strong>\"{name}\"</strong>?", "resourceExists": "Resource Already Exists", "resourceExistsMessage": "Image \"{name}\" already exists in this atlas", "duplicateNotAllowed": "Duplicate resources are not allowed"}, "resource": {"title": "Resource", "image": "Image", "folder": "Folder", "file": "File", "name": "Name", "path": "Path", "size": "Size", "type": "Type", "format": "Format", "dimensions": "Dimensions", "created": "Created", "modified": "Modified", "loaded": "Loaded", "notLoaded": "Not Loaded", "loading": "Loading", "error": "Error", "empty": "Empty Resource", "noResources": "No Resources", "selectResource": "Please select a resource", "resourceCount": "{count} resources in total", "search": "Search resources...", "filter": "Filter", "sort": "Sort", "deleteConfirm": "Are you sure you want to delete {type} <strong>\"{name}\"</strong>?"}, "export": {"title": "Export", "type": "Export Type", "format": "Format", "quality": "Quality", "path": "Export Path", "filename": "Filename", "prefix": "Prefix", "suffix": "Suffix", "includeOriginal": "Include Original", "croppedImages": "Cropped Images", "atlas": "Atlas", "folder": "Folder Export", "single": "Single Export", "all": "Export All", "selected": "Export Selected", "preview": "Preview", "options": "Options", "progress": "Progress", "stage": "Stage", "current": "Current", "total": "Total", "files": "Files", "success": "Export Success", "failed": "Export Failed", "cancelled": "Export Cancelled", "preparing": "Preparing", "processing": "Processing", "saving": "Saving", "completed": "Completed", "exportedCount": "Exported", "skippedCount": "Skipped", "errorCount": "Errors", "totalSize": "Total Size", "duration": "Duration", "statistics": "Export Statistics", "totalAtlases": "Total Atlases", "validAtlases": "<PERSON><PERSON>", "totalImages": "Total Images", "settings": "Export Settings", "estimatedTime": "Estimated Time", "startExport": "Start Export", "compressionLevel": "Compression Level", "optimizeSize": "Optimize File Size", "folders": "Folders", "folderList": "Folder List", "folderStructure": "Folder Structure", "noCropDataWarning": "images have no crop data and will be exported as full images.", "invalidAtlasWarning": "atlases have no valid images and will be skipped."}, "crop": {"title": "Crop", "area": "Crop Area", "areas": "Crop Areas", "grid": "Grid", "gridMode": "Grid Mode", "cellWidth": "Cell Width", "cellHeight": "Cell Height", "rows": "Rows", "cols": "Columns", "padding": "Padding", "spacing": "Spacing", "autoDetect": "Auto Detect", "autoTrim": "Auto Trim", "minWidth": "<PERSON>", "minHeight": "Min Height", "selected": "Selected", "add": "Add", "remove": "Remove", "clear": "Clear", "apply": "Apply", "reset": "Reset", "failed": "Failed", "noCropData": "Crop information is missing, please set crop areas first", "autoDetectHint": "Intelligently detect non-transparent areas in images, suitable for sprite sheets", "gridModeHint": "Cut images by fixed size, suitable for regular atlases", "selectModeHint": "Please select a crop mode: Grid Mode or Auto Detect"}, "layout": {"algorithm": {"maxrects": "Max Rects", "shelf": "<PERSON><PERSON>", "potpack": "Potpack", "guillotine": "Guillotine", "none": "No Layout"}, "settings": "Layout Settings", "algorithmLabel": "Layout Algorithm", "padding": "Padding", "spacing": "Spacing", "powerOfTwo": "Power of Two", "allowRotation": "Allow Rotation", "maxWidth": "<PERSON>", "maxHeight": "Max Height", "efficiency": "Efficiency"}, "project": {"title": "Project", "new": "New Project", "open": "Open Project", "save": "Save Project", "saveAs": "Save As", "recent": "Recent Projects", "path": "Project Path", "name": "Project Name", "description": "Project Description", "created": "Created", "modified": "Modified", "version": "Version", "author": "Author", "settings": "Project Settings", "properties": "Project Properties", "validate": "Validate Project", "invalid": "Invalid Project", "valid": "Valid Project", "loading": "Loading project...", "saving": "Saving project...", "loadSuccess": "Project loaded successfully", "loadFailed": "Failed to load project", "saveSuccess": "Project saved successfully", "saveFailed": "Failed to save project"}, "error": {"title": "Error", "unknown": "Unknown Error", "fileNotFound": "File Not Found", "folderNotFound": "Folder Not Found", "invalidFormat": "Invalid Format", "invalidPath": "Invalid Path", "accessDenied": "Access Denied", "networkError": "Network Error", "timeout": "Timeout", "cancelled": "Cancelled", "outOfMemory": "Out of Memory", "diskFull": "Disk Full", "readFailed": "Read Failed", "writeFailed": "Write Failed", "loadFailed": "Load Failed", "saveFailed": "Save Failed", "exportFailed": "Export Failed", "importFailed": "Import Failed", "processingFailed": "Processing Failed", "invalidData": "Invalid Data", "corruptedFile": "Corrupted File", "unsupportedFormat": "Unsupported Format"}, "ui": {"required": "Required", "optional": "Optional", "placeholder": "Please enter...", "noData": "No Data", "empty": "Empty", "loading": "Loading...", "retry": "Retry", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish", "skip": "<PERSON><PERSON>", "continue": "Continue", "pause": "Pause", "resume": "Resume", "stop": "Stop", "start": "Start", "enable": "Enable", "disable": "Disable", "show": "Show", "hide": "<PERSON>de", "expand": "Expand", "collapse": "Collapse", "maximize": "Maximize", "minimize": "Minimize", "restore": "Rest<PERSON>", "yes": "Yes", "no": "No", "warning": "Warning", "items": "items", "unknownType": "Unknown Type", "drag": "Drag", "clickToShow": "Click to show in right panel", "shortcut": "Shortcut", "images": "images", "zoom": "Zoom", "selectImageOrAtlas": "Select an image or atlas to preview", "operationTips": "Operation Tips", "mouseWheel": "Mouse Wheel", "zoomImage": "Zoom Image", "key": "Key", "zoomInOut": "Zoom In/Out", "spaceDrag": "Space + Drag", "moveImage": "Move Image", "button": "<PERSON><PERSON>", "oneClickReset": "One-click reset to default state", "importHint": "Click the button above to import image files or folders", "dragHint": "You can also drag files directly to this area", "seconds": "seconds", "virtualScrollOptimization": "Using virtual scroll optimization", "selectAtlasOrResource": "Please select an atlas or resource", "recommended": "Recommended", "optimizeGPU": "Optimize GPU performance", "improveSpaceUtilization": "Improve space utilization", "quickSettings": "Quick Settings"}, "exportTypes": {"cropped-images": {"name": "Export Cropped Images", "description": "Export all crop areas as individual image files"}, "plist": {"name": "Cocos Creator (plist)", "description": "Cocos Creator/cocos2d-x compatible plist format"}, "json-array": {"name": "JSON Array", "description": "Generic JSON array format for various game engines"}, "json-hash": {"name": "JSON Hash", "description": "Generic JSON object format for fast lookup"}, "unity": {"name": "Unity Texture2D", "description": "Unity game engine specific format"}, "phaser3": {"name": "Phaser 3", "description": "Phaser 3 HTML5 game framework format"}, "gamemaker": {"name": "GameMaker Studio", "description": "GameMaker Studio game engine format"}, "unreal-paper2d": {"name": "Unreal Paper2D", "description": "Unreal Engine Paper2D system format"}, "godot": {"name": "Godot SpriteSheet", "description": "Godot game engine sprite sheet format"}, "defold": {"name": "Defold", "description": "Defold game engine format"}, "libgdx": {"name": "LibGDX", "description": "LibGDX Java game framework format"}, "xml-generic": {"name": "XML Generic", "description": "Generic XML format for custom parsing"}, "pixi": {"name": "Pixi.js", "description": "Pixi.js HTML5 rendering engine format"}}}