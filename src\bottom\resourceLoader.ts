/**
 * 🎯 资源加载器 - 处理文件和文件夹的导入逻辑
 * 分离BottomPanel的复杂加载逻辑，使组件只关注UI更新
 */

import { resourceStore } from '../stores/resourceStore';
import { TauriAPI } from '../lib/tauriAPI';
import type { ImageResource, FolderResource } from '../types/imageType';

// 🎯 进度回调类型
export interface LoadProgress {
  completed: number;
  total: number;
  stage: string;
}

export type ProgressCallback = (progress: LoadProgress) => void;

// 🎯 生成唯一ID
function generateUniqueId(prefix: string = 'resource'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// 🎯 获取MIME类型
function getMimeType(filePath: string): string {
  const ext = filePath.split('.').pop()?.toLowerCase();
  const mimeTypes: Record<string, string> = {
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'webp': 'image/webp',
    'svg': 'image/svg+xml'
  };
  return mimeTypes[ext || ''] || 'image/png';
}

// 🎯 检查是否为图片文件
function isImageFile(fileName: string): boolean {
  const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'];
  const ext = fileName.split('.').pop()?.toLowerCase();
  return ext ? imageExtensions.includes(ext) : false;
}

/**
 * 🎯 文件导入器类
 */
export class ResourceLoader {

  /**
   * 导入单个或多个文件
   */
  static async importFiles(
    filePaths: string[],
    onProgress?: ProgressCallback,
    currentFolderPath?: string  // 🎯 改为：当前文件夹路径
  ): Promise<void> {
    console.log('📁 ResourceLoader: 开始导入文件', filePaths.length);

    try {
      // 第一步：读取文件buffer
      onProgress?.({ completed: 0, total: filePaths.length, stage: '读取文件...' });
      const imageBuffers = await this.readImageBuffers(filePaths, onProgress);

      console.log('📊 ResourceLoader: 文件读取结果', {
        totalFiles: filePaths.length,
        successfulReads: imageBuffers.length,
        files: filePaths
      });

      // 🎯 检查是否成功读取了任何文件
      if (imageBuffers.length === 0) {
        throw new Error(`无法读取任何文件。请检查文件路径是否正确，以及文件是否存在。尝试的文件: ${filePaths.join(', ')}`);
      }

      // 第二步：使用后端处理图片
      onProgress?.({ completed: 0, total: imageBuffers.length, stage: '处理图片...' });
      const processedImages = await this.processImagesWithBackend(imageBuffers, onProgress);

      // 🎯 检查是否成功处理了任何图片
      if (processedImages.length === 0) {
        throw new Error(`无法处理任何图片。读取了 ${imageBuffers.length} 个文件，但处理失败。`);
      }

      // 第三步：创建ImageResource并添加到store
      onProgress?.({ completed: 0, total: processedImages.length, stage: '创建资源...' });
      await this.createAndStoreImageResources(processedImages, onProgress, currentFolderPath);

      console.log('✅ ResourceLoader: 文件导入完成', processedImages.length);

    } catch (error) {
      console.error('❌ ResourceLoader: 导入文件失败', error);
      throw error;
    }
  }

  /**
   * 导入文件夹
   */
  static async importFolder(
    folderPath: string,
    onProgress?: ProgressCallback,
    currentFolderPath?: string  // 🎯 改为：当前文件夹路径
  ): Promise<void> {
    console.log('📁 ResourceLoader: 开始导入文件夹', folderPath);

    try {
      // 第一步：扫描文件夹，收集所有图片文件
      onProgress?.({ completed: 0, total: 0, stage: '扫描文件夹...' });
      const allImageFiles = await this.scanFolderForImages(folderPath);

      if (allImageFiles.length === 0) {
        console.warn('⚠️ ResourceLoader: 文件夹中没有找到图片文件');
        throw new Error('所选文件夹中没有找到支持的图片文件');
      }

      // 第二步：读取所有图片文件的buffer
      onProgress?.({ completed: 0, total: allImageFiles.length, stage: '读取图片文件...' });
      const imageBuffers = await this.readImageBuffersFromFiles(allImageFiles, onProgress);

      // 第三步：使用后端处理图片
      onProgress?.({ completed: 0, total: imageBuffers.length, stage: '处理图片...' });
      const processedImages = await this.processImagesWithBackend(imageBuffers, onProgress);

      // 第四步：创建文件夹结构和ImageResource
      onProgress?.({ completed: 0, total: processedImages.length, stage: '创建文件夹结构...' });
      await this.createFolderStructureAndResources(folderPath, processedImages, allImageFiles, onProgress, currentFolderPath);

      console.log('✅ ResourceLoader: 文件夹导入完成', {
        folderPath,
        totalProcessed: processedImages.length
      });

    } catch (error) {
      console.error('❌ ResourceLoader: 导入文件夹失败', error);
      throw error;
    }
  }

  /**
   * 读取图片buffer
   */
  private static async readImageBuffers(
    filePaths: string[],
    onProgress?: ProgressCallback
  ): Promise<any[]> {
    const imageBuffers = [];

    for (let i = 0; i < filePaths.length; i++) {
      const filePath = filePaths[i];
      const fileName = filePath.split(/[/\\]/).pop() || 'Unknown File';

      try {
        onProgress?.({
          completed: i,
          total: filePaths.length,
          stage: `读取文件: ${fileName}`
        });

        console.log(`📄 ResourceLoader: 尝试读取文件 ${i + 1}/${filePaths.length}: ${filePath}`);
        const response = await TauriAPI.Project.readProjectBinaryFile(filePath);

        if (response.success && response.data?.buffer) {
          console.log(`✅ ResourceLoader: 成功读取文件: ${fileName}, 大小: ${response.data.buffer.byteLength} bytes`);
          imageBuffers.push({
            id: generateUniqueId('img'),
            buffer: response.data.buffer,
            originalType: getMimeType(filePath),
            filePath,
            fileName
          });
        } else {
          console.error(`❌ ResourceLoader: 读取文件失败: ${fileName}`, {
            success: response.success,
            error: response.error,
            hasData: !!response.data,
            hasBuffer: !!response.data?.buffer
          });
        }

      } catch (error) {
        console.error(`❌ ResourceLoader: 读取文件异常: ${fileName}`, error);
      }
    }

    return imageBuffers;
  }

  /**
   * 扫描文件夹，收集所有图片文件（支持嵌套）
   */
  private static async scanFolderForImages(folderPath: string): Promise<Array<{
    filePath: string;
    fileName: string;
    relativePath: string;
    folderPath: string;
  }>> {
    const { readDir } = await import('@tauri-apps/plugin-fs');
    const allImageFiles: Array<{
      filePath: string;
      fileName: string;
      relativePath: string;
      folderPath: string;
    }> = [];

    // 🎯 递归扫描文件夹
    async function scanDirectory(currentPath: string, basePath: string): Promise<void> {
      try {
        const entries = await readDir(currentPath);

        for (const entry of entries) {
          // 🎯 修复：保持原始路径格式，在 Windows 上使用反斜杠
          const fullPath = currentPath.endsWith('\\') || currentPath.endsWith('/')
            ? `${currentPath}${entry.name}`
            : `${currentPath}\\${entry.name}`;  // 在 Windows 上使用反斜杠

          const relativePath = fullPath.replace(basePath, '').replace(/^[\\\/]/, '');

          if (entry.isDirectory) {
            // 递归扫描子文件夹
            await scanDirectory(fullPath, basePath);
          } else if (isImageFile(entry.name)) {
            // 添加图片文件
            allImageFiles.push({
              filePath: fullPath,
              fileName: entry.name,
              relativePath: relativePath,
              folderPath: currentPath
            });
          }
        }
      } catch (error) {
        console.error(`❌ 扫描文件夹失败: ${currentPath}`, error);
      }
    }

    await scanDirectory(folderPath, folderPath);

    // 🎯 分析文件夹层级深度
    const uniqueFolders = [...new Set(allImageFiles.map(f => f.folderPath))];
    const folderDepths = uniqueFolders.map(folder => ({
      path: folder,
      depth: folder.split(/[\\\/]/).length - folderPath.split(/[\\\/]/).length,
      relativePath: folder.replace(folderPath, '').replace(/^[\\\/]/, '')
    }));

    console.log('📊 ResourceLoader: 文件夹扫描结果', {
      basePath: folderPath,
      totalImages: allImageFiles.length,
      totalFolders: uniqueFolders.length,
      maxDepth: Math.max(...folderDepths.map(f => f.depth)),
      folderStructure: folderDepths.sort((a, b) => a.depth - b.depth)
    });

    return allImageFiles;
  }

  /**
   * 🎯 读取图片文件的buffer数据 - 使用后端 Rust 读取
   */
  private static async readImageBuffersFromFiles(
    imageFiles: Array<{
      filePath: string;
      fileName: string;
      relativePath: string;
      folderPath: string;
    }>,
    onProgress?: ProgressCallback
  ): Promise<Array<{
    id: string;
    buffer: ArrayBuffer;
    fileName: string;
    filePath: string;
    relativePath: string;
    folderPath: string;
    originalType: string;
  }>> {
    const imageBuffers: Array<{
      id: string;
      buffer: ArrayBuffer;
      fileName: string;
      filePath: string;
      relativePath: string;
      folderPath: string;
      originalType: string;
    }> = [];

    for (let i = 0; i < imageFiles.length; i++) {
      const imageFile = imageFiles[i];

      try {
        onProgress?.({
          completed: i,
          total: imageFiles.length,
          stage: `读取文件: ${imageFile.fileName}`
        });

        // 🎯 使用后端 Rust 读取文件，避免前端权限问题
        const response = await TauriAPI.Project.readProjectBinaryFile(imageFile.filePath);

        if (response.success && response.data?.buffer) {
          imageBuffers.push({
            id: generateUniqueId('file'),
            buffer: response.data.buffer,
            fileName: imageFile.fileName,
            filePath: imageFile.filePath,
            relativePath: imageFile.relativePath,
            folderPath: imageFile.folderPath,
            originalType: getMimeType(imageFile.fileName)
          });
        } else {
          console.error(`❌ 后端读取文件失败: ${imageFile.filePath}`, response.error);
        }

      } catch (error) {
        console.error(`❌ 读取文件失败: ${imageFile.filePath}`, error);
      }
    }

    return imageBuffers;
  }

  /**
   * 🎯 使用后端批量处理图片 - 替换Worker处理
   */
  private static async processImagesWithBackend(
    imageBuffers: any[],
    onProgress?: ProgressCallback
  ): Promise<any[]> {
    if (imageBuffers.length === 0) return [];

    console.log('🚀 ResourceLoader: 开始后端批量处理图片', imageBuffers.length);

    const imageInputs = imageBuffers.map(item => ({
      id: item.id,
      buffer: item.buffer,
      originalType: item.originalType,
      fileName: item.fileName
    }));

    try {
      const result = await TauriAPI.Project.batchProcessImages(imageInputs, {
        generateThumbnail: true,
        thumbnailSize: 128,
        generatePreview: true,
        previewSize: 256,
        quality: 0.8,
        format: 'webp'
      });

      if (!result.success || !result.data) {
        throw new Error(result.error || '后端处理失败');
      }

      const batchResponse = result.data;

      // 合并处理结果和原始数据
      const mergedResults = batchResponse.results?.map((processedResult: any) => {
        const originalBuffer = imageBuffers.find(item => item.id === processedResult.id);
        return {
          ...processedResult,
          ...originalBuffer,
          // 将后端返回的processedData映射到前端期望的格式
          original: processedResult.processedData?.original,
          thumbnail: processedResult.processedData?.thumbnail,
          preview: processedResult.processedData?.preview,
          processedAt: processedResult.processedData?.processedAt
        };
      }) || [];

      console.log('✅ ResourceLoader: 后端处理完成', {
        inputCount: imageBuffers.length,
        outputCount: mergedResults.length,
        processingTime: batchResponse.processingTimeMs
      });

      // 模拟进度更新（后端处理太快，可能看不到进度）
      onProgress?.({
        completed: mergedResults.length,
        total: imageBuffers.length,
        stage: '处理完成'
      });

      return mergedResults;

    } catch (error) {
      console.error('❌ ResourceLoader: 后端处理失败', error);
      return [];
    }
  }

  /**
   * 创建ImageResource并添加到store
   */
  private static async createAndStoreImageResources(
    processedImages: any[],
    onProgress?: ProgressCallback,
    currentFolderPath?: string  // 🎯 改为：当前文件夹路径
  ): Promise<void> {
    const imageResources: ImageResource[] = [];
    const now = new Date().toISOString();

    for (let i = 0; i < processedImages.length; i++) {
      const processedImage = processedImages[i];

      if (!processedImage.success) {
        console.error('❌ 跳过处理失败的图片', processedImage.id);
        continue;
      }

      onProgress?.({
        completed: i,
        total: processedImages.length,
        stage: `创建资源: ${processedImage.fileName}`
      });

      // 🎯 创建完整的ImageResource对象
      const imageResource: ImageResource = {
        id: processedImage.uniqueId || generateUniqueId('img'), // 🎯 使用Worker生成的uniqueId或生成新的
        name: processedImage.fileName,
        type: 'image',
        path: processedImage.filePath,
        createdAt: now,
        updatedAt: now,
        isLoaded: true,
        data: processedImage.buffer,
        width: processedImage.original?.width,
        height: processedImage.original?.height,
        originalFile: {
          name: processedImage.fileName,
          size: processedImage.buffer.byteLength,
          lastModified: Date.now(),
          type: processedImage.originalType
        },
        processedData: {
          original: processedImage.original,
          thumbnail: processedImage.thumbnail,
          preview: processedImage.preview,
          processedAt: now
        }
      };

      imageResources.push(imageResource);
    }

    // 🎯 批量添加到resourceStore
    if (imageResources.length > 0) {
      if (currentFolderPath) {
        // 🎯 添加到当前文件夹
        resourceStore.update(state => {
          // 🎯 递归查找目标文件夹并直接修改其children（不创建副本）
          const findAndUpdateFolder = (resources: any[], targetPath: string): { targetFolder: any, found: boolean } => {
            for (const resource of resources) {
              if (resource.type === 'folder' && resource.path === targetPath) {
                // 🎯 找到目标文件夹，直接修改其children数组
                resource.children = [...(resource.children || []), ...imageResources];
                console.log('🎯 ResourceLoader: 找到目标文件夹，直接添加图片', {
                  folderName: resource.name,
                  folderPath: resource.path,
                  imageCount: imageResources.length,
                  newChildrenCount: resource.children.length
                });
                return { targetFolder: resource, found: true };
              } else if (resource.type === 'folder' && resource.children) {
                // 递归查找嵌套文件夹
                const result = findAndUpdateFolder(resource.children, targetPath);
                if (result.found) {
                  return result;
                }
              }
            }
            return { targetFolder: null, found: false };
          };

          // 🎯 在rootResources中查找和更新
          const result = findAndUpdateFolder(state.rootResources, currentFolderPath);

          // 🎯 如果当前显示的文件夹就是目标文件夹，确保currentFolder指向同一个对象
          let updatedCurrentFolder = state.currentFolder;
          if (result.found && result.targetFolder && state.currentFolder && state.currentFolder.path === currentFolderPath) {
            // 🎯 直接使用rootResources中找到的文件夹对象，确保引用一致
            updatedCurrentFolder = result.targetFolder;
            console.log('🎯 ResourceLoader: 更新currentFolder为rootResources中的同一对象', {
              folderName: updatedCurrentFolder.name,
              childrenCount: updatedCurrentFolder.children?.length || 0,
              isSameObject: updatedCurrentFolder === result.targetFolder
            });
          }

          console.log('✅ ResourceLoader: ImageResource添加到当前文件夹', {
            currentFolderPath,
            imageCount: imageResources.length,
            foundInRoot: result.found,
            updatedCurrentFolder: !!updatedCurrentFolder
          });

          return {
            ...state,
            currentFolder: updatedCurrentFolder
          };
        });
      } else {
        // 🎯 添加到根级别
        resourceStore.update(state => ({
          ...state,
          rootResources: [...state.rootResources, ...imageResources]
        }));

        console.log('✅ ResourceLoader: ImageResource添加到根级别', imageResources.length);
      }
    }
  }

  /**
   * 🎯 计算相对文件夹路径 - 以 rootResources 为根
   */
  private static calculateRelativeFolderPath(
    absoluteFolderPath: string,
    baseFolderPath: string,
    currentFolderPath?: string
  ): string {
    // 🎯 移除基础路径，得到相对于导入根的路径
    let relativePath = absoluteFolderPath.replace(baseFolderPath, '').replace(/^[\\\/]/, '');

    // 🎯 统一使用正斜杠
    relativePath = relativePath.replace(/\\/g, '/');

    // 🎯 如果在文件夹内导入，需要加上当前文件夹路径前缀
    if (currentFolderPath && relativePath) {
      return `${currentFolderPath}/${relativePath}`;
    } else if (currentFolderPath && !relativePath) {
      // 根文件夹的情况
      return currentFolderPath;
    } else {
      // 根级别导入的情况
      return relativePath || '';
    }
  }

  /**
   * 🎯 计算图片相对路径 - 以 rootResources 为根
   */
  private static calculateRelativeImagePath(
    absoluteImagePath: string,
    baseFolderPath: string,
    currentFolderPath?: string
  ): string {
    // 🎯 移除基础路径，得到相对路径
    let relativePath = absoluteImagePath.replace(baseFolderPath, '').replace(/^[\\\/]/, '');

    // 🎯 统一使用正斜杠
    relativePath = relativePath.replace(/\\/g, '/');

    // 🎯 如果在文件夹内导入，需要加上当前文件夹路径前缀
    if (currentFolderPath) {
      return `${currentFolderPath}/${relativePath}`;
    } else {
      return relativePath;
    }
  }

  /**
   * 🎯 创建文件夹结构和ImageResource - 使用相对路径结构
   */
  private static async createFolderStructureAndResources(
    baseFolderPath: string,
    processedImages: any[],
    allImageFiles: Array<{
      filePath: string;
      fileName: string;
      relativePath: string;
      folderPath: string;
    }>,
    onProgress?: ProgressCallback,
    currentFolderPath?: string  // 🎯 当前文件夹的相对路径
  ): Promise<void> {
    const now = new Date().toISOString();

    // 🎯 创建根文件夹 - 使用相对路径
    const folderName = baseFolderPath.split(/[/\\]/).pop() || 'Unknown Folder';

    // 🎯 计算相对路径：以 rootResources 为根
    let rootFolderRelativePath: string;
    if (currentFolderPath) {
      // 在文件夹内导入：相对路径 = 当前文件夹路径 + 新文件夹名
      rootFolderRelativePath = `${currentFolderPath}/${folderName}`;
    } else {
      // 在根级别导入：相对路径 = 文件夹名
      rootFolderRelativePath = folderName;
    }

    const rootFolderResource: FolderResource = {
      id: generateUniqueId('folder'),
      name: folderName,
      type: 'folder',
      path: rootFolderRelativePath,  // 🎯 使用相对路径
      createdAt: now,
      updatedAt: now,
      isLoaded: true,
      children: []
    };

    console.log('📁 ResourceLoader: 创建根文件夹结构', {
      folderName,
      folderPath: baseFolderPath,
      folderId: rootFolderResource.id
    });

    // 🎯 按文件夹分组处理图片
    const imagesByFolder = new Map<string, Array<{
      processedImage: any;
      originalFile: any;
    }>>();

    for (let i = 0; i < processedImages.length; i++) {
      const processedImage = processedImages[i];
      const originalFile = allImageFiles.find(f => f.fileName === processedImage.fileName);

      if (!originalFile) {
        console.warn('⚠️ 找不到原始文件信息', processedImage.fileName);
        continue;
      }

      onProgress?.({
        completed: i,
        total: processedImages.length,
        stage: `组织文件结构: ${processedImage.fileName}`
      });

      // 🎯 计算相对文件夹路径
      const relativeFolderPath = this.calculateRelativeFolderPath(
        originalFile.folderPath,
        baseFolderPath,
        currentFolderPath
      );

      if (!imagesByFolder.has(relativeFolderPath)) {
        imagesByFolder.set(relativeFolderPath, []);
      }
      imagesByFolder.get(relativeFolderPath)!.push({
        processedImage,
        originalFile
      });
    }

    // 🎯 创建正确的嵌套文件夹结构 - 使用相对路径
    const folderMap = new Map<string, FolderResource>();
    const allFolderPaths = Array.from(imagesByFolder.keys());

    // 🎯 按路径深度排序（相对路径）
    allFolderPaths.sort((a, b) => {
      const depthA = a ? a.split('/').length : 0;
      const depthB = b ? b.split('/').length : 0;
      return depthA - depthB;
    });

    console.log('🏗️ ResourceLoader: 开始创建文件夹结构（相对路径）', {
      totalFolderPaths: allFolderPaths.length,
      rootFolderRelativePath: rootFolderRelativePath,
      folderPaths: allFolderPaths.map(path => ({
        relativePath: path,
        depth: path ? path.split('/').length : 0,
        isRoot: path === '' || path === rootFolderRelativePath
      }))
    });

    // 🎯 第一步：为每个相对路径创建FolderResource
    for (const relativeFolderPath of allFolderPaths) {
      const imagesInFolder = imagesByFolder.get(relativeFolderPath) || [];

      // 创建该文件夹的ImageResource数组
      const folderImages: ImageResource[] = [];

      for (const { processedImage, originalFile } of imagesInFolder) {
        // 🎯 计算图片的相对路径
        const imageRelativePath = this.calculateRelativeImagePath(
          originalFile.filePath,
          baseFolderPath,
          currentFolderPath
        );

        const imageResource: ImageResource = {
          id: processedImage.uniqueId || generateUniqueId('img'),
          name: processedImage.fileName,
          type: 'image',
          path: imageRelativePath,  // 🎯 使用相对路径
          createdAt: now,
          updatedAt: now,
          isLoaded: true,
          data: processedImage.buffer,
          width: processedImage.original?.width,
          height: processedImage.original?.height,
          originalFile: {
            name: processedImage.fileName,
            size: processedImage.buffer.byteLength,
            lastModified: Date.now(),
            type: processedImage.originalType,
            webkitRelativePath: originalFile.relativePath
          },
          processedData: {
            original: processedImage.original,
            thumbnail: processedImage.thumbnail,
            preview: processedImage.preview,
            processedAt: now
          }
        };

        folderImages.push(imageResource);
      }

      if (relativeFolderPath === '' || relativeFolderPath === rootFolderRelativePath) {
        // 🎯 根文件夹：直接设置图片
        rootFolderResource.children = [...folderImages];
        folderMap.set(relativeFolderPath, rootFolderResource);

        console.log('📁 ResourceLoader: 根文件夹添加图片', {
          relativeFolderPath,
          imageCount: folderImages.length
        });
      } else {
        // 🎯 子文件夹：创建新的FolderResource
        const subFolderName = relativeFolderPath.split('/').pop() || 'Unknown';
        const subFolderResource: FolderResource = {
          id: generateUniqueId('folder'),
          name: subFolderName,
          type: 'folder',
          path: relativeFolderPath,  // 🎯 使用相对路径
          createdAt: now,
          updatedAt: now,
          isLoaded: true,
          children: [...folderImages] // 先添加图片
        };

        folderMap.set(relativeFolderPath, subFolderResource);

        console.log('📁 ResourceLoader: 创建子文件夹', {
          subFolderName,
          relativeFolderPath,
          imageCount: folderImages.length,
          folderId: subFolderResource.id
        });
      }
    }

    // 🎯 第二步：建立正确的父子关系（基于相对路径）
    for (const relativeFolderPath of allFolderPaths) {
      // 跳过根文件夹
      if (relativeFolderPath === '' || relativeFolderPath === rootFolderRelativePath) continue;

      const currentFolder = folderMap.get(relativeFolderPath);
      if (!currentFolder) continue;

      // 🎯 找到父文件夹的相对路径
      const pathParts = relativeFolderPath.split('/');
      const parentRelativePath = pathParts.slice(0, -1).join('/');

      // 🎯 如果父路径是空或者是根路径，添加到根文件夹
      if (parentRelativePath === '' || parentRelativePath === rootFolderRelativePath) {
        rootFolderResource.children.push(currentFolder);
        console.log('📁 ResourceLoader: 子文件夹添加到根文件夹', {
          childName: currentFolder.name,
          childRelativePath: relativeFolderPath,
          parentRelativePath: rootFolderRelativePath
        });
      } else {
        // 🎯 找到父文件夹并添加到其children中
        const parentFolder = folderMap.get(parentRelativePath);
        if (parentFolder) {
          parentFolder.children.push(currentFolder);
          console.log('📁 ResourceLoader: 子文件夹添加到父文件夹', {
            childName: currentFolder.name,
            childRelativePath: relativeFolderPath,
            parentName: parentFolder.name,
            parentRelativePath: parentRelativePath
          });
        } else {
          // 🎯 如果找不到父文件夹，添加到根文件夹作为备选
          rootFolderResource.children.push(currentFolder);
          console.warn('⚠️ ResourceLoader: 找不到父文件夹，添加到根文件夹', {
            childName: currentFolder.name,
            childRelativePath: relativeFolderPath,
            expectedParentRelativePath: parentRelativePath
          });
        }
      }
    }

    // 🎯 将完整的文件夹结构添加到resourceStore
    if (currentFolderPath) {
      // 🎯 添加到当前文件夹
      resourceStore.update(state => {
        // 🎯 递归查找目标文件夹并直接修改其children（不创建副本）
        const findAndUpdateFolder = (resources: any[], targetPath: string): { targetFolder: any, found: boolean } => {
          for (const resource of resources) {
            if (resource.type === 'folder' && resource.path === targetPath) {
              // 🎯 找到目标文件夹，直接修改其children数组
              resource.children = [...(resource.children || []), rootFolderResource];
              console.log('🎯 ResourceLoader: 找到目标文件夹，直接添加新文件夹', {
                folderName: resource.name,
                folderPath: resource.path,
                newFolderName: rootFolderResource.name,
                newFolderId: rootFolderResource.id,
                newChildrenCount: resource.children.length
              });
              return { targetFolder: resource, found: true };
            } else if (resource.type === 'folder' && resource.children) {
              // 递归查找嵌套文件夹
              const result = findAndUpdateFolder(resource.children, targetPath);
              if (result.found) {
                return result;
              }
            }
          }
          return { targetFolder: null, found: false };
        };

        // 🎯 在rootResources中查找和更新
        const result = findAndUpdateFolder(state.rootResources, currentFolderPath);

        // 🎯 如果当前显示的文件夹就是目标文件夹，确保currentFolder指向同一个对象
        let updatedCurrentFolder = state.currentFolder;
        if (result.found && result.targetFolder && state.currentFolder && state.currentFolder.path === currentFolderPath) {
          // 🎯 直接使用rootResources中找到的文件夹对象，确保引用一致
          updatedCurrentFolder = result.targetFolder;
          console.log('🎯 ResourceLoader: 更新currentFolder为rootResources中的同一对象', {
            folderName: result.targetFolder.name,
            childrenCount: result.targetFolder.children?.length || 0,
            addedFolderName: rootFolderResource.name,
            isSameObject: updatedCurrentFolder === result.targetFolder
          });
        }

        console.log('✅ ResourceLoader: 文件夹添加到当前文件夹', {
          currentFolderPath,
          newFolderName: rootFolderResource.name,
          newFolderId: rootFolderResource.id,
          foundInRoot: result.found,
          updatedCurrentFolder: !!updatedCurrentFolder
        });

        return {
          ...state,
          currentFolder: updatedCurrentFolder
        };
      });
    } else {
      // 🎯 添加到根级别
      resourceStore.update(state => ({
        ...state,
        rootResources: [...state.rootResources, rootFolderResource]
      }));

      console.log('✅ ResourceLoader: 文件夹添加到根级别', rootFolderResource.name);
    }

    // 🎯 递归函数：显示文件夹结构
    function logFolderStructure(folder: FolderResource, indent: string = ''): any {
      const children = folder.children.map((child: any) => {
        if (child.type === 'folder') {
          return {
            name: child.name,
            type: child.type,
            id: child.id,
            children: logFolderStructure(child, indent + '  ')
          };
        } else {
          return {
            name: child.name,
            type: child.type,
            id: child.id
          };
        }
      });
      return children;
    }

    console.log('✅ ResourceLoader: 文件夹结构创建完成（相对路径）', {
      baseFolderPath,
      rootFolderRelativePath,
      rootFolderId: rootFolderResource.id,
      totalChildren: rootFolderResource.children.length,
      folderStructure: {
        name: rootFolderResource.name,
        path: rootFolderResource.path,
        id: rootFolderResource.id,
        children: logFolderStructure(rootFolderResource)
      }
    });
  }
}