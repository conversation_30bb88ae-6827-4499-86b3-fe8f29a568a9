/**
 * 文件夹批量导出工具函数
 * 负责递归遍历文件夹、收集资源、创建导出项目等功能
 */

import type { ResourceItem, ImageResource, FolderResource } from '../types';
import type { ExportItem } from '../exportDialog/exportTypes';
import { resourceActions } from '../stores/resourceStore';
import { TauriAPI } from '../lib/tauriAPI';
import { globalExportState, exportSettingsStore } from '../stores/exportSettingsStore';
import { exportManager } from '../exportDialog/exportUtils';
import { open } from '@tauri-apps/plugin-dialog';
// cropStore已删除，不再需要导入

/**
 * 扩展的导出项目接口，包含路径信息
 */
export interface ExportItemWithPath extends ExportItem {
  relativePath: string;  // 相对于基础文件夹的路径
  parentFolderPath: string;  // 父文件夹路径
}

/**
 * 根据splitSettings生成裁切区域
 * @param image 图片资源
 * @returns 生成的裁切区域数组
 */
function generateCropAreasFromSplitSettings(image: ImageResource): any[] {
  if (!image.splitSettings || !image.width || !image.height) {
    return [];
  }

  const { cellWidth, cellHeight, spacing = 0, margin = 0 } = image.splitSettings;
  const cropAreas: any[] = [];

  // 计算可以容纳的行列数
  const availableWidth = image.width - 2 * margin;
  const availableHeight = image.height - 2 * margin;

  const cols = Math.floor((availableWidth + spacing) / (cellWidth + spacing));
  const rows = Math.floor((availableHeight + spacing) / (cellHeight + spacing));

  console.log('🎯 生成网格裁切区域:', {
    imageSize: { width: image.width, height: image.height },
    cellSize: { width: cellWidth, height: cellHeight },
    spacing,
    margin,
    gridSize: { rows, cols },
    totalAreas: rows * cols
  });

  // 生成裁切区域
  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < cols; col++) {
      const x = margin + col * (cellWidth + spacing);
      const y = margin + row * (cellHeight + spacing);

      cropAreas.push({
        id: `grid_${row}_${col}`,
        name: `${image.name}_${row}_${col}`,
        x,
        y,
        width: cellWidth,
        height: cellHeight
      });
    }
  }

  return cropAreas;
}

/**
 * 检查图片是否有可导出的裁切数据
 * @param image 图片资源
 * @returns 是否有裁切数据和裁切区域数组
 */
function checkImageCropData(image: ImageResource): { hasCropData: boolean; cropAreas: any[] } {
  console.log('🔍 检查图片裁切数据:', {
    imageName: image.name,
    imageId: image.id,
    // 🎯 重点关注 cropData
    hasCropData: !!image.cropData,
    cropData: image.cropData,
    cropDataType: typeof image.cropData,
    cropDataKeys: image.cropData ? Object.keys(image.cropData) : null,
    cropDataAreas: image.cropData ? (image.cropData as any).areas : null,
    cropDataAreasLength: image.cropData && (image.cropData as any).areas ? (image.cropData as any).areas.length : 0,
    // 其他信息
    hasSplitInfo: !!image.splitInfo,
    hasChildren: !!(image.children && image.children.length > 0),
    hasSplitSettings: !!image.splitSettings,
    imageWidth: image.width,
    imageHeight: image.height,
    allKeys: Object.keys(image)
  });

  // 🎯 只检查真正的裁切数据，忽略 splitSettings



  // 1. 🎯 优先检查 cropData.areas（SpriteCutDialog保存的真正裁切数据）
  if (image.cropData && (image.cropData as any).areas && Array.isArray((image.cropData as any).areas) && (image.cropData as any).areas.length > 0) {
    console.log('✅ 发现cropData.areas裁切数据:', (image.cropData as any).areas);
    return {
      hasCropData: true,
      cropAreas: (image.cropData as any).areas
    };
  }

  // 2. 检查是否有cropData（兼容旧版本 - 直接是数组）
  if (image.cropData && Array.isArray(image.cropData) && image.cropData.length > 0) {
    console.log('✅ 发现cropData数组裁切数据:', image.cropData);
    return {
      hasCropData: true,
      cropAreas: image.cropData
    };
  }

  // 3. 检查是否有splitInfo（表示这是一个裁切出来的子图片）
  if (image.splitInfo && image.splitInfo.region) {
    console.log('✅ 发现splitInfo裁切数据:', image.splitInfo);
    return {
      hasCropData: true,
      cropAreas: [{
        id: image.id,
        name: image.name,
        x: image.splitInfo.region.x,
        y: image.splitInfo.region.y,
        width: image.splitInfo.region.width,
        height: image.splitInfo.region.height
      }]
    };
  }

  // 4. 检查是否有children（表示这个图片被拆分了）
  if (image.children && image.children.length > 0) {
    console.log('✅ 发现children裁切数据:', image.children);
    const cropAreas = image.children.map(child => ({
      id: child.id,
      name: child.name,
      x: child.splitInfo?.region?.x || 0,
      y: child.splitInfo?.region?.y || 0,
      width: child.splitInfo?.region?.width || child.width || 0,
      height: child.splitInfo?.region?.height || child.height || 0
    }));

    return {
      hasCropData: true,
      cropAreas
    };
  }

  // 🚫 移除对 splitSettings 的处理 - 这只是配置，不是数据

  console.log('❌ 未发现任何裁切数据');
  return {
    hasCropData: false,
    cropAreas: []
  };
}

/**
 * 🎯 优化的图片数据加载（内存管理优化）
 * @param imagePath 图片文件路径
 * @returns 图片数据buffer
 */
async function loadImageDataDirectly(imagePath: string): Promise<ArrayBuffer | null> {
  try {
    console.log('📂 FolderExportUtils: 直接加载图片数据', imagePath);

    // 🎯 添加内存监控
    const memoryBefore = performance.memory ? performance.memory.usedJSHeapSize : 0;

    const response = await TauriAPI.Project.readProjectBinaryFile(imagePath);

    if (response.success && response.data?.buffer) {
      const buffer = response.data.buffer instanceof ArrayBuffer ?
        response.data.buffer :
        null;

      if (buffer) {
        const memoryAfter = performance.memory ? performance.memory.usedJSHeapSize : 0;
        const memoryDelta = memoryAfter - memoryBefore;

        console.log('✅ FolderExportUtils: 图片数据加载成功', {
          imagePath,
          size: buffer.byteLength,
          memoryDelta: `${(memoryDelta / 1024 / 1024).toFixed(2)}MB`
        });

        // 🎯 大文件警告
        if (buffer.byteLength > 10 * 1024 * 1024) { // 10MB
          console.warn('⚠️ FolderExportUtils: 检测到大文件', {
            imagePath,
            size: `${(buffer.byteLength / 1024 / 1024).toFixed(2)}MB`
          });
        }

        return buffer;
      }
    }

    console.warn('⚠️ FolderExportUtils: 图片数据加载失败', {
      imagePath,
      success: response.success,
      hasBuffer: !!response.data?.buffer
    });

    return null;
  } catch (error) {
    console.error('❌ FolderExportUtils: 加载图片数据异常', {
      imagePath,
      error
    });
    return null;
  }
}

/**
 * 文件夹导出统计信息
 */
export interface FolderExportStats {
  totalImages: number;
  imagesWithCropData: number;
  imagesWithoutCropData: number;
  totalFolders: number;
  totalSize: number;
  maxDepth: number;
}

/**
 * 递归收集文件夹中的所有图片资源
 * @param folder 要遍历的文件夹资源
 * @param baseFolderPath 基础文件夹路径（用于计算相对路径）
 * @param currentDepth 当前遍历深度
 * @returns 收集到的所有图片资源数组
 */
export async function collectAllImagesFromFolder(
  folder: FolderResource,
  baseFolderPath?: string,
  currentDepth: number = 0
): Promise<ImageResource[]> {
  const images: ImageResource[] = [];

  // 设置基础路径
  if (!baseFolderPath) {
    baseFolderPath = folder.path || '';
  }

  console.log('🔍 FolderExportUtils: 开始收集文件夹中的图片', {
    folderName: folder.name,
    folderPath: folder.path,
    baseFolderPath,
    currentDepth,
    isLoaded: folder.isLoaded,
    childrenCount: folder.children?.length || 0
  });

  // 确保文件夹已加载
  if (!folder.isLoaded) {
    console.log('📂 FolderExportUtils: 文件夹未加载，开始加载...', folder.name);
    await resourceActions.loadFolderFromFileSystem(folder.path || '', folder.id);

    // 重新获取更新后的文件夹数据
    // 注意：这里需要从store中重新获取最新的文件夹数据
    console.log('✅ FolderExportUtils: 文件夹加载完成', folder.name);
  }

  // 递归遍历children
  for (const child of folder.children || []) {
    if (child.type === 'image') {
      const imageResource = child as ImageResource;

      // 确保图片数据已加载
      if (!imageResource.isLoaded || !imageResource.data) {
        console.log('🖼️ FolderExportUtils: 加载图片数据', imageResource.name);

        // 直接加载图片数据，不依赖store
        if (imageResource.path) {
          const buffer = await loadImageDataDirectly(imageResource.path);
          if (buffer) {
            // 直接更新图片资源的数据
            imageResource.data = buffer;
            imageResource.isLoaded = true;
          }
        }
      }

      images.push(imageResource);
      console.log('✅ FolderExportUtils: 收集图片', {
        imageName: imageResource.name,
        imagePath: imageResource.path,
        hasData: !!imageResource.data,
        size: imageResource.data?.byteLength || 0
      });

    } else if (child.type === 'folder') {
      console.log('📁 FolderExportUtils: 递归进入子文件夹', child.name);
      const subImages = await collectAllImagesFromFolder(
        child as FolderResource,
        baseFolderPath,
        currentDepth + 1
      );
      images.push(...subImages);
    }
  }

  console.log('📊 FolderExportUtils: 文件夹遍历完成', {
    folderName: folder.name,
    collectedImages: images.length,
    currentDepth
  });

  return images;
}

/**
 * 创建带路径信息的导出项目
 * @param images 图片资源数组
 * @param baseFolderPath 基础文件夹路径
 * @param baseFolderName 基础文件夹名称
 * @returns 带路径信息的导出项目数组
 */
export function createExportItemsWithPaths(
  images: ImageResource[],
  baseFolderPath: string,
  baseFolderName: string
): { exportItems: ExportItemWithPath[], stats: { total: number, withCropData: number, skipped: number } } {
  console.log('🔧 FolderExportUtils: 创建导出项目', {
    imageCount: images.length,
    baseFolderPath,
    baseFolderName
  });

  const exportItems: ExportItemWithPath[] = [];
  let withCropData = 0;
  let skipped = 0;

  for (const image of images) {
    // 🎯 检查图片是否有裁切数据
    const { hasCropData, cropAreas } = checkImageCropData(image);

    // 🎯 如果没有裁切数据，创建一个包含整个图片的裁切区域
    let finalCropAreas = cropAreas;
    if (!hasCropData) {
      console.log('📝 FolderExportUtils: 图片没有裁切数据，创建整图导出项', {
        imageName: image.name,
        imageWidth: image.width,
        imageHeight: image.height
      });

      // 创建一个覆盖整个图片的裁切区域
      finalCropAreas = [{
        id: `${image.id}_full`,
        name: image.name,
        x: 0,
        y: 0,
        width: image.width || 0,
        height: image.height || 0
      }];
    }

    // 🎯 使用收集时记录的文件夹路径信息
    const folderPath = (image as any).__folderPath || '';
    const relativePath = folderPath;
    const parentFolderPath = folderPath;

    // 🎯 参考SplitPanel的导出逻辑，正确创建resource对象
    const exportItem: ExportItemWithPath = {
      id: image.id,
      name: image.name,
      resource: {
        id: image.id,
        name: image.name,
        buffer: image.data ? new Uint8Array(image.data) : new Uint8Array(0), // 🎯 关键：设置buffer字段
        path: image.path || image.name,
        width: image.width || 0,
        height: image.height || 0
      } as any,
      relativePath: relativePath,
      parentFolderPath: parentFolderPath,
      cropAreas: finalCropAreas  // 使用处理后的裁切区域
    };

    exportItems.push(exportItem);
    withCropData++;

    console.log('📝 FolderExportUtils: 创建导出项目', {
      imageName: image.name,
      imagePath: image.path,
      relativePath: exportItem.relativePath,
      parentFolderPath: exportItem.parentFolderPath,
      cropAreasCount: exportItem.cropAreas?.length || 0,
      hasCropData,
      isFullImage: !hasCropData
    });
  }

  const stats = {
    total: images.length,
    withCropData,
    skipped
  };

  console.log('📊 FolderExportUtils: 导出项目创建完成', stats);

  return { exportItems, stats };
}

/**
 * 计算文件夹导出统计信息
 * @param folder 文件夹资源
 * @returns 统计信息
 */
export async function calculateFolderStats(folder: FolderResource): Promise<FolderExportStats> {
  const stats: FolderExportStats = {
    totalImages: 0,
    imagesWithCropData: 0,
    imagesWithoutCropData: 0,
    totalFolders: 0,
    totalSize: 0,
    maxDepth: 0
  };

  async function traverseFolder(currentFolder: FolderResource, depth: number = 0) {
    stats.maxDepth = Math.max(stats.maxDepth, depth);

    // 确保文件夹已加载
    if (!currentFolder.isLoaded) {
      await resourceActions.loadFolderFromFileSystem(currentFolder.path || '', currentFolder.id);
    }

    for (const child of currentFolder.children || []) {
      if (child.type === 'image') {
        stats.totalImages++;
        const imageResource = child as ImageResource;

        // 检查是否有裁切数据（与createExportItemsWithPaths中的逻辑保持一致）
        const { hasCropData } = checkImageCropData(imageResource);

        if (hasCropData) {
          stats.imagesWithCropData++;
        } else {
          stats.imagesWithoutCropData++;
        }

        if (imageResource.data) {
          stats.totalSize += imageResource.data.byteLength;
        }
      } else if (child.type === 'folder') {
        stats.totalFolders++;
        await traverseFolder(child as FolderResource, depth + 1);
      }
    }
  }

  await traverseFolder(folder);

  console.log('📊 FolderExportUtils: 文件夹统计完成', stats);
  return stats;
}

/**
 * 按目录分组导出项目
 * @param items 导出项目数组
 * @returns 按目录分组的Map
 */
export function groupItemsByFolder(items: ExportItemWithPath[]): Map<string, ExportItemWithPath[]> {
  const folderGroups = new Map<string, ExportItemWithPath[]>();

  for (const item of items) {
    const folderPath = item.relativePath || ''; // 空字符串表示根目录

    if (!folderGroups.has(folderPath)) {
      folderGroups.set(folderPath, []);
    }

    folderGroups.get(folderPath)!.push(item);
  }

  console.log('📁 FolderExportUtils: 按目录分组完成', {
    totalGroups: folderGroups.size,
    groups: Array.from(folderGroups.entries()).map(([path, items]) => ({
      path: path || '(根目录)',
      count: items.length
    }))
  });

  return folderGroups;
}

/**
 * 验证文件夹是否可以导出
 * @param folder 文件夹资源
 * @returns 验证结果和错误信息
 */
export async function validateFolderForExport(folder: FolderResource): Promise<{
  isValid: boolean;
  error?: string;
  warnings?: string[];
}> {
  const warnings: string[] = [];

  // 检查文件夹路径
  if (!folder.path) {
    return {
      isValid: false,
      error: '文件夹路径无效'
    };
  }

  // 检查是否有图片
  try {
    const images = await collectAllImagesFromFolder(folder);

    if (images.length === 0) {
      return {
        isValid: false,
        error: '文件夹中没有找到图片文件'
      };
    }

    // 检查图片数据
    let invalidImages = 0;
    for (const image of images) {
      if (!image.data || image.data.byteLength === 0) {
        invalidImages++;
      }
    }

    if (invalidImages > 0) {
      warnings.push(`发现 ${invalidImages} 个图片文件数据无效，将跳过导出`);
    }

    // 检查文件夹深度
    const stats = await calculateFolderStats(folder);
    if (stats.maxDepth > 10) {
      warnings.push(`文件夹嵌套层级较深 (${stats.maxDepth} 层)，可能影响导出性能`);
    }

    return {
      isValid: true,
      warnings: warnings.length > 0 ? warnings : undefined
    };

  } catch (error) {
    return {
      isValid: false,
      error: `验证文件夹时发生错误: ${error instanceof Error ? error.message : String(error)}`
    };
  }
}

/**
 * 🎯 rootResources 导出预览数据接口
 */
export interface RootResourcesExportPreview {
  totalFolders: number;
  totalImages: number;
  imagesWithCropData: number;
  imagesWithoutCropData: number;
  totalSize: number;
  maxDepth: number;
  folderStructure: string[];
  estimatedExportTime: number; // 预估导出时间（秒）
}

/**
 * 🎯 分析 rootResources 的导出预览信息
 * @param rootResources 根资源数组
 * @returns 导出预览数据
 */
export async function analyzeRootResourcesForExport(rootResources: ResourceItem[]): Promise<RootResourcesExportPreview> {
  console.log('🔍 开始分析 rootResources 导出预览', {
    rootResourcesCount: rootResources.length
  });

  const preview: RootResourcesExportPreview = {
    totalFolders: 0,
    totalImages: 0,
    imagesWithCropData: 0,
    imagesWithoutCropData: 0,
    totalSize: 0,
    maxDepth: 0,
    folderStructure: [],
    estimatedExportTime: 0
  };

  const folderPaths = new Set<string>();

  // 递归分析函数
  async function analyzeResource(resource: ResourceItem, depth: number = 0, currentPath: string = ''): Promise<void> {
    preview.maxDepth = Math.max(preview.maxDepth, depth);

    if (resource.type === 'folder') {
      const folder = resource as FolderResource;
      preview.totalFolders++;

      const folderPath = currentPath ? `${currentPath}/${folder.name}` : folder.name;
      folderPaths.add(folderPath);

      // 🎯 暂时跳过文件夹加载，避免API问题
      // if (!folder.isLoaded && folder.path) {
      //   try {
      //     await resourceActions.addFolder(folder.path);
      //   } catch (error) {
      //     console.warn('⚠️ 加载文件夹失败:', folder.path, error);
      //   }
      // }

      // 递归分析子资源
      for (const child of folder.children || []) {
        await analyzeResource(child, depth + 1, folderPath);
      }

    } else if (resource.type === 'image') {
      const image = resource as ImageResource;
      preview.totalImages++;

      // 检查是否有裁切数据
      const { hasCropData } = checkImageCropData(image);
      if (hasCropData) {
        preview.imagesWithCropData++;
      } else {
        preview.imagesWithoutCropData++;
      }

      // 计算文件大小
      if (image.data) {
        preview.totalSize += image.data.byteLength;
      }
    }
  }

  // 分析所有根资源
  for (const resource of rootResources) {
    await analyzeResource(resource);
  }

  // 生成文件夹结构列表
  preview.folderStructure = Array.from(folderPaths).sort();

  // 预估导出时间（基于图片数量，每张图片约0.5秒）
  preview.estimatedExportTime = Math.ceil(preview.imagesWithCropData * 0.5);

  console.log('📊 rootResources 分析完成', preview);
  return preview;
}

/**
 * 🎯 从 rootResources 收集所有图片（带路径信息）
 * @param rootResources 根资源数组
 * @returns 所有图片资源数组（包含文件夹路径信息）
 */
export async function collectAllImagesFromRootResources(rootResources: ResourceItem[]): Promise<ImageResource[]> {
  console.log('🔍 开始从 rootResources 收集图片', {
    rootResourcesCount: rootResources.length
  });

  const allImages: ImageResource[] = [];

  // 递归收集函数，带路径信息
  async function collectFromResource(resource: ResourceItem, currentPath: string = ''): Promise<void> {
    if (resource.type === 'folder') {
      const folder = resource as FolderResource;

      // 构建当前文件夹路径
      const folderPath = currentPath ? `${currentPath}/${folder.name}` : folder.name;

      // 递归收集子资源
      for (const child of folder.children || []) {
        await collectFromResource(child, folderPath);
      }

    } else if (resource.type === 'image') {
      const image = resource as ImageResource;

      // 🎯 为图片添加文件夹路径信息
      (image as any).__folderPath = currentPath;

      // 确保图片数据已加载
      if (!image.isLoaded || !image.data) {
        if (image.path) {
          const buffer = await loadImageDataDirectly(image.path);
          if (buffer) {
            image.data = buffer;
            image.isLoaded = true;
          }
        }
      }

      allImages.push(image);

      console.log('📝 收集图片:', {
        imageName: image.name,
        folderPath: currentPath,
        hasData: !!image.data
      });
    }
  }

  // 收集所有根资源
  for (const resource of rootResources) {
    await collectFromResource(resource);
  }

  console.log('📊 从 rootResources 收集图片完成', {
    totalImages: allImages.length,
    loadedImages: allImages.filter(img => img.data).length
  });

  return allImages;
}

/**
 * 🎯 导出 rootResources 到文件夹（保持结构）- 使用现有导出系统
 * @param rootResources 根资源数组
 * @returns 导出结果
 */
export async function exportRootResourcesToFolder(
  rootResources: ResourceItem[]
): Promise<{
  success: boolean;
  exportedCount: number;
  skippedCount: number;
  errors: string[];
}> {
  console.log('📤 开始导出 rootResources 到文件夹', {
    rootResourcesCount: rootResources.length
  });

  const result = {
    success: false,
    exportedCount: 0,
    skippedCount: 0,
    errors: [] as string[]
  };

  try {
    // 1. 获取当前导出设置
    let currentSettings: any = null;
    const unsubscribe = exportSettingsStore.subscribe(settings => {
      currentSettings = settings;
    });
    unsubscribe();

    if (!currentSettings) {
      throw new Error('无法获取导出设置');
    }

    // 2. 选择导出文件夹
    const selectedPath = await open({
      title: '选择导出文件夹',
      directory: true,
      multiple: false
    });

    if (!selectedPath || typeof selectedPath !== 'string') {
      throw new Error('用户取消了导出');
    }

    console.log('📁 选择的导出文件夹:', selectedPath);

    // 🎯 只有在选择完文件夹后才开始显示全局导出遮罩层
    globalExportState.update(state => ({
      ...state,
      isExporting: true,
      exportType: 'folder',
      currentFile: '',
      progress: { current: 0, total: 0, stage: 'preparing' },
      startTime: Date.now()
    }));

    // 3. 收集所有图片
    globalExportState.update(state => ({
      ...state,
      progress: { ...state.progress, stage: 'preparing' }
    }));

    const allImages = await collectAllImagesFromRootResources(rootResources);

    // 4. 创建导出项目（保持文件夹结构）
    const { exportItems } = createExportItemsWithPaths(allImages, '', 'rootResources');

    globalExportState.update(state => ({
      ...state,
      progress: { current: 0, total: exportItems.length, stage: 'processing' }
    }));

    // 5. 按文件夹分组
    const folderGroups = groupItemsByFolder(exportItems);

    // 6. 🎯 确保导出器已注册
    const { registerExporters } = await import('../exportDialog/formats');
    registerExporters();

    // 7. 逐个文件夹导出
    let exportedCount = 0;
    let currentIndex = 0;

    for (const [folderPath, items] of folderGroups) {
      const targetFolderPath = folderPath ? `${selectedPath}/${folderPath}` : selectedPath;

      console.log('📁 导出文件夹:', {
        folderPath,
        targetFolderPath,
        itemsCount: items.length
      });

      // 🎯 确保目标文件夹存在（包括根目录下的文件夹）
      try {
        const { mkdir } = await import('@tauri-apps/plugin-fs');
        await mkdir(targetFolderPath, { recursive: true });
        console.log('✅ 创建文件夹成功:', targetFolderPath);
      } catch (error) {
        console.warn('⚠️ 创建文件夹失败或已存在:', targetFolderPath, error);
      }

      // 使用现有的导出系统导出该文件夹的所有图片
      try {
        const exportResult = await exportManager.export(
          currentSettings.selectedType || 'cropped-images',
          items,
          {
            type: currentSettings.selectedType || 'cropped-images',
            outputPath: targetFolderPath,
            fileName: '',
            format: currentSettings.format || 'png',
            quality: currentSettings.quality || 0.9,
            includeOriginal: currentSettings.includeOriginal || false,
            namePrefix: currentSettings.namePrefix || '',
            nameSuffix: currentSettings.nameSuffix || ''
          },
          targetFolderPath,
          (progress) => {
            currentIndex = progress.current;
            globalExportState.update(state => ({
              ...state,
              currentFile: progress.currentFile,
              progress: { ...state.progress, current: currentIndex }
            }));
          }
        );

        if (exportResult.success) {
          exportedCount += exportResult.files.length;
          console.log('✅ 文件夹导出成功:', {
            folderPath,
            exportedFiles: exportResult.files.length,
            files: exportResult.files.map(f => f.name)
          });
        } else {
          result.errors.push(`文件夹 ${folderPath} 导出失败: ${exportResult.error || '未知错误'}`);
          result.skippedCount += items.length;
        }

      } catch (error) {
        console.error('❌ 文件夹导出失败:', folderPath, error);
        result.errors.push(`文件夹 ${folderPath} 导出失败: ${error instanceof Error ? error.message : String(error)}`);
        result.skippedCount += items.length;
      }
    }

    result.exportedCount = exportedCount;
    result.success = result.errors.length === 0 || exportedCount > 0;

    console.log('✅ rootResources 导出完成', result);

  } catch (error) {
    console.error('❌ rootResources 导出失败:', error);
    result.errors.push(`导出过程发生错误: ${error instanceof Error ? error.message : String(error)}`);
  } finally {
    // 清除全局导出状态
    globalExportState.update(state => ({
      ...state,
      isExporting: false,
      exportType: null,
      currentFile: '',
      progress: { current: 0, total: 0, stage: 'preparing' },
      startTime: null
    }));
  }

  return result;
}

/**
 * 🎯 导出单个图片及其裁切区域
 * @param exportItem 导出项目
 * @param targetFolderPath 目标文件夹路径
 */
async function exportImageWithCropAreas(exportItem: ExportItemWithPath, targetFolderPath: string): Promise<void> {
  const { resource, cropAreas, name } = exportItem;

  if (!resource.data) {
    throw new Error(`图片 ${name} 没有数据`);
  }

  // 创建目标文件夹（如果不存在）
  try {
    // 使用 Tauri 的文件系统 API 创建文件夹
    const { mkdir } = await import('@tauri-apps/plugin-fs');
    await mkdir(targetFolderPath, { recursive: true });
  } catch (error) {
    console.warn('创建文件夹可能失败或已存在:', targetFolderPath, error);
  }

  // 创建 Canvas 来处理图片裁切
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('无法创建 Canvas 上下文');
  }

  // 加载原图
  const blob = new Blob([resource.data], { type: 'image/png' });
  const imageUrl = URL.createObjectURL(blob);

  try {
    const img = new Image();
    await new Promise<void>((resolve, reject) => {
      img.onload = () => resolve();
      img.onerror = () => reject(new Error('图片加载失败'));
      img.src = imageUrl;
    });

    // 如果没有裁切区域，导出原图
    if (!cropAreas || cropAreas.length === 0) {
      await saveImageToFile(resource.data, `${targetFolderPath}/${name}`);
      return;
    }

    // 导出每个裁切区域
    for (let i = 0; i < cropAreas.length; i++) {
      const area = cropAreas[i];

      // 设置 Canvas 尺寸
      canvas.width = area.width;
      canvas.height = area.height;

      // 清除 Canvas
      ctx.clearRect(0, 0, area.width, area.height);

      // 绘制裁切区域
      ctx.drawImage(
        img,
        area.x, area.y, area.width, area.height,  // 源区域
        0, 0, area.width, area.height             // 目标区域
      );

      // 转换为 Blob
      const croppedBlob = await new Promise<Blob>((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Canvas 转换失败'));
          }
        }, 'image/png');
      });

      // 生成文件名
      const fileName = area.name || `${name.replace(/\.[^/.]+$/, '')}_${i}.png`;
      const filePath = `${targetFolderPath}/${fileName}`;

      // 保存文件
      const arrayBuffer = await croppedBlob.arrayBuffer();
      await saveImageToFile(arrayBuffer, filePath);

      console.log('✅ 裁切区域导出成功:', {
        fileName,
        area: `${area.x},${area.y},${area.width}x${area.height}`
      });
    }

  } finally {
    // 清理资源
    URL.revokeObjectURL(imageUrl);
  }
}

/**
 * 🎯 保存图片数据到文件
 * @param data 图片数据
 * @param filePath 文件路径
 */
async function saveImageToFile(data: ArrayBuffer, filePath: string): Promise<void> {
  try {
    // 使用 Tauri 的文件系统 API 保存文件
    const { writeFile } = await import('@tauri-apps/plugin-fs');
    const uint8Array = new Uint8Array(data);
    await writeFile(filePath, uint8Array);

    console.log('💾 文件保存成功:', filePath);
  } catch (error) {
    console.error('❌ 文件保存失败:', filePath, error);
    throw new Error(`保存文件失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}
