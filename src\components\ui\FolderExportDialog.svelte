<script lang="ts">
  /**
   * 文件夹导出确认对话框
   * 显示导出预览信息并确认导出设置
   */

  import type { FolderResource } from '../../types';
  import type { FolderExportStats } from '../../utils/folderExportUtils';
  import { formatFileSize } from '../../exportDialog/exportUtils';

  interface Props {
    visible: boolean;
    folder: FolderResource | null;
    stats: FolderExportStats | null;
    isLoading: boolean;
    onConfirm: () => void;
    onCancel: () => void;
  }

  let {
    visible,
    folder,
    stats,
    isLoading,
    onConfirm,
    onCancel
  }: Props = $props();

  // 格式化统计信息
  const formattedStats = $derived(() => {
    if (!stats) return null;

    return {
      totalImages: stats.totalImages,
      imagesWithCropData: stats.imagesWithCropData,
      imagesWithoutCropData: stats.imagesWithoutCropData,
      totalFolders: stats.totalFolders,
      totalSize: formatFileSize(stats.totalSize),
      maxDepth: stats.maxDepth
    };
  });
</script>

{#if visible}
  <!-- 遮罩层 -->
  <div class="dialog-overlay" onclick={onCancel}></div>

  <!-- 对话框 -->
  <div class="export-dialog">
    <div class="dialog-header">
      <h3>📤 导出文件夹</h3>
      <button class="close-btn" onclick={onCancel}>✕</button>
    </div>

    <div class="dialog-content">
      {#if folder}
        <div class="folder-info">
          <div class="folder-name">
            <span class="folder-icon">📁</span>
            <span class="name">{folder.name}</span>
          </div>
          <div class="folder-path">{folder.path}</div>
        </div>

        {#if isLoading}
          <div class="loading-section">
            <div class="loading-spinner"></div>
            <p>正在分析文件夹内容...</p>
          </div>
        {:else if formattedStats}
          <div class="stats-section">
            <h4>📊 导出预览</h4>
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-label">图片文件:</span>
                <span class="stat-value">{formattedStats().totalImages} 个</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">有裁切数据:</span>
                <span class="stat-value">{formattedStats().imagesWithCropData} 个</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">无裁切数据:</span>
                <span class="stat-value">{formattedStats().imagesWithoutCropData} 个</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">子文件夹:</span>
                <span class="stat-value">{formattedStats().totalFolders} 个</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">总大小:</span>
                <span class="stat-value">{formattedStats().totalSize}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">最大深度:</span>
                <span class="stat-value">{formattedStats().maxDepth} 层</span>
              </div>
            </div>
          </div>

          <div class="export-info">
            <div class="info-item">
              <span class="info-icon">⚙️</span>
              <span>将使用当前的全局导出设置</span>
            </div>
            <div class="info-item">
              <span class="info-icon">📁</span>
              <span>将保持原有的文件夹结构</span>
            </div>
            <div class="info-item">
              <span class="info-icon">🔄</span>
              <span>支持嵌套文件夹递归导出</span>
            </div>
          </div>

          {#if formattedStats() && formattedStats().imagesWithCropData === 0}
            <div class="warning-section">
              <div class="warning-item">
                <span class="warning-icon">⚠️</span>
                <span>文件夹中没有包含裁切数据的图片</span>
              </div>
              {#if formattedStats().imagesWithoutCropData > 0}
                <div class="warning-item">
                  <span class="warning-icon">ℹ️</span>
                  <span>发现 {formattedStats().imagesWithoutCropData} 个图片，但都没有裁切数据</span>
                </div>
              {/if}
            </div>
          {/if}
        {:else}
          <div class="error-section">
            <p>无法获取文件夹信息</p>
          </div>
        {/if}
      {/if}
    </div>

    <div class="dialog-footer">
      <button class="btn btn-secondary" onclick={onCancel}>
        取消
      </button>
      <button
        class="btn btn-primary"
        onclick={onConfirm}
        disabled={isLoading || !formattedStats() || (formattedStats() && formattedStats().imagesWithCropData === 0)}
      >
        {#if isLoading}
          分析中...
        {:else}
          确认导出
        {/if}
      </button>
    </div>
  </div>
{/if}

<style>
  .dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(2px);
  }

  .export-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    min-width: 480px;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--color-border);
    background: var(--color-surface-variant);
  }

  .dialog-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--color-on-surface);
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--color-on-surface-variant);
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s;
  }

  .close-btn:hover {
    background: var(--color-surface-variant);
  }

  .dialog-content {
    padding: 1.5rem;
    overflow-y: auto;
    flex: 1;
  }

  .folder-info {
    margin-bottom: 1.5rem;
  }

  .folder-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }

  .folder-icon {
    font-size: 1.2rem;
  }

  .folder-path {
    font-size: 0.9rem;
    color: var(--color-on-surface-variant);
    font-family: monospace;
    background: var(--color-surface-variant);
    padding: 0.5rem;
    border-radius: 4px;
    word-break: break-all;
  }

  .loading-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem;
    text-align: center;
  }

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--color-surface-variant);
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .stats-section {
    margin-bottom: 1.5rem;
  }

  .stats-section h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 500;
    color: var(--color-on-surface);
  }

  .stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--color-surface-variant);
    border-radius: 6px;
  }

  .stat-label {
    font-size: 0.9rem;
    color: var(--color-on-surface-variant);
  }

  .stat-value {
    font-weight: 500;
    color: var(--color-on-surface);
  }

  .export-info {
    margin-bottom: 1.5rem;
  }

  .info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    font-size: 0.9rem;
    color: var(--color-on-surface-variant);
  }

  .info-icon {
    font-size: 1rem;
  }

  .warning-section {
    margin-bottom: 1rem;
  }

  .warning-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 6px;
    color: #856404;
  }

  .warning-icon {
    font-size: 1.1rem;
  }

  .error-section {
    text-align: center;
    padding: 2rem;
    color: var(--color-error);
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--color-border);
    background: var(--color-surface-variant);
  }

  .btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }

  .btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .btn-secondary {
    background: var(--color-surface);
    color: var(--color-on-surface);
    border: 1px solid var(--color-border);
  }

  .btn-secondary:hover:not(:disabled) {
    background: var(--color-surface-variant);
  }

  .btn-primary {
    background: var(--color-primary);
    color: var(--color-on-primary);
  }

  .btn-primary:hover:not(:disabled) {
    background: var(--color-primary-variant);
  }
</style>
