/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Paper.js-drawing-tools.
 *
 * Paper.js-drawing-tools is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Paper.js-drawing-tools is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Paper.js-drawing-tools.  If not, see <https://www.gnu.org/licenses/>.
 */

import * as paper from "paper";
import $ from "jquery";

interface TextItemExtension {
  attachTextArea(paper: paper.PaperScope): void;
  edit(paper: paper.PaperScope): void;
  finishEditing(): void;
}

const editElem: JQuery<HTMLTextAreaElement> = $(
  '<textarea style="resize: none;">'
);
editElem.css("position", "absolute");
editElem.css("overflow", "hidden");
editElem.css("width", "100px");
editElem.css("height", "100px");
editElem.css("left", "0px");
editElem.css("top", "0px");
editElem.css("resize", "none");
editElem.css("line-height", "1.2");
editElem.css("background-color", "#ffffff");
editElem.css("box-sizing", "content-box");
editElem.css("-moz-box-sizing", "content-box");
editElem.css("-webkit-box-sizing", "content-box");
editElem.css("border", "none");

(paper as any).TextItem.inject({
  attachTextArea: function (paper: paper.PaperScope): void {
    // Just in case the textbox is still on screen somehow...
    if (editElem) {
      editElem.remove();
    }

    $(paper.view.element.offsetParent).append(editElem);
    editElem.focus();

    const clone = this.clone();
    clone.rotation = 0;
    clone.scaling = new paper.Point(1, 1);
    clone.remove();

    const extraPadding = 3; // Extra padding so edit item doesn't get cut off.

    const width = clone.bounds.width * paper.view.zoom + extraPadding;
    const height = clone.bounds.height * paper.view.zoom + extraPadding;
    editElem.css("width", width + "px");
    editElem.css("height", height + "px");

    const outlineWidth = 1;
    editElem.css("outline", outlineWidth * paper.view.zoom + "px dashed black");

    const position = paper.view.projectToView(
      clone.bounds.topLeft.x,
      clone.bounds.topLeft.y
    );
    position.x -= extraPadding / 2 + outlineWidth;
    position.y -= extraPadding / 2 + outlineWidth;
    const scale = this.scaling;
    const rotation = this.rotation;

    const fontSize = this.fontSize * paper.view.zoom;
    const fontFamily = this.fontFamily;
    const content = this.content;
    editElem.css("font-family", fontFamily);
    editElem.css("font-size", fontSize);
    editElem.val(content);

    let transformString = "";
    transformString += "translate(" + position.x + "px," + position.y + "px) ";
    transformString += "rotate(" + rotation + "deg) ";
    transformString += "scale(" + scale.x + "," + scale.y + ") ";
    editElem.css("transform", transformString);
  },
  edit: function (paper: paper.PaperScope): void {
    this.attachTextArea(paper);
    const self = this;
    editElem[0].oninput = function (): void {
      self.content = editElem[0].value;
      self.attachTextArea(paper);
    };
  },
  finishEditing: function (): void {
    editElem.remove();
  },
} as TextItemExtension);
