<script lang="ts">
  /**
   * DragPreview 组件 - 拖拽时跟随鼠标的预览组件
   */
  import { dragStore } from '../../stores/dragStore';

  // 导入类型
  import type { DragData, MousePosition } from '../../stores/dragStore';

  // 🚀 优化：监听拖拽状态，减少不必要的更新
  let isDragging = $state(false);
  let dragData = $state<DragData | null>(null);
  let mousePosition = $state<MousePosition | null>(null);

  // 🚀 优化：订阅store变化，只在值真正变化时更新
  dragStore.subscribe((state) => {
    // 🚀 只在拖拽状态变化时更新
    if (isDragging !== state.isDragging) {
      isDragging = state.isDragging;
    }

    // 🚀 只在拖拽数据变化时更新（通常只在拖拽开始时）
    if (dragData !== state.dragData) {
      dragData = state.dragData;
    }

    // 🚀 鼠标位置总是更新（但已经通过节流优化了频率）
    mousePosition = state.mousePosition;
  });

  // 调试信息 - 只在拖拽开始和结束时输出
  $effect(() => {
    if (isDragging && dragData) {
      console.log('🎯 DragPreview: 拖拽开始', {
        name: dragData.name,
        type: dragData.type,
        src: dragData.src ? 'has src' : 'no src',
        size: `${dragData.width}×${dragData.height}`,
        mousePosition: mousePosition,
        hasResource: !!dragData.resource,
        shouldShow: shouldShow // 🚀 添加显示状态调试
      });
    } else if (!isDragging) {
      console.log('🎯 DragPreview: 拖拽结束');
    }
  });

  // 🚀 优化：进一步减少调试日志的性能影响
  let lastLogTime = 0;
  $effect(() => {
    if (isDragging && mousePosition) {
      const now = Date.now();
      // 🚀 增加日志间隔到1秒，减少性能影响
      if (now - lastLogTime > 1000) {
        console.log('🖱️ DragPreview: 鼠标位置更新', {
          x: mousePosition.x,
          y: mousePosition.y,
          fps: `~${Math.round(1000 / 16)}fps` // 显示理论FPS
        });
        lastLogTime = now;
      }
    }
  });

  // 🚀 优化：计算完整的样式，确保正确应用
  const previewStyle = $derived(() => {
    if (!isDragging || !mousePosition) return 'display: none;';

    return `
      position: fixed;
      left: 0;
      top: 0;
      transform: translate3d(${mousePosition.x - 40}px, ${mousePosition.y - 40}px, 0);
      z-index: 10000;
      pointer-events: none;
      opacity: 0.9;
      will-change: transform;
    `;
  });

  // 🚀 预览显示状态
  const shouldShow = $derived(isDragging && dragData && mousePosition);
</script>

{#if shouldShow && dragData}
  <div
    class="drag-preview"
    style={previewStyle()}
  >
    <div class="preview-container">
      {#if dragData.type === 'image'}
        <!-- 直接使用img元素避免Image组件的状态问题 -->
        <img
          src={dragData.src}
          alt={dragData.alt || '拖拽预览'}
          class="preview-image"
          draggable="false"
        />
        <div class="preview-info">
          <span class="preview-name">{dragData.name}</span>
          {#if dragData.width && dragData.height}
            <span class="preview-size">{dragData.width}×{dragData.height}</span>
          {/if}
        </div>
      {/if}
    </div>
  </div>
{/if}

<style>
  .drag-preview {
    user-select: none;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    backface-visibility: hidden;
    /* 🚀 移除transform-style，避免与内联transform冲突 */
  }

  .preview-container {
    background: var(--theme-surface, #fff);
    border: 2px solid var(--theme-primary, #007bff);
    border-radius: 8px;
    padding: 8px;
    min-width: 80px;
    max-width: 120px;
  }

  .preview-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 4px;
    display: block;
  }

  .preview-info {
    margin-top: 4px;
    text-align: center;
  }

  .preview-name {
    font-size: 0.7rem;
    color: var(--theme-text, #333);
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
    font-weight: 500;
  }

  .preview-size {
    font-size: 0.6rem;
    color: var(--theme-text-secondary, #666);
    display: block;
    text-align: center;
    margin-top: 2px;
  }
</style>
