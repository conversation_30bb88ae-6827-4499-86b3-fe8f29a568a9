/**
 * 图片导出器 - 导出裁切的图片
 */

import { save, open } from '@tauri-apps/plugin-dialog';
import { invoke } from '@tauri-apps/api/core';
import { BaseExporter, createImageFromBuffer, canvasToBlob, getMimeType, sanitizeFileName, ensureExtension } from '../exportUtils';
import type { ExportItem, ExportConfig, ExportResult } from '../exportTypes';

export class ImageExporter extends BaseExporter {
  async export(items: ExportItem[], config: ExportConfig): Promise<ExportResult> {
    const startTime = Date.now();
    const exportedFiles: ExportResult['files'] = [];
    let totalSize = 0;

    this.reportProgress(0, 0, '', 'preparing');

    // 计算总任务数
    let totalTasks = 0;
    for (const item of items) {
      if (item.cropAreas && item.cropAreas.length > 0) {
        totalTasks += item.cropAreas.length;
      }
      if (config.includeOriginal) {
        totalTasks += 1;
      }
    }

    if (totalTasks === 0) {
      throw new Error('没有可导出的内容');
    }

    // 让用户选择导出文件夹
    let selectedFolder = config.outputPath;

    if (!selectedFolder) {
      const selectedPath = await open({
        title: '选择导出文件夹',
        directory: true,
        multiple: false,
        defaultPath: await (async () => {
          try {
            const { documentDir } = await import('@tauri-apps/api/path');
            return await documentDir();
          } catch {
            return undefined;
          }
        })()
      });

      if (!selectedPath || typeof selectedPath !== 'string') {
        throw new Error('用户取消了导出');
      }

      selectedFolder = selectedPath;
      console.log('📁 ImageExporter: 选择的导出文件夹', { selectedFolder });
    }

    let currentTask = 0;
    const filesToExport: Array<{ name: string; data: Uint8Array; size: number }> = [];
    const usedFileNames = new Set<string>(); // 用于跟踪已使用的文件名

    // 处理每个导出项，收集所有文件数据
    for (const item of items) {
      // 🎯 修复：检查data字段而不是buffer字段
      const imageData = item.resource.buffer || item.resource.data;
      if (!imageData) {
        console.warn(`跳过没有图片数据的资源: ${item.name}`, {
          hasBuffer: !!item.resource.buffer,
          hasData: !!item.resource.data,
          resourceKeys: Object.keys(item.resource)
        });
        continue;
      }

      // 创建原始图片
      const mimeType = getMimeType(item.resource.path);
      const originalImg = await createImageFromBuffer(imageData, mimeType);

      // 导出原始图片（如果需要）
      if (config.includeOriginal) {
        currentTask++;
        this.reportProgress(currentTask, totalTasks, `${item.name} (原图)`, 'processing');

        let originalFileName = this.generateFileName(item.name, '', config);

        // 确保原始图片文件名唯一
        let uniqueOriginalFileName = originalFileName;
        let counter = 1;
        while (usedFileNames.has(uniqueOriginalFileName)) {
          const nameWithoutExt = originalFileName.replace(/\.[^/.]+$/, '');
          const extension = originalFileName.match(/\.[^/.]+$/)?.[0] || '.png';
          uniqueOriginalFileName = `${nameWithoutExt}_${counter}${extension}`;
          counter++;
        }
        usedFileNames.add(uniqueOriginalFileName);

        const originalBlob = await this.createImageBlob(originalImg, originalImg.width, originalImg.height, 0, 0, config);

        const arrayBuffer = await originalBlob.arrayBuffer();
        const uint8Array = new Uint8Array(arrayBuffer);

        filesToExport.push({
          name: uniqueOriginalFileName,
          data: uint8Array,
          size: originalBlob.size
        });
        totalSize += originalBlob.size;
      }

      // 导出裁切图片
      if (item.cropAreas && item.cropAreas.length > 0) {
        for (let cropIndex = 0; cropIndex < item.cropAreas.length; cropIndex++) {
          const cropArea = item.cropAreas[cropIndex];
          currentTask++;

          // 生成唯一的文件名，确保不会重复
          let cropFileName = cropArea.name;
          if (!cropFileName || cropFileName.trim() === '') {
            // 如果没有名称，使用索引生成唯一名称
            cropFileName = `crop_${cropIndex}`;
          } else {
            // 清理文件名，移除扩展名
            cropFileName = cropFileName.replace(/\.[^/.]+$/, '');
          }

          this.reportProgress(currentTask, totalTasks, cropFileName, 'processing');

          // 创建裁切图片
          const croppedBlob = await this.createImageBlob(
            originalImg,
            cropArea.width,
            cropArea.height,
            cropArea.x,
            cropArea.y,
            config
          );

          // 生成唯一的文件名
          let fileName = this.generateFileName(item.name, cropFileName, config);

          // 确保文件名唯一，如果重复则添加数字后缀
          let uniqueFileName = fileName;
          let counter = 1;
          while (usedFileNames.has(uniqueFileName)) {
            const nameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
            const extension = fileName.match(/\.[^/.]+$/)?.[0] || '.png';
            uniqueFileName = `${nameWithoutExt}_${counter}${extension}`;
            counter++;
          }
          usedFileNames.add(uniqueFileName);

          const arrayBuffer = await croppedBlob.arrayBuffer();
          const uint8Array = new Uint8Array(arrayBuffer);

          filesToExport.push({
            name: uniqueFileName,
            data: uint8Array,
            size: croppedBlob.size
          });
          totalSize += croppedBlob.size;
        }
      }
    }

    // 批量导出所有文件到Rust后端
    this.reportProgress(totalTasks, totalTasks, '保存文件...', 'saving');

    try {
      const exportData = filesToExport.map(file => ({
        name: file.name,
        data: Array.from(file.data) // 转换为普通数组，因为Tauri不支持Uint8Array
      }));

      console.log('🚀 ImageExporter: 调用Rust后端批量导出', {
        targetDirectory: selectedFolder,
        fileCount: exportData.length
      });

      const exportedPaths = await invoke<string[]>('batch_export_files', {
        targetDirectory: selectedFolder,
        files: exportData
      });

      console.log('✅ ImageExporter: Rust后端导出成功', { exportedPaths });

      // 构建导出结果
      for (let i = 0; i < filesToExport.length; i++) {
        const file = filesToExport[i];
        const path = exportedPaths[i] || `${selectedFolder}/${file.name}`;

        exportedFiles.push({
          name: file.name,
          path: path,
          size: file.size,
          type: 'image'
        });
      }

    } catch (error) {
      console.error('❌ ImageExporter: Rust后端导出失败', error);
      throw new Error(`批量导出失败: ${error}`);
    }

    this.reportProgress(totalTasks, totalTasks, '', 'completed');

    return {
      success: true,
      files: exportedFiles,
      totalSize,
      duration: Date.now() - startTime
    };
  }

  private async createImageBlob(
    sourceImg: HTMLImageElement,
    width: number,
    height: number,
    sourceX: number = 0,
    sourceY: number = 0,
    config: ExportConfig
  ): Promise<Blob> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('无法创建Canvas上下文');
    }

    canvas.width = width;
    canvas.height = height;

    // 绘制图片
    ctx.drawImage(
      sourceImg,
      sourceX, sourceY, width, height,  // 源区域
      0, 0, width, height               // 目标区域
    );

    // 转换为Blob
    const format = this.getImageFormat(config.format || 'png');
    const quality = config.quality || 0.9;

    return await canvasToBlob(canvas, format, quality);
  }

  private generateFileName(baseName: string, cropName: string, config: ExportConfig): string {
    const extension = config.format || 'png';

    let fileName: string;

    // 🎯 如果用户设置了文件名，完整使用设置的文件名，不添加前缀后缀
    if (config.fileName && config.fileName.trim() !== '') {
      const userFileName = config.fileName.replace(/\.[^/.]+$/, ''); // 移除用户文件名中的扩展名
      if (cropName) {
        fileName = `${userFileName}_${cropName}`;
      } else {
        fileName = userFileName;
      }
    } else {
      // 🎯 如果没有设置文件名，使用原始逻辑（前缀+原名+后缀）
      const prefix = config.namePrefix || '';
      const suffix = config.nameSuffix || '';
      const nameWithoutExt = baseName.replace(/\.[^/.]+$/, '');

      if (cropName) {
        fileName = `${prefix}${nameWithoutExt}_${cropName}${suffix}`;
      } else {
        fileName = `${prefix}${nameWithoutExt}${suffix}`;
      }
    }

    fileName = sanitizeFileName(fileName);
    return ensureExtension(fileName, extension);
  }

  private getImageFormat(format: string): string {
    switch (format.toLowerCase()) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'webp':
        return 'image/webp';
      case 'png':
      default:
        return 'image/png';
    }
  }

  private async saveFileWithDialog(defaultFileName: string, blob: Blob, config: ExportConfig): Promise<string | null> {
    try {
      console.log('🔧 ImageExporter: 显示保存对话框', { defaultFileName, size: blob.size });

      const selectedPath = await save({
        title: `保存 ${defaultFileName}`,
        defaultPath: defaultFileName,
        filters: [{
          name: '图片文件',
          extensions: [config.format || 'png']
        }]
      });

      if (!selectedPath) {
        return null; // 用户取消
      }

      console.log('🔧 ImageExporter: 开始保存文件', { path: selectedPath, size: blob.size });

      const arrayBuffer = await blob.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);

      await writeFile(selectedPath, uint8Array);

      console.log('✅ ImageExporter: 文件保存成功', { path: selectedPath });
      return selectedPath;
    } catch (error) {
      console.error('❌ ImageExporter: 保存文件失败', { defaultFileName, error });
      throw new Error(`保存文件失败: ${defaultFileName} - ${error}`);
    }
  }


}
