/**
 * 导出设置Store - 管理全局导出设置
 */

import { writable } from 'svelte/store';
import type { ExportUIState } from '../exportDialog/exportTypes';
import { EXPORT_TYPES, type ExportTypeKey } from '../exportDialog/formats';

// 导出设置接口 - 扩展原有的 ExportUIState
export interface ExportSettings extends ExportUIState {
  // 添加额外的全局设置
  targetEngine: ExportTypeKey;
  includeMetadata: boolean;
  optimizeSize: boolean;
  generatePreview: boolean;
}

// 🎯 全局布局设置接口
export interface LayoutSettings {
  padding: number;           // 图片与包围盒的间距
  spacing: number;           // 图片与图片的间距
  algorithm: 'maxrects' | 'shelf' | 'potpack' | 'guillotine' | 'none';
  powerOfTwo: boolean;       // 尺寸为2的幂
  allowRotation: boolean;    // 允许旋转图片
  maxWidth: number;
  maxHeight: number;
}

// 🎯 全局裁切设置接口
export interface GlobalCropSettings {
  cropPadding: number;       // 外边距
}

// 🎯 全局导出状态接口
export interface GlobalExportState {
  isExporting: boolean;
  exportType: 'folder' | 'single' | 'atlas' | null;
  currentFile: string;
  progress: {
    current: number;
    total: number;
    stage: 'preparing' | 'processing' | 'saving' | 'completed';
  };
  startTime: number | null;
}

// 默认设置
const defaultSettings: ExportSettings = {
  selectedType: 'cropped-images',
  outputPath: '',
  fileName: '',
  format: 'png',
  quality: 0.9,
  includeOriginal: false,
  namePrefix: '',
  nameSuffix: '',
  // 新增的全局设置
  targetEngine: 'cropped-images',
  includeMetadata: true,
  optimizeSize: true,
  generatePreview: true
};

// 创建store
export const exportSettingsStore = writable<ExportSettings>(defaultSettings);

// 辅助函数
export const exportSettingsActions = {
  // 更新设置
  updateSettings(newSettings: Partial<ExportSettings>) {
    exportSettingsStore.update(settings => ({
      ...settings,
      ...newSettings
    }));
    console.log('🔄 ExportSettings: 设置已更新', newSettings);
  },

  // 更新导出类型（同时更新相关格式）
  updateExportType(type: ExportTypeKey) {
    const typeConfig = EXPORT_TYPES[type];
    if (typeConfig) {
      exportSettingsStore.update(settings => ({
        ...settings,
        selectedType: type,
        targetEngine: type,
        format: typeConfig.defaultFormat as any
      }));
      console.log('🔄 ExportSettings: 导出类型已更新', type);
    }
  },

  // 重置为默认设置
  resetToDefaults() {
    exportSettingsStore.set({ ...defaultSettings });
    console.log('🔄 ExportSettings: 重置为默认设置');
  },

  // 获取当前设置
  getCurrentSettings(): Promise<ExportSettings> {
    return new Promise((resolve) => {
      let unsubscribe: (() => void) | undefined;
      unsubscribe = exportSettingsStore.subscribe(settings => {
        resolve(settings);
        if (unsubscribe) {
          unsubscribe();
        }
      });
    });
  },

  // 获取当前导出类型配置
  getCurrentTypeConfig() {
    return new Promise((resolve) => {
      let unsubscribe: (() => void) | undefined;
      unsubscribe = exportSettingsStore.subscribe(settings => {
        const config = EXPORT_TYPES[settings.targetEngine];
        resolve(config);
        if (unsubscribe) {
          unsubscribe();
        }
      });
    });
  }
};

// 🎯 全局导出状态store
const defaultExportState: GlobalExportState = {
  isExporting: false,
  exportType: null,
  currentFile: '',
  progress: {
    current: 0,
    total: 0,
    stage: 'preparing'
  },
  startTime: null
};

export const globalExportState = writable<GlobalExportState>(defaultExportState);

// 🎯 全局导出状态操作
// 🎯 全局布局设置store
const defaultLayoutSettings: LayoutSettings = {
  padding: 2,
  spacing: 0,
  algorithm: 'maxrects',
  powerOfTwo: false,
  allowRotation: false,
  maxWidth: 2048,
  maxHeight: 2048
};

export const layoutSettingsStore = writable<LayoutSettings>(defaultLayoutSettings);

// 🎯 全局裁切设置store
const defaultCropSettings: GlobalCropSettings = {
  cropPadding: 0
};

export const globalCropSettingsStore = writable<GlobalCropSettings>(defaultCropSettings);

// 🎯 布局设置操作
export const layoutSettingsActions = {
  // 更新布局设置
  updateLayoutSettings(newSettings: Partial<LayoutSettings>) {
    layoutSettingsStore.update(settings => ({
      ...settings,
      ...newSettings
    }));
    console.log('🔄 LayoutSettings: 布局设置已更新', newSettings);
  },

  // 获取当前布局设置
  getCurrentLayoutSettings(): Promise<LayoutSettings> {
    return new Promise((resolve) => {
      let unsubscribe: (() => void) | undefined;
      unsubscribe = layoutSettingsStore.subscribe(settings => {
        resolve(settings);
        if (unsubscribe) {
          unsubscribe();
        }
      });
    });
  },

  // 重置布局设置
  resetLayoutSettings() {
    layoutSettingsStore.set({ ...defaultLayoutSettings });
    console.log('🔄 LayoutSettings: 重置为默认设置');
  }
};

// 🎯 裁切设置操作
export const globalCropSettingsActions = {
  // 更新裁切设置
  updateCropSettings(newSettings: Partial<GlobalCropSettings>) {
    globalCropSettingsStore.update(settings => ({
      ...settings,
      ...newSettings
    }));
    console.log('🔄 GlobalCropSettings: 裁切设置已更新', newSettings);
  },

  // 获取当前裁切设置
  getCurrentCropSettings(): Promise<GlobalCropSettings> {
    return new Promise((resolve) => {
      let unsubscribe: (() => void) | undefined;
      unsubscribe = globalCropSettingsStore.subscribe(settings => {
        resolve(settings);
        if (unsubscribe) {
          unsubscribe();
        }
      });
    });
  }
};

export const globalExportActions = {
  // 开始导出
  startExport(type: 'folder' | 'single' | 'atlas', total: number = 0) {
    globalExportState.update(state => ({
      ...state,
      isExporting: true,
      exportType: type,
      currentFile: '',
      progress: {
        current: 0,
        total,
        stage: 'preparing'
      },
      startTime: Date.now()
    }));
    console.log('🚀 GlobalExport: 开始导出', { type, total });
  },

  // 更新进度
  updateProgress(current: number, total?: number, currentFile?: string, stage?: GlobalExportState['progress']['stage']) {
    globalExportState.update(state => ({
      ...state,
      currentFile: currentFile || state.currentFile,
      progress: {
        current,
        total: total !== undefined ? total : state.progress.total,
        stage: stage || state.progress.stage
      }
    }));
  },

  // 结束导出
  endExport() {
    globalExportState.update(state => {
      const duration = state.startTime ? Date.now() - state.startTime : 0;
      console.log('✅ GlobalExport: 导出完成', {
        type: state.exportType,
        duration: `${duration}ms`,
        files: state.progress.current
      });

      return {
        ...defaultExportState,
        progress: {
          ...state.progress,
          stage: 'completed'
        }
      };
    });

    // 延迟重置状态，让用户看到完成状态
    setTimeout(() => {
      globalExportState.set(defaultExportState);
    }, 2000);
  },

  // 取消导出
  cancelExport() {
    globalExportState.set(defaultExportState);
    console.log('❌ GlobalExport: 导出已取消');
  },

  // 获取当前状态
  getCurrentState(): Promise<GlobalExportState> {
    return new Promise((resolve) => {
      let unsubscribe: (() => void) | undefined;
      unsubscribe = globalExportState.subscribe(state => {
        resolve(state);
        if (unsubscribe) {
          unsubscribe();
        }
      });
    });
  }
};
