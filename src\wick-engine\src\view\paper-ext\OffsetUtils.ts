/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Paper.js-drawing-tools.
 *
 * Paper.js-drawing-tools is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Paper.js-drawing-tools is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Paper.js-drawing-tools.  If not, see <https://www.gnu.org/licenses/>.
 */

import * as paper from "paper";

interface OffsetUtilsInterface {
  offsetPath(path: paper.Path, offset: number, dontMerge?: boolean): paper.Path;
  joinOffsets(
    outerPath: paper.Path,
    innerPath: paper.Path,
    originPath: paper.Path,
    offset: number
  ): paper.Path;
  cleanupPath(path: paper.CompoundPath): void;
}

class OffsetUtilsClass implements OffsetUtilsInterface {
  private errorThreshold: number = 0.1;
  private epsilon: number = 1e-12;
  private geomEpsilon: number = 1e-8;
  private enforeArcs: boolean = false;

  private getOffsetSegments(
    curve: paper.Curve,
    offset: number
  ): paper.Segment[] {
    if (curve.isStraight()) {
      const n = curve.getNormalAtTime(0.5).multiply(offset);
      const p1 = curve.point1.add(n);
      const p2 = curve.point2.add(n);
      return [new paper.Segment(p1), new paper.Segment(p2)];
    } else {
      const curves = this.splitCurveForOffseting(curve);
      const segments: paper.Segment[] = [];
      for (let i = 0; i < curves.length; i++) {
        const offsetCurves = this.getOffsetCurves(curves[i], offset);
        let prevSegment: paper.Segment | null = null;
        for (let j = 0; j < offsetCurves.length; j++) {
          const curve = offsetCurves[j];
          const segment = curve.segment1;
          if (prevSegment) {
            prevSegment.handleOut = segment.handleOut.project(
              prevSegment.handleIn
            );
          } else {
            segments.push(segment);
          }
          segments.push((prevSegment = curve.segment2));
        }
      }
      return segments;
    }
  }

  private connect(
    path: paper.Path,
    dest: paper.Segment,
    originSegment: paper.Segment,
    offset: number,
    type: string,
    miterLimit?: number,
    addLine?: boolean
  ): boolean {
    const fixHandles = (seg: paper.Segment) => {
      const handleIn = seg.handleIn;
      const handleOut = seg.handleOut;
      if (handleIn.length < handleOut.length) {
        seg.handleIn = handleIn.project(handleOut);
      } else {
        seg.handleOut = handleOut.project(handleIn);
      }
    };

    const addPoint = (point: paper.Point) => {
      if (!point.equals(path.lastSegment.point)) {
        path.add(point);
      }
    };

    const center = originSegment.point;
    const start = path.lastSegment;
    const pt1 = start.point;
    const pt2 = dest.point;
    let connected = false;

    if (!pt1.isClose(pt2, this.geomEpsilon)) {
      if (
        this.enforeArcs ||
        new paper.Line(pt1, pt2).getSignedDistance(center) * offset <=
          this.geomEpsilon
      ) {
        const radius = Math.abs(offset);
        switch (type) {
          case "round":
            const v1 = pt1.subtract(center);
            const v2 = pt2.subtract(center);
            const v = v1.add(v2);
            const through =
              v.length < this.geomEpsilon
                ? v2.rotate(90).add(center)
                : center.add(v.normalize(radius));
            path.arcTo(through, pt2);
            break;
          case "miter":
            (paper.Path as any)._addBevelJoin(
              originSegment,
              "miter",
              radius,
              4,
              null,
              null,
              addPoint
            );
            break;
          case "square":
            (paper.Path as any)._addSquareCap(
              originSegment,
              "square",
              radius,
              null,
              null,
              addPoint
            );
            break;
          default:
            path.lineTo(pt2);
        }
        connected = true;
      } else if (addLine) {
        path.lineTo(pt2);
        connected = true;
      }
      if (connected) {
        fixHandles(start);
        const last = path.lastSegment;
        fixHandles(last);
        if (dest !== path.firstSegment) {
          last.handleOut = dest.handleOut;
        }
      }
    }
    return connected;
  }

  public offsetPath(
    path: paper.Path,
    offset: number,
    dontMerge?: boolean
  ): paper.Path {
    const result = new paper.Path({ insert: false });
    const curves = path.curves;
    const strokeJoin = path.strokeJoin;
    const miterLimit = path.miterLimit;

    for (let i = 0; i < curves.length; i++) {
      const curve = curves[i];
      if (curve.length > this.geomEpsilon) {
        const segments = this.getOffsetSegments(curve, offset);
        if (!result.isEmpty()) {
          this.connect(
            result,
            segments.shift()!,
            curve.segment1,
            offset,
            strokeJoin,
            miterLimit,
            true
          );
        }
        result.addSegments(segments);
      }
    }

    if (path.closed && !result.isEmpty()) {
      this.connect(
        result,
        result.firstSegment,
        path.firstSegment,
        offset,
        strokeJoin,
        miterLimit
      );
      if (dontMerge) {
        result.closed = true;
      } else {
        result.closePath();
      }
    }

    return result;
  }

  public joinOffsets(
    outerPath: paper.Path,
    innerPath: paper.Path,
    originPath: paper.Path,
    offset: number
  ): paper.Path {
    outerPath.closed = innerPath.closed = false;
    const path = outerPath;
    const open = !originPath.closed;
    const strokeCap = originPath.strokeCap;
    path.reverse();

    if (open) {
      this.connect(
        path,
        innerPath.firstSegment,
        originPath.firstSegment,
        offset,
        strokeCap
      );
    }

    path.join(innerPath);

    if (open) {
      this.connect(
        path,
        path.firstSegment,
        originPath.lastSegment,
        offset,
        strokeCap
      );
    }

    path.closePath();
    return path;
  }

  public cleanupPath(path: paper.CompoundPath): void {
    path.children.forEach((child) => {
      if (Math.abs(child.area) < this.errorThreshold) {
        child.remove();
      }
    });
  }

  private getOffsetCurves(curve: paper.Curve, offset: number): paper.Curve[] {
    const radius = Math.abs(offset);

    const getOffsetPoint = (v: number[], t: number): paper.Point => {
      return (paper.Curve as any)
        .getPoint(v, t)
        .add((paper.Curve as any).getNormal(v, t).multiply(offset));
    };

    const offsetAndSubdivide = (
      curve: paper.Curve,
      curves: paper.Curve[]
    ): paper.Curve[] => {
      const v = curve.values;
      const ps = [getOffsetPoint(v, 0), getOffsetPoint(v, 1)];
      const ts = [
        (paper.Curve as any).getTangent(v, 0),
        (paper.Curve as any).getTangent(v, 1),
      ];
      const pt = getOffsetPoint(v, 0.5);
      const div = (ts[0].cross(ts[1]) * 3) / 4;
      const d = pt.add(pt).subtract(ps[0].add(ps[1]));
      const a = d.cross(ts[1]) / div;
      const b = d.cross(ts[0]) / div;
      const hs = [ts[0].multiply(a), ts[1].multiply(-b)];

      if ((a < 0 && b > 0) || (a > 0 && b < 0)) {
        const flip = Math.abs(a) > Math.abs(b);
        const i1 = flip ? 0 : 1;
        const i2 = i1 ^ 1;
        const p = ps[i1];
        const h = hs[i1];
        const cross = new paper.Line(p, h, true).intersect(
          new paper.Line(ps[i2], ts[i2], true),
          true
        );

        hs[i2] = null;

        if (cross) {
          const nh = cross.subtract(p);
          const scale = nh.dot(h) / h.dot(h);
          if (0 < scale && scale < 1) {
            hs[i1] = nh;
          }
        }
      }

      const offsetCurve = new paper.Curve(ps[0], hs[0], hs[1], ps[1]);
      const error = this.getOffsetError(v, offsetCurve.values, radius);

      if (
        error > this.errorThreshold &&
        offsetCurve.length > this.errorThreshold
      ) {
        const curve2 = curve.divideAtTime(this.getAverageTangentTime(v));
        offsetAndSubdivide(curve, curves);
        offsetAndSubdivide(curve2, curves);
      } else {
        curves.push(offsetCurve);
      }

      return curves;
    };

    return offsetAndSubdivide(curve, []);
  }

  private getOffsetError(cv: number[], ov: number[], radius: number): number {
    const count = 16;
    let error = 0;

    for (let i = 1; i < count; i++) {
      const t = i / count;
      const p = (paper.Curve as any).getPoint(cv, t);
      const n = (paper.Curve as any).getNormal(cv, t);
      const roots = (paper.Curve as any).getCurveLineIntersections(
        ov,
        p.x,
        p.y,
        n.x,
        n.y
      );
      let dist = 2 * radius;

      for (let j = 0; j < roots.length; j++) {
        const d = (paper.Curve as any).getPoint(ov, roots[j]).getDistance(p);
        if (d < dist) dist = d;
      }

      const err = Math.abs(radius - dist);
      if (err > error) error = err;
    }

    return error;
  }

  private splitCurveForOffseting(curve: paper.Curve): paper.Curve[] {
    const curves = [curve.clone()];
    if (curve.isStraight()) return curves;

    const splitAtRoots = (index: number, roots: number[] | null) => {
      for (let i = 0, prevT = 0; roots && i < roots.length; i++) {
        const t = roots[i];
        const curve = curves[index].divideAtTime(
          i ? (t - prevT) / (1 - prevT) : t
        );
        prevT = t;
        if (curve) {
          curves.splice(++index, 0, curve);
        }
      }
    };

    const splitLargeAngles = (index: number, recursion: number) => {
      const curve = curves[index];
      const v = curve.values;
      const n1 = (paper.Curve as any).getNormal(v, 0);
      const n2 = (paper.Curve as any).getNormal(v, 1).negate();
      const cos = n1.dot(n2);

      if (cos > -0.5 && ++recursion < 4) {
        curves.splice(
          index + 1,
          0,
          curve.divideAtTime(this.getAverageTangentTime(v))
        );
        splitLargeAngles(index + 1, recursion);
        splitLargeAngles(index, recursion);
      }
    };

    const info = curve.classify();
    const roots = info.roots;

    if (roots && info.type !== "loop") {
      splitAtRoots(0, roots);
    }

    const getPeaks = (paper.Curve as any).getPeaks;
    for (let i = curves.length - 1; i >= 0; i--) {
      splitAtRoots(i, getPeaks(curves[i].values));
    }

    for (let i = curves.length - 1; i >= 0; i--) {
      splitLargeAngles(i, 0);
    }

    return curves;
  }

  private getAverageTangentTime(v: number[]): number {
    const tan = (paper.Curve as any)
      .getTangent(v, 0)
      .add((paper.Curve as any).getTangent(v, 0.5))
      .add((paper.Curve as any).getTangent(v, 1));

    const tx = tan.x;
    const ty = tan.y;
    const flip = Math.abs(ty) < Math.abs(tx);
    const s = flip ? ty / tx : tx / ty;
    const ia = flip ? 1 : 0;
    const io = ia ^ 1;
    const a0 = v[ia + 0],
      o0 = v[io + 0];
    const a1 = v[ia + 2],
      o1 = v[io + 2];
    const a2 = v[ia + 4],
      o2 = v[io + 4];
    const a3 = v[ia + 6],
      o3 = v[io + 6];
    const aA = -a0 + 3 * a1 - 3 * a2 + a3;
    const aB = 3 * a0 - 6 * a1 + 3 * a2;
    const aC = -3 * a0 + 3 * a1;
    const oA = -o0 + 3 * o1 - 3 * o2 + o3;
    const oB = 3 * o0 - 6 * o1 + 3 * o2;
    const oC = -3 * o0 + 3 * o1;
    const roots: number[] = [];
    const epsilon = (paper as any).Numerical.CURVETIME_EPSILON;
    const count = (paper as any).Numerical.solveQuadratic(
      3 * (aA - s * oA),
      2 * (aB - s * oB),
      aC - s * oC,
      roots,
      epsilon,
      1 - epsilon
    );

    return count > 0 ? roots[0] : 0.5;
  }
}

const OffsetUtils = new OffsetUtilsClass();
export default OffsetUtils;
