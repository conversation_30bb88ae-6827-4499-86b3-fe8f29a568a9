/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>..
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { ActionButton } from "./ActionButton";

type PopupMenuMode = "gapfill" | "framesize";

export class PopupMenu extends GUIElement {
  protected x: number;
  protected y: number;
  protected height: number;
  protected mode: PopupMenuMode;

  protected extendFramesButton: ActionButton;
  protected emptyFramesButton: ActionButton;
  protected smallFramesButton: ActionButton;
  protected normalFramesButton: ActionButton;
  protected largeFramesButton: ActionButton;

  constructor(
    model: Base,
    args: { x: number; y: number; mode: PopupMenuMode }
  ) {
    super(model);

    this.x = args.x;
    this.y = args.y;
    this.height = 40;
    this.mode = args.mode;

    this.extendFramesButton = new ActionButton(this.model, {
      tooltip: "Extend Frames",
      icon: "gap_fill_extend_frames",
      clickFn: () => {
        this.project.model.activeTimeline.fillGapsMethod = "auto_extend";
        this.projectWasModified();
      },
    });

    this.emptyFramesButton = new ActionButton(this.model, {
      tooltip: "Add Blank Frames",
      icon: "gap_fill_empty_frames",
      clickFn: () => {
        this.project.model.activeTimeline.fillGapsMethod = "blank_frames";
        this.projectWasModified();
      },
    });

    this.smallFramesButton = new ActionButton(this.model, {
      tooltip: "Small",
      icon: "small_frames",
      clickFn: () => {
        GUIElement.GRID_DEFAULT_CELL_WIDTH = GUIElement.GRID_SMALL_CELL_WIDTH;
        GUIElement.GRID_DEFAULT_CELL_HEIGHT = GUIElement.GRID_SMALL_CELL_HEIGHT;
      },
    });

    this.normalFramesButton = new ActionButton(this.model, {
      tooltip: "Medium",
      icon: "normal_frames",
      clickFn: () => {
        GUIElement.GRID_DEFAULT_CELL_WIDTH = GUIElement.GRID_NORMAL_CELL_WIDTH;
        GUIElement.GRID_DEFAULT_CELL_HEIGHT =
          GUIElement.GRID_NORMAL_CELL_HEIGHT;
      },
    });

    this.largeFramesButton = new ActionButton(this.model, {
      tooltip: "Large",
      icon: "large_frames",
      clickFn: () => {
        GUIElement.GRID_DEFAULT_CELL_WIDTH = GUIElement.GRID_LARGE_CELL_WIDTH;
        GUIElement.GRID_DEFAULT_CELL_HEIGHT = GUIElement.GRID_LARGE_CELL_HEIGHT;
      },
    });
  }

  draw(): void {
    super.draw();

    if (this.mode === "gapfill") {
      this._drawFrameGapsButtons();
    } else if (this.mode === "framesize") {
      this._drawFrameSizeButtons();
    }
  }

  protected _drawFrameGapsButtons(): void {
    const ctx = this.ctx;
    const method = this.project.model.activeTimeline.fillGapsMethod;

    ctx.save();
    ctx.translate(this.x, this.y - this.height);

    // Background
    ctx.fillStyle = "#111";
    ctx.beginPath();
    ctx.roundRect(0, 0, 80, 40, 3);
    ctx.fill();

    // Buttons
    ctx.save();
    ctx.translate(5, 5);
    this.extendFramesButton.draw(method === "auto_extend");
    ctx.translate(35, 0);
    this.emptyFramesButton.draw(method === "blank_frames");
    ctx.restore();

    ctx.restore();
  }

  protected _drawFrameSizeButtons(): void {
    const ctx = this.ctx;

    ctx.save();
    ctx.translate(this.x, this.y - this.height);

    // Background
    ctx.fillStyle = "#111";
    ctx.beginPath();
    ctx.roundRect(0, 0, 115, 40, 3);
    ctx.fill();

    // Buttons
    ctx.save();
    ctx.translate(5, 5);
    this.smallFramesButton.draw(
      GUIElement.GRID_DEFAULT_CELL_WIDTH === GUIElement.GRID_SMALL_CELL_WIDTH
    );
    ctx.translate(35, 0);
    this.normalFramesButton.draw(
      GUIElement.GRID_DEFAULT_CELL_WIDTH === GUIElement.GRID_NORMAL_CELL_WIDTH
    );
    ctx.translate(35, 0);
    this.largeFramesButton.draw(
      GUIElement.GRID_DEFAULT_CELL_WIDTH === GUIElement.GRID_LARGE_CELL_WIDTH
    );
    ctx.restore();

    ctx.restore();
  }
}
