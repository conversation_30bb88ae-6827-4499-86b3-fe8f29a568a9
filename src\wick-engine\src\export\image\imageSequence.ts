/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

declare global {
  interface Window {
    Wick: any;
    JSZip: any;
  }
}

interface ImageSequenceArgs {
  project: any; // Wick.Project type
  width?: number;
  height?: number;
  onProgress?: (frame: number, maxFrames: number) => void;
  onFinish: (blob: Blob) => void;
}

interface ImageFile {
  src: string;
}

export class ImageSequence {
  /**
   * Create a png sequence from a project.
   * @param args - Configuration object containing project and callback functions
   */
  static toPNGSequence(args: ImageSequenceArgs): void {
    const { project, onProgress, onFinish } = args;
    const zip = new window.JSZip();

    const buildZip = (files: ImageFile[]) => {
      files.forEach((file, index) => {
        const blob = (window.Wick.ExportUtils as any).dataURItoBlob(file.src);
        const paddedNum = String(index).padStart(12, "0");
        zip.file(`frame${paddedNum}.png`, blob);
      });

      zip
        .generateAsync({
          type: "blob",
          compression: "DEFLATE",
          compressionOptions: {
            level: 9,
          },
        })
        .then(onFinish);
    };

    project.generateImageSequence({
      width: args.width,
      height: args.height,
      onFinish: buildZip,
      onProgress: onProgress,
    });
  }
}

// Add to global Wick namespace
if (typeof window !== "undefined") {
  window.Wick = window.Wick || {};
  window.Wick.ImageSequence = ImageSequence;
}
