import * as paper from "paper";
import { MY_EPSILON, MIN_PATH_LENGTH, LINE_TOLERANCE } from "./Consts";

/**
 * Checks if two numbers are close within a given precision
 */
export function IsClose(a: number, b: number, Precision: number): boolean {
  const diff = Math.abs(a - b);
  return diff < Precision;
}

/**
 * Make in bounds, so we can roll over on lists
 */
export function MIB(i: number, max: number): number {
  while (i >= max) {
    i -= max;
  }
  while (i < 0) {
    i += max;
  }
  return i;
}

/**
 * Returns the value between value1 and value2 at the given percentage
 */
export function Lerp(
  value1: number,
  value2: number,
  percentage: number
): number {
  return value1 + (value2 - value1) * percentage;
}

/**
 * Returns a line as a curve object
 */
export function GetCurveLine(
  p1: paper.Point,
  p2: paper.Point,
  asvector: boolean = false
): paper.Curve {
  if (asvector === true) {
    p2 = p2.add(p1);
  }
  return new paper.Curve(
    p1,
    p1.Lerp(p2, ONE_THIRD).subtract(p1),
    p1.Lerp(p2, TWO_THIRD).subtract(p2),
    p2
  );
}

/**
 * Makes a path from segments, combines close segments into one
 */
export function MakePathFromSegments(
  segments: paper.Segment[],
  tolerance: number = MY_EPSILON
): paper.Path {
  for (let i = 0; i < segments.length - 1; i++) {
    if (segments[i].point.isClose(segments[i + 1].point, tolerance)) {
      segments[i].handleOut = segments[i + 1].handleOut;
      segments.splice(i + 1, 1);
    }
  }
  return new paper.Path(segments);
}

/**
 * Makes a path from curves, assumes one curve's end is the next curve's start
 */
export function MakePathFromCurves(
  curves: paper.Curve | paper.Curve[]
): paper.Path | null {
  if (!Array.isArray(curves)) {
    curves = [curves];
  }
  if (curves.length === 0) {
    return null;
  }

  const segs: paper.Segment[] = [];
  segs.push(curves[0].segment1);

  for (let i = 0; i < curves.length - 1; i++) {
    segs.push(
      new paper.Segment(
        curves[i].point2.Lerp(curves[i + 1].point1, 0.5),
        curves[i].segment2.handleIn,
        curves[i + 1].segment1.handleOut
      )
    );
  }
  segs.push(curves[curves.length - 1].segment2);
  return new paper.Path(segs);
}

/**
 * Gets the offsets for curves based on length from start
 */
export function GetOffsetsForCurves(
  curves: paper.Curve[],
  offset1: number,
  offset2: number
): [number, number][] {
  let totallength = 0;
  for (let i = 0; i < curves.length; i++) {
    totallength += curves[i].length;
  }

  const offsetchange = (offset2 - offset1) / totallength;
  const offsets: [number, number][] = [];
  let currentlength = 0;

  for (let i = 0; i < curves.length; i++) {
    const s = offset1 + currentlength * offsetchange;
    currentlength += curves[i].length;
    const e = offset1 + currentlength * offsetchange;
    offsets.push([s, e]);
  }

  return offsets;
}

/**
 * Gets the center of two possible circles given two points and a radius
 */
export function GetCircleCenter(
  point1: paper.Point,
  point2: paper.Point,
  radius: number
): paper.Point | null {
  const absoffset = Math.abs(radius);
  const midpoint = point1.Lerp(point2, 0.5);
  const middistance = point1.getDistance(midpoint);
  const centerdistance = Math.sqrt(
    Math.abs(absoffset * absoffset - middistance * middistance)
  );
  const linetocenter = midpoint.subtract(point1).normalize(centerdistance);

  if (radius < 0) {
    linetocenter.rotate(90);
  } else {
    linetocenter.rotate(-90);
  }

  const rval = midpoint.add(linetocenter);
  if (rval.isNaN()) {
    return null;
  }
  return rval;
}

/**
 * Gets the trim times for a curve pair
 */
export function GetTrimTimesForCurvePair(
  curve1: paper.Curve,
  curve2: paper.Curve,
  radius: number
): {
  Time1: number;
  Time2: number;
  Pos1: paper.Point;
  Pos2: paper.Point;
  CenterPos: paper.Point;
} | null {
  const radiusabs = Math.abs(radius);
  if (curve1.AnyChanceBBoxWillIntersect(curve2, radiusabs)) {
    const tdata = curve1.GetShortestDistanceBetween(curve2);
    if (tdata.Distance <= radiusabs * 2) {
      let p1 = tdata.MyPos;
      let p2 = tdata.OtherPos;
      let newpos = new paper.Point(0, 0);
      let oldpos = new paper.Point(Number.MAX_VALUE, Number.MAX_VALUE);

      if (p1.equals(p2)) {
        const midpoint = curve1.point2.Lerp(curve2.point1, 0.5);
        const norm = curve1.getNormalAtTime(1).add(curve2.getNormalAtTime(0));

        if (norm.isZero()) {
          norm = curve1
            .getTangentAtTime(1)
            .add(curve2.getTangentAtTime(0).multiply(-1));
        }
        newpos = midpoint.add(norm.normalize(radius));
        p1 = curve1.getNearestPoint(newpos);
        p2 = curve2.getNearestPoint(newpos);
      }

      let count = 50;
      while (
        !newpos.isClose(oldpos, MY_EPSILON) &&
        newpos !== null &&
        count > 0
      ) {
        count--;
        oldpos = newpos;
        newpos = GetCircleCenter(p1, p2, radius);
        if (newpos === null) {
          return null;
        }
        p1 = curve1.getNearestPoint(newpos);
        p2 = curve2.getNearestPoint(newpos);
      }

      if (newpos === null) {
        return null;
      }

      const t1 = curve1.getTimeOf(p1);
      const t2 = curve2.getTimeOf(p2);
      return {
        Time1: t1,
        Time2: t2,
        Pos1: p1,
        Pos2: p2,
        CenterPos: newpos,
      };
    }
  }
  return null;
}

/**
 * Gets the trim times for a list of curves
 */
export function GetTrimTimesForCurves(
  curves: paper.Curve[],
  startoffset: number,
  endoffset: number,
  openpath: number
): [number, number][] {
  const offsets = GetOffsetsForCurves(curves, startoffset, endoffset);
  const trimtimes: [number, number][] = [];
  const curvepairs: [number, number][] = [];

  for (let i = 0; i < curves.length; i++) {
    trimtimes.push([0, 1]);
    if (i < curves.length - openpath) {
      curvepairs.push([i, MIB(i + 1, curves.length)]);
    }
  }

  for (let i = 0; i < curvepairs.length; i++) {
    const [index0, index1] = curvepairs[i];
    const c1 = curves[index0];
    const c2 = curves[index1];
    const offset1 = offsets[index0][1];
    const offset2 = offsets[index1][0];

    const data1 = GetTrimTimesForCurvePair(c1, c2, offset1);
    if (data1) {
      trimtimes[index0][1] = data1.Time1;
      trimtimes[index1][0] = data1.Time2;
    }

    if (offset1 !== offset2) {
      const data2 = GetTrimTimesForCurvePair(c1, c2, offset2);
      if (data2) {
        if (!data1 || data2.Time1 < data1.Time1) {
          trimtimes[index0][1] = data2.Time1;
        }
        if (!data1 || data2.Time2 > data1.Time2) {
          trimtimes[index1][0] = data2.Time2;
        }
      }
    }
  }

  return trimtimes;
}

/**
 * Trims curves based on trim times
 */
export function TrimCurves(
  curves: paper.Curve[],
  startoffset: number,
  endoffset: number,
  openpath: number
): paper.Curve[] {
  const trimtimes = GetTrimTimesForCurves(
    curves,
    startoffset,
    endoffset,
    openpath
  );
  const newcurves: paper.Curve[] = [];

  for (let i = 0; i < curves.length; i++) {
    if (trimtimes[i][0] !== 0 || trimtimes[i][1] !== 1) {
      newcurves.push(curves[i].GetPart(trimtimes[i][0], trimtimes[i][1]));
    } else {
      newcurves.push(curves[i]);
    }
  }

  return newcurves;
}
