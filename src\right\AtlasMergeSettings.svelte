<script lang="ts">
  /**
   * 图集合并设置组件
   * 用于设置选中图集的LayoutSettings属性
   */
  import { atlasStore } from '../stores/atlasStore';
  import type { AtlasResource, LayoutSettings } from '../types/imageType';
  import { exportAtlasResource, showExportSuccess, showExportError } from './exportUtils';

  // 导入国际化
  import { _ } from '../lib/i18n';

  interface Props {
    atlas: AtlasResource;
  }

  let { atlas }: Props = $props();

  // 🎯 从 atlasStore 获取最新的图集状态，确保数据是最新的
  let currentAtlas = $state<AtlasResource>(atlas);

  // 🎯 监听 atlasStore 的变化，确保获取最新的图集数据
  $effect(() => {
    const unsubscribe = atlasStore.subscribe(state => {
      const latestAtlas = state.atlases.find(a => a.id === atlas.id);
      if (latestAtlas) {
        currentAtlas = latestAtlas;
        console.log('🔄 AtlasMergeSettings: 图集状态更新', {
          atlasId: latestAtlas.id,
          atlasName: latestAtlas.name,
          childrenCount: latestAtlas.children.length,
          updatedAt: latestAtlas.updatedAt
        });
      }
    });

    return unsubscribe;
  });

  // 获取当前图集的布局设置，如果没有则使用默认值
  const currentSettings = $derived(currentAtlas.layoutSettings || {
    padding: 10,
    spacing: 5,
    algorithm: 'maxrects',
    powerOfTwo: false,
    allowRotation: false,
    maxWidth: 1024,
    maxHeight: 1024
  });

  // 🎯 导出相关状态
  let isExporting = $state(false);
  let exportMessage = $state('');

  // 更新布局设置
  function updateLayoutSetting<K extends keyof LayoutSettings>(
    key: K,
    value: LayoutSettings[K]
  ) {
    // 🎯 只需要调用一次更新，updateAtlasLayoutSettings 已经会更新 updatedAt
    atlasStore.updateAtlasLayoutSettings(currentAtlas, { [key]: value });

    console.log('🔧 AtlasMergeSettings: 更新布局设置', {
      atlasId: currentAtlas.id,
      atlasName: currentAtlas.name,
      key,
      value,
      timestamp: new Date().toISOString()
    });
  }

  // 重置为默认设置
  function resetToDefaults() {
    const defaultSettings: LayoutSettings = {
      padding: 10,
      spacing: 5,
      algorithm: 'maxrects',
      powerOfTwo: false,
      allowRotation: false,
      maxWidth: 1024,
      maxHeight: 1024
    };

    // 🎯 只需要调用一次更新，updateAtlasLayoutSettings 已经会更新 updatedAt
    atlasStore.updateAtlasLayoutSettings(currentAtlas, defaultSettings);

    console.log('🔧 AtlasMergeSettings: 重置为默认设置', {
      atlasId: currentAtlas.id,
      atlasName: currentAtlas.name,
      timestamp: new Date().toISOString()
    });
  }

  // 算法选项 - 使用英文原名
  const algorithmOptions = $derived([
    { value: 'maxrects', label: `Max Rects (${$_('ui.recommended')})` },
    { value: 'shelf', label: 'Shelf' },
    { value: 'potpack', label: 'Potpack' },
    { value: 'guillotine', label: 'Guillotine' },
    { value: 'none', label: $_('layout.algorithm.none') }
  ] as const);

  // 🎯 导出当前图集
  async function handleExport() {
    try {
      isExporting = true;
      exportMessage = $_('status.exporting');

      console.log('📤 AtlasMergeSettings: 开始导出图集', {
        atlasId: currentAtlas.id,
        atlasName: currentAtlas.name,
        childrenCount: currentAtlas.children.length
      });

      const success = await exportAtlasResource(currentAtlas);

      if (success) {
        exportMessage = $_('export.success');
        showExportSuccess(currentAtlas.children.length, $_('dialog.selectFolder'));

        // 3秒后清除成功消息
        setTimeout(() => {
          exportMessage = '';
        }, 3000);
      } else {
        exportMessage = $_('export.failed');
        showExportError($_('error.exportFailed'));

        // 5秒后清除错误消息
        setTimeout(() => {
          exportMessage = '';
        }, 5000);
      }

    } catch (error) {
      console.error('❌ AtlasMergeSettings: 导出失败', error);
      exportMessage = $_('export.failed');
      showExportError(error instanceof Error ? error.message : $_('error.unknown'));

      // 5秒后清除错误消息
      setTimeout(() => {
        exportMessage = '';
      }, 5000);
    } finally {
      isExporting = false;
    }
  }
</script>

<div class="atlas-merge-settings">
  <!-- <div class="settings-header">
    <div class="atlas-info">
      <span class="atlas-name">{currentAtlas.name}</span>
      <span class="image-count">{currentAtlas.children.length} 张图片</span>
    </div>
  </div> -->

  <div class="settings-content">
    <!-- 🎯 统一的布局设置模块 -->
    <div class="setting-group">
      <h4 class="group-title">{$_('layout.settings')}</h4>

      <!-- 间距设置 -->
      <div class="setting-item">
        <label class="setting-label">{$_('layout.padding')}: {currentSettings.padding}px</label>
        <input
          type="range"
          min="0"
          max="50"
          value={currentSettings.padding}
          oninput={(e) => updateLayoutSetting('padding', parseInt((e.target as HTMLInputElement).value))}
        />
      </div>

      <div class="setting-item">
        <label class="setting-label">{$_('layout.spacing')}: {currentSettings.spacing}px</label>
        <input
          type="range"
          min="0"
          max="20"
          value={currentSettings.spacing}
          oninput={(e) => updateLayoutSetting('spacing', parseInt((e.target as HTMLInputElement).value))}
        />
      </div>

      <!-- 算法选择 -->
      <div class="setting-item">
        <label class="setting-label">{$_('layout.algorithmLabel')}</label>
        <select
          value={currentSettings.algorithm}
          onchange={(e) => updateLayoutSetting('algorithm', (e.target as HTMLSelectElement).value as LayoutSettings['algorithm'])}
        >
          {#each algorithmOptions as option}
            <option value={option.value}>{option.label}</option>
          {/each}
        </select>
      </div>

      <!-- 选项设置 -->
      <div class="setting-item">
        <label class="checkbox-label">
          <input
            type="checkbox"
            checked={currentSettings.powerOfTwo}
            onchange={(e) => updateLayoutSetting('powerOfTwo', (e.target as HTMLInputElement).checked)}
          />
          {$_('layout.powerOfTwo')} ({$_('ui.optimizeGPU')})
        </label>
      </div>

      <div class="setting-item">
        <label class="checkbox-label">
          <input
            type="checkbox"
            checked={currentSettings.allowRotation}
            onchange={(e) => updateLayoutSetting('allowRotation', (e.target as HTMLInputElement).checked)}
          />
          {$_('layout.allowRotation')} ({$_('ui.improveSpaceUtilization')})
        </label>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="setting-actions">
      <button class="reset-button" onclick={resetToDefaults} disabled={isExporting}>
        {$_('actions.reset')}
      </button>

      <button class="export-button" onclick={handleExport} disabled={isExporting}>
        {#if isExporting}
          ⏳ {$_('status.exporting')}
        {:else}
          📤 {$_('export.atlas')}
        {/if}
      </button>
    </div>

    <!-- 导出状态消息 -->
    {#if exportMessage}
      <div class="export-message" class:success={exportMessage === $_('export.success')} class:error={exportMessage === $_('export.failed')}>
        {exportMessage}
      </div>
    {/if}
  </div>
</div>

<style>
  /* 🎯 参考SplitPanel的现代化设计 */
  .atlas-merge-settings {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-surface);
    color: var(--theme-text);
    overflow: hidden;
  }

  .settings-header {
    padding: 1rem;
    background: var(--theme-background);
    border-bottom: 1px solid var(--theme-border);
    border-radius: 6px 6px 0 0;
  }

  .settings-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--theme-text);
    padding-bottom: 0.4rem;
    border-bottom: 1px solid var(--theme-border);
  }

  .atlas-info {
    display: flex;
    gap: 1rem;
    align-items: center;
    font-size: 0.85rem;
    margin-top: 0.5rem;
  }

  .atlas-name {
    color: var(--theme-text);
    font-weight: 500;
  }

  .image-count {
    color: var(--theme-text-secondary);
    padding: 0.2rem 0.5rem;
    background: var(--theme-surface);
    border-radius: 4px;
    border: 1px solid var(--theme-border);
  }

  .settings-content {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  /* 🎯 参考SplitPanel的设置组样式 */
  .setting-group {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--theme-background);
    border-radius: 6px;
    border: 1px solid var(--theme-border);
  }

  .setting-group:last-child {
    margin-bottom: 0;
  }

  /* 🎯 添加组标题样式 */
  .group-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--theme-text);
    margin: 0 0 0.75rem 0;
    padding-bottom: 0.4rem;
    border-bottom: 1px solid var(--theme-border);
  }

  /* 🎯 设置项样式 */
  .setting-item {
    margin-bottom: 0.75rem;
  }

  .setting-item:last-child {
    margin-bottom: 0;
  }

  .setting-label {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--theme-text);
    margin-bottom: 0.5rem;
    display: block;
  }

  /* 🎯 滑块和输入控件样式 */

  input[type="range"] {
    width: 100%;
    height: 4px;
    background: var(--theme-border);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
  }

  input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--theme-primary, #3b82f6);
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--theme-primary, #3b82f6);
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  input[type="range"]::-moz-range-track {
    width: 100%;
    height: 4px;
    background: var(--theme-border);
    border-radius: 2px;
  }

  /* 🎯 参考SplitPanel的选择框样式 */
  select {
    padding: 0.5rem;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    background: var(--theme-surface);
    color: var(--theme-text);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  select:focus {
    outline: none;
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  /* 🎯 参考SplitPanel的复选框样式 */
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: var(--theme-text);
    cursor: pointer;
    padding: 0.3rem 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .checkbox-label:hover {
    background: var(--theme-hover, var(--theme-surface));
  }

  .checkbox-label input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
  }

  /* 🎯 参考SplitPanel的操作区域样式 */
  .setting-actions {
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid var(--theme-border);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  /* 🎯 参考SplitPanel的按钮样式 */
  .reset-button {
    width: 100%;
    padding: 0.75rem;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    color: var(--theme-text);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .reset-button:hover:not(:disabled) {
    background: var(--theme-hover, var(--theme-surface));
    border-color: var(--theme-primary);
  }

  .reset-button:disabled {
    background: var(--theme-background);
    color: var(--theme-text-secondary);
    cursor: not-allowed;
    opacity: 0.6;
  }

  .export-button {
    width: 100%;
    padding: 0.75rem;
    background: var(--theme-primary, #3b82f6);
    border: 1px solid var(--theme-primary, #3b82f6);
    border-radius: 4px;
    color: white;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .export-button:hover:not(:disabled) {
    background: var(--theme-primary-dark, #2563eb);
    border-color: var(--theme-primary-dark, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .export-button:disabled {
    background: var(--theme-background);
    border-color: var(--theme-border);
    color: var(--theme-text-secondary);
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
  }

  /* 🎯 参考SplitPanel的消息样式 */
  .export-message {
    margin-top: 0.5rem;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    text-align: center;
    font-weight: 500;
    animation: slideIn 0.3s ease-out;
  }

  .export-message.success {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.3);
  }

  .export-message.error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
  }

  /* 🎯 添加滚动条样式 */
  .settings-content::-webkit-scrollbar {
    width: 8px;
  }

  .settings-content::-webkit-scrollbar-track {
    background: var(--theme-surface);
    border-radius: 4px;
  }

  .settings-content::-webkit-scrollbar-thumb {
    background: var(--theme-border);
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  .settings-content::-webkit-scrollbar-thumb:hover {
    background: var(--theme-text-secondary);
  }

  /* 🎯 添加动画 */
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
