<script lang="ts">
  /**
   * 简化的 Checkbox 组件
   * 使用原生 HTML checkbox 样式
   */

  // Props using $props()
  let {
    checked = $bindable(false),
    disabled = false,
    label = '',
    onChange = () => {}
  }: {
    checked?: boolean;
    disabled?: boolean;
    label?: string;
    onChange?: (checked: boolean, event: Event) => void;
  } = $props();

  // 处理变化事件
  function handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const newChecked = target.checked;

    checked = newChecked;
    onChange(newChecked, event);
  }
</script>

<label class="simple-checkbox">
  <input
    type="checkbox"
    bind:checked
    {disabled}
    onchange={handleChange}
  />
  {#if label}
    <span class="checkbox-label">{label}</span>
  {/if}
</label>

<style>
  .simple-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
  }

  .simple-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
  }

  .checkbox-label {
    font-size: 14px;
    color: var(--theme-text, #374151);
  }

  .simple-checkbox:hover .checkbox-label {
    color: var(--theme-primary, #3b82f6);
  }
</style>
