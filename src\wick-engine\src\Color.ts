/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

declare const paper: any;

/** 颜色工具类 */
export class Color {
  protected _color: any;

  /**
   * 创建一个颜色实例
   * @param color - (可选) 用于创建Wick.Color的十六进制或RGBA颜色
   */
  constructor(color?: string) {
    if (color) {
      this._color = new paper.Color(color);
    } else {
      this._color = new paper.Color();
    }
  }

  /**
   * 颜色的红色值。范围从0.0到1.0
   */
  get r(): number {
    return this._color.red;
  }

  set r(r: number) {
    this._color.red = r;
  }

  /**
   * 颜色的绿色值。范围从0.0到1.0
   */
  get g(): number {
    return this._color.green;
  }

  set g(g: number) {
    this._color.green = g;
  }

  /**
   * 颜色的蓝色值。范围从0.0到1.0
   */
  get b(): number {
    return this._color.blue;
  }

  set b(b: number) {
    this._color.blue = b;
  }

  /**
   * 颜色的透明度值。范围从0.0到1.0
   */
  get a(): number {
    return this._color.alpha;
  }

  set a(a: number) {
    this._color.alpha = a;
  }

  /**
   * 以十六进制字符串形式表示的颜色。例如："#AABBCC"
   */
  get hex(): string {
    return this._color.toCSS(true);
  }

  /**
   * 以rgba字符串形式表示的颜色。例如："rgba(r,g,b,a)"
   */
  get rgba(): string {
    return this._color.toCSS();
  }

  /**
   * 将两个颜色的r、g和b值相加，生成一个新的颜色
   * @param color - 要与此颜色相加的颜色
   * @returns 结果颜色
   */
  add(color: Color): Color {
    const newColor = new Color();
    newColor.r = this.r + color.r;
    newColor.g = this.g + color.g;
    newColor.b = this.b + color.b;
    return newColor;
  }

  /**
   * 将颜色的r、g和b值与一个数字相乘，生成一个新的颜色
   * @param n - 要与此颜色相乘的数字
   * @returns 结果颜色
   */
  multiply(n: number): Color {
    const newColor = new Color();
    newColor.r = this.r * n;
    newColor.g = this.g * n;
    newColor.b = this.b * n;
    return newColor;
  }

  /**
   * 计算两个颜色的r、g和b值的平均值
   * @param colorA - 要平均的颜色之一（顺序无关）
   * @param colorB - 要平均的颜色之一（顺序无关）
   * @returns 平均后的结果颜色
   */
  static average(colorA: Color, colorB: Color): Color {
    return colorA.multiply(0.5).add(colorB.multiply(0.5));
  }
}
