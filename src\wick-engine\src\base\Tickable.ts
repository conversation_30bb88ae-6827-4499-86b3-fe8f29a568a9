/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "./Base";

/**
 * 任何需要tick的Wick对象的基类
 */
export class Tickable extends Base {
  /**
   * 调试功能。记录错误发生的情况
   */
  static get LOG_ERRORS(): boolean {
    return false;
  }

  /**
   * 返回此对象的所有可能事件列表
   * @return {string[]} 所有可能的脚本数组
   */
  static get possibleScripts(): string[] {
    return [
      "default",
      "mouseenter",
      "mousedown",
      "mousepressed",
      "mousereleased",
      "mouseleave",
      "mousehover",
      "mousedrag",
      "mouseclick",
      "keypressed",
      "keyreleased",
      "keydown",
      "load",
      "update",
      "unload",
    ];
  }

  protected _onscreen: boolean;
  protected _onscreenLastTick: boolean;
  protected _mouseState: string;
  protected _lastMouseState: string;
  protected _isClickTarget: boolean;
  protected _scripts: any[];
  protected _onEventFns: { [key: string]: Function };
  protected _cachedScripts: { [key: string]: any };
  public cursor: string;

  /**
   * 创建一个可tick的对象
   */
  constructor(args: any = {}) {
    super(args);

    this._onscreen = false;
    this._onscreenLastTick = false;

    this._mouseState = "out";
    this._lastMouseState = "out";
    this._isClickTarget = false;

    this._scripts = [];

    this.cursor = "default";

    this.addScript("default", "");

    this._onEventFns = {};
    this._cachedScripts = {};
  }

  protected _deserialize(data: any): void {
    super._deserialize(data);

    this._onscreen = false;
    this._onscreenLastTick = false;

    this._mouseState = "out";
    this._lastMouseState = "out";

    this._scripts = JSON.parse(JSON.stringify(data.scripts));
    this.cursor = data.cursor;

    this._onEventFns = {};
    this._cachedScripts = {};
  }

  protected _serialize(args: any): any {
    const data = super._serialize(args);

    data.scripts = JSON.parse(JSON.stringify(this._scripts));
    data.cursor = this.cursor;

    return data;
  }
}
