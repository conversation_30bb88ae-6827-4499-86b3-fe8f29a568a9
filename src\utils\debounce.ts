/**
 * 防抖工具函数
 */

/**
 * 创建防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @param immediate 是否立即执行第一次调用
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  immediate = false
): T & { cancel: () => void } {
  let timeoutId: number | null = null;
  let lastCallTime = 0;

  const debouncedFunction = function (this: any, ...args: Parameters<T>) {
    const now = Date.now();
    const timeSinceLastCall = now - lastCallTime;

    const callNow = immediate && !timeoutId;

    const later = () => {
      timeoutId = null;
      lastCallTime = Date.now();
      if (!immediate) {
        func.apply(this, args);
      }
    };

    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(later, delay) as any;
    lastCallTime = now;

    if (callNow) {
      func.apply(this, args);
    }
  } as T & { cancel: () => void };

  // 添加取消方法
  debouncedFunction.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  return debouncedFunction;
}

/**
 * 创建节流函数
 * @param func 要节流的函数
 * @param delay 节流间隔（毫秒）
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T & { cancel: () => void } {
  let timeoutId: number | null = null;
  let lastExecTime = 0;

  const throttledFunction = function (this: any, ...args: Parameters<T>) {
    const now = Date.now();

    if (now - lastExecTime > delay) {
      func.apply(this, args);
      lastExecTime = now;
    } else if (!timeoutId) {
      timeoutId = setTimeout(() => {
        func.apply(this, args);
        lastExecTime = Date.now();
        timeoutId = null;
      }, delay - (now - lastExecTime)) as any;
    }
  } as T & { cancel: () => void };

  // 添加取消方法
  throttledFunction.cancel = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };

  return throttledFunction;
}

/**
 * 🎯 requestAnimationFrame防抖 - 用于渲染优化
 * @param func 要防抖的函数
 * @returns 防抖后的函数
 */
export function rafDebounce<T extends (...args: any[]) => any>(
  func: T
): T & { cancel: () => void } {
  let rafId: number | undefined;

  const rafDebouncedFunction = function (this: any, ...args: Parameters<T>) {
    if (rafId) {
      cancelAnimationFrame(rafId);
    }

    rafId = requestAnimationFrame(() => {
      func.apply(this, args);
      rafId = undefined;
    });
  } as T & { cancel: () => void };

  // 添加取消方法
  rafDebouncedFunction.cancel = () => {
    if (rafId) {
      cancelAnimationFrame(rafId);
      rafId = undefined;
    }
  };

  return rafDebouncedFunction;
}
