/**
 * 拖拽状态管理
 */
import { writable } from 'svelte/store';

interface DragData {
  type: 'image';
  src: string;
  alt?: string;
  name: string;
  resourceId?: string;
  // 添加完整的资源信息
  resource?: any; // ResourceItem类型
  width?: number;
  height?: number;
  buffer?: ArrayBuffer;
  [key: string]: any;
}

interface MousePosition {
  x: number;
  y: number;
}

interface DragState {
  isDragging: boolean;
  dragData: DragData | null;
  mousePosition: MousePosition | null;
}

// 创建拖拽状态
function createDragStore() {
  const { subscribe, set, update } = writable<DragState>({
    isDragging: false,
    dragData: null,
    mousePosition: null
  });

  return {
    subscribe,

    // 开始拖拽
    startDrag(dragData: DragData, mousePosition: MousePosition) {
      console.log('🎯 开始拖拽:', dragData);
      set({
        isDragging: true,
        dragData,
        mousePosition
      });
    },

    // 更新鼠标位置
    updateMousePosition(mousePosition: MousePosition) {
      update(state => {
        if (state.isDragging) {
          return { ...state, mousePosition };
        }
        return state;
      });
    },

    // 结束拖拽
    endDrag() {
      console.log('🎯 结束拖拽');
      set({
        isDragging: false,
        dragData: null,
        mousePosition: null
      });
    }
  };
}

export const dragStore = createDragStore();
export type { DragData, MousePosition };
