/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { Ghost } from "./Ghost";
import { GUIElement } from "./GUIElement";
import { Frame } from "../base/Frame";

type EdgeType = "left" | "right";

export class FrameEdgeGhost extends Ghost {
  protected _mainFrame: Frame;
  protected _frames: Frame[];
  protected _edge: EdgeType;
  protected moveCols: number;

  constructor(model: Base, edge: EdgeType) {
    super(model);

    this._mainFrame = model as Frame;
    this._frames = [];
    if (edge === "left") {
      this._frames = model.project.selection.getLeftmostFrames();
    } else if (edge === "right") {
      this._frames = model.project.selection.getRightmostFrames();
    }

    this._edge = edge;
    this.moveCols = 0;
  }

  draw(): void {
    super.draw();

    const ctx = this.ctx;
    const mainFrame = this._mainFrame;

    // Calculate position values...
    const start = mainFrame.start - this._mainFrame.start;
    const row = mainFrame.parentLayer.index - this._mainFrame.parentLayer.index;

    this.moveCols = Math.round(this._mouseDiff.x / this.gridCellWidth);

    // Prevent 'inside out' frames
    let movePx = this._mouseDiff.x;
    this._frames.forEach((frame) => {
      const length = frame.length;

      if (this._edge === "right") {
        this.moveCols = Math.max(-length + 1, this.moveCols);
      } else if (this._edge === "left") {
        this.moveCols = Math.min(length - 1, this.moveCols);
      }
      if (this._edge === "right") {
        movePx = Math.max(movePx, this.moveCols * this.gridCellWidth);
      } else if (this._edge === "left") {
        movePx = Math.min(movePx, this.moveCols * this.gridCellWidth);
      }
    });

    this._frames.forEach((frame) => {
      const x = start * this.gridCellWidth;
      const y = row * this.gridCellHeight;
      const width = frame.length * this.gridCellWidth;
      const height = this.gridCellHeight;

      // Offset frame by it's position
      const gridDiffX = frame.start - mainFrame.start;
      const gridDiffY = frame.parentLayer.index - mainFrame.parentLayer.index;
      ctx.save();
      ctx.translate(
        gridDiffX * this.gridCellWidth,
        gridDiffY * this.gridCellHeight
      );

      // New length of frames based on mouse x,y
      // (this makes things feel more responsive)
      ctx.save();
      ctx.globalAlpha = 0.4;
      ctx.fillStyle = GUIElement.FRAME_GHOST_COLOR;
      ctx.beginPath();
      if (this._edge === "right") {
        ctx.roundRect(
          x,
          y,
          width + movePx,
          height,
          GUIElement.FRAME_BORDER_RADIUS
        );
      } else if (this._edge === "left") {
        ctx.roundRect(
          x + movePx,
          y,
          width - movePx,
          height,
          GUIElement.FRAME_BORDER_RADIUS
        );
      }
      ctx.fill();
      ctx.restore();

      // New length of frames based on grid cells moved
      // (this makes it easy to tell where frames will land)
      ctx.strokeStyle = "#00ff00";
      ctx.setLineDash([5, 5]);
      ctx.lineWidth = 3;
      ctx.beginPath();
      if (this._edge === "right") {
        ctx.roundRect(
          x,
          y,
          width + this.moveCols * this.gridCellWidth,
          height,
          GUIElement.FRAME_BORDER_RADIUS
        );
      } else if (this._edge === "left") {
        const gridMovePx = this.moveCols * this.gridCellWidth;
        ctx.roundRect(
          x + gridMovePx,
          y,
          width - gridMovePx,
          height,
          GUIElement.FRAME_BORDER_RADIUS
        );
      }
      ctx.save();
      ctx.globalAlpha = 0.8;
      ctx.stroke();
      ctx.restore();

      ctx.restore();
    });
  }

  finish(): void {
    // Move frames
    this._frames.forEach((frame) => {
      frame._originalLayer = frame.parentLayer;
      frame.remove();

      if (this._edge === "right") {
        frame.end += this.moveCols;
      } else if (this._edge === "left") {
        frame.start += this.moveCols;
      }
    });

    // Re-add frames to trigger overlap/gap cleanup
    this._frames.forEach((frame) => {
      frame._originalLayer.addFrame(frame);
      delete frame._originalLayer;
    });
  }
}
