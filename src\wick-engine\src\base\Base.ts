import { v4 as uuidv4 } from "uuid";
import { View } from "../view/View";
import * as PIXI from "pixi.js";
import { Project } from "./Project";

/**
 * 所有Wick引擎对象的基类
 */
export default class Base extends PIXI.Container {
  protected _uuid: string;
  protected _identifier: string | null;
  protected _name: string | null;
  protected _view: View | null;
  protected _project: Project | null;
  protected _children: Base[];
  protected _parent: Base | null;
  protected needsAutosave: boolean;

  /**
   * 创建一个Base对象
   * @param identifier - 对象的标识符（可选）
   * @param name - 对象的名称（可选）
   */
  constructor(
    args: { identifier?: string; name?: string; project?: any } = {}
  ) {
    super();
    this._uuid = uuidv4();
    this._identifier = args.identifier || null;
    this._name = args.name || null;
    this._view = null;
    this._project = args.project || null;
    this._children = [];
    this._parent = null;
    this.needsAutosave = true;
  }

  /**
   * 获取对象的UUID
   */
  get uuid(): string {
    return this._uuid;
  }

  /**
   * 获取对象的标识符
   */
  get identifier(): string | null {
    return this._identifier;
  }

  /**
   * 设置对象的标识符
   */
  set identifier(value: string | null) {
    this._identifier = value;
  }

  /**
   * 获取对象的名称
   */
  get name(): string | null {
    return this._name;
  }

  /**
   * 设置对象的名称
   */
  set name(value: string | null) {
    this._name = value;
  }

  /**
   * 获取对象的视图
   */
  get view(): View | null {
    return this._view;
  }

  /**
   * 设置对象的视图
   */
  set view(value: View | null) {
    this._view = value;
  }

  /**
   * 获取对象的项目
   */
  get project(): Project | null {
    return this._project;
  }

  /**
   * 获取对象的父级
   */
  get parent(): Base | null {
    return this._parent;
  }

  /**
   * 设置对象的父级
   */
  set parent(value: Base | null) {
    this._parent = value;
  }

  /**
   * 获取对象的子级列表
   */
  get children(): Base[] {
    return this._children;
  }

  /**
   * 添加子级对象
   */
  addChild(child: Base): void {
    this._children.push(child);
    child.parent = this;
  }

  /**
   * 移除子级对象
   */
  removeChild(child: Base): void {
    const index = this._children.indexOf(child);
    if (index !== -1) {
      this._children.splice(index, 1);
      child.parent = null;
    }
  }
}
