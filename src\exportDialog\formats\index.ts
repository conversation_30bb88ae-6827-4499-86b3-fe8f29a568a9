/**
 * 导出器统一入口
 */

import { exportManager } from '../exportUtils';
import { ImageExporter } from './imageExporter';
import { PlistExporter } from './plistExporter';
import { JsonArrayExporter } from './jsonArrayExporter';
import { JsonHashExporter } from './jsonHashExporter';
import { UnityExporter } from './unityExporter';
import { Phaser3Exporter } from './phaser3Exporter';
import { GameMakerExporter } from './gamemakerExporter';
import { UnrealPaper2dExporter } from './unrealPaper2dExporter';
import { GodotExporter } from './godotExporter';
import { DefoldExporter } from './defoldExporter';
import { LibGDXExporter } from './libgdxExporter';
import { XmlGenericExporter } from './xmlGenericExporter';
import { PixiExporter } from './pixiExporter';

// 注册所有导出器
export function registerExporters() {
  exportManager.registerExporter('cropped-images', new ImageExporter());
  exportManager.registerExporter('plist', new PlistExporter());
  exportManager.registerExporter('json-array', new JsonArrayExporter());
  exportManager.registerExporter('json-hash', new JsonHashExporter());
  exportManager.registerExporter('unity', new UnityExporter());
  exportManager.registerExporter('phaser3', new Phaser3Exporter());
  exportManager.registerExporter('gamemaker', new GameMakerExporter());
  exportManager.registerExporter('unreal-paper2d', new UnrealPaper2dExporter());
  exportManager.registerExporter('godot', new GodotExporter());
  exportManager.registerExporter('defold', new DefoldExporter());
  exportManager.registerExporter('libgdx', new LibGDXExporter());
  exportManager.registerExporter('xml-generic', new XmlGenericExporter());
  exportManager.registerExporter('pixi', new PixiExporter());

  console.log('✅ 导出器注册完成:', exportManager.getAvailableExporters());
}

// 导出类型配置
export const EXPORT_TYPES = {
  'cropped-images': {
    name: '导出裁切图片',
    description: '将所有裁切区域导出为独立的图片文件',
    icon: '🖼️',
    formats: ['png', 'jpg', 'webp'],
    defaultFormat: 'png'
  },
  'plist': {
    name: 'Cocos Creator (plist)',
    description: 'Cocos Creator/cocos2d-x兼容的plist格式',
    icon: '🎮',
    formats: ['xml'],
    defaultFormat: 'xml'
  },
  'json-array': {
    name: 'JSON Array',
    description: '通用JSON数组格式，适用于多种游戏引擎',
    icon: '📊',
    formats: ['json'],
    defaultFormat: 'json'
  },
  'json-hash': {
    name: 'JSON Hash',
    description: '通用JSON对象格式，便于快速查找',
    icon: '🔍',
    formats: ['json'],
    defaultFormat: 'json'
  },
  'unity': {
    name: 'Unity Texture2D',
    description: 'Unity游戏引擎专用格式',
    icon: '🔷',
    formats: ['tpsheet'],
    defaultFormat: 'tpsheet'
  },
  'phaser3': {
    name: 'Phaser 3',
    description: 'Phaser 3 HTML5游戏框架格式',
    icon: '🌐',
    formats: ['json'],
    defaultFormat: 'json'
  },
  'gamemaker': {
    name: 'GameMaker Studio',
    description: 'GameMaker Studio游戏引擎格式',
    icon: '🎯',
    formats: ['json'],
    defaultFormat: 'json'
  },
  'unreal-paper2d': {
    name: 'Unreal Paper2D',
    description: 'Unreal Engine Paper2D系统格式',
    icon: '🚀',
    formats: ['json'],
    defaultFormat: 'json'
  },
  'godot': {
    name: 'Godot SpriteSheet',
    description: 'Godot游戏引擎精灵表格式',
    icon: '🤖',
    formats: ['json'],
    defaultFormat: 'json'
  },
  'defold': {
    name: 'Defold',
    description: 'Defold游戏引擎格式',
    icon: '💎',
    formats: ['tpinfo'],
    defaultFormat: 'tpinfo'
  },
  'libgdx': {
    name: 'LibGDX',
    description: 'LibGDX Java游戏框架格式',
    icon: '☕',
    formats: ['atlas'],
    defaultFormat: 'atlas'
  },
  'xml-generic': {
    name: 'XML Generic',
    description: '通用XML格式，便于自定义解析',
    icon: '📄',
    formats: ['xml'],
    defaultFormat: 'xml'
  },
  'pixi': {
    name: 'Pixi.js',
    description: 'Pixi.js HTML5渲染引擎格式',
    icon: '🎨',
    formats: ['json'],
    defaultFormat: 'json'
  }
} as const;

export type ExportTypeKey = keyof typeof EXPORT_TYPES;

// 导出所有相关类型和工具
export * from './imageExporter';
export * from './plistExporter';
export * from './jsonArrayExporter';
export * from './jsonHashExporter';
export * from './unityExporter';
export * from './phaser3Exporter';
export * from './gamemakerExporter';
export * from './unrealPaper2dExporter';
export * from './godotExporter';
export * from './defoldExporter';
export * from './libgdxExporter';
export * from './xmlGenericExporter';
export * from './pixiExporter';
export { exportManager } from '../exportUtils';
