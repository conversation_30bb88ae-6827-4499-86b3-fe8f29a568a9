<script lang="ts">
  /**
   * AtlasItem 组件 - 图集合并操作的主组件
   * 手风琴风格，可展开显示子集图片，支持导出功能
   */

  import type { ResourceItem, AtlasResource, ImageResource } from '../types/imageType';
  import type { AddResourceResult } from '../stores/atlasStore';
  import Image from '../components/ui/Image.svelte';
  import DeleteButton from '../components/ui/DeleteButton.svelte';
  import DeleteConfirmDialog from '../components/ui/DeleteConfirmDialog.svelte';
  import Toast from '../components/ui/Toast.svelte';
  import { dragStore } from '../stores/dragStore';
  import { atlasStore } from '../stores/atlasStore';

  import { debounce } from '../utils/debounce';

  // 导入国际化
  import { _ } from '../lib/i18n';

  interface Props {
    atlas: AtlasResource;
    onToggle?: (atlasId: string) => void;
    onRemoveChild?: (atlasId: string, childId: string) => void;
    onDelete?: (atlasId: string) => void;
  }

  let {
    atlas,
    onToggle,
    onRemoveChild,
    onDelete
  }: Props = $props();

  // 删除确认对话框状态
  let showDeleteConfirm = $state(false);

  // 拖拽释放状态
  let isDragOver = $state(false);

  // 🎯 防重复删除标志
  let isDeleting = $state(false);

  // 🎯 Toast提示状态
  let toastState = $state<{
    show: boolean;
    type: 'info' | 'success' | 'warning' | 'error';
    title?: string;
    message: string;
  }>({
    show: false,
    type: 'info',
    message: ''
  });

  // 🎯 拖拽操作状态管理
  let dragOperationState = $state<{
    isProcessing: boolean;
    progress?: number;
    error?: string;
    operation?: 'adding' | 'creating';
  }>({ isProcessing: false });

  // 🎯 简化的状态管理 - 不监听atlasStore，直接使用props的atlas
  let currentDragState = $state<any>({ isDragging: false, dragData: null, mousePosition: null });

  // 🎯 计算属性 - 基于props的atlas
  const childrenCount = $derived(atlas.children?.length || 0);
  const isEmpty = $derived(childrenCount === 0);
  const isExpanded = $derived(atlas.isExpanded || false);

  // 🎯 防抖的自动删除检查
  const debouncedDeleteCheck = debounce(() => {
    if (isEmpty && !isDeleting) {
      console.log('🗑️ AtlasItem: 图集为空，准备自动删除', atlas.name);
      checkAndDeleteIfEmpty();
    }
  }, 500); // 500ms防抖

  // 🎯 简化的effect - 监听dragStore
  $effect(() => {
    const unsubscribeDrag = dragStore.subscribe((state) => {
      currentDragState = state;
    });

    // 自动清理订阅
    return () => {
      unsubscribeDrag();
      debouncedDeleteCheck.cancel(); // 取消待执行的删除检查
    };
  });

  // 🎯 保留getCurrentAtlas函数用于向后兼容
  function getCurrentAtlas() {
    return atlas;
  }

  // 🎯 显示Toast提示
  function showToast(type: 'info' | 'success' | 'warning' | 'error', message: string, title?: string) {
    toastState = {
      show: true,
      type,
      message,
      title
    };
  }

  // 🎯 关闭Toast提示
  function closeToast() {
    toastState = {
      ...toastState,
      show: false
    };
  }

  // 🎯 检查图集是否为空，如果为空则自动删除
  function checkAndDeleteIfEmpty() {
    // 🎯 防重复删除检查
    if (isDeleting) {
      console.log('🔄 AtlasItem: 图集正在删除中，跳过重复删除检查');
      return;
    }

    const currentAtlas = getCurrentAtlas();
    if (currentAtlas.children.length === 0) {
      console.log('🗑️ AtlasItem: 图集为空，自动删除', currentAtlas.name);

      // 🎯 设置删除标志，防止重复删除
      isDeleting = true;

      // 延迟删除，确保UI更新完成
      setTimeout(() => {
        onDelete?.(atlas.id);
      }, 100);
    }
  }

  // 🎯 切换展开/收起状态
  function toggleExpanded() {
    console.log('🎯 AtlasItem: 点击展开图标，切换展开状态', atlas.name);

    // 调用LeftPanel的回调来更新atlasStore
    // isExpanded状态会通过$effect自动同步
    onToggle?.(atlas.id);
  }

  // 🎯 选中图集 - 设置到atlasStore，同时清空resourceStore的选择
  async function selectAtlasOnly() {
    console.log('🎯 AtlasItem: 点击图集信息，选中图集', atlas.name);
    // 设置到atlasStore的selectedAtlas（会自动清理selectedResource）
    await atlasStore.selectAtlas(atlas);
  }

  // 键盘事件处理
  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      toggleExpanded();
    }
  }



  // 显示删除确认对话框
  function handleDelete(event: MouseEvent) {
    event.stopPropagation(); // 阻止事件冒泡
    console.log('🗑️ AtlasItem: 点击删除按钮', atlas.name);
    showDeleteConfirm = true;
  }

  // 确认删除
  function confirmDelete() {
    console.log('✅ AtlasItem: 确认删除图集', atlas.name);
    showDeleteConfirm = false;

    // 🎯 先更新全局数据，LeftPanel.handleDeleteAtlas会调用atlasStore.deleteAtlas
    // atlasStore.deleteAtlas内部已经处理了selectedAtlas的清理
    onDelete?.(atlas.id);
  }

  // 取消删除
  function cancelDelete() {
    console.log('❌ AtlasItem: 取消删除图集', atlas.name);
    showDeleteConfirm = false;
  }

  // 移除子项
  function handleRemoveChild(childId: string, event: MouseEvent) {
    event.stopPropagation(); // 阻止事件冒泡

    console.log('🗑️ AtlasItem: 移除子项', {
      atlasId: atlas.id,
      atlasName: atlas.name,
      childId
    });

    // 🎯 先更新全局数据，LeftPanel.handleRemoveChild会调用atlasStore.removeResourceFromAtlas
    onRemoveChild?.(atlas.id, childId);

    // 🎯 删除成功后，检查图集是否为空
    setTimeout(() => {
      const updatedAtlas = atlasStore.getAtlas(atlas.id);
      if (updatedAtlas) {
        if (updatedAtlas.children.length === 0) {
          // 图集为空，自动删除
          checkAndDeleteIfEmpty();
        } else {
          // 图集不为空，重新选中以触发canvas刷新
          atlasStore.selectAtlas(updatedAtlas);
          console.log('🔄 AtlasItem: 删除子项后重新选中图集', updatedAtlas.name);
        }
      }
    }, 50);
  }

  // 鼠标事件处理拖拽悬停和释放
  function handleMouseEnter() {
    // 只有在拖拽状态下才显示悬停效果
    if (currentDragState.isDragging) {
      isDragOver = true;
      console.log('🎯 AtlasItem: 鼠标进入拖拽区域', atlas.name);
    }
  }

  function handleMouseLeave() {
    isDragOver = false;
  }

  async function handleMouseUp(_event: MouseEvent) {
    if (isDragOver && currentDragState.isDragging && currentDragState.dragData) {
      const dragData = currentDragState.dragData;

      console.log('🎯 AtlasItem: 接收拖拽释放', {
        atlasId: atlas.id,
        atlasName: atlas.name,
        dragType: dragData.type,
        hasCropArea: !!dragData.cropArea,
        resourceName: dragData.name
      });

      let success = false;

      // 🎯 处理裁切图片的拖拽 - 优化用户反馈
      if (dragData.cropArea && dragData.src) {
        console.log('🎯 AtlasItem: 处理裁切图片拖拽', {
          cropAreaName: dragData.cropArea.name,
          cropSize: `${dragData.cropArea.width}×${dragData.cropArea.height}`,
          originalResource: dragData.resource?.name
        });

        // 🎯 设置处理状态
        dragOperationState = {
          isProcessing: true,
          operation: 'adding',
          progress: 0
        };

        // 创建裁切图片的资源对象 - 异步处理
        createCroppedResource(dragData).then(croppedResource => {
          dragOperationState.progress = 50;

          if (croppedResource) {
            const addResult = atlasStore.addResourceToAtlas(atlas.id, croppedResource);
            if (addResult.success) {
              console.log('✅ AtlasItem: 成功添加裁切资源到图集');
              dragOperationState = { isProcessing: false };

              // 🎯 不再需要预加载，直接使用ProcessedImageData
            } else {
              console.log('❌ AtlasItem: 添加裁切资源失败', addResult.reason);

              // 🎯 处理不同的失败原因
              if (addResult.reason === 'duplicate') {
                showToast('warning', $_('atlas.resourceExistsMessage', { values: { name: croppedResource.name } }), $_('atlas.resourceExists'));
              } else {
                dragOperationState = {
                  isProcessing: false,
                  error: addResult.message || $_('error.exportFailed')
                };
              }
            }
          } else {
            dragOperationState = {
              isProcessing: false,
              error: $_('crop.failed')
            };
          }
        }).catch(error => {
          console.error('❌ AtlasItem: 创建裁切资源失败', error);
          dragOperationState = {
            isProcessing: false,
            error: `${$_('crop.failed')}: ${error.message}`
          };
        });

        // 🎯 对于裁切小图片的异步操作，不设置success=true，避免触发选择状态设置
        // success保持false，这样不会进入后续的选择状态设置逻辑
        console.log('🎯 AtlasItem: 裁切小图片异步处理，不设置success标志');
      }
      // 🎯 处理完整图片的拖拽
      else if (dragData.resource) {
        console.log('🎯 AtlasItem: 处理完整图片拖拽', {
          resourceId: dragData.resource.id,
          resourceName: dragData.resource.name,
          resourceType: dragData.resource.type,
          hasData: !!dragData.resource.data,
          dataSize: dragData.resource.data?.byteLength || 0,
          atlasId: atlas.id,
          atlasName: atlas.name,
          currentChildrenCount: atlas.children.length
        });

        const addResult = atlasStore.addResourceToAtlas(atlas.id, dragData.resource);
        success = addResult.success;

        console.log('🔍 AtlasItem: addResourceToAtlas 调用结果', {
          success: addResult.success,
          reason: addResult.reason,
          atlasId: atlas.id,
          resourceId: dragData.resource.id,
          resourceName: dragData.resource.name
        });

        // 🎯 处理重复资源的情况
        if (!addResult.success && addResult.reason === 'duplicate') {
          showToast('warning', $_('atlas.resourceExistsMessage', { values: { name: dragData.resource.name } }), $_('atlas.resourceExists'));
        } else if (!addResult.success) {
          console.error('❌ AtlasItem: 添加资源失败', addResult.message);
          showToast('error', addResult.message || $_('error.exportFailed'));
        }
      } else {
        console.warn('⚠️ AtlasItem: 无效的拖拽数据', {
          hasCropArea: !!dragData.cropArea,
          hasSrc: !!dragData.src,
          hasResource: !!dragData.resource,
          dragData
        });
      }

      if (success) {
        console.log('✅ AtlasItem: 成功添加资源到图集');

        // 🎯 根据拖拽的资源类型决定是否设置选中状态
        if (dragData.resource) {
          if (dragData.resource.type === 'image') {
            // ImageResource拖拽：设置atlasStore选中状态
            console.log('🎯 AtlasItem: ImageResource拖拽，设置图集选中状态');
            setTimeout(async () => {
              const updatedAtlas = atlasStore.getAtlas(atlas.id);
              if (updatedAtlas) {
                await atlasStore.selectAtlas(updatedAtlas);
                console.log('🔄 AtlasItem: 重新选中图集', updatedAtlas.name);
              }
            }, 50);
          } else if (dragData.resource.type === 'atlas') {
            // AtlasResource拖拽：只设置atlasStore，不设置当前选中对象
            console.log('🎯 AtlasItem: AtlasResource拖拽，只设置atlasStore');
            await atlasStore.selectAtlas(atlas);
            // 不调用selectAtlas，保持当前选中状态
          }
        } else {
          // 裁切图片：检查是否为裁切图像资源，如果是则不更新canvas
          const isCroppedResource = dragData.cropArea && dragData.src;

          if (!isCroppedResource) {
            // 🎯 只有非裁切资源才触发canvas刷新
            console.log('🎯 AtlasItem: 非裁切资源，设置图集选中状态');
            setTimeout(() => {
              const updatedAtlas = atlasStore.getAtlas(atlas.id);
              if (updatedAtlas) {
                atlasStore.selectAtlas(updatedAtlas);
                console.log('🔄 AtlasItem: 重新选中图集', updatedAtlas.name);
              }
            }, 50);
          } else {
            console.log('🎯 AtlasItem: 裁切图像资源已添加，跳过canvas更新');
          }
        }
      } else {
        console.log('❌ AtlasItem: 添加资源失败');
      }

      // 结束拖拽状态
      dragStore.endDrag();
    }

    isDragOver = false;
  }

  // 🎯 创建裁切图片的资源对象 - 修复Blob URL处理
  async function createCroppedResource(dragData: any): Promise<ResourceItem | null> {
    if (!dragData.cropArea || !dragData.src) {
      console.warn('❌ AtlasItem: 创建裁切资源失败 - 缺少必要数据', {
        hasCropArea: !!dragData.cropArea,
        hasSrc: !!dragData.src,
        hasResource: !!dragData.resource
      });
      return null;
    }

    // 🎯 如果dragData.resource存在且有processedData，直接使用它（cropAreaSelector传递的完整ImageResource）
    if (dragData.resource && dragData.resource.processedData) {
      console.log('✅ AtlasItem: 使用cropAreaSelector传递的完整ImageResource', {
        resourceId: dragData.resource.id,
        resourceName: dragData.resource.name,
        hasProcessedData: !!dragData.resource.processedData
      });
      return dragData.resource;
    }

    try {
      let bytes: Uint8Array;

      // 🎯 检查src是Data URL还是Blob URL
      if (dragData.src.startsWith('data:')) {
        // Data URL格式：data:image/png;base64,xxxxx
        console.log('🔧 AtlasItem: 处理Data URL格式');
        const base64Data = dragData.src.split(',')[1];
        if (!base64Data) {
          throw new Error('无效的Data URL格式');
        }
        const binaryString = atob(base64Data);
        bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
      } else if (dragData.src.startsWith('blob:')) {
        // Blob URL格式：blob:http://localhost:1420/xxxxx
        console.log('🔧 AtlasItem: 处理Blob URL格式');
        const response = await fetch(dragData.src);
        if (!response.ok) {
          throw new Error(`获取Blob数据失败: ${response.status}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        bytes = new Uint8Array(arrayBuffer);
      } else {
        throw new Error(`不支持的URL格式: ${dragData.src.substring(0, 50)}...`);
      }

      // 创建裁切图片的资源对象
      const croppedResource: ImageResource = {
        id: `cropped_${dragData.cropArea.id}_${Date.now()}`,
        name: dragData.cropArea.name,
        type: 'image',
        path: `${dragData.resource?.path || 'unknown'}_cropped_${dragData.cropArea.name}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isLoaded: true,
        data: bytes.buffer as ArrayBuffer,
        width: dragData.cropArea.width,
        height: dragData.cropArea.height,
        originalFile: {
          name: dragData.cropArea.name,
          size: bytes.length,
          lastModified: Date.now(),
          type: 'image/png'
        },
        // 🎯 添加processedData，使用dragData.src作为预览
        processedData: {
          original: {
            width: dragData.cropArea.width,
            height: dragData.cropArea.height,
            dataUrl: dragData.src
          },
          preview: {
            width: dragData.cropArea.width,
            height: dragData.cropArea.height,
            dataUrl: dragData.src,
            size: Math.min(dragData.cropArea.width, dragData.cropArea.height, 256)
          },
          thumbnail: {
            width: dragData.cropArea.width,
            height: dragData.cropArea.height,
            dataUrl: dragData.src,
            size: Math.min(dragData.cropArea.width, dragData.cropArea.height, 128)
          },
          processedAt: new Date().toISOString()
        },
        // 🎯 使用 splitInfo 存储裁切相关信息
        splitInfo: {
          parentId: dragData.resource?.id || 'unknown',
          region: {
            x: dragData.cropArea.x,
            y: dragData.cropArea.y,
            width: dragData.cropArea.width,
            height: dragData.cropArea.height
          },
          splitMethod: 'manual'
        }
      };

      console.log('✅ AtlasItem: 创建裁切资源对象', {
        croppedId: croppedResource.id,
        croppedName: croppedResource.name,
        croppedSize: `${croppedResource.width}×${croppedResource.height}`,
        dataSize: bytes.length,
        originalResource: dragData.resource?.name || 'unknown'
      });

      return croppedResource;
    } catch (error) {
      console.error('❌ AtlasItem: 创建裁切资源失败', error);
      return null;
    }
  }



  // 🎯 直接使用ProcessedImageData的预览图
  function createImageSrc(resource: ResourceItem): string {
    if (resource.type !== 'image') {
      return '';
    }

    // 🎯 优先使用ProcessedImageData中的预览图
    if (resource.processedData?.preview?.dataUrl) {
      return resource.processedData.preview.dataUrl;
    }

    // 🎯 如果没有预览图，使用缩略图
    if (resource.processedData?.thumbnail?.dataUrl) {
      return resource.processedData.thumbnail.dataUrl;
    }

    // 🎯 如果没有缩略图，使用原图
    if (resource.processedData?.original?.dataUrl) {
      return resource.processedData.original.dataUrl;
    }

    // 🎯 如果没有ProcessedImageData，返回空字符串
    console.warn('⚠️ AtlasItem: 无法获取图片预览，缺少ProcessedImageData', {
      resourceId: resource.id,
      resourceName: resource.name,
      hasProcessedData: !!resource.processedData
    });

    return '';
  }

  // 🎯 不再需要预加载逻辑，直接使用ProcessedImageData

  // 🎯 自动清理错误状态
  $effect(() => {
    if (dragOperationState.error) {
      const timer = setTimeout(() => {
        dragOperationState = { isProcessing: false };
      }, 5000); // 5秒后自动清理错误状态

      return () => clearTimeout(timer);
    }
  });
</script>

<div
  class="atlas-item"
  class:expanded={isExpanded}
  class:drag-over={isDragOver}
  onmouseenter={handleMouseEnter}
  onmouseleave={handleMouseLeave}
  onmouseup={handleMouseUp}
  role="button"
  tabindex="0"
  aria-label={$_('atlas.itemDragArea')}
>
  <!-- 图集头部 -->
  <div class="atlas-header">
    <div class="header-left">
      <button
        class="expand-icon"
        class:expanded={isExpanded}
        onclick={toggleExpanded}
        onkeydown={handleKeyDown}
        title={isExpanded ? $_('ui.collapse') : $_('ui.expand')}
        aria-label={isExpanded ? $_('ui.collapse') : $_('ui.expand')}
        aria-expanded={isExpanded}
      >
        {isExpanded ? '▼' : '▶'}
      </button>
      <button
        class="atlas-info"
        onclick={selectAtlasOnly}
        onkeydown={(e) => e.key === 'Enter' && selectAtlasOnly()}
        title={$_('atlas.selectAtlas')}
        aria-label={$_('atlas.selectAtlas')}
      >
        <div class="atlas-name" title={getCurrentAtlas().name}>{getCurrentAtlas().name}</div>

        <!-- 🎯 拖拽操作状态指示器 -->
        {#if dragOperationState.error}
          <div class="operation-error">
            <span class="error-icon">❌</span>
            <span class="error-text">{dragOperationState.error}</span>
          </div>
        {/if}

      </button>
    </div>

    <div class="header-actions">
      <button
        class="delete-btn"
        onclick={handleDelete}
        title={$_('atlas.delete')}
        aria-label={$_('atlas.delete')}
      >
        🗑️
      </button>
    </div>
  </div>

  <!-- 子集图片列表 -->
  {#if isExpanded}
    <div class="children-container">
      {#if getCurrentAtlas().children.length > 0}
        <div class="children-grid">
          {#each getCurrentAtlas().children as child (child.id)}
            <div class="child-item">
              <div class="child-image">
                {#if createImageSrc(child)}
                  <Image
                    src={createImageSrc(child)}
                    alt={child.name}
                    width="100%"
                    height="100%"
                    fit="contain"
                  />
                {:else}
                  <div class="placeholder">
                    <span>📷</span>
                  </div>
                {/if}

                <!-- 🎯 叠加的文字信息 -->
                <div class="child-info">
                  <div class="child-name" title={child.name}>{child.name}</div>
                  {#if child.width && child.height}
                    <div class="child-size">{child.width}×{child.height}</div>
                  {/if}
                </div>
              </div>

              <DeleteButton
                onclick={(e) => handleRemoveChild(child.id, e)}
                title={$_('actions.delete')}
                ariaLabel={$_('actions.delete')}
                size="small"
                variant="overlay"
              />
            </div>
          {/each}
        </div>
      {:else}
        <div class="empty-state" class:drag-over={isDragOver}>
          <div class="empty-icon">📁</div>
          <div class="empty-text">{$_('resource.noResources')}</div>
          <div class="empty-hint">{$_('atlas.dragToAdd')}</div>
        </div>
      {/if}
    </div>
  {/if}
</div>

<!-- 删除确认对话框 -->
<DeleteConfirmDialog
  show={showDeleteConfirm}
  message={$_('atlas.deleteConfirm', { values: { name: getCurrentAtlas().name } })}
  warning={$_('dialog.deleteWarning')}
  onConfirm={confirmDelete}
  onCancel={cancelDelete}
/>

<!-- Toast提示 -->
<Toast
  show={toastState.show}
  type={toastState.type}
  title={toastState.title}
  message={toastState.message}
  onclose={closeToast}
/>

<style>
  .atlas-item {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius-large);
    overflow: hidden;
    transition: var(--transition-base);
    margin-bottom: var(--spacing-2);
  }

  .atlas-item:hover {
    border-color: var(--theme-border-dark);
    box-shadow: 0 2px 8px var(--theme-shadow-light);
  }

  .atlas-item.expanded {
    border-color: var(--theme-primary);
  }

  .atlas-item.drag-over {
    border-color: var(--theme-primary);
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.1) 0%,
      rgba(147, 197, 253, 0.1) 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    transform: scale(1.02);
  }

  /* 图集头部 */
  .atlas-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-2) var(--spacing-3);
    cursor: pointer;
    transition: var(--transition-base);
    background: var(--theme-surface-light);
  }

  .atlas-header:hover {
    background: var(--theme-surface);
  }

  .header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    flex: 1;
    min-width: 0;
  }

  .expand-icon {
    font-size: 0.8rem;
    color: var(--theme-text-secondary);
    transition: all 0.2s ease;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: transparent;
    border: none; /* 🎯 移除外边框 */
    outline: none; /* 🎯 移除轮廓 */
    border-radius: 4px;
  }

  .expand-icon:hover {
    background: var(--theme-surface-light);
    color: var(--theme-text);
    transform: scale(1.1);
  }

  .expand-icon:focus {
    outline: none; /* 🎯 移除默认轮廓 */
    box-shadow: 0 0 0 2px var(--theme-primary); /* 🎯 使用box-shadow代替outline */
  }

  .expand-icon.expanded {
    transform: rotate(0deg);
  }

  .expand-icon.expanded:hover {
    transform: rotate(0deg) scale(1.1);
  }

  .atlas-info {
    flex: 1;
    min-width: 0;
    background: transparent;
    border: none; /* 🎯 移除外边框 */
    outline: none; /* 🎯 移除轮廓 */
    text-align: left;
    cursor: pointer;
    padding: 0;
    transition: all 0.2s ease;
  }

  .atlas-info:hover {
    opacity: 0.8;
  }

  .atlas-info:focus {
    outline: none; /* 🎯 移除默认轮廓 */
    box-shadow: 0 0 0 2px var(--theme-primary); /* 🎯 使用box-shadow代替outline */
    border-radius: var(--border-radius);
  }

  .atlas-name {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--theme-text);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }



  .header-actions {
    display: flex;
    gap: var(--spacing-1);
    align-items: center;
  }

  .delete-btn {
    border: none; /* 🎯 移除外边框 */
    outline: none; /* 🎯 移除轮廓 */
    border-radius: 6px;
    padding: 4px 6px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  .delete-btn:hover {
    background: rgba(239, 68, 68, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
  }



  /* 子集容器 */
  .children-container {
    padding: var(--spacing-3);
    border-top: 1px solid var(--theme-border);
    background: var(--theme-surface);
    animation: slideDown 0.3s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      max-height: 0;
      padding-top: 0;
      padding-bottom: 0;
    }
    to {
      opacity: 1;
      max-height: 500px;
      padding-top: var(--spacing-3);
      padding-bottom: var(--spacing-3);
    }
  }

  .children-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.5rem;
  }

  .child-item {
    position: relative;
    display: flex;
    flex-direction: column;
    border: 2px solid var(--theme-border);
    border-radius: 6px;
    background: var(--theme-surface);
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
  }

  .child-item:hover {
    border-color: var(--theme-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .child-image {
    width: 100%;
    height: 70px;
    overflow: hidden;
    background: var(--theme-surface-light);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--theme-text-secondary);
    opacity: 0.5;
  }

  .child-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.3), transparent);
    color: white;
    padding: 0.25rem 0.375rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    pointer-events: none;
  }

  .child-name {
    font-size: 0.65rem;
    font-weight: 500;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    flex: 1;
    margin-right: 0.25rem;
  }

  .child-size {
    font-size: 0.6rem;
    color: rgba(255, 255, 255, 0.9);
    font-family: monospace;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    flex-shrink: 0;
  }



  /* 空状态 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-6);
    color: var(--theme-text-secondary);
    text-align: center;
  }

  .empty-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-2);
    opacity: 0.5;
  }

  .empty-text {
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--spacing-1);
  }

  .empty-hint {
    font-size: var(--font-size-xs);
    opacity: 0.7;
  }

  /* 🎯 拖拽操作状态指示器样式 */
  .operation-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    margin-top: var(--spacing-1);
    padding: var(--spacing-1) var(--spacing-2);
    background: rgba(59, 130, 246, 0.1);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    color: var(--theme-primary);
  }

  .loading-spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .status-text {
    font-weight: 500;
  }

  .progress-bar {
    flex: 1;
    height: 4px;
    background: rgba(59, 130, 246, 0.2);
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background: var(--theme-primary);
    transition: width 0.3s ease;
  }

  .operation-error {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    margin-top: var(--spacing-1);
    padding: var(--spacing-1) var(--spacing-2);
    background: rgba(239, 68, 68, 0.1);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    color: var(--theme-error, #ef4444);
  }

  .error-text {
    font-weight: 500;
  }

</style>
