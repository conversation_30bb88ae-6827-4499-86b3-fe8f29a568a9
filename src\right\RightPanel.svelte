<script lang="ts">
  /**
   * 右侧面板组件
   * 根据当前选中的对象类型智能显示相应的面板
   */

  import SplitPanel from './SplitPanel.svelte';
  import AtlasMergeSettings from './AtlasMergeSettings.svelte';
  import { resourceStore } from '../stores/resourceStore';
  import { atlasStore } from '../stores/atlasStore';
  import type { ResourceItem, AtlasResource } from '../types/imageType';

  // 导入国际化
  import { _ } from '../lib/i18n';

  // 🎯 简化的Props接口，暂时不需要额外参数
  interface Props {}

  let {}: Props = $props();

  // 🎯 监听resourceStore中的selectedResource
  let currentSelectedResource = $state<ResourceItem | null>(null);

  // 🎯 监听atlasStore中的selectedAtlas
  let currentSelectedAtlas = $state<AtlasResource | null>(null);

  // 🎯 使用$effect监听resourceStore的selectedResource变化
  $effect(() => {
    const unsubscribeResource = resourceStore.subscribe((state) => {
      const newResource = state.selectedResource;

      // 🎯 使用ID和updatedAt来判断是否需要更新，避免Proxy比较问题
      const shouldUpdate =
        currentSelectedResource?.id !== newResource?.id ||
        (currentSelectedResource as any)?.updatedAt !== (newResource as any)?.updatedAt;

      if (shouldUpdate) {
        console.log('🎯 RightPanel: selectedResource变化', {
          oldResource: currentSelectedResource?.name,
          newResource: newResource?.name,
          resourceType: newResource?.type,
          idChanged: currentSelectedResource?.id !== newResource?.id,
          updatedAtChanged: (currentSelectedResource as any)?.updatedAt !== (newResource as any)?.updatedAt
        });

        currentSelectedResource = newResource;
      }
    });

    const unsubscribeAtlas = atlasStore.subscribe((state) => {
      const newAtlas = state.selectedAtlas;

      // 🎯 使用ID和updatedAt来判断是否需要更新
      const shouldUpdate =
        currentSelectedAtlas?.id !== newAtlas?.id ||
        (currentSelectedAtlas as any)?.updatedAt !== (newAtlas as any)?.updatedAt;

      if (shouldUpdate) {
        console.log('🎯 RightPanel: selectedAtlas变化', {
          oldAtlas: currentSelectedAtlas?.name,
          newAtlas: newAtlas?.name,
          idChanged: currentSelectedAtlas?.id !== newAtlas?.id,
          updatedAtChanged: (currentSelectedAtlas as any)?.updatedAt !== (newAtlas as any)?.updatedAt
        });

        currentSelectedAtlas = newAtlas;
      }
    });

    return () => {
      unsubscribeResource();
      unsubscribeAtlas();
    };
  });

  // 🎯 计算当前应该显示的面板类型
  const currentPanelType = $derived(() => {
    if (currentSelectedAtlas) {
      return 'atlas';
    }
    if (currentSelectedResource) {
      return 'resource';
    }
    return 'none';
  });


</script>

<div class="right-panel">
  <!-- 🎯 根据选中对象类型智能显示面板 -->
  <div class="panel-content">
    <div class="scrollable-content">
      {#if currentPanelType() === 'atlas'}
        <!-- 选中AtlasResource时显示合并设置面板 -->
        <div class="panel-section">
          <div class="panel-header">
            <div class="panel-title">
              <span class="panel-icon">🔧</span>
              <span class="panel-name">{$_('atlas.mergeSettings')}</span>
            </div>
            <div class="panel-description">
              {$_('atlas.selectAtlas')}: {currentSelectedAtlas?.name || $_('atlas.noAtlas')}
            </div>
          </div>
          <div class="panel-body">
            {#if currentSelectedAtlas}
              <AtlasMergeSettings atlas={currentSelectedAtlas} />
            {/if}
          </div>
        </div>
      {:else if currentPanelType() === 'resource'}
        <!-- 选中ResourceItem时显示拆分面板 -->
        <div class="panel-section">
          <div class="panel-header">
            <div class="panel-title">
              <span class="panel-icon">🔧</span>
              <span class="panel-name">{$_('resource.title')}</span>
            </div>
            <div class="panel-description">
              {$_('resource.selectResource')}: {currentSelectedResource?.name || $_('resource.noResources')}
            </div>
          </div>
          <div class="panel-body">
            {#if currentSelectedResource}
              <SplitPanel currentResource={currentSelectedResource} />
            {/if}
          </div>
        </div>
      {:else}
        <!-- 没有选中任何对象时不显示任何内容 -->
        <div class="empty-state">
          <div class="empty-icon">📋</div>
          <div class="empty-text">{$_('ui.selectAtlasOrResource')}</div>
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .right-panel {
    height: 100%;
    background: var(--theme-surface);
    color: var(--theme-text);
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  /* 面板内容样式 */
  .panel-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  /* 🎯 滚动容器样式 */
  .scrollable-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
  }

  /* 🎯 面板区块样式 */
  .panel-section {
    flex-shrink: 0;
    border-bottom: 1px solid var(--theme-border);
  }

  .panel-section:last-child {
    border-bottom: none;
  }

  .panel-body {
    /* 让子组件自己控制样式 */
    display: flex;
    flex-direction: column;
  }

  /* 🎯 滚动条样式优化 */
  .scrollable-content::-webkit-scrollbar {
    width: 8px;
  }

  .scrollable-content::-webkit-scrollbar-track {
    background: var(--theme-surface);
    border-radius: 4px;
  }

  .scrollable-content::-webkit-scrollbar-thumb {
    background: var(--theme-border);
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  .scrollable-content::-webkit-scrollbar-thumb:hover {
    background: var(--theme-text-secondary);
  }

  /* 🎯 确保滚动平滑 */
  .scrollable-content {
    scroll-behavior: smooth;
  }

  /* 面板头部样式 */
  .panel-header {
    padding: 1rem;
    background: var(--theme-background);
    border-bottom: 1px solid var(--theme-border);
  }

  .panel-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .panel-icon {
    font-size: 1.2rem;
    line-height: 1;
  }

  .panel-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--theme-text);
  }

  .panel-description {
    font-size: 0.875rem;
    color: var(--theme-text-secondary);
    margin: 0;
  }



  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
  }

  /* 🎯 移除未使用的空状态样式，现在总是显示内容 */

  /* 确保子组件填满容器 */
  .panel-content :global(.split-panel),
  .panel-content :global(.merge-panel) {
    flex: 1;
    overflow: hidden;
  }

  /* 空状态样式 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    color: var(--theme-text-secondary);
    text-align: center;
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 1rem;
    font-weight: 500;
  }
</style>
