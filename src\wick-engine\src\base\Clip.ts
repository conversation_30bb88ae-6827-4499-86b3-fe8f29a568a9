/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Tickable } from "./Tickable";
import { Timeline } from "./Timeline";
import { Layer } from "./Layer";
import { Frame } from "./Frame";
import { Transformation } from "../Transformation";

/**
 * 表示一个Wick剪辑
 */
export class Clip extends Tickable {
  /**
   * 返回此对象的所有可能动画类型列表
   */
  static get animationTypes(): { [key: string]: string } {
    return {
      loop: "Loop",
      single: "Single Frame",
      playOnce: "Play Once",
    };
  }

  protected timeline: Timeline;
  protected _animationType: string;
  protected _singleFrameNumber: number;
  protected _playedOnce: boolean;
  protected _isSynced: boolean;
  protected _transformation: Transformation;
  protected _isClone: boolean;
  protected _sourceClipUUID: string | null;
  protected _assetSourceUUID: string | null;
  protected _clones: Clip[];

  /**
   * 创建一个新的剪辑
   * @param args - 剪辑参数
   */
  constructor(args: any = {}) {
    super(args);

    this.timeline = new Timeline();
    this.timeline.addLayer(new Layer());
    this.timeline.activeLayer.addFrame(new Frame());
    this._animationType = "loop";
    this._singleFrameNumber = 1;
    this._playedOnce = false;
    this._isSynced = false;

    this._transformation = args.transformation || new Transformation();

    this.cursor = "default";

    this._isClone = false;
    this._sourceClipUUID = null;

    this._assetSourceUUID = null;

    if (args.objects) {
      this.addObjects(args.objects);
    }

    this._clones = [];
  }

  protected _serialize(args: any): any {
    const data = super._serialize(args);

    data.transformation = this._transformation.values;
    data.timeline = this._timeline;
    data.animationType = this._animationType;
    data.singleFrameNumber = this._singleFrameNumber;
    data.assetSourceUUID = this._assetSourceUUID;
    data.isSynced = this._isSynced;

    return data;
  }

  protected _deserialize(data: any): void {
    super._deserialize(data);

    this._transformation = new Transformation(data.transformation);
    this._timeline = data.timeline;
    this._animationType = data.animationType || "loop";
    this._singleFrameNumber = data.singleFrameNumber || 1;
    this._assetSourceUUID = data.assetSourceUUID;
    this._isSynced = data.isSynced;

    this._playedOnce = false;

    this._clones = [];
  }

  get classname(): string {
    return "Clip";
  }
}
