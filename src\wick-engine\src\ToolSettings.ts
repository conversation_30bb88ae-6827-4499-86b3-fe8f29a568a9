/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

declare global {
  interface Window {
    Wick: any;
  }
}

interface ToolSetting {
  type: "color" | "number" | "boolean" | "choice";
  name: string;
  default: any;
  min?: number;
  max?: number;
  step?: number;
  options?: string[];
}

export class ToolSettings {
  static get DEFAULT_SETTINGS(): ToolSetting[] {
    return [
      {
        type: "color",
        name: "fillColor",
        default: new window.Wick.Color("#000000"),
      },
      {
        type: "color",
        name: "strokeColor",
        default: new window.Wick.Color("#000000"),
      },
      {
        type: "number",
        name: "strokeWidth",
        default: 1,
        min: 0,
        max: 100,
        step: 1,
      },
      {
        type: "number",
        name: "brushSize",
        default: 10,
        min: 1,
        max: 100,
        step: 1,
      },
      {
        type: "number",
        name: "eraserSize",
        default: 10,
        min: 1,
        max: 100,
        step: 1,
      },
      {
        type: "number",
        name: "cornerRadius",
        default: 0,
        min: 0,
        max: 100,
        step: 1,
      },
      {
        type: "number",
        name: "brushStabilizerWeight",
        default: 20,
        min: 0,
        max: 100,
        step: 1,
      },
      {
        type: "boolean",
        name: "pressureEnabled",
        default: false,
      },
      {
        type: "boolean",
        name: "relativeBrushSize",
        default: true,
      },
      {
        type: "number",
        name: "gapFillAmount",
        default: 1,
        min: 0,
        max: 5,
        step: 1,
      },
      {
        type: "choice",
        name: "onionSkinStyle",
        default: "standard",
        options: ["standard", "outlines", "tint"],
      },
      {
        type: "number",
        name: "onionSkinOutlineWidth",
        default: 2,
        min: 1,
        max: 25,
        step: 0.1,
      },
      {
        type: "color",
        name: "backwardOnionSkinTint",
        default: new window.Wick.Color("#ff0000"),
      },
      {
        type: "color",
        name: "forwardOnionSkinTint",
        default: new window.Wick.Color("#0000ff"),
      },
    ];
  }
}

// Add to global Wick namespace
if (typeof window !== "undefined") {
  window.Wick = window.Wick || {};
  window.Wick.ToolSettings = ToolSettings;
}
