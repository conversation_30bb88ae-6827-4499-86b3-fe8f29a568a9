/**
 * GameSprite Engine - 简化版核心引擎
 */

import type { EngineOptions } from './types.js';
import { generateId } from './types.js';
import { PixiRenderer } from './PixiRenderer.js';
import { Scene } from './Scene.js';
import { Project, type ProjectSettings } from './Project.js';
import { Viewport } from './Viewport.js';
import { EventSystem, ENGINE_EVENTS } from './EventSystem.js';
import { HotkeySystem, COMMON_HOTKEYS } from './HotkeySystem.js';
import * as PIXI from 'pixi.js';

export class GameSpriteEngine {
  readonly id: string;

  // 渲染器
  private renderer: PixiRenderer | null = null;

  // 项目管理
  private project: Project | null = null;

  // 视口管理
  private viewport: Viewport | null = null;

  // 引擎状态
  private initialized: boolean = false;
  private running: boolean = false;

  constructor() {
    this.id = generateId();
  }

  // === 初始化 ===
  async initialize(canvas: HTMLCanvasElement, options?: EngineOptions): Promise<void> {
    if (this.initialized) {
      throw new Error('Engine already initialized');
    }

    try {
      // 创建渲染器 - canvas设置为100%，实际尺寸由CSS控制
      this.renderer = new PixiRenderer();
      await this.renderer.initialize(canvas, {
        width: options?.width || canvas.width || 800,
        height: options?.height || canvas.height || 600,
        backgroundColor: options?.backgroundColor || 0x2a2a2a
      });

      // 创建项目 - 项目尺寸独立于canvas尺寸，默认3:2比例
      const projectWidth = 720;  // 默认项目宽度
      const projectHeight = 480; // 默认项目高度 (3:2比例)

      this.project = new Project({
        name: 'Default Project',
        width: projectWidth,
        height: projectHeight,
        backgroundColor: options?.backgroundColor || 0x2a2a2a
      });

      // 创建视口 - 管理项目在canvas上的显示
      this.viewport = new Viewport({
        projectWidth: projectWidth,
        projectHeight: projectHeight,
        canvasWidth: this.renderer.width,
        canvasHeight: this.renderer.height,
        backgroundColor: options?.backgroundColor || 0x2a2a2a,
        borderColor: 0x666666,
        borderWidth: 1
      });
       this.renderer.getStage().addChild(new PIXI.Graphics().rect(0, 0, 1000, 1000).fill(0x252525));
      // 将视口添加到舞台
      this.renderer.getStage().addChild(this.viewport.getContainer());

      // 获取当前场景并添加到视口
      const currentScene = this.project.getCurrentScene();
      if (currentScene) {
        //测试
        currentScene.getPixiObject().addChild(new PIXI.Graphics().rect(0, 0, 100, 100).fill(0xff0000));
        // 将场景添加到视口内容容器
        this.viewport.addContent(currentScene.getPixiObject());
      }

      // 初始化系统
      this.initializeSystems();

      this.initialized = true;

      // 派发引擎初始化事件
      EventSystem.emit(ENGINE_EVENTS.ENGINE_INIT, { engine: this });

      console.log('✅ GameSprite Engine: 初始化完成');

    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to initialize engine: ${message}`);
    }
  }

  // === 场景管理 ===
  setScene(scene: Scene): void {
    if (!this.project || !this.viewport) return;

    const currentScene = this.project.getCurrentScene();
    if (currentScene) {
      this.viewport.removeContent(currentScene.getPixiObject());
    }

    // 将场景添加到项目中并设为当前场景
    this.project.addScene(scene);
    this.project.setCurrentScene(scene.id);

    // 将新场景添加到视口
    this.viewport.addContent(scene.getPixiObject());
  }

  getScene(): Scene | null {
    return this.project?.getCurrentScene() || null;
  }

  createScene(width?: number, height?: number, name?: string): Scene {
    if (!this.project) {
      // 如果没有项目，创建临时场景
      return new Scene(
        width || this.renderer?.width || 800,
        height || this.renderer?.height || 600,
        name
      );
    }

    return this.project.createScene(name);
  }

  // === 项目管理 ===
  getProject(): Project | null {
    return this.project;
  }

  createProject(settings?: ProjectSettings): Project {
    // 如果已有项目，先销毁
    if (this.project) {
      this.project.destroy();
    }

    // 创建新项目，使用独立的项目尺寸
    const projectWidth = settings?.width || 720;
    const projectHeight = settings?.height || 480;

    this.project = new Project({
      width: projectWidth,
      height: projectHeight,
      backgroundColor: 0x2a2a2a,
      ...settings
    });

    // 更新视口的项目尺寸
    if (this.viewport) {
      this.viewport.resizeProject(projectWidth, projectHeight);
    }

    return this.project;
  }

  // === 视口管理 ===
  getViewport(): Viewport | null {
    return this.viewport;
  }

  resizeCanvas(width: number, height: number): void {
    if (this.viewport) {
      this.viewport.resizeCanvas(width, height);
    }
  }

  // === 工具方法 ===
  getRenderer(): PixiRenderer | null {
    return this.renderer;
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  isRunning(): boolean {
    return this.running;
  }

  // === 系统管理 ===
  private initializeSystems(): void {
    // 初始化快捷键系统
    HotkeySystem.initialize();

    // 注册默认快捷键
    this.registerDefaultHotkeys();

    console.log('🔧 GameSprite Engine: 系统初始化完成');
  }

  private registerDefaultHotkeys(): void {
    // 注册视口相关快捷键
    HotkeySystem.register(COMMON_HOTKEYS.ZOOM_IN, ENGINE_EVENTS.VIEWPORT_ZOOM);
    HotkeySystem.register(COMMON_HOTKEYS.ZOOM_OUT, ENGINE_EVENTS.VIEWPORT_ZOOM);
    HotkeySystem.register(COMMON_HOTKEYS.ZOOM_RESET, ENGINE_EVENTS.VIEWPORT_RESET);

    // 注册项目相关快捷键
    HotkeySystem.register(COMMON_HOTKEYS.SAVE_PROJECT, ENGINE_EVENTS.PROJECT_SAVE);

    // 监听快捷键事件
    EventSystem.on(ENGINE_EVENTS.VIEWPORT_ZOOM, (data) => {
      console.log('🔍 视口缩放快捷键触发', data);
      // 这里可以调用视口的缩放方法
    });

    EventSystem.on(ENGINE_EVENTS.VIEWPORT_RESET, (data) => {
      console.log('🔄 视口重置快捷键触发', data);
      // 这里可以调用视口的重置方法
    });

    EventSystem.on(ENGINE_EVENTS.PROJECT_SAVE, (data) => {
      console.log('💾 项目保存快捷键触发', data);
      // 这里可以调用项目保存方法
    });
  }

  // === 清理 ===
  destroy(): void {
    this.running = false;

    // 派发引擎销毁事件
    EventSystem.emit(ENGINE_EVENTS.ENGINE_DESTROY, { engine: this });

    // 销毁系统
    HotkeySystem.destroy();
    EventSystem.clear();

    if (this.viewport) {
      this.viewport.destroy();
      this.viewport = null;
    }

    if (this.project) {
      this.project.destroy();
      this.project = null;
    }

    if (this.renderer) {
      this.renderer.destroy();
    }

    this.initialized = false;
    console.log('🧹 GameSprite Engine: 引擎已销毁');
  }
}
