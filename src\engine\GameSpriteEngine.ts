/**
 * GameSprite Engine - 简化版核心引擎
 */

import type { EngineOptions } from './types.js';
import { generateId } from './types.js';
import { PixiRenderer } from './PixiRenderer.js';
import { Scene } from './Scene.js';
import * as PIXI from 'pixi.js';

export class GameSpriteEngine {
  readonly id: string;
  
  // 渲染器
  private renderer: PixiRenderer | null = null;
  
  // 当前场景
  private currentScene: Scene | null = null;
  
  // 引擎状态
  private initialized: boolean = false;
  private running: boolean = false;
  private lastUpdateTime: number = 0;
  
  constructor() {
    this.id = generateId();
  }
  
  // === 初始化 ===
  async initialize(canvas: HTMLCanvasElement, options?: EngineOptions): Promise<void> {
    if (this.initialized) {
      throw new Error('Engine already initialized');
    }
    
    try {
      // 创建渲染器
      this.renderer = new PixiRenderer();
      await this.renderer.initialize(canvas, {
        width: options?.width || canvas.width || 800,
        height: options?.height || canvas.height || 600,
        backgroundColor: options?.backgroundColor || 0x2a2a2a
      });
      
      // 创建默认场景
      this.currentScene = new Scene(
        this.renderer.width,
        this.renderer.height,
        'DefaultScene'
      );
      //测试
      this.currentScene.getPixiObject().addChild( new PIXI.Graphics().rect(0, 0, 100, 100).fill(0xff0000));  
      // 将场景添加到舞台
      this.renderer.getStage().addChild(this.currentScene.getPixiObject());
      
      // 启动更新循环
      this.startUpdateLoop();
      
      this.initialized = true;
      console.log('✅ GameSprite Engine: 初始化完成');
      
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to initialize engine: ${message}`);
    }
  }
  
  // === 场景管理 ===
  setScene(scene: Scene): void {
    if (this.currentScene) {
      this.renderer?.getStage().removeChild(this.currentScene.getPixiObject());
    }
    
    this.currentScene = scene;
    
    if (this.renderer) {
      this.renderer.getStage().addChild(scene.getPixiObject());
    }
  }
  
  getScene(): Scene | null {
    return this.currentScene;
  }
  
  createScene(width?: number, height?: number, name?: string): Scene {
    const scene = new Scene(
      width || this.renderer?.width || 800,
      height || this.renderer?.height || 600,
      name
    );
    return scene;
  }
  
  // === 渲染循环 ===
  private startUpdateLoop(): void {
    if (this.running) return;
    
    this.running = true;
    this.lastUpdateTime = performance.now();
    
    const update = (currentTime: number) => {
      if (!this.running) return;
      
      const deltaTime = currentTime - this.lastUpdateTime;
      this.lastUpdateTime = currentTime;
      
      // 更新场景
      if (this.currentScene) {
        this.currentScene.update(deltaTime);
      }
      
      // 继续循环
      requestAnimationFrame(update);
    };
    
    requestAnimationFrame(update);
  }
  
  // === 工具方法 ===
  getRenderer(): PixiRenderer | null {
    return this.renderer;
  }
  
  isInitialized(): boolean {
    return this.initialized;
  }
  
  isRunning(): boolean {
    return this.running;
  }
  
  // === 清理 ===
  destroy(): void {
    this.running = false;
    
    if (this.currentScene) {
      this.currentScene.destroy();
    }
    
    if (this.renderer) {
      this.renderer.destroy();
    }
    
    this.initialized = false;
    console.log('🧹 GameSprite Engine: 引擎已销毁');
  }
}
