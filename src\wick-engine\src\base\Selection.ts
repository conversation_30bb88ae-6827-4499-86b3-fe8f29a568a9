/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "./Base";

/**
 * 表示一个Wick选择
 */
export class Selection extends Base {
  static get LOCATION_NAMES(): string[] {
    return ["Canvas", "Timeline", "AssetLibrary"];
  }

  protected _selectedObjectsUUIDs: string[];
  protected _widgetRotation: number;
  protected _pivotPoint: { x: number; y: number };
  protected _originalWidth: number;
  protected _originalHeight: number;
  protected SELECTABLE_OBJECT_TYPES: string[];
  protected SELECTABLE_OBJECT_TYPES_SET: Set<string>;

  /**
   * 创建一个Wick选择
   * @param args - 选择参数
   */
  constructor(
    args: {
      selectedObjects?: string[] | undefined;
      widgetRotation?: number | undefined;
    } = {}
  ) {
    super(args);

    this._selectedObjectsUUIDs = args.selectedObjects || [];
    this._widgetRotation = args.widgetRotation || 0;
    this._pivotPoint = { x: 0, y: 0 };
    this._originalWidth = 0;
    this._originalHeight = 0;

    this.SELECTABLE_OBJECT_TYPES = [
      "Path",
      "Clip",
      "Frame",
      "Tween",
      "Layer",
      "Asset",
      "Button",
      "ClipAsset",
      "FileAsset",
      "FontAsset",
      "GIFAsset",
      "ImageAsset",
      "SoundAsset",
      "SVGAsset",
    ];
    this.SELECTABLE_OBJECT_TYPES_SET = new Set(this.SELECTABLE_OBJECT_TYPES);
  }

  protected _serialize(args: any): any {
    const data = super._serialize(args);
    data.selectedObjects = Array.from(this._selectedObjectsUUIDs);
    data.widgetRotation = this._widgetRotation;
    data.pivotPoint = {
      x: this._pivotPoint.x,
      y: this._pivotPoint.y,
    };
    data.originalWidth = this._originalWidth;
    data.originalHeight = this._originalHeight;
    return data;
  }

  protected _deserialize(data: any): void {
    super._deserialize(data);
    this._selectedObjectsUUIDs = data.selectedObjects || [];
    this._widgetRotation = data.widgetRotation;
    this._pivotPoint = {
      x: data.pivotPoint.x,
      y: data.pivotPoint.y,
    };
    this._originalWidth = data.originalWidth;
    this._originalHeight = data.originalHeight;
  }

  get classname(): string {
    return "Selection";
  }

  /**
   * 获取可以更改的所有选择属性的名称
   */
  get allAttributeNames(): string[] {
    return [
      "strokeWidth",
      "fillColor",
      "strokeColor",
      "name",
      "filename",
      "fontSize",
      "fontFamily",
      "fontWeight",
      "fontStyle",
      "src",
      "frameLength",
      "x",
      "y",
      "originX",
      "originY",
      "width",
      "height",
      "rotation",
      "opacity",
      "sound",
    ];
  }
}
