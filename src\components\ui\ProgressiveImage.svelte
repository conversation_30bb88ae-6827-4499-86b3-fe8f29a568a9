<script lang="ts">
  import { onMount } from 'svelte';
  import { thumbnailManager, type ThumbnailSize } from '../../utils/thumbnailManager';
  import type { ResourceItem } from '../../types/imageType';

  interface Props {
    resource: ResourceItem;
    size?: ThumbnailSize | number;
    placeholder?: string;
    alt?: string;
    className?: string;
    onLoad?: (stage: string) => void;
    onError?: (error: Error) => void;
  }

  let {
    resource,
    size = 'MEDIUM',

    placeholder = '',
    alt = '',
    className = '',
    onLoad,
    onError
  }: Props = $props();

  // 状态管理
  let currentSrc = $state(placeholder);
  let isLoading = $state(true);
  let hasError = $state(false);
  let loadingStage = $state<'placeholder' | 'tiny' | 'small' | 'medium' | 'large' | 'final'>('placeholder');
  let imageElement = $state<HTMLImageElement>();

  // 加载进度
  let loadProgress = $state(0);

  // 🎯 渐进式图片加载
  async function startProgressiveLoad() {
    if (!resource || resource.type !== 'image') {
      hasError = true;
      onError?.(new Error('无效的图片资源'));
      return;
    }

    // 🎯 如果resource.data为空，尝试等待加载
    if (!resource.data) {
      console.log('⏳ ProgressiveImage: 等待资源数据加载', {
        resourceId: resource.id,
        resourceName: resource.name,
        hasData: !!resource.data,
        isLoaded: resource.isLoaded
      });

      // 给一些时间让资源加载，但不要无限等待
      let retryCount = 0;
      const maxRetries = 5;

      const checkData = () => {
        if (resource.data) {
          startProgressiveLoad();
        } else if (retryCount < maxRetries) {
          retryCount++;
          setTimeout(checkData, 200 * retryCount); // 递增延迟
        } else {
          console.warn('⚠️ ProgressiveImage: 资源数据加载超时，跳过', resource.name);
          // 不报错，只是跳过加载
          isLoading = false;
        }
      };

      setTimeout(checkData, 100);
      return;
    }

    try {
      isLoading = true;
      hasError = false;
      loadProgress = 0;

      // 🎯 渐进式加载
      await loadProgressively();

    } catch (error) {
      console.error('❌ ProgressiveImage: 加载失败', error);
      hasError = true;
      isLoading = false;
      onError?.(error instanceof Error ? error : new Error('加载失败'));
    }
  }

  // 🎯 渐进式加载实现
  async function loadProgressively() {
    try {
      // 第一阶段
      const smallThumbnail = await thumbnailManager.getThumbnail(resource, 'SMALL', {
        onProgress: (progress) => {
          loadProgress = progress.percentage * 0.3 || 0; // 前30%进度
        }
      });

      if (smallThumbnail) {
        await updateImage(smallThumbnail.dataUrl, 'small');
        loadingStage = 'small';
      }

      // 第二阶段
      const targetThumbnail = await thumbnailManager.getThumbnail(resource, size, {
        onProgress: (progress) => {
          loadProgress = 30 + (progress.percentage * 0.7 || 0); // 后70%进度
        }
      });

      if (targetThumbnail) {
        await updateImage(targetThumbnail.dataUrl, 'final');
        isLoading = false;
        loadingStage = 'final';
        onLoad?.('final');
      } else {
        throw new Error('无法生成目标尺寸缩略图');
      }
    } catch (error) {
      console.warn('⚠️ ProgressiveImage: 渐进式加载失败，尝试直接加载', error);
      // 如果渐进式失败，尝试直接加载
      await loadDirectly();
    }
  }



  // 直接加载
  async function loadDirectly() {
    console.log('🔄 ProgressiveImage: 直接加载', {
      resourceId: resource.id,
      size
    });

    const thumbnail = await thumbnailManager.getThumbnail(resource, size, {
      onProgress: (progress) => {
        loadProgress = progress.percentage || 0;
      }
    });

    if (thumbnail) {
      await updateImage(thumbnail.dataUrl, 'final');
      isLoading = false;
      onLoad?.('final');
    } else {
      throw new Error('无法生成缩略图');
    }
  }

  // 更新图片
  async function updateImage(src: string, stage: typeof loadingStage): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();

      img.onload = () => {
        currentSrc = src;
        loadingStage = stage;
        resolve();
      };

      img.onerror = () => {
        reject(new Error(`图片加载失败: ${stage}`));
      };

      img.src = src;
    });
  }

  // 处理图片加载事件
  function handleImageLoad() {
    console.log('✅ ProgressiveImage: 图片显示完成', {
      resourceId: resource.id,
      stage: loadingStage
    });
  }

  function handleImageError() {
    console.error('❌ ProgressiveImage: 图片显示错误', {
      resourceId: resource.id,
      src: currentSrc
    });
    hasError = true;
    isLoading = false;
  }

  // 组件挂载时开始加载
  onMount(() => {
    startProgressiveLoad();
  });

  // 监听resource变化
  $effect(() => {
    if (resource) {
      startProgressiveLoad();
    }
  });
</script>

<div class="progressive-image {className}" class:loading={isLoading} class:error={hasError}>
  {#if hasError}

    <div class="error-placeholder">
      <div class="error-icon">❌</div>
    </div>
  {:else if currentSrc}

    <img
      bind:this={imageElement}
      src={currentSrc}
      {alt}
      class="progressive-img"
      class:low-quality={loadingStage === 'small'}
      class:high-quality={loadingStage === 'final'}
      onload={handleImageLoad}
      onerror={handleImageError}
    />


    {#if isLoading && loadingStage === 'placeholder'}
      <div class="loading-overlay">
        <div class="loading-spinner"></div>
      </div>
    {/if}
  {:else}

    <div class="placeholder">
      <div class="placeholder-icon">🖼️</div>
    </div>
  {/if}
</div>

<style>
  .progressive-image {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: var(--theme-surface-light, #f8fafc);
    border-radius: var(--border-radius, 8px);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .progressive-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: filter 0.3s ease, opacity 0.3s ease;
  }

  .progressive-img.low-quality {
    filter: blur(1px);
    opacity: 0.8;
  }

  .progressive-img.medium-quality {
    filter: blur(0.5px);
    opacity: 0.9;
  }

  .progressive-img.high-quality {
    filter: none;
    opacity: 1;
  }

  /* 加载覆盖层 */
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(2px);
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* 占位符 */
  .placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .placeholder-icon {
    font-size: 24px;
    opacity: 0.5;
  }

  /* 错误状态 */
  .error-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .error-icon {
    font-size: 24px;
  }

  /* 状态样式 */
  .progressive-image.loading {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    animation: loading-pattern 1s linear infinite;
  }

  @keyframes loading-pattern {
    0% { background-position: 0 0, 0 10px, 10px -10px, -10px 0px; }
    100% { background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px; }
  }

  .progressive-image.error {
    background: var(--theme-error-light, #fef2f2);
    border: 1px solid var(--theme-error, #ef4444);
  }
</style>
