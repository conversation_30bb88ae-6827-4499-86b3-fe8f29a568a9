/**
 * GameSprite Engine - 核心类型定义
 */

// 基础几何类型
export interface Point {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Rectangle extends Point, Size {}

// 渲染器接口
export interface Renderer {
  width: number;
  height: number;
  clear(): void;
  render(object: any): void;
  destroy(): void;
}

// 引擎配置
export interface EngineOptions {
  width?: number;
  height?: number;
  backgroundColor?: number;
}

// 工具函数
export function generateId(): string {
  return Math.random().toString(36).substr(2, 16);
}
