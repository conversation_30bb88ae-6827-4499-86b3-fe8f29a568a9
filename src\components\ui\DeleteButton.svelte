<script lang="ts">
  /**
   * DeleteButton 组件 - 通用删除按钮
   * 支持不同尺寸、样式和位置
   */

  interface Props {
    // 基础属性
    onclick?: (event: MouseEvent) => void;
    disabled?: boolean;
    title?: string;
    ariaLabel?: string;

    // 样式属性
    size?: 'small' | 'medium' | 'large';
    variant?: 'overlay' | 'solid' | 'outline' | 'ghost';
    position?: 'absolute' | 'relative' | 'static';

    // 位置属性（当 position 为 absolute 时）
    top?: string;
    right?: string;
    bottom?: string;
    left?: string;

    // 自定义样式
    className?: string;
    style?: string;

    // 图标
    icon?: string;
  }

  let {
    onclick,
    disabled = false,
    title = '删除',
    ariaLabel = '删除',
    size = 'medium',
    variant = 'overlay',
    position = 'absolute',
    top = '0.25rem',
    right = '0.25rem',
    bottom,
    left,
    className = '',
    style = '',
    icon = '❌'
  }: Props = $props();

  // 处理点击事件
  function handleClick(event: MouseEvent) {
    if (disabled) return;
    event.stopPropagation(); // 阻止事件冒泡
    onclick?.(event);
  }

  // 处理键盘事件
  function handleKeyDown(event: KeyboardEvent) {
    if (disabled) return;
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      event.stopPropagation();
      onclick?.(event as any);
    }
  }

  // 计算样式类
  const computedClasses = $derived([
    'delete-button',
    `delete-button--${size}`,
    `delete-button--${variant}`,
    `delete-button--${position}`,
    disabled ? 'delete-button--disabled' : '',
    className
  ].filter(Boolean).join(' '));

  // 计算内联样式
  const computedStyle = $derived([
    position === 'absolute' && top ? `top: ${top}` : '',
    position === 'absolute' && right ? `right: ${right}` : '',
    position === 'absolute' && bottom ? `bottom: ${bottom}` : '',
    position === 'absolute' && left ? `left: ${left}` : '',
    style
  ].filter(Boolean).join('; '));
</script>

<button
  class={computedClasses}
  style={computedStyle}
  onclick={handleClick}
  onkeydown={handleKeyDown}
  {disabled}
  {title}
  aria-label={ariaLabel}
  tabindex={disabled ? -1 : 0}
>
  {icon}
</button>

<style>
  /* 🎯 基础样式 */
  .delete-button {
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    font-family: inherit;
    transition: all 0.2s ease;
    user-select: none;
    z-index: 10;
  }

  .delete-button:focus {
    outline: 2px solid var(--theme-primary, #007bff);
    outline-offset: 2px;
  }

  .delete-button:focus:not(:focus-visible) {
    outline: none;
  }

  /* 🎯 尺寸变体 */
  .delete-button--small {
    width: 14px;
    height: 14px;
    font-size: 0.55rem;
  }

  .delete-button--medium {
    width: 18px;
    height: 18px;
    font-size: 0.65rem;
  }

  .delete-button--large {
    width: 24px;
    height: 24px;
    font-size: 0.8rem;
  }

  /* 🎯 样式变体 */
  .delete-button--overlay {
    background: rgba(0, 0, 0, 0.6);
    color: #ff4444;
    opacity: 0.7;
    backdrop-filter: blur(2px);
  }

  .delete-button--overlay:hover:not(.delete-button--disabled) {
    opacity: 1;
    background: rgba(0, 0, 0, 0.8);
    color: #ff6666;
    transform: scale(1.1);
  }

  .delete-button--solid {
    background: rgba(220, 53, 69, 0.9);
    color: white;
    opacity: 0.8;
  }

  .delete-button--solid:hover:not(.delete-button--disabled) {
    opacity: 1;
    background: rgba(220, 53, 69, 1);
    transform: scale(1.1);
  }

  .delete-button--outline {
    background: transparent;
    color: #dc3545;
    border: 1px solid #dc3545;
    opacity: 0.8;
  }

  .delete-button--outline:hover:not(.delete-button--disabled) {
    opacity: 1;
    background: rgba(220, 53, 69, 0.1);
    transform: scale(1.05);
  }

  .delete-button--ghost {
    background: transparent;
    color: #dc3545;
    opacity: 0.6;
  }

  .delete-button--ghost:hover:not(.delete-button--disabled) {
    opacity: 1;
    background: rgba(220, 53, 69, 0.1);
    transform: scale(1.05);
  }

  /* 🎯 位置变体 */
  .delete-button--absolute {
    position: absolute;
  }

  .delete-button--relative {
    position: relative;
  }

  .delete-button--static {
    position: static;
  }

  /* 🎯 禁用状态 */
  .delete-button--disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none !important;
  }

  .delete-button--disabled:hover {
    transform: none !important;
  }

  /* 🎯 响应式调整 */
  @media (max-width: 768px) {
    .delete-button--small {
      width: 16px;
      height: 16px;
      font-size: 0.6rem;
    }

    .delete-button--medium {
      width: 20px;
      height: 20px;
      font-size: 0.7rem;
    }

    .delete-button--large {
      width: 26px;
      height: 26px;
      font-size: 0.85rem;
    }
  }

  /* 🎯 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .delete-button--overlay {
      background: rgba(0, 0, 0, 0.9);
      border: 1px solid #ff4444;
    }

    .delete-button--ghost {
      border: 1px solid #dc3545;
    }
  }

  /* 🎯 减少动画模式支持 */
  @media (prefers-reduced-motion: reduce) {
    .delete-button {
      transition: none;
    }

    .delete-button:hover:not(.delete-button--disabled) {
      transform: none;
    }
  }
</style>
