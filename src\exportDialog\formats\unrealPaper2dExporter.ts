import { invoke } from '@tauri-apps/api/core';
import type { ExportConfig, ExportItem, ExportResult, ExportProgress } from '../exportTypes';
import { ensureExtension, sanitizeFileName, getMimeType, createImageFromBuffer, canvasToBlob } from '../exportUtils';

// Unreal Paper2D格式的帧数据
interface UnrealPaper2dFrame {
  frame: { x: number; y: number; w: number; h: number };
  rotated: boolean;
  trimmed: boolean;
  spriteSourceSize: { x: number; y: number; w: number; h: number };
  sourceSize: { w: number; h: number };
}

// Unreal Paper2D格式的数据结构
interface UnrealPaper2dData {
  frames: Record<string, UnrealPaper2dFrame>;
  meta: {
    app: string;
    version: string;
    target: string;
    image: string;
    format: string;
    size: { w: number; h: number };
    scale: string;
    smartupdate: string;
  };
}

export class UnrealPaper2dExporter {
  private progressCallback?: (progress: ExportProgress) => void;

  constructor(progressCallback?: (progress: ExportProgress) => void) {
    this.progressCallback = progressCallback;
  }

  private reportProgress(current: number, total: number, fileName: string, status: 'processing' | 'completed' | 'error') {
    if (this.progressCallback) {
      this.progressCallback({
        current,
        total,
        fileName,
        status
      });
    }
  }

  async export(items: ExportItem[], config: ExportConfig, selectedFolder: string): Promise<ExportResult> {
    console.log('🚀 UnrealPaper2dExporter: 开始导出', { items, config, selectedFolder });

    const exportedFiles: Array<{ name: string; path: string; size: number; type: 'data' | 'image' }> = [];
    let totalSize = 0;
    const totalTasks = items.length;

    // 处理每个导出项
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      this.reportProgress(i + 1, totalTasks, `${item.name}.json`, 'processing');

      if (!item.cropAreas || item.cropAreas.length === 0) {
        console.warn(`跳过没有裁切数据的资源: ${item.name}`);
        continue;
      }

      // 导出原始图片（如果需要）
      if (config.includeOriginal && item.resource.buffer) {
        try {
          const mimeType = getMimeType(item.resource.path);
          const originalImg = await createImageFromBuffer(item.resource.buffer, mimeType);

          // 创建原图blob
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (!ctx) throw new Error('无法创建Canvas上下文');

          canvas.width = originalImg.width;
          canvas.height = originalImg.height;
          ctx.drawImage(originalImg, 0, 0);

          const originalBlob = await canvasToBlob(canvas, 'image/png');
          const arrayBuffer = await originalBlob.arrayBuffer();
          const uint8Array = new Uint8Array(arrayBuffer);

          // 生成原图文件名
          const originalFileName = this.generateImageFileName(item.name);

          console.log('🚀 UnrealPaper2dExporter: 导出原始图片', {
            targetDirectory: selectedFolder,
            fileName: originalFileName
          });

          const originalPath = await invoke<string>('export_single_file', {
            filePath: `${selectedFolder}/${originalFileName}`,
            fileData: Array.from(uint8Array)
          });

          console.log('✅ UnrealPaper2dExporter: 原始图片导出成功', { originalPath });

          exportedFiles.push({
            name: originalFileName,
            path: originalPath,
            size: originalBlob.size,
            type: 'image'
          });
          totalSize += originalBlob.size;

        } catch (error) {
          console.error('❌ UnrealPaper2dExporter: 原始图片导出失败', error);
          // 不中断Unreal Paper2D导出，只是警告
        }
      }

      // 生成Unreal Paper2D数据
      const unrealData = this.generateUnrealPaper2dData(item);

      // 生成JSON内容
      const fileContent = JSON.stringify(unrealData, null, '\t');

      // 生成文件名
      const fileName = this.generateFileName(item.name, config);

      // 使用Rust后端保存文件
      try {
        const encoder = new TextEncoder();
        const fileData = encoder.encode(fileContent);

        console.log('🚀 UnrealPaper2dExporter: 调用Rust后端导出', {
          targetDirectory: selectedFolder,
          fileName: fileName
        });

        const exportedPath = await invoke<string>('export_single_file', {
          filePath: `${selectedFolder}/${fileName}`,
          fileData: Array.from(fileData)
        });

        console.log('✅ UnrealPaper2dExporter: Rust后端导出成功', { exportedPath });

        const fileSize = fileData.length;
        exportedFiles.push({
          name: fileName,
          path: exportedPath,
          size: fileSize,
          type: 'data'
        });
        totalSize += fileSize;

      } catch (error) {
        console.error('❌ UnrealPaper2dExporter: Rust后端导出失败', error);
        throw new Error(`导出Unreal Paper2D文件失败: ${error}`);
      }
    }

    this.reportProgress(totalTasks, totalTasks, '导出完成', 'completed');

    return {
      success: true,
      message: `成功导出 ${exportedFiles.length} 个文件`,
      files: exportedFiles,
      totalSize
    };
  }

  private generateUnrealPaper2dData(item: ExportItem): UnrealPaper2dData {
    const frames: Record<string, UnrealPaper2dFrame> = {};

    // 为每个裁切区域生成帧数据
    item.cropAreas?.forEach((area, index) => {
      // 处理文件名，避免重复扩展名
      let frameName: string;
      if (area.name) {
        // 如果area.name已经有扩展名，直接使用；否则添加.png
        frameName = area.name.includes('.') ? area.name : `${area.name}.png`;
      } else {
        frameName = `${item.name.replace(/\.[^/.]+$/, '')}-${index}.png`;
      }

      frames[frameName] = {
        frame: { x: area.x, y: area.y, w: area.width, h: area.height },
        rotated: false,
        trimmed: false,
        spriteSourceSize: { x: 0, y: 0, w: area.width, h: area.height },
        sourceSize: { w: area.width, h: area.height }
      };
    });

    const imageFileName = this.generateImageFileName(item.name);

    return {
      frames,
      meta: {
        app: "GameSprite Studio",
        version: "1.0",
        target: "paper2d",
        image: imageFileName,
        format: "RGBA8888",
        size: { w: item.resource.width || 0, h: item.resource.height || 0 },
        scale: "1"
      }
    };
  }

  private generateFileName(baseName: string, config: ExportConfig): string {
    let fileName: string;

    // 🎯 如果用户设置了文件名，完整使用设置的文件名
    if (config.fileName && config.fileName.trim() !== '') {
      fileName = config.fileName.replace(/\.[^/.]+$/, ''); // 移除扩展名
    } else {
      fileName = baseName.replace(/\.[^/.]+$/, '');
    }

    return ensureExtension(sanitizeFileName(fileName), 'json');
  }

  private generateImageFileName(baseName: string): string {
    const nameWithoutExt = baseName.replace(/\.[^/.]+$/, '');
    return ensureExtension(sanitizeFileName(nameWithoutExt), 'png');
  }


}
