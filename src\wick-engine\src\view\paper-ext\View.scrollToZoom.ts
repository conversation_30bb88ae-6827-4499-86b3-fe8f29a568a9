/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Paper.js-drawing-tools.
 *
 * Paper.js-drawing-tools is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Paper.js-drawing-tools is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Paper.js-drawing-tools.  If not, see <https://www.gnu.org/licenses/>.
 */

interface ScrollToZoomArgs {
  enabled?: boolean;
  minZoom?: number;
  maxZoom?: number;
  factor?: number;
}

declare module 'paper' {
  interface View {
    enableScrollToZoom(args?: ScrollToZoomArgs): void;
  }
}

paper.View.inject({
  enableScrollToZoom: function(args: ScrollToZoomArgs = {}) {
    const {
      enabled = true,
      minZoom = 0.1,
      maxZoom = 10,
      factor = 1.1
    } = args;

    if (!enabled) return;

    this.on('mousewheel', (event: any) => {
      const view = this;
      const oldZoom = view.zoom;
      const center = view.center;
      const mousePosition = view.viewToProject(event.point);
      const delta = event.delta.y;

      let newZoom = oldZoom;
      if (delta < 0) {
        newZoom = view.zoom * factor;
      } else {
        newZoom = view.zoom / factor;
      }

      // Ensure zoom stays within bounds
      newZoom = Math.min(Math.max(newZoom, minZoom), maxZoom);

      if (newZoom !== oldZoom) {
        view.scale(
          newZoom / oldZoom,
          mousePosition
        );
        view.center = center;
      }

      event.preventDefault();
      event.stopPropagation();
    });
  },
}));