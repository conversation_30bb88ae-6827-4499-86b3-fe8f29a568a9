/**
 * 状态管理相关的类型定义
 */

import type { ResourceItem, ImageResource, AtlasResource } from './imageType';
import type { ProjectFile, ProjectData } from './projectType';

// 全局项目状态
export interface ProjectState {
    // 当前项目
    currentProject?: ProjectData;
    
    // 项目文件信息
    projectFile?: {
        path?: string;
        lastSaved?: string;
        isDirty: boolean;
        autoSave: boolean;
    };
    
    // 加载状态
    loading: {
        isLoading: boolean;
        operation?: string;
        progress?: number;
    };
    
    // 错误状态
    error?: {
        message: string;
        code?: string;
        timestamp: string;
    };
}

// 资源管理状态
export interface ResourceState {
    // 已加载的资源映射
    loadedResources: Map<string, ResourceItem>;
    
    // 资源索引（用于快速查找）
    resourceIndex: Map<string, {
        type: 'image' | 'folder' | 'atlas';
        parentId?: string;
        path: string[];
    }>;
    
    // 选中的资源
    selectedResources: Set<string>;
    
    // 展开的文件夹
    expandedFolders: Set<string>;
    
    // 资源加载状态
    loadingResources: Set<string>;
    
    // 资源缓存
    resourceCache: {
        images: Map<string, HTMLImageElement>;
        blobs: Map<string, string>; // blob URLs
    };
}

// UI状态管理
export interface UIState {
    // 面板状态
    panels: {
        left: PanelState;
        center: PanelState;
        right: PanelState;
        bottom: PanelState;
    };
    
    // 主题设置
    theme: 'light' | 'dark';
    
    // 布局设置
    layout: {
        leftPanelWidth: number;
        rightPanelWidth: number;
        bottomPanelHeight: number;
    };
    
    // 对话框状态
    dialogs: {
        createProject: boolean;
        openProject: boolean;
        saveProject: boolean;
        settings: boolean;
        about: boolean;
    };
    
    // 通知状态
    notifications: Notification[];
    
    // 拖拽状态
    dragDrop: DragDropState;
}

// 面板状态
export interface PanelState {
    visible: boolean;
    collapsed: boolean;
    activeTab?: string;
    size?: number;
}

// 通知
export interface Notification {
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    timestamp: string;
    duration?: number; // 自动消失时间（毫秒）
    actions?: NotificationAction[];
}

// 通知操作
export interface NotificationAction {
    label: string;
    action: () => void;
    style?: 'primary' | 'secondary' | 'danger';
}

// 拖拽状态
export interface DragDropState {
    isDragging: boolean;
    dragType?: 'resource' | 'file';
    dragData?: any;
    dropTarget?: string;
    dropZones: Set<string>;
}

// 图集工作状态
export interface AtlasWorkState {
    // 当前活动的图集
    activeAtlas?: AtlasResource;
    
    // 图集编辑状态
    editingAtlas?: {
        id: string;
        originalData: AtlasResource;
        hasChanges: boolean;
    };
    
    // 布局计算状态
    layoutState: {
        isCalculating: boolean;
        algorithm?: string;
        progress?: number;
        result?: any;
    };
    
    // 预览状态
    previewState: {
        isVisible: boolean;
        scale: number;
        offset: { x: number; y: number };
        selectedItems: Set<string>;
    };
}

// 编辑操作状态
export interface EditState {
    // 历史记录
    history: {
        undoStack: EditAction[];
        redoStack: EditAction[];
        maxSize: number;
    };
    
    // 当前操作
    currentOperation?: {
        type: string;
        data: any;
        startTime: number;
    };
    
    // 选择状态
    selection: {
        type: 'none' | 'single' | 'multiple';
        items: Set<string>;
        bounds?: SelectionBounds;
    };
    
    // 剪贴板
    clipboard: {
        type?: 'resource' | 'atlas';
        data?: any;
        timestamp?: string;
    };
}

// 编辑操作
export interface EditAction {
    id: string;
    type: string;
    description: string;
    timestamp: string;
    data: {
        before: any;
        after: any;
    };
    undo: () => Promise<void>;
    redo: () => Promise<void>;
}

// 选择边界
export interface SelectionBounds {
    x: number;
    y: number;
    width: number;
    height: number;
}

// 应用状态（根状态）
export interface AppState {
    project: ProjectState;
    resources: ResourceState;
    ui: UIState;
    atlas: AtlasWorkState;
    edit: EditState;
}

// 状态更新动作
export interface StateAction {
    type: string;
    payload?: any;
    meta?: {
        timestamp: string;
        source?: string;
    };
}

// 状态管理器接口
export interface StateManager {
    // 获取状态
    getState(): AppState;
    
    // 派发动作
    dispatch(action: StateAction): void;
    
    // 订阅状态变化
    subscribe(listener: StateListener): () => void;
    
    // 中间件
    use(middleware: StateMiddleware): void;
}

// 状态监听器
export type StateListener = (state: AppState, prevState: AppState, action: StateAction) => void;

// 状态中间件
export type StateMiddleware = (
    action: StateAction,
    state: AppState,
    next: (action: StateAction) => void
) => void;

// 状态选择器
export type StateSelector<T> = (state: AppState) => T;

// 状态更新器
export type StateUpdater<T> = (state: T) => T | void;

// 异步状态操作
export interface AsyncStateOperation<T = any> {
    type: string;
    execute: (state: AppState) => Promise<T>;
    onSuccess?: (result: T, state: AppState) => StateAction[];
    onError?: (error: Error, state: AppState) => StateAction[];
    onFinally?: (state: AppState) => StateAction[];
}

// 状态持久化配置
export interface StatePersistConfig {
    key: string;
    storage: 'localStorage' | 'sessionStorage' | 'indexedDB';
    whitelist?: string[]; // 需要持久化的状态键
    blacklist?: string[]; // 不需要持久化的状态键
    transforms?: StateTransform[];
}

// 状态转换器
export interface StateTransform {
    in: (state: any) => any;   // 序列化时的转换
    out: (state: any) => any;  // 反序列化时的转换
}

// 状态验证器
export interface StateValidator {
    validate(state: AppState): ValidationResult;
    sanitize(state: AppState): AppState;
}

// 验证结果
export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

// 状态调试信息
export interface StateDebugInfo {
    actionHistory: StateAction[];
    stateSnapshots: { timestamp: string; state: AppState }[];
    performance: {
        actionCount: number;
        averageUpdateTime: number;
        slowActions: { type: string; duration: number }[];
    };
}
