/**
 * LRU (Least Recently Used) 缓存管理器
 * 用于管理图片预览缓存，自动清理最久未使用的项目
 */

export interface CacheItem<T> {
  key: string;
  value: T;
  size: number;
  timestamp: number;
  accessCount: number;
}

export interface CacheStats {
  size: number;
  count: number;
  maxSize: number;
  maxCount: number;
  hitRate: number;
  memoryUsage: string;
}

export class LRUCache<T> {
  private cache = new Map<string, CacheItem<T>>();
  private maxSize: number; // 最大内存大小（字节）
  private maxCount: number; // 最大项目数量
  private currentSize = 0;
  private hits = 0;
  private misses = 0;

  constructor(maxSize: number = 100 * 1024 * 1024, maxCount: number = 200) {
    this.maxSize = maxSize; // 默认100MB
    this.maxCount = maxCount; // 默认200个项目
  }

  /**
   * 获取缓存项
   */
  get(key: string): T | null {
    const item = this.cache.get(key);
    
    if (item) {
      // 更新访问信息
      item.timestamp = Date.now();
      item.accessCount++;
      this.hits++;
      
      // 移到最后（最近使用）
      this.cache.delete(key);
      this.cache.set(key, item);
      
      console.log('🎯 LRUCache: 缓存命中', {
        key: key.substring(0, 20),
        accessCount: item.accessCount,
        size: this.formatSize(item.size)
      });
      
      return item.value;
    }
    
    this.misses++;
    console.log('❌ LRUCache: 缓存未命中', key.substring(0, 20));
    return null;
  }

  /**
   * 设置缓存项
   */
  set(key: string, value: T, size: number): void {
    // 如果已存在，先删除旧的
    if (this.cache.has(key)) {
      const oldItem = this.cache.get(key)!;
      this.currentSize -= oldItem.size;
      this.cache.delete(key);
    }

    // 创建新项目
    const item: CacheItem<T> = {
      key,
      value,
      size,
      timestamp: Date.now(),
      accessCount: 1
    };

    // 检查是否需要清理空间
    this.ensureSpace(size);

    // 添加新项目
    this.cache.set(key, item);
    this.currentSize += size;

    console.log('💾 LRUCache: 添加缓存', {
      key: key.substring(0, 20),
      size: this.formatSize(size),
      totalSize: this.formatSize(this.currentSize),
      count: this.cache.size
    });

    // 检查数量限制
    this.enforceCountLimit();
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    const item = this.cache.get(key);
    if (item) {
      this.cache.delete(key);
      this.currentSize -= item.size;
      
      console.log('🗑️ LRUCache: 删除缓存', {
        key: key.substring(0, 20),
        size: this.formatSize(item.size)
      });
      
      return true;
    }
    return false;
  }

  /**
   * 检查是否存在
   */
  has(key: string): boolean {
    return this.cache.has(key);
  }

  /**
   * 清空缓存
   */
  clear(): void {
    const oldCount = this.cache.size;
    const oldSize = this.currentSize;
    
    this.cache.clear();
    this.currentSize = 0;
    this.hits = 0;
    this.misses = 0;
    
    console.log('🧹 LRUCache: 清空缓存', {
      clearedCount: oldCount,
      clearedSize: this.formatSize(oldSize)
    });
  }

  /**
   * 确保有足够空间
   */
  private ensureSpace(requiredSize: number): void {
    // 如果单个项目就超过最大大小，拒绝缓存
    if (requiredSize > this.maxSize) {
      console.warn('⚠️ LRUCache: 项目大小超过最大限制', {
        requiredSize: this.formatSize(requiredSize),
        maxSize: this.formatSize(this.maxSize)
      });
      return;
    }

    // 清理空间直到有足够的空间
    while (this.currentSize + requiredSize > this.maxSize && this.cache.size > 0) {
      this.evictLeastRecentlyUsed();
    }
  }

  /**
   * 强制执行数量限制
   */
  private enforceCountLimit(): void {
    while (this.cache.size > this.maxCount) {
      this.evictLeastRecentlyUsed();
    }
  }

  /**
   * 清理最久未使用的项目
   */
  private evictLeastRecentlyUsed(): void {
    // Map的迭代器按插入顺序，第一个就是最久未使用的
    const firstEntry = this.cache.entries().next();
    
    if (!firstEntry.done) {
      const [key, item] = firstEntry.value;
      this.cache.delete(key);
      this.currentSize -= item.size;
      
      console.log('🔄 LRUCache: 清理最久未使用项目', {
        key: key.substring(0, 20),
        size: this.formatSize(item.size),
        accessCount: item.accessCount,
        age: Date.now() - item.timestamp
      });
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    const hitRate = this.hits + this.misses > 0 ? this.hits / (this.hits + this.misses) : 0;
    
    return {
      size: this.currentSize,
      count: this.cache.size,
      maxSize: this.maxSize,
      maxCount: this.maxCount,
      hitRate: hitRate * 100,
      memoryUsage: this.formatSize(this.currentSize)
    };
  }

  /**
   * 格式化大小显示
   */
  private formatSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取所有缓存键
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * 获取缓存项详情（用于调试）
   */
  getItemDetails(key: string): CacheItem<T> | null {
    return this.cache.get(key) || null;
  }

  /**
   * 批量清理过期项目
   */
  cleanupExpired(maxAge: number = 30 * 60 * 1000): number {
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > maxAge) {
        this.delete(key);
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      console.log('🧹 LRUCache: 清理过期项目', {
        cleanedCount,
        maxAge: maxAge / 1000 + 's'
      });
    }
    
    return cleanedCount;
  }

  /**
   * 设置最大大小
   */
  setMaxSize(maxSize: number): void {
    this.maxSize = maxSize;
    this.ensureSpace(0); // 立即检查并清理
  }

  /**
   * 设置最大数量
   */
  setMaxCount(maxCount: number): void {
    this.maxCount = maxCount;
    this.enforceCountLimit(); // 立即检查并清理
  }
}
