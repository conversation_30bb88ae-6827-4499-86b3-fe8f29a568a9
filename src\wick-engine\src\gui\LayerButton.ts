/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { Button } from "./Button";
import { GUIElement } from "./GUIElement";
import { Bounds } from "./GUIElement";

interface LayerButtonArgs {
  toggledIcon: string;
  untoggledIcon: string;
  toggledTooltip: string;
  untoggledTooltip: string;
  isToggledFn: () => boolean;
  clickFn: () => void;
}

export class LayerButton extends Button {
  protected toggledIcon: string;
  protected untoggledIcon: string;
  protected toggledTooltip: string;
  protected untoggledTooltip: string;
  protected isToggledFn: () => boolean;

  constructor(model: Base, args: LayerButtonArgs) {
    super(model, args);

    this.toggledIcon = args.toggledIcon;
    this.untoggledIcon = args.untoggledIcon;
    this.toggledTooltip = args.toggledTooltip;
    this.untoggledTooltip = args.untoggledTooltip;
    this.isToggledFn = args.isToggledFn;
  }

  /**
   * Draw this layer button.
   * @param {boolean} isToggled - Should the button be toggled?
   */
  draw(isToggled?: boolean): void {
    super.draw();

    // Check if the button is toggled
    const isButtonToggled = this.isToggledFn && this.isToggledFn();

    const ctx = this.ctx;

    // Render different options depending on isToggledFn
    let icon: string;
    if (isButtonToggled) {
      this.tooltip.label = this.toggledTooltip;
      icon = this.toggledIcon;
    } else {
      this.tooltip.label = this.untoggledTooltip;
      icon = this.untoggledIcon;
    }

    // Change fill color depending on mouse interactions
    let fillColor: string;
    if (this.mouseState === "down") {
      fillColor = GUIElement.LAYER_BUTTON_MOUSEDOWN_COLOR;
    } else if (this.mouseState === "over") {
      fillColor = GUIElement.LAYER_BUTTON_HOVER_COLOR;
    } else if (isButtonToggled) {
      fillColor = GUIElement.LAYER_BUTTON_TOGGLE_ACTIVE_COLOR;
    } else {
      fillColor = GUIElement.LAYER_BUTTON_TOGGLE_INACTIVE_COLOR;
    }
    ctx.fillStyle = fillColor;

    // Button circle
    ctx.beginPath();
    ctx.arc(0, 0, GUIElement.LAYER_BUTTON_ICON_RADIUS, 0, 2 * Math.PI);
    ctx.fill();

    // Button icon
    const r = GUIElement.LAYER_BUTTON_ICON_RADIUS * 0.8;
    ctx.globalAlpha = 0.5;
    ctx.drawImage(GUIElement.Icons.getIcon(icon), -r, -r, r * 2, r * 2);
    ctx.globalAlpha = 1.0;
  }

  get bounds(): Bounds {
    const r = GUIElement.LAYER_BUTTON_ICON_RADIUS;
    return {
      x: -r,
      y: -r,
      width: r * 2,
      height: r * 2,
    };
  }
}
