/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Tool } from "./Tool";

export class Pencil extends Tool {
  static get MIN_ADD_POINT_MOVEMENT(): number {
    return 2;
  }

  protected name: string;
  protected path: any;
  protected _movement: any;

  /**
   * Creates a pencil tool.
   */
  constructor() {
    super();

    this.name = "pencil";
    this.path = null;
    this._movement = new (this.paper as any).Point();
  }

  get doubleClickEnabled(): boolean {
    return false;
  }

  /**
   * The pencil cursor.
   */
  get cursor(): string {
    return "url(cursors/pencil.png) 32 32, auto";
  }

  get isDrawingTool(): boolean {
    return true;
  }

  onActivate(e: any): void {}

  onDeactivate(e: any): void {}

  onMouseDown(e: any): void {
    this._movement = new (this.paper as any).Point();

    if (!this.path) {
      this.path = new (this.paper as any).Path({
        strokeColor: this.getSetting("strokeColor").rgba,
        strokeWidth: this.getSetting("strokeWidth"),
        strokeCap: "round",
      });
    }

    this.path.add(e.point);
  }

  onMouseDrag(e: any): void {
    if (!this.path) return;

    this._movement = this._movement.add(e.delta);

    if (
      this._movement.length >
      Pencil.MIN_ADD_POINT_MOVEMENT / this.paper.view.zoom
    ) {
      this._movement = new (this.paper as any).Point();
      this.path.add(e.point);
      this.path.smooth();
    }
  }

  onMouseUp(e: any): void {
    if (!this.path) return;

    this.path.add(e.point);
    this.path.simplify();
    this.path.remove();
    this.addPathToProject(this.path);
    this.path = null;
    this.fire("canvasModified", {}, "pencil");
  }

  // Helper methods (to be implemented)
  protected getSetting(name: string): any {
    return {};
  }
  protected addPathToProject(path: any): void {}
}
