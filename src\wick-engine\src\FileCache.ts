/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

declare const localforage: any;

interface FileInfo {
  src: string;
}

interface FileData {
  uuid: string;
  src: string;
}

/**
 * 用于存储和检索大型文件数据的全局工具类
 */
export class FileCache {
  protected static _files: { [key: string]: FileInfo } = {};

  /**
   * 在localforage中用于标识文件项的前缀
   */
  static get FILE_LOCALFORAGE_KEY_PREFIX(): string {
    return "filesrc_"; // 这个值不应该改变
  }

  /**
   * 将文件添加到缓存中
   * @param src - 文件源
   * @param uuid - 文件的UUID
   */
  static addFile(src: string, uuid: string): void {
    this._files[uuid] = {
      src: src,
    };

    // 将资源保存到localforage
    localforage.setItem(this.getLocalForageKeyForUUID(uuid), src).then(() => {
      // 保存完成
    });
  }

  /**
   * 通过UUID获取文件信息
   * @param uuid - 文件的UUID
   * @returns 文件信息
   */
  static getFile(uuid: string): FileInfo | null {
    const file = this._files[uuid];
    if (!file) {
      console.error("Asset with UUID " + uuid + " was not found in FileCache!");
      return null;
    } else {
      return file;
    }
  }

  /**
   * 从FileCache中移除指定UUID的文件
   * @param uuid - 要移除的文件的UUID
   */
  static removeFile(uuid: string): void {
    delete this._files[uuid];

    // 从localforage中移除文件
    localforage.removeItem(this.getLocalForageKeyForUUID(uuid)).then(() => {});
  }

  /**
   * 从localforage加载之前保存的项目的所有文件（如果可能）
   * @param project - 要加载资源的项目
   * @param callback - 资源加载完成时调用的回调函数
   */
  static loadFilesFromLocalforage(project: any, callback: () => void): void {
    Promise.all(
      project.getAssets().map((asset: any) => {
        return localforage.getItem(this.getLocalForageKeyForUUID(asset.uuid));
      })
    ).then((assets: any[]) => {
      for (let i = 0; i < assets.length; i++) {
        this.addFile(assets[i], project.getAssets()[i].uuid);
      }
      callback();
    });
  }

  /**
   * 获取WickFileCache中的所有文件
   * @returns 包含所有文件的对象数组
   */
  static getAllFiles(): FileData[] {
    const files: FileData[] = [];
    for (const uuid in this._files) {
      files.push({
        uuid: uuid,
        src: this._files[uuid].src,
      });
    }
    return files;
  }

  /**
   * 清除缓存
   */
  static clear(): void {
    this._files = {};
  }

  /**
   * 清除localforage中的所有文件
   */
  static clearLocalforage(): void {
    // 从localforage中清除所有文件
    for (const uuid in this._files) {
      localforage
        .removeItem(this.getLocalForageKeyForUUID(uuid))
        .then(() => {});
    }
  }

  /**
   * 获取UUID对应的localforage键
   * @param uuid - 文件的UUID
   * @returns localforage键
   */
  static getLocalForageKeyForUUID(uuid: string): string {
    return this.FILE_LOCALFORAGE_KEY_PREFIX + uuid;
  }
}
