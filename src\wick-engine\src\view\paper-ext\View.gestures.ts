/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Paper.js-drawing-tools.
 *
 * Paper.js-drawing-tools is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Paper.js-drawing-tools is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Paper.js-drawing-tools.  If not, see <https://www.gnu.org/licenses/>.
 */

interface GestureArgs {
  enabled?: boolean;
  pinchToZoom?: boolean;
  rotateEnabled?: boolean;
  minScale?: number;
  maxScale?: number;
}

declare module 'paper' {
  interface View {
    enableGestures(args?: GestureArgs): void;
  }
}

declare const $: any;

paper.View.inject({
  enableGestures: function(args: GestureArgs = {}) {
    const {
      enabled = true,
      pinchToZoom = true,
      rotateEnabled = true,
      minScale = 0.1,
      maxScale = 10
    } = args;

    if (!enabled) return;

    const view = this;
    let initialScale = 1;
    let initialRotation = 0;

    $(this.element.parentElement).on('gesturestart', (event: any) => {
      event.preventDefault();
      initialScale = view.zoom;
      initialRotation = view.rotation;
    });

    $(this.element.parentElement).on('gesturechange', (event: any) => {
      event.preventDefault();
      
      if (pinchToZoom) {
        const newScale = initialScale * event.originalEvent.scale;
        const boundedScale = Math.min(Math.max(newScale, minScale), maxScale);
        view.zoom = boundedScale;
      }

      if (rotateEnabled) {
        const rotation = initialRotation + event.originalEvent.rotation;
        view.rotate(rotation - view.rotation);
      }
    });

    $(this.element.parentElement).on('gestureend', (event: any) => {
      event.preventDefault();
    });
  },
}));