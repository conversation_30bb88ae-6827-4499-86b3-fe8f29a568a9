import { Base } from "./Base";
import { Frame } from "./Frame";

/**
 * Timeline类，用于管理动画时间轴
 */
export class Timeline extends Base {
  private _frames: Frame[];
  private _currentFrame: number;
  private _playheadPosition: number;
  private _isPlaying: boolean;

  /**
   * 创建一个时间轴对象
   */
  constructor(args: { project?: any } = {}) {
    super({ project: args.project });

    this._frames = [];
    this._currentFrame = 0;
    this._playheadPosition = 0;
    this._isPlaying = false;
  }

  /**
   * 添加帧
   */
  addFrame(frame: Frame, position?: number): void {
    if (position === undefined) {
      position = this._frames.length;
    }
    this._frames.splice(position, 0, frame);
    frame.parent = this;
  }

  /**
   * 移除帧
   */
  removeFrame(frame: Frame): void {
    const index = this._frames.indexOf(frame);
    if (index !== -1) {
      this._frames.splice(index, 1);
      frame.parent = null;
    }
  }

  /**
   * 播放动画
   */
  play(): void {
    this._isPlaying = true;
  }

  /**
   * 暂停动画
   */
  pause(): void {
    this._isPlaying = false;
  }

  /**
   * 停止动画
   */
  stop(): void {
    this._isPlaying = false;
    this._playheadPosition = 0;
    this._currentFrame = 0;
  }

  /**
   * 更新时间轴
   */
  update(): void {
    if (!this._isPlaying) return;

    this._playheadPosition++;
    if (this._playheadPosition >= this._frames.length) {
      this._playheadPosition = 0;
    }

    this._currentFrame = this._playheadPosition;
  }

  /**
   * 获取当前帧
   */
  get currentFrame(): Frame | null {
    return this._frames[this._currentFrame] || null;
  }

  /**
   * 获取播放头位置
   */
  get playheadPosition(): number {
    return this._playheadPosition;
  }

  /**
   * 设置播放头位置
   */
  set playheadPosition(value: number) {
    this._playheadPosition = Math.max(
      0,
      Math.min(value, this._frames.length - 1)
    );
    this._currentFrame = this._playheadPosition;
  }

  /**
   * 获取是否正在播放
   */
  get isPlaying(): boolean {
    return this._isPlaying;
  }

  /**
   * 获取所有帧
   */
  get frames(): Frame[] {
    return this._frames;
  }
}
