/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Tool } from "./Tool";

export class Text extends Tool {
  protected name: string;
  protected path: any;
  protected textItem: any;
  protected isEditing: boolean;

  /**
   * Creates an instance of the text tool.
   */
  constructor() {
    super();

    this.name = "text";
    this.path = null;
    this.textItem = null;
    this.isEditing = false;
  }

  get doubleClickEnabled(): boolean {
    return false;
  }

  /**
   * The cursor style for the text tool.
   */
  get cursor(): string {
    return "text";
  }

  get isDrawingTool(): boolean {
    return true;
  }

  onActivate(e: any): void {
    if (this.textItem) {
      this.textItem.remove();
      this.textItem = null;
    }
  }

  onDeactivate(e: any): void {
    if (this.textItem) {
      this.textItem.remove();
      this.textItem = null;
    }
  }

  onMouseDown(e: any): void {
    if (!this.textItem) {
      this.textItem = new (this.paper as any).PointText({
        point: e.point,
        content: "",
        fillColor: this.getSetting("fillColor").rgba,
        fontFamily: this.getSetting("fontFamily"),
        fontSize: this.getSetting("fontSize"),
        fontWeight: this.getSetting("fontWeight"),
        fontStyle: this.getSetting("fontStyle"),
      });
      this.textItem.edit();
      this.isEditing = true;
    }
  }

  onMouseDrag(e: any): void {
    // Text tool doesn't need drag functionality
  }

  onMouseUp(e: any): void {
    if (this.isEditing && this.textItem) {
      if (this.textItem.content.length > 0) {
        this.addPathToProject(this.textItem);
        this.fire("canvasModified", {}, "text");
      }
      this.textItem.remove();
      this.textItem = null;
      this.isEditing = false;
    }
  }

  // Helper methods (to be implemented)
  protected getSetting(name: string): any {
    return {};
  }
  protected addPathToProject(path: any): void {}
}
