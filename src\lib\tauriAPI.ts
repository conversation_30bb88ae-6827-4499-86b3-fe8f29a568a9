/**
 * Tauri API封装模块
 * 统一管理所有与Tauri后端的通信接口
 * 注意：这是客户端代码，用于与Tauri后端通信
 */

import { invoke } from '@tauri-apps/api/core';

/**
 * 窗口信息接口
 */
export interface WindowInfo {
  is_maximized: boolean;
  is_minimized: boolean;
  is_visible: boolean;
  is_focused: boolean;
}

/**
 * API响应结果接口
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * 窗口管理API类
 * 封装所有窗口相关的操作
 */
export class WindowAPI {
  /**
   * 最小化窗口
   * @returns Promise<ApiResponse> 操作结果
   */
  static async minimize(): Promise<ApiResponse> {
    try {
      await invoke('minimize_window');
      return { success: true };
    } catch (error) {
      console.error('最小化窗口失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 最大化窗口
   * @returns Promise<ApiResponse> 操作结果
   */
  static async maximize(): Promise<ApiResponse> {
    try {
      await invoke('maximize_window');
      return { success: true };
    } catch (error) {
      console.error('最大化窗口失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 取消最大化窗口
   * @returns Promise<ApiResponse> 操作结果
   */
  static async unmaximize(): Promise<ApiResponse> {
    try {
      await invoke('unmaximize_window');
      return { success: true };
    } catch (error) {
      console.error('取消最大化窗口失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 关闭窗口
   * @returns Promise<ApiResponse> 操作结果
   */
  static async close(): Promise<ApiResponse> {
    try {
      await invoke('close_window');
      return { success: true };
    } catch (error) {
      console.error('关闭窗口失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 检查窗口是否最大化
   * @returns Promise<ApiResponse<boolean>> 窗口最大化状态
   */
  static async isMaximized(): Promise<ApiResponse<boolean>> {
    try {
      const result = await invoke('is_window_maximized') as boolean;
      return { success: true, data: result };
    } catch (error) {
      console.error('获取窗口最大化状态失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 切换窗口最大化状态
   * @returns Promise<ApiResponse<boolean>> 新的最大化状态
   */
  static async toggleMaximize(): Promise<ApiResponse<boolean>> {
    try {
      const result = await invoke('toggle_maximize_window') as boolean;
      return { success: true, data: result };
    } catch (error) {
      console.error('切换窗口最大化状态失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 设置窗口大小
   * @param width 窗口宽度
   * @param height 窗口高度
   * @returns Promise<ApiResponse> 操作结果
   */
  static async setSize(width: number, height: number): Promise<ApiResponse> {
    try {
      await invoke('set_window_size', { width, height });
      return { success: true };
    } catch (error) {
      console.error('设置窗口大小失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 设置窗口位置
   * @param x 窗口X坐标
   * @param y 窗口Y坐标
   * @returns Promise<ApiResponse> 操作结果
   */
  static async setPosition(x: number, y: number): Promise<ApiResponse> {
    try {
      await invoke('set_window_position', { x, y });
      return { success: true };
    } catch (error) {
      console.error('设置窗口位置失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 获取窗口信息
   * @returns Promise<ApiResponse<WindowInfo>> 窗口信息
   */
  static async getInfo(): Promise<ApiResponse<WindowInfo>> {
    try {
      const result = await invoke('get_window_info') as WindowInfo;
      return { success: true, data: result };
    } catch (error) {
      console.error('获取窗口信息失败:', error);
      return { success: false, error: String(error) };
    }
  }
}

/**
 * 应用程序API类
 * 封装应用程序相关的操作
 */
export class AppAPI {
  /**
   * 问候功能（示例API）
   * @param name 用户名
   * @returns Promise<ApiResponse<string>> 问候消息
   */
  static async greet(name: string): Promise<ApiResponse<string>> {
    try {
      const result = await invoke('greet', { name }) as string;
      return { success: true, data: result };
    } catch (error) {
      console.error('问候功能失败:', error);
      return { success: false, error: String(error) };
    }
  }
}

/**
 * 文件系统API类
 * 封装文件操作相关的功能
 */
export class FileAPI {
  /**
   * 打开文件对话框
   * @returns Promise<ApiResponse<string>> 选择的文件路径
   */
  static async openFileDialog(): Promise<ApiResponse<string>> {
    try {
      // 这里需要后端实现文件对话框功能
      // const result = await invoke('open_file_dialog') as string;
      // return { success: true, data: result };

      // 临时返回，等待后端实现
      return { success: false, error: '文件对话框功能尚未实现' };
    } catch (error) {
      console.error('打开文件对话框失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 保存文件对话框
   * @returns Promise<ApiResponse<string>> 保存的文件路径
   */
  static async saveFileDialog(): Promise<ApiResponse<string>> {
    try {
      // 这里需要后端实现文件对话框功能
      // const result = await invoke('save_file_dialog') as string;
      // return { success: true, data: result };

      // 临时返回，等待后端实现
      return { success: false, error: '保存文件对话框功能尚未实现' };
    } catch (error) {
      console.error('保存文件对话框失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 读取文件内容
   * @param filePath 文件路径
   * @returns Promise<ApiResponse<string>> 文件内容
   */
  static async readFile(filePath: string): Promise<ApiResponse<string>> {
    try {
      // 这里需要后端实现文件读取功能
      // const result = await invoke('read_file', { filePath }) as string;
      // return { success: true, data: result };

      // 临时返回，等待后端实现
      console.log('读取文件:', filePath);
      return { success: false, error: '文件读取功能尚未实现' };
    } catch (error) {
      console.error('读取文件失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 写入文件内容
   * @param filePath 文件路径
   * @param content 文件内容
   * @returns Promise<ApiResponse> 操作结果
   */
  static async writeFile(filePath: string, content: string): Promise<ApiResponse> {
    try {
      // 这里需要后端实现文件写入功能
      // await invoke('write_file', { filePath, content });
      // return { success: true };

      // 临时返回，等待后端实现
      console.log('写入文件:', filePath, content);
      return { success: false, error: '文件写入功能尚未实现' };
    } catch (error) {
      console.error('写入文件失败:', error);
      return { success: false, error: String(error) };
    }
  }
}

/**
 * 项目配置接口
 */
export interface ProjectConfig {
  name: string;
  path: string;
  engine_version: string;
  script_files: string[];
  data_path: string;
  img_path: string;
  audio_path: string;
}

/**
 * 脚本信息接口
 */
export interface ScriptInfo {
  url: string;
  local_path: string;
  is_required: boolean;
}

/**
 * 当前项目信息接口
 */
export interface CurrentProjectInfo {
  project_path: string;
  project_file_path: string;
  timestamp: number;
}

/**
 * 插件保存结果接口
 */
export interface PluginSaveResult {
  file_name: string;
  file_path: string;
  plugin_name: string;
}

/**
 * 项目保存结果接口
 */
export interface ProjectSaveResult {
  success: boolean;
  file_path?: string;
  file_size?: number;
  error?: string;
}

/**
 * 项目加载结果接口
 */
export interface ProjectLoadResult {
  success: boolean;
  data?: any;
  error?: string;
  warnings?: string[];
}

/**
 * 项目管理API类
 * 封装项目相关的操作
 */
export class ProjectAPI {
  /**
   * 获取项目配置
   * @param projectPath 项目路径
   * @returns Promise<ApiResponse<ProjectConfig>> 项目配置
   */
  static async getProjectConfig(projectPath: string): Promise<ApiResponse<ProjectConfig>> {
    try {
      const result = await invoke('get_project_config', { projectPath }) as ProjectConfig;
      return { success: true, data: result };
    } catch (error) {
      console.error('获取项目配置失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 扫描项目脚本文件
   * @param projectPath 项目路径
   * @returns Promise<ApiResponse<ScriptInfo[]>> 脚本文件列表
   */
  static async scanProjectScripts(projectPath: string): Promise<ApiResponse<ScriptInfo[]>> {
    try {
      const result = await invoke('scan_project_scripts', { projectPath }) as ScriptInfo[];
      return { success: true, data: result };
    } catch (error) {
      console.error('扫描项目脚本失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 读取项目文件内容（文本文件）
   * @param filePath 文件路径
   * @returns Promise<ApiResponse<string>> 文件内容
   */
  static async readProjectFile(filePath: string): Promise<ApiResponse<string>> {
    try {
      const result = await invoke('read_project_file', { filePath }) as string;
      return { success: true, data: result };
    } catch (error) {
      console.error('读取项目文件失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 读取项目二进制文件内容
   * @param filePath 文件路径
   * @returns Promise<ApiResponse<{buffer: ArrayBuffer}>> 二进制文件内容
   */
  static async readProjectBinaryFile(filePath: string): Promise<ApiResponse<{buffer: ArrayBuffer}>> {
    try {
      const result = await invoke('read_project_binary_file', { filePath }) as number[];

      // 🎯 修复：正确转换为ArrayBuffer
      const uint8Array = new Uint8Array(result);
      const arrayBuffer = uint8Array.buffer.slice(uint8Array.byteOffset, uint8Array.byteOffset + uint8Array.byteLength);

      console.log('📁 TauriAPI: 读取二进制文件成功', {
        filePath: filePath.split(/[/\\]/).pop(),
        originalSize: result.length,
        bufferSize: arrayBuffer.byteLength
      });

      return {
        success: true,
        data: { buffer: arrayBuffer }
      };
    } catch (error) {
      console.error('读取项目二进制文件失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 🎯 批量处理图片 - 新增API
   * @param images 图片输入数组
   * @param options 处理选项
   * @returns Promise<ApiResponse<BatchProcessResponse>> 批量处理结果
   */
  static async batchProcessImages(
    images: Array<{
      id: string;
      buffer: ArrayBuffer;
      originalType: string;
      fileName: string;
    }>,
    options: {
      generateThumbnail?: boolean;
      thumbnailSize?: number;
      generatePreview?: boolean;
      previewSize?: number;
      quality?: number;
      format?: string;
    } = {}
  ): Promise<ApiResponse<any>> {
    try {
      console.log('🚀 TauriAPI: 开始批量处理图片', {
        imageCount: images.length,
        options
      });

      // 转换ArrayBuffer为数组格式（Tauri需要）
      const imageInputs = images.map(img => ({
        id: img.id,
        buffer: Array.from(new Uint8Array(img.buffer)),
        originalType: img.originalType,
        fileName: img.fileName
      }));

      const result = await invoke('batch_process_images', {
        images: imageInputs,
        options: {
          generateThumbnail: options.generateThumbnail ?? true,
          thumbnailSize: options.thumbnailSize ?? 128,
          generatePreview: options.generatePreview ?? true,
          previewSize: options.previewSize ?? 256,
          quality: options.quality ?? 0.8,
          format: options.format ?? 'webp'
        }
      });

      console.log('✅ TauriAPI: 批量处理图片成功', result);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('❌ TauriAPI: 批量处理图片失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 🎯 统一保存项目 - 新API
   * @param filePath 保存文件路径
   * @param projectName 项目名称
   * @param description 项目描述
   * @param tags 项目标签
   * @param imageResources 图片资源数据
   * @param atlasData 图集数据
   * @param exportSettings 导出设置
   * @returns Promise<ApiResponse<ProjectSaveResult>> 保存结果
   */
  static async saveProjectUnified(
    filePath: string,
    projectName: string,
    description: string | undefined,
    tags: string[] | undefined,
    imageResources: any,
    atlasData: any,
    exportSettings: any
  ): Promise<ApiResponse<any>> {
    try {
      console.log('🚀 TauriAPI: 开始统一保存项目', {
        filePath,
        projectName,
        hasImageResources: !!imageResources,
        hasAtlasData: !!atlasData,
        hasExportSettings: !!exportSettings
      });

      const result = await invoke('save_project_unified', {
        filePath,
        projectName,
        description,
        tags,
        imageResources,
        atlasData,
        exportSettings
      });

      console.log('✅ TauriAPI: 统一保存项目成功', result);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('❌ TauriAPI: 统一保存项目失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 🎯 统一加载项目 - 新API
   * @param filePath 项目文件路径
   * @returns Promise<ApiResponse<ProjectLoadResult>> 加载结果
   */
  static async loadProjectUnified(filePath: string): Promise<ApiResponse<any>> {
    try {
      console.log('🚀 TauriAPI: 开始统一加载项目', { filePath });

      const result = await invoke('load_project_unified', {
        filePath
      });

      console.log('✅ TauriAPI: 统一加载项目成功', result);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('❌ TauriAPI: 统一加载项目失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 🎯 二进制保存项目 - 新API
   * @param filePath 项目文件路径
   * @param projectName 项目名称
   * @param description 项目描述
   * @param tags 项目标签
   * @param imageResources 图片资源数据
   * @param atlasData 图集数据
   * @param exportSettings 导出设置
   * @returns Promise<ApiResponse<BinarySaveResult>> 保存结果
   */
  static async saveProjectBinary(
    filePath: string,
    projectName: string,
    description: string | undefined,
    tags: string[] | undefined,
    imageResources: any,
    atlasData: any,
    exportSettings: any
  ): Promise<ApiResponse<any>> {
    try {
      console.log('🚀 TauriAPI: 开始二进制保存项目', {
        filePath,
        projectName,
        resourceCount: imageResources?.resources?.length || 0
      });

      const result = await invoke('save_project_binary', {
        filePath,
        projectName,
        description,
        tags,
        imageResources,
        atlasData,
        exportSettings
      });

      console.log('✅ TauriAPI: 二进制保存项目成功', result);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('❌ TauriAPI: 二进制保存项目失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 🎯 二进制加载项目 - 新API
   * @param filePath 项目文件路径
   * @returns Promise<ApiResponse<BinaryLoadResult>> 加载结果
   */
  static async loadProjectBinary(filePath: string): Promise<ApiResponse<any>> {
    try {
      console.log('🚀 TauriAPI: 开始二进制加载项目', { filePath });

      const result = await invoke('load_project_binary', {
        filePath
      });

      console.log('✅ TauriAPI: 二进制加载项目成功', result);

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('❌ TauriAPI: 二进制加载项目失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 🎯 验证二进制项目文件
   * @param filePath 项目文件路径
   * @returns Promise<ApiResponse<boolean>> 验证结果
   */
  static async validateProjectBinary(filePath: string): Promise<ApiResponse<boolean>> {
    try {
      const result = await invoke('validate_project_binary', {
        filePath
      }) as boolean;

      return {
        success: true,
        data: result
      };
    } catch (error) {
      console.error('❌ TauriAPI: 验证二进制项目文件失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 验证项目是否有效
   * @param projectPath 项目路径
   * @returns Promise<ApiResponse<boolean>> 验证结果
   */
  static async validateProject(projectPath: string): Promise<ApiResponse<boolean>> {
    try {
      const result = await invoke('validate_project', { projectPath }) as boolean;
      return { success: true, data: result };
    } catch (error) {
      console.error('验证项目失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 获取最近的项目列表
   * @returns Promise<ApiResponse<ProjectConfig[]>> 项目列表
   */
  static async getRecentProjects(): Promise<ApiResponse<ProjectConfig[]>> {
    try {
      const result = await invoke('get_recent_projects') as ProjectConfig[];
      return { success: true, data: result };
    } catch (error) {
      console.error('获取最近项目失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 选择项目文件（.rmmzproject）
   * @returns Promise<ApiResponse<string>> 选择的项目文件路径
   */
  static async selectProjectFile(): Promise<ApiResponse<string>> {
    try {
      // 使用 Tauri 的前端对话框 API
      const { open } = await import('@tauri-apps/plugin-dialog');
      const result = await open({
        directory: false,
        multiple: false,
        title: '选择 RPG Maker 项目文件',
        filters: [
          {
            name: 'RPG Maker MZ Project',
            extensions: ['rmmzproject']
          },
          {
            name: 'All Files',
            extensions: ['*']
          }
        ]
      });

      if (result && typeof result === 'string') {
        return { success: true, data: result };
      } else {
        return { success: false, error: '用户取消选择' };
      }
    } catch (error) {
      console.error('选择项目文件失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 从项目文件路径获取项目目录
   * @param projectFilePath 项目文件路径
   * @returns Promise<ApiResponse<string>> 项目目录路径
   */
  static async getProjectDirectoryFromFile(projectFilePath: string): Promise<ApiResponse<string>> {
    try {
      const result = await invoke('get_project_directory_from_file', { projectFilePath }) as string;
      return { success: true, data: result };
    } catch (error) {
      console.error('获取项目目录失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 选择项目目录（使用前端对话框）
   * @returns Promise<ApiResponse<string>> 选择的目录路径
   */
  static async selectProjectDirectory(): Promise<ApiResponse<string>> {
    try {
      // 使用 Tauri 的前端对话框 API
      const { open } = await import('@tauri-apps/plugin-dialog');
      const result = await open({
        directory: true,
        multiple: false,
        title: '选择 RPG Maker 项目目录'
      });

      if (result && typeof result === 'string') {
        return { success: true, data: result };
      } else {
        return { success: false, error: '用户取消选择' };
      }
    } catch (error) {
      console.error('选择项目目录失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 保存当前项目信息到后端全局容器
   * @param projectPath 项目路径
   * @param projectFilePath 项目文件路径
   * @returns Promise<ApiResponse> 操作结果
   */
  static async saveCurrentProjectInfo(projectPath: string, projectFilePath: string): Promise<ApiResponse> {
    try {
      await invoke('save_current_project_info', { projectPath, projectFilePath });
      return { success: true };
    } catch (error) {
      console.error('保存当前项目信息失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 从后端全局容器获取当前项目信息
   * @returns Promise<ApiResponse<CurrentProjectInfo | null>> 项目信息
   */
  static async getCurrentProjectInfo(): Promise<ApiResponse<CurrentProjectInfo | null>> {
    try {
      const result = await invoke('get_current_project_info') as CurrentProjectInfo | null;
      return { success: true, data: result };
    } catch (error) {
      console.error('获取当前项目信息失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 清除后端全局容器中的当前项目信息
   * @returns Promise<ApiResponse> 操作结果
   */
  static async clearCurrentProjectInfo(): Promise<ApiResponse> {
    try {
      await invoke('clear_current_project_info');
      return { success: true };
    } catch (error) {
      console.error('清除当前项目信息失败:', error);
      return { success: false, error: String(error) };
    }
  }
}

/**
 * 操作记录API类
 * 封装操作记录的保存和加载功能
 */
export class OperationRecordsAPI {
  /**
   * 保存操作记录到项目目录
   * @param projectPath 项目路径
   * @param recordsData 操作记录数据（JSON字符串）
   * @returns Promise<ApiResponse<string>> 保存的文件路径
   */
  static async saveOperationRecords(projectPath: string, recordsData: string): Promise<ApiResponse<string>> {
    try {
      const result = await invoke('save_operation_records', {
        projectPath,
        recordsData
      }) as string;
      return { success: true, data: result };
    } catch (error) {
      console.error('保存操作记录失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 从项目目录加载操作记录
   * @param projectPath 项目路径
   * @returns Promise<ApiResponse<string | null>> 操作记录数据（JSON字符串）
   */
  static async loadOperationRecords(projectPath: string): Promise<ApiResponse<string | null>> {
    try {
      const result = await invoke('load_operation_records', { projectPath }) as string | null;
      return { success: true, data: result };
    } catch (error) {
      console.error('加载操作记录失败:', error);
      return { success: false, error: String(error) };
    }
  }



  /**
   * 获取操作记录文件的路径
   * @param projectPath 项目路径
   * @returns Promise<ApiResponse<string | null>> 记录文件路径
   */
  static async getOperationRecordsFilePath(projectPath: string): Promise<ApiResponse<string | null>> {
    try {
      const result = await invoke('get_operation_records_file_path', { projectPath }) as string | null;
      return { success: true, data: result };
    } catch (error) {
      console.error('获取操作记录文件路径失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 保存插件文件到项目的 js/plugins 目录
   * @param projectPath 项目路径
   * @param pluginCode 插件代码
   * @returns Promise<ApiResponse<PluginSaveResult>> 保存结果
   */
  static async savePluginFile(projectPath: string, pluginCode: string): Promise<ApiResponse<PluginSaveResult>> {
    try {
      const result = await invoke('save_plugin_file', {
        projectPath,
        pluginCode
      }) as PluginSaveResult;
      return { success: true, data: result };
    } catch (error) {
      console.error('保存插件文件失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 保存项目文件
   * @param filePath 保存文件路径
   * @param atlasData 图集数据
   * @param resourceData 资源数据
   * @param exportSettings 导出设置
   * @param projectName 项目名称
   * @param description 项目描述
   * @param tags 项目标签
   * @returns Promise<ApiResponse<ProjectSaveResult>> 保存结果
   */
  static async saveProjectFile(
    filePath: string,
    atlasData: any,
    resourceData: any,
    exportSettings: any,
    projectName: string,
    description?: string,
    tags?: string[]
  ): Promise<ApiResponse<ProjectSaveResult>> {
    try {
      const result = await invoke('save_project_file', {
        filePath,
        atlasData,
        resourceData,
        exportSettings,
        projectName,
        description,
        tags
      }) as ProjectSaveResult;
      return { success: true, data: result };
    } catch (error) {
      console.error('保存项目文件失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 加载项目文件
   * @param filePath 项目文件路径
   * @returns Promise<ApiResponse<ProjectLoadResult>> 加载结果
   */
  static async loadProjectFile(filePath: string): Promise<ApiResponse<ProjectLoadResult>> {
    try {
      const result = await invoke('load_project_file', {
        filePath
      }) as ProjectLoadResult;
      return { success: true, data: result };
    } catch (error) {
      console.error('加载项目文件失败:', error);
      return { success: false, error: String(error) };
    }
  }

  /**
   * 验证项目文件
   * @param filePath 项目文件路径
   * @returns Promise<ApiResponse<boolean>> 验证结果
   */
  static async validateProjectFile(filePath: string): Promise<ApiResponse<boolean>> {
    try {
      const result = await invoke('validate_project_file', {
        filePath
      }) as boolean;
      return { success: true, data: result };
    } catch (error) {
      console.error('验证项目文件失败:', error);
      return { success: false, error: String(error) };
    }
  }

}

/**
 * 统一的Tauri API导出
 * 提供所有API类的统一访问入口
 */
export const TauriAPI = {
  Window: WindowAPI,
  App: AppAPI,
  File: FileAPI,
  Project: ProjectAPI,
  OperationRecords: OperationRecordsAPI,
} as const;

// 默认导出
export default TauriAPI;
