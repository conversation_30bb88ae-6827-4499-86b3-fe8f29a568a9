/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

declare global {
  interface Window {
    Wick: any;
  }
}

interface WickObjectFileOptions {
  width?: number;
  height?: number;
  onProgress?: (frame: number, maxFrames: number) => void;
  onFinish: (wickObjectData: string) => void;
  format?: "base64" | "json";
}

export class WickObjectFile {
  /**
   * Create a Wick object file from a project object.
   * @param object - The object to create a Wick object file from
   * @param options - Configuration options for Wick object file generation
   */
  static toWickObjectFile(object: any, options: WickObjectFileOptions): void {
    const { onProgress, onFinish, format = "json" } = options;

    object.generateWickObjectFile({
      width: options.width,
      height: options.height,
      onFinish: (wickObjectData: string) => {
        onFinish(format === "base64" ? btoa(wickObjectData) : wickObjectData);
      },
      onProgress: onProgress,
    });
  }

  /**
   * Load an object from a Wick object file.
   * @param wickObjectData - The Wick object file data to load
   * @param callback - Function that receives the loaded object
   */
  static fromWickObjectFile(
    wickObjectData: string,
    callback: (object: any) => void
  ): void {
    try {
      const objectData = JSON.parse(wickObjectData);
      const object = new (window.Wick.Base as any)(objectData);
      callback(object);
    } catch (e) {
      console.error(
        "WickObjectFile: Error loading object from Wick object file"
      );
      console.error(e);
      callback(null);
    }
  }
}

// Add to global Wick namespace
if (typeof window !== "undefined") {
  window.Wick = window.Wick || {};
  window.Wick.WickObjectFile = WickObjectFile;
}
