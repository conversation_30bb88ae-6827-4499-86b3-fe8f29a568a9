<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { createEngine, type GameSpriteEngine, type Scene, type Project } from '../engine/index.js';

  // 引擎实例
  let engine: GameSpriteEngine | null = null;
  let scene: Scene | null = null;
  let project: Project | null = null;

  // Canvas 元素
  let canvasElement: HTMLCanvasElement;

  // 状态
  let isInitialized = false;
  let initError: string | null = null;

  // 初始化引擎
  async function initializeEngine() {
    try {
      console.log('🎮 GameSprite Engine: 开始初始化...');

      // 创建引擎实例
      const newEngine = await createEngine(canvasElement, {
        width: 800,
        height: 600,
        backgroundColor: 0x2a2a2a
      });

      // 获取项目和默认场景
      const newProject = newEngine.getProject();
      const newScene = newEngine.getScene();

      console.log('✅ GameSprite Engine: 引擎创建成功', {
        engineId: newEngine.id,
        projectId: newProject?.id,
        projectName: newProject?.name,
        sceneId: newScene?.id,
        renderer: newEngine.getRenderer()
      });

      // 赋值给组件变量
      engine = newEngine;
      project = newProject;
      scene = newScene;
      isInitialized = true;

    } catch (error) {
      console.error('❌ GameSprite Engine: 初始化失败', error);
      initError = error instanceof Error ? error.message : '初始化失败';
    }
  }

  // 组件挂载时初始化
  onMount(async () => {
    if (canvasElement) {
      await initializeEngine();
    }
  });

  // 组件销毁时清理
  onDestroy(() => {
    if (engine) {
      console.log('🧹 GameSprite Engine: 清理引擎');
      engine.destroy();
      engine = null;
      scene = null;
    }
  });

  // 暴露给父组件的方法
  export function getEngine(): GameSpriteEngine | null {
    return engine;
  }

  export function getScene(): Scene | null {
    return scene;
  }

  export function isEngineReady(): boolean {
    return isInitialized && engine !== null;
  }
</script>

<div class="center-panel">
  <!-- 引擎状态显示 -->
  <div class="engine-status">
    {#if initError}
      <div class="status-error">
        ❌ 引擎初始化失败: {initError}
      </div>
    {:else if !isInitialized}
      <div class="status-loading">
        🔄 正在初始化 GameSprite Engine...
      </div>
    {:else}
      <div class="status-ready">
        ✅ GameSprite Engine 已就绪
      </div>
    {/if}
  </div>

  <!-- 主画布 -->
  <div class="canvas-container">
    <canvas
      bind:this={canvasElement}
      class="main-canvas"
      class:initialized={isInitialized}
      class:error={!!initError}
    ></canvas>
  </div>

  <!-- 调试信息 -->
  {#if isInitialized && engine}
    <div class="debug-info">
      <div class="debug-item">
        <span class="debug-label">引擎ID:</span>
        <span class="debug-value">{engine.id}</span>
      </div>
      {#if project}
        <div class="debug-item">
          <span class="debug-label">项目:</span>
          <span class="debug-value">{project.name} ({project.width}×{project.height})</span>
        </div>
      {/if}
      {#if scene}
        <div class="debug-item">
          <span class="debug-label">场景:</span>
          <span class="debug-value">{scene.name} ({scene.width}×{scene.height})</span>
        </div>
      {/if}
      <div class="debug-item">
        <span class="debug-label">状态:</span>
        <span class="debug-value">{engine.isRunning() ? '运行中' : '已停止'}</span>
      </div>
    </div>
  {/if}
</div>

<style>
  .center-panel {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: var(--theme-bg-secondary, #1e1e1e);
    position: relative;
    overflow: hidden;
  }

  .engine-status {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 10;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .status-loading {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
  }

  .status-ready {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
  }

  .status-error {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
  }

  .canvas-container {
    /* 容器保持在文档流中，为canvas提供定位上下文 */
    flex: 1;
    position: relative;
    width: 100%;
    height: 100%;
    background: #2a2a2a;
    /* 移除边界，让canvas无缝填充 */
    overflow: hidden;
  }

  .main-canvas {
    /* canvas保持固定尺寸，在CenterPanel中居中显示 */
    position: absolute !important;  /* 相对于canvas-container定位 */
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important; /* 居中显示 */
    width: 1200px !important;  /* 固定宽度，比大多数面板都大 */
    height: 900px !important;  /* 固定高度，比大多数面板都大 */
    z-index: 1 !important;
    transition: opacity 0.3s ease;
    pointer-events: auto !important; /* 确保可以接收事件 */
  }

  .main-canvas:not(.initialized) {
    opacity: 0.5;
  }

  .main-canvas.error {
    border-color: #dc3545;
    opacity: 0.3;
  }

  .debug-info {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-family: 'Courier New', monospace;
    z-index: 10;
  }

  .debug-item {
    display: flex;
    margin-bottom: 2px;
  }

  .debug-item:last-child {
    margin-bottom: 0;
  }

  .debug-label {
    color: #888;
    margin-right: 8px;
    min-width: 60px;
  }

  .debug-value {
    color: #fff;
    font-weight: 500;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .engine-status,
    .debug-info {
      font-size: 0.75rem;
      padding: 6px 8px;
    }

    .debug-info {
      position: relative;
      margin-top: 10px;
    }
  }
</style>
