/**
 * 项目状态管理 Store
 * 使用 Svelte 官方的 stores 来管理全局项目状态
 */

import { writable, derived } from 'svelte/store';
import type { ScriptInfo } from '../lib/tauriAPI';

/**
 * 资源项接口
 */
export interface ResourceItem {
  id: string;
  name: string;
  path: string;
  type: 'image' | 'folder';
  size?: number;
  lastModified?: Date;
  thumbnail?: string;
}

/**
 * 项目状态接口
 */
export interface ProjectState {
  projectPath: string | null;
  projectName: string | null;
  projectFilePath: string | null;
  scripts: ScriptInfo[];
  resources: ResourceItem[];
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
  // 运行时状态
  runtimeLoaded: boolean;
  runtimeLoading: boolean;
  runtimeError: string | null;
}

/**
 * 初始项目状态
 */
const initialState: ProjectState = {
  projectPath: null,
  projectName: null,
  projectFilePath: null,
  scripts: [],
  resources: [],
  isLoaded: false,
  isLoading: false,
  error: null,
  runtimeLoaded: false,
  runtimeLoading: false,
  runtimeError: null
};

/**
 * 项目状态 store
 */
export const projectStore = writable<ProjectState>(initialState);

/**
 * 派生状态：是否有项目加载
 */
export const hasProject = derived(
  projectStore,
  ($projectStore) => $projectStore.isLoaded && $projectStore.projectPath !== null
);

/**
 * 派生状态：项目基础路径（用于资源加载）
 */
export const projectBasePath = derived(
  projectStore,
  ($projectStore) => $projectStore.projectPath
);

/**
 * 派生状态：JS 文件夹路径
 */
export const jsPath = derived(
  projectStore,
  ($projectStore) => $projectStore.projectPath ? `${$projectStore.projectPath}/js` : null
);

/**
 * 派生状态：运行时是否已加载
 */
export const runtimeReady = derived(
  projectStore,
  ($projectStore) => $projectStore.runtimeLoaded && !$projectStore.runtimeLoading && !$projectStore.runtimeError
);

/**
 * 派生状态：是否可以进行资源操作
 */
export const canManageResources = derived(
  projectStore,
  ($projectStore) => $projectStore.isLoaded && $projectStore.runtimeLoaded && !$projectStore.runtimeLoading
);

/**
 * 项目状态操作函数
 */
export const projectActions = {
  /**
   * 开始加载项目
   */
  startLoading() {
    projectStore.update(state => ({
      ...state,
      isLoading: true,
      error: null
    }));
  },

  /**
   * 设置项目信息
   */
  setProject(projectPath: string, projectFilePath: string, scripts: ScriptInfo[]) {
    const projectName = projectPath.split(/[/\\]/).pop() || 'Unknown Project';

    projectStore.update(state => ({
      ...state,
      projectPath,
      projectName,
      projectFilePath,
      scripts,
      resources: [], // 重置资源列表
      isLoaded: true,
      isLoading: false,
      error: null
    }));

    console.log('项目状态已更新:', {
      projectPath,
      projectName,
      projectFilePath,
      scriptsCount: scripts.length
    });
  },

  /**
   * 设置加载错误
   */
  setError(error: string) {
    projectStore.update(state => ({
      ...state,
      isLoading: false,
      error
    }));
  },

  /**
   * 清除项目
   */
  clearProject() {
    projectStore.set(initialState);
    console.log('项目状态已清除');
  },

  /**
   * 更新脚本列表
   */
  updateScripts(scripts: ScriptInfo[]) {
    projectStore.update(state => ({
      ...state,
      scripts
    }));
  },

  /**
   * 添加资源
   */
  addResource(resource: ResourceItem) {
    projectStore.update(state => ({
      ...state,
      resources: [...state.resources, resource]
    }));
  },

  /**
   * 添加多个资源
   */
  addResources(resources: ResourceItem[]) {
    projectStore.update(state => ({
      ...state,
      resources: [...state.resources, ...resources]
    }));
  },

  /**
   * 删除资源
   */
  removeResource(resourceId: string) {
    projectStore.update(state => ({
      ...state,
      resources: state.resources.filter(r => r.id !== resourceId)
    }));
  },

  /**
   * 更新资源列表
   */
  updateResources(resources: ResourceItem[]) {
    projectStore.update(state => ({
      ...state,
      resources
    }));
  },

  /**
   * 清空资源列表
   */
  clearResources() {
    projectStore.update(state => ({
      ...state,
      resources: []
    }));
  },

  /**
   * 开始加载运行时
   */
  startRuntimeLoading() {
    projectStore.update(state => ({
      ...state,
      runtimeLoading: true,
      runtimeError: null
    }));
  },

  /**
   * 运行时加载完成
   */
  setRuntimeLoaded() {
    projectStore.update(state => ({
      ...state,
      runtimeLoaded: true,
      runtimeLoading: false,
      runtimeError: null
    }));
    console.log('运行时加载完成，资源管理功能已启用');
  },

  /**
   * 运行时加载失败
   */
  setRuntimeError(error: string) {
    projectStore.update(state => ({
      ...state,
      runtimeLoaded: false,
      runtimeLoading: false,
      runtimeError: error
    }));
    console.error('运行时加载失败:', error);
  },

  /**
   * 重置运行时状态
   */
  resetRuntimeState() {
    projectStore.update(state => ({
      ...state,
      runtimeLoaded: false,
      runtimeLoading: false,
      runtimeError: null
    }));
  }
};

/**
 * 便捷的获取当前状态函数
 */
export function getCurrentProjectState(): ProjectState {
  let currentState: ProjectState;
  projectStore.subscribe(state => {
    currentState = state;
  })();
  return currentState!;
}

/**
 * 便捷的订阅项目路径变化
 */
export function subscribeToProjectPath(callback: (path: string | null) => void) {
  return projectStore.subscribe(state => {
    callback(state.projectPath);
  });
}

/**
 * 便捷的订阅项目加载状态
 */
export function subscribeToProjectLoaded(callback: (isLoaded: boolean) => void) {
  return projectStore.subscribe(state => {
    callback(state.isLoaded);
  });
}
