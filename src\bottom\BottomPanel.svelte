<script lang="ts">
  /**
   * 🎯 重新实现的BottomPanel组件
   * 新流程：先处理图片 → 创建ImageResource → 添加到store → 创建ImageItem
   */

  import { onMount } from 'svelte';
  import { resourceStore, resourceActions } from '../stores/resourceStore';
  import { performanceMonitor } from '../utils/performanceMonitor';
  import type { ResourceItem } from '../types/imageType';
  import ImageItem from './ImageItem.svelte';
  import { ResourceLoader, type LoadProgress } from './resourceLoader';

  // 🎯 导入导出相关功能
  import {
    analyzeRootResourcesForExport,
    exportRootResourcesToFolder,
    type RootResourcesExportPreview
  } from '../utils/folderExportUtils';
  import { exportSettingsStore } from '../stores/exportSettingsStore';

  // 导入国际化
  import { _ } from '../lib/i18n';

  // 导入Toast提示
  import { toast } from '../stores/toastStore';

  // 🎯 导入Tauri拖拽API
  import { getCurrentWebview } from '@tauri-apps/api/webview';

  // 🎯 状态变量
  let dragOver = $state(false);
  let searchQuery = $state('');

  // 🎯 导入状态
  let isImporting = $state(false);
  let importProgress = $state<LoadProgress>({ completed: 0, total: 0, stage: '' });

  // 🎯 导出状态
  let showExportPreview = $state(false);
  let exportPreviewData = $state<RootResourcesExportPreview | null>(null);
  let isAnalyzing = $state(false);
  let currentExportSettings = $state<any>(null);

  // 🎯 Tauri拖拽监听器清理函数
  let tauriDragUnlisten: (() => void) | null = null;

  // 🎯 响应式数据 - 使用rootResources和currentFolder
  let storeState = $state($resourceStore);
  const rootResources = $derived(storeState.rootResources);
  const currentFolder = $derived(storeState.currentFolder);

  // 🎯 计算当前显示的资源
  const displayedResources = $derived(() => {
    if (!currentFolder) {
      // 根级别：显示所有根级别资源
      return rootResources;
    } else {
      // 文件夹内：直接返回当前文件夹的children
      return currentFolder.children || [];
    }
  });

  // 🎯 过滤后的资源列表 - 移除调试日志避免重复输出
  const filteredResources = $derived(() => {
    return displayedResources().filter((resource: ResourceItem) =>
      resource.name.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });

  // 🎯 监听store变化 - 优化，避免重复订阅
  $effect(() => {
    storeState = $resourceStore;
  });

  // 🎯 组件挂载时设置Tauri拖拽监听器
  onMount(() => {
    const setupTauriDragListener = async () => {
      try {
        const webview = getCurrentWebview();
        tauriDragUnlisten = await webview.onDragDropEvent((event) => {
          // 🎯 减少日志输出，只在关键事件时输出
          if (event.payload.type === 'drop' || event.payload.type === 'enter') {
            console.log('🎯 BottomPanel: Tauri拖拽事件', event.payload);
          }

          if (event.payload.type === 'over' || event.payload.type === 'enter') {
            // 文件悬停在窗口上或进入窗口
            if (!dragOver) {
              dragOver = true;
            }
          } else if (event.payload.type === 'leave') {
            // 文件离开窗口
            dragOver = false;
          } else if (event.payload.type === 'drop') {
            // 文件被释放
            dragOver = false;
            console.log('🎯 BottomPanel: 开始处理拖拽文件', event.payload.paths);
            handleTauriFileDrop(event.payload.paths);
          }
        });

        console.log('✅ BottomPanel: Tauri拖拽监听器已设置');
      } catch (error) {
        console.error('❌ BottomPanel: 设置Tauri拖拽监听器失败', error);
      }
    };

    setupTauriDragListener();

    // 返回清理函数
    return () => {
      if (tauriDragUnlisten) {
        tauriDragUnlisten();
        tauriDragUnlisten = null;
        console.log('🧹 BottomPanel: Tauri拖拽监听器已清理');
      }
    };
  });

  // 🎯 新的导入文件流程
  async function importFiles() {
    performanceMonitor.startMeasure('files-import');

    try {
      isImporting = true;
      importProgress = { completed: 0, total: 0, stage: '选择文件...' };

      const { open } = await import('@tauri-apps/plugin-dialog');
      const result = await open({
        directory: false,
        multiple: true,
        title: '选择图像文件',
        filters: [
          {
            name: 'Image Files',
            extensions: ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg']
          }
        ]
      });

      if (!result) {
        isImporting = false;
        return;
      }

      const filePaths = Array.isArray(result) ? result : [result];
      console.log('📁 BottomPanel: 选择的文件', filePaths.length);

      // 🎯 使用ResourceLoader处理文件导入，传递当前文件夹路径
      await ResourceLoader.importFiles(filePaths, (progress) => {
        importProgress = progress;
      }, currentFolder?.path || undefined);

      console.log('✅ BottomPanel: 文件导入完成');
      performanceMonitor.endMeasure('files-import');

    } catch (error) {
      console.error('❌ BottomPanel: 导入文件失败', error);
    } finally {
      isImporting = false;
      importProgress = { completed: 0, total: 0, stage: '' };
    }
  }









  // 🎯 导入文件夹 - 使用ResourceLoader
  async function importFolder() {
    performanceMonitor.startMeasure('folder-import');

    try {
      isImporting = true;
      importProgress = { completed: 0, total: 0, stage: '选择文件夹...' };

      const { open } = await import('@tauri-apps/plugin-dialog');
      const result = await open({
        directory: true,
        multiple: false,
        title: '选择图像文件夹'
      });

      if (!result) {
        isImporting = false;
        return;
      }

      const folderPath = Array.isArray(result) ? result[0] : result;
      console.log('📁 BottomPanel: 选择的文件夹', folderPath);

      // 🎯 使用ResourceLoader处理文件夹导入，传递当前文件夹路径
      await ResourceLoader.importFolder(folderPath, (progress) => {
        importProgress = progress;
      }, currentFolder?.path || undefined);

      console.log('✅ BottomPanel: 文件夹导入完成');
      performanceMonitor.endMeasure('folder-import');

    } catch (error) {
      console.error('❌ BottomPanel: 导入文件夹失败', error);
      alert(`导入文件夹失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      isImporting = false;
      importProgress = { completed: 0, total: 0, stage: '' };
    }
  }



  // 🎯 拖拽处理
  function handleDragOver(event: DragEvent) {
    event.preventDefault();
    dragOver = true;
  }

  function handleDragLeave(event: DragEvent) {
    event.preventDefault();
    dragOver = false;
  }

  async function handleDrop(event: DragEvent) {
    event.preventDefault();
    dragOver = false;

    // 🎯 Web API拖拽处理（作为备用方案）
    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      console.log('🎯 BottomPanel: Web API拖拽文件', files.length);

      // 注意：Web API无法直接获取文件完整路径，这里主要用于显示反馈
      toast.info(`检测到 ${files.length} 个文件，请使用Tauri拖拽功能获得更好的体验`, '文件拖拽');
    }
  }

  // 🎯 Tauri文件拖拽处理（主要方案）- 支持文件和文件夹
  async function handleTauriFileDrop(filePaths: string[]) {
    if (!filePaths || filePaths.length === 0) {
      console.warn('⚠️ BottomPanel: 没有接收到文件路径');
      toast.warning('没有接收到文件路径', '文件拖拽');
      return;
    }

    console.log('🎯 BottomPanel: 开始处理Tauri拖拽文件', {
      fileCount: filePaths.length,
      files: filePaths,
      currentFolder: currentFolder?.path || '根目录'
    });

    // 🎯 检查是否正在导入
    if (isImporting) {
      toast.warning('正在导入文件，请等待当前操作完成', '文件拖拽');
      console.warn('⚠️ BottomPanel: 正在导入文件，跳过新的拖拽操作');
      return;
    }

    try {
      // 🎯 分离文件和文件夹
      const supportedExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'];
      const imageFiles: string[] = [];
      const folders: string[] = [];

      for (const path of filePaths) {
        // 检查是否有文件扩展名
        const extension = path.toLowerCase().split('.').pop();
        const hasExtension = extension && extension !== path.toLowerCase();

        if (hasExtension && supportedExtensions.includes(extension)) {
          // 是支持的图片文件
          imageFiles.push(path);
          console.log(`📄 检查文件: ${path}, 扩展名: ${extension}, 类型: 图片文件`);
        } else if (!hasExtension || extension === path.toLowerCase()) {
          // 没有扩展名，可能是文件夹
          folders.push(path);
          console.log(`� 检查路径: ${path}, 类型: 可能是文件夹`);
        } else {
          console.log(`📄 检查文件: ${path}, 扩展名: ${extension}, 类型: 不支持的文件`);
        }
      }

      console.log(`📊 拖拽内容分析: 总数 ${filePaths.length}, 图片文件 ${imageFiles.length}, 文件夹 ${folders.length}`);

      // 🎯 处理策略：优先处理文件夹，然后处理单个文件
      if (folders.length > 0) {
        // 有文件夹，使用文件夹导入逻辑
        if (folders.length > 1) {
          toast.warning('一次只能拖拽一个文件夹，将导入第一个文件夹', '文件拖拽');
        }

        const folderPath = folders[0];
        console.log('🚀 BottomPanel: 开始拖拽文件夹导入', folderPath);

        // 🎯 直接复用现有的文件夹导入逻辑
        performanceMonitor.startMeasure('drag-drop-folder-import');
        isImporting = true;
        importProgress = { completed: 0, total: 0, stage: '扫描文件夹...' };

        await ResourceLoader.importFolder(folderPath, (progress) => {
          console.log('📈 拖拽文件夹导入进度:', progress);
          importProgress = progress;
        }, currentFolder?.path || undefined);

        console.log('✅ BottomPanel: Tauri拖拽文件夹导入完成');
        performanceMonitor.endMeasure('drag-drop-folder-import');

      } else if (imageFiles.length > 0) {
        // 只有图片文件，使用文件导入逻辑
        console.log('🚀 BottomPanel: 开始拖拽文件导入', imageFiles);

        performanceMonitor.startMeasure('drag-drop-files-import');
        isImporting = true;
        importProgress = { completed: 0, total: imageFiles.length, stage: '准备导入...' };

        await ResourceLoader.importFiles(imageFiles, (progress) => {
          console.log('📈 拖拽文件导入进度:', progress);
          importProgress = progress;
        }, currentFolder?.path || undefined);

        console.log('✅ BottomPanel: Tauri拖拽文件导入完成');
        performanceMonitor.endMeasure('drag-drop-files-import');

      } else {
        // 没有找到支持的内容
        toast.warning('没有找到支持的图片文件或文件夹', '文件拖拽');
        console.warn('⚠️ BottomPanel: 没有找到支持的图片文件或文件夹');
        return;
      }

    } catch (error) {
      console.error('❌ BottomPanel: Tauri拖拽导入失败', error);
      toast.error(`拖拽导入失败: ${error instanceof Error ? error.message : '未知错误'}`, '文件拖拽');
    } finally {
      isImporting = false;
      importProgress = { completed: 0, total: 0, stage: '' };
      console.log('🏁 BottomPanel: 拖拽导入操作结束');
    }
  }

  // 🎯 导出按钮点击处理
  async function handleExportClick() {
    try {
      isAnalyzing = true;
      console.log('📤 开始分析 rootResources 导出预览');

      // 获取当前导出设置
      const unsubscribe = exportSettingsStore.subscribe(settings => {
        currentExportSettings = settings;
      });
      unsubscribe();

      // 分析 rootResources
      exportPreviewData = await analyzeRootResourcesForExport(rootResources);
      showExportPreview = true;

      console.log('✅ 导出预览数据生成完成', exportPreviewData);
      console.log('🔧 当前导出设置', currentExportSettings);
    } catch (error) {
      console.error('❌ 分析导出预览失败:', error);
      toast.error(`${error instanceof Error ? error.message : $_('error.unknown')}`, $_('export.failed'));
    } finally {
      isAnalyzing = false;
    }
  }

  // 🎯 确认导出
  async function confirmExport() {
    try {
      // 关闭预览弹窗
      showExportPreview = false;

      // 开始导出（文件夹选择在导出函数内部处理）
      const exportResult = await exportRootResourcesToFolder(rootResources);

      // 显示导出结果
      if (exportResult.success) {
        toast.success(
          `${$_('export.exportedCount')}: ${exportResult.exportedCount} 个文件<br>${$_('export.skippedCount')}: ${exportResult.skippedCount} 个文件`,
          $_('export.success')
        );
      } else {
        toast.error(
          exportResult.errors.join('<br>'),
          $_('export.failed')
        );
      }

    } catch (error) {
      console.error('❌ 导出失败:', error);
      toast.error(`${error instanceof Error ? error.message : $_('error.unknown')}`, $_('export.failed'));
    }
  }

  // 🎯 取消导出
  function cancelExport() {
    showExportPreview = false;
    exportPreviewData = null;
  }
</script>

<div class="bottom-panel">
  <!-- 工具栏 -->
  <div class="toolbar">
    <div class="toolbar-left">


      <button
        class="import-btn primary"
        onclick={importFolder}
        disabled={isImporting}
      >
        📁 {$_('actions.importFolder')}
      </button>

      <button
        class="import-btn outline"
        onclick={importFiles}
        disabled={isImporting}
      >
        📄 {$_('actions.importFiles')}
      </button>
      <!-- 🎯 导航区域 -->
      {#if currentFolder}
        <button class="nav-btn" onclick={() => resourceActions.exitFolder()}>
          ← {$_('ui.back')}
        </button>
        <div class="breadcrumb">
          <span class="breadcrumb-item">📁 {currentFolder.name}</span>
        </div>
      {/if}
    </div>

    <!-- 🎯 中间区域标题 -->
    <div class="toolbar-center">
      <div class="area-title">{$_('resource.title')}</div>
    </div>

    <div class="toolbar-right">
      <!-- 🎯 导入状态指示器 - 横向布局 -->
      {#if isImporting}
        <div class="operation-status simple">
          <span class="loading-spinner">⏳</span>
        </div>
      {/if}

      <div class="search-box">
        <span class="search-icon">🔍</span>
        <input
          type="text"
          placeholder={$_('resource.search')}
          bind:value={searchQuery}
          class="search-input"
        />
      </div>

      <div class="resource-stats">
        <span class="stats-text">{$_('resource.resourceCount', { values: { count: filteredResources().length } })}</span>
      </div>

      <!-- 🎯 导出按钮 -->
      <button
        class="export-btn"
        onclick={handleExportClick}
        title={$_('export.all')}
        disabled={isAnalyzing}
      >
        {#if isAnalyzing}
          ⏳ {$_('status.processing')}
        {:else}
          📤 {$_('actions.export')}
        {/if}
      </button>
    </div>
  </div>

  <!-- 资源区域 -->
  <div
    class="resource-area"
    class:drag-over={dragOver}
    role="region"
    aria-label={$_('resource.title')}
    ondragover={handleDragOver}
    ondragleave={handleDragLeave}
    ondrop={handleDrop}
  >
    {#if filteredResources().length === 0}
      <div class="empty-state">
        <div class="empty-icon">📁</div>
        <h3>{$_('resource.noResources')}</h3>
        <p>{$_('ui.importHint')}</p>
        <p class="hint">{$_('ui.dragHint')}</p>
      </div>
    {:else}
      <div class="resource-list">
        {#each filteredResources() as resource (resource.id)}
          <!-- 🎯 所有资源都通过ImageItem组件处理，组件内部判断类型 -->
          <ImageItem
            imageResource={resource}
          />
        {/each}
      </div>
    {/if}
  </div>
</div>

<!-- 🎯 导出预览弹窗 - 参考ExportPanel设计 -->
{#if showExportPreview && exportPreviewData}
  <div class="export-preview-overlay">
    <div class="export-preview-modal">
      <!-- 头部 -->
      <div class="modal-header">
        <h3>📤 {$_('export.preview')}</h3>
        <button class="close-btn" onclick={cancelExport}>✕</button>
      </div>

      <div class="modal-content">
        <!-- 统计信息 -->
        <div class="stats-section">
          <h4>📊 {$_('export.statistics')}</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">{$_('export.folders')}:</span>
              <span class="stat-value">{exportPreviewData.totalFolders}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">{$_('export.validAtlases')}:</span>
              <span class="stat-value success">{exportPreviewData.imagesWithCropData}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">{$_('export.skippedCount')}:</span>
              <span class="stat-value warning">{exportPreviewData.imagesWithoutCropData}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">{$_('export.totalSize')}:</span>
              <span class="stat-value">{(exportPreviewData.totalSize / 1024 / 1024).toFixed(1)} MB</span>
            </div>
          </div>
        </div>

        <!-- 导出设置 -->
        {#if currentExportSettings}
          <div class="settings-section">
            <h4>⚙️ {$_('export.settings')}</h4>
            <div class="settings-grid">
              <div class="setting-row">
                <div class="setting-group">
                  <span class="setting-label">{$_('export.type')}:</span>
                  <span class="setting-value">{currentExportSettings.selectedType || 'cropped-images'}</span>
                </div>
                <div class="setting-group">
                  <span class="setting-label">{$_('export.format')}:</span>
                  <span class="setting-value">{(currentExportSettings.format || 'png').toUpperCase()}</span>
                </div>
              </div>
              <div class="setting-row">
                <div class="setting-group">
                  <span class="setting-label">{$_('export.quality')}:</span>
                  <span class="setting-value">{Math.round((currentExportSettings.quality || 0.9) * 100)}%</span>
                </div>
                <div class="setting-group">
                  <span class="setting-label">{$_('export.includeOriginal')}:</span>
                  <span class="setting-value">{currentExportSettings.includeOriginal ? $_('ui.yes') : $_('ui.no')}</span>
                </div>
              </div>
              {#if currentExportSettings.namePrefix || currentExportSettings.nameSuffix}
                <div class="setting-row">
                  {#if currentExportSettings.namePrefix}
                    <div class="setting-group">
                      <span class="setting-label">{$_('export.prefix')}:</span>
                      <span class="setting-value">"{currentExportSettings.namePrefix}"</span>
                    </div>
                  {/if}
                  {#if currentExportSettings.nameSuffix}
                    <div class="setting-group">
                      <span class="setting-label">{$_('export.suffix')}:</span>
                      <span class="setting-value">"{currentExportSettings.nameSuffix}"</span>
                    </div>
                  {/if}
                </div>
              {/if}
            </div>
          </div>
        {/if}

        <!-- 文件夹结构 -->
        {#if exportPreviewData.folderStructure.length > 0}
          <div class="structure-section">
            <h4>📂 {$_('export.folderStructure')}</h4>
            <div class="folder-container">
              <div class="folder-list">
                {#each exportPreviewData.folderStructure.slice(0, 10) as folder}
                  <div class="folder-item">
                    <span class="folder-icon">📁</span>
                    <span class="folder-name">{folder}</span>
                  </div>
                {/each}
                {#if exportPreviewData.folderStructure.length > 10}
                  <div class="folder-item more">
                    <span class="folder-icon">⋯</span>
                    <span class="folder-name">还有 {exportPreviewData.folderStructure.length - 10} 个文件夹</span>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        {/if}

        <!-- 警告信息 -->
        {#if exportPreviewData.imagesWithoutCropData > 0}
          <div class="warning-section">
            <div class="warning-content">
              <span class="warning-icon">⚠️</span>
              <span class="warning-text">
                {$_('ui.warning')}: {exportPreviewData.imagesWithoutCropData} {$_('export.noCropDataWarning')}
              </span>
            </div>
          </div>
        {/if}
      </div>

      <!-- 底部操作 -->
      <div class="modal-footer">
        <div class="footer-info">
          <span class="time-estimate">⏱️ {$_('export.estimatedTime')}: {exportPreviewData.estimatedExportTime} {$_('ui.seconds')}</span>
        </div>
        <div class="footer-actions">
          <button class="cancel-btn" onclick={cancelExport}>{$_('actions.cancel')}</button>
          <button class="confirm-btn" onclick={confirmExport}>{$_('export.startExport')}</button>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .bottom-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--color-surface);
    border-top: 1px solid var(--color-border);
  }

  .toolbar {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    background: var(--color-surface-secondary);
    border-bottom: 1px solid var(--color-border);
    gap: var(--spacing-4);
  }

  .toolbar-left {
    display: flex;
    justify-self: start;
    align-items: center;
  }

  /* 🎯 使用传统的margin方法确保间距 */
  .toolbar-left > *:not(:last-child) {
    margin-right: 8px !important;
  }

  .toolbar-left > * {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .toolbar-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .toolbar-right {
    display: flex;
    align-items: center;
    justify-self: end;
  }

  /* 🎯 使用更高特异性确保间距生效 */
  .bottom-panel .toolbar .toolbar-right > * {
    margin: 0 !important;
  }

  .bottom-panel .toolbar .toolbar-right > *:not(:last-child) {
    margin-right: 12px !important; /* 🎯 使用12px，比8px更明显 */
  }

  /* 🎯 确保最后一个元素没有右边距 */
  .bottom-panel .toolbar .toolbar-right > *:last-child {
    margin-right: 0 !important;
  }

  /* 🎯 给元素添加微妙的视觉分隔，替代边框 */
  .bottom-panel .toolbar .toolbar-right .search-box,
  .bottom-panel .toolbar .toolbar-right .operation-status {
    box-shadow: 0 0 0 1px var(--color-border);
  }

  .bottom-panel .toolbar .toolbar-right .resource-stats {
    padding: 4px 8px;
    background: var(--color-surface-secondary);
    border-radius: 4px;
  }

  .bottom-panel .toolbar .toolbar-right .export-btn {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .area-title {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
    opacity: 0.8;
  }

  .import-btn {
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-base);
    border: 1px solid transparent;
  }

  .import-btn.primary {
    background: var(--color-primary);
    color: var(--color-text-on-primary);
  }

  .import-btn.primary:hover {
    background: var(--color-primary-hover);
  }

  .import-btn.outline {
    background: transparent;
    color: var(--color-text);
    border-color: var(--color-border);
  }

  .import-btn.outline:hover {
    background: var(--color-surface-hover);
  }

  .import-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* 🎯 导航样式 - 移除margin，使用flexbox gap */
  .nav-btn {
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-base);
    border: 1px solid var(--color-border);
    background: var(--color-surface);
    color: var(--color-text);
  }

  .nav-btn:hover {
    background: var(--color-surface-hover);
    border-color: var(--color-primary);
  }

  .breadcrumb {
    display: flex;
    align-items: center;
  }

  .breadcrumb-item {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    font-weight: 500;
  }



  .operation-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--color-surface-secondary);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    border: 1px solid var(--color-border);
  }

  /* 🎯 简化版样式 - 只显示加载图标 */
  .operation-status.simple {
    padding: var(--spacing-1) var(--spacing-2);
    min-width: auto;
    width: auto;
    justify-content: center;
  }

  .status-text {
    font-weight: 500;
    color: var(--color-text);
    white-space: nowrap;
    min-width: 120px;
  }

  .progress-bar {
    width: 100px;
    height: 6px;
    background: var(--color-border);
    border-radius: 3px;
    overflow: hidden;
    flex-shrink: 0;
  }

  .progress-fill {
    height: 100%;
    background: var(--color-primary);
    transition: width 0.3s ease;
    border-radius: 3px;
  }

  /* 🎯 加载动画样式 */
  .loading-spinner {
    animation: spin 1s linear infinite;
    font-size: 1.1em;
    flex-shrink: 0;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .search-box {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--color-surface);
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
  }

  .search-input {
    border: none;
    background: transparent;
    color: var(--color-text);
    font-size: var(--font-size-sm);
    outline: none;
    width: 150px;
  }

  .resource-stats {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    font-weight: 500;
  }

  .resource-area {
    flex: 1;
    padding: var(--spacing-4);
    overflow: auto;
    position: relative;
    transition: var(--transition-base);
  }

  .resource-area.drag-over {
    background: var(--color-primary-light);
    border: 2px dashed var(--color-primary);
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;
    color: var(--color-text-secondary);
    gap: var(--spacing-4);
  }

  .empty-icon {
    font-size: 4rem;
    opacity: 0.5;
  }

  .empty-state h3 {
    margin: 0;
    font-size: var(--font-size-xl);
    color: var(--color-text);
  }

  .empty-state p {
    margin: 0;
    font-size: var(--font-size-sm);
  }

  .empty-state .hint {
    font-style: italic;
    opacity: 0.7;
  }

  .resource-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--spacing-4);
  }

  /* 🎯 导出按钮样式 */
  .export-btn {
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-base);
    border: 1px solid var(--color-primary);
    background: var(--color-primary);
    color: var(--color-text-on-primary);
  }

  .export-btn:hover:not(:disabled) {
    background: var(--color-primary-hover);
  }

  .export-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  /* 🎯 导出预览弹窗样式 - 参考ExportPanel */
  .export-preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
  }

  .export-preview-modal {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 8px;
    padding: 0;
    width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease-out;
  }

  /* 头部样式 */
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--theme-surface);
    border-bottom: 1px solid var(--theme-border);
  }

  .modal-header h3 {
    margin: 0;
    color: var(--theme-text);
    font-size: 16px;
    font-weight: 600;
  }



  .close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--theme-text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn:hover {
    background: var(--theme-surface-hover, var(--theme-surface));
    color: var(--theme-text);
  }

  .modal-content {
    padding: 16px 20px;
    max-height: 60vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  /* 🎯 参考ExportPanel的简洁样式 */

  /* 统计信息样式 */
  .stats-section {
    margin-bottom: 12px;
  }

  .stats-section h4 {
    margin: 0 0 8px 0;
    color: var(--theme-text);
    font-size: 14px;
    font-weight: 600;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background: var(--theme-background);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
  }

  .stat-label {
    font-size: 12px;
    color: var(--theme-text-secondary);
    font-weight: 500;
  }

  .stat-value {
    font-size: 12px;
    color: var(--theme-text);
    font-weight: 600;
    font-family: monospace;
  }

  .stat-value.success {
    color: #10b981;
  }

  .stat-value.warning {
    color: #f59e0b;
  }

  /* 设置样式 */
  .settings-section {
    margin-bottom: 12px;
  }

  .settings-section h4 {
    margin: 0 0 8px 0;
    color: var(--theme-text);
    font-size: 14px;
    font-weight: 600;
  }

  .settings-grid {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .setting-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .setting-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background: var(--theme-background);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
  }

  .setting-label {
    font-size: 12px;
    color: var(--theme-text-secondary);
    font-weight: 500;
    margin: 0;
  }

  .setting-value {
    font-size: 12px;
    color: var(--theme-text);
    font-weight: 600;
    font-family: monospace;
  }

  /* 文件夹结构样式 */
  .structure-section {
    margin-bottom: 12px;
  }

  .structure-section h4 {
    margin: 0 0 8px 0;
    color: var(--theme-text);
    font-size: 14px;
    font-weight: 600;
  }

  .folder-container {
    height: 150px;
    overflow-y: auto;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    background: var(--theme-background);
  }

  .folder-list {
    padding: 6px;
  }

  .folder-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 6px;
    font-size: 12px;
    color: var(--theme-text);
    border-bottom: 1px solid var(--theme-border);
  }

  .folder-item:last-child {
    border-bottom: none;
  }

  .folder-item.more {
    font-style: italic;
    color: var(--theme-text-secondary);
  }

  .folder-icon {
    font-size: 12px;
    opacity: 0.8;
  }

  .folder-name {
    font-family: monospace;
    font-size: 11px;
  }

  /* 警告样式 */
  .warning-section {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 12px;
  }

  .warning-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .warning-icon {
    font-size: 14px;
    flex-shrink: 0;
  }

  .warning-text {
    font-size: 12px;
    color: var(--theme-text);
    line-height: 1.4;
  }

  /* 底部样式 */
  .modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-top: 1px solid var(--theme-border);
    background: var(--theme-surface);
  }

  .footer-info {
    font-size: 12px;
    color: var(--theme-text-secondary);
  }

  .time-estimate {
    font-size: 12px;
    color: var(--theme-text-secondary);
  }

  .footer-actions {
    display: flex;
    gap: 8px;
  }

  .cancel-btn, .confirm-btn {
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
  }

  .cancel-btn {
    background: var(--theme-background);
    color: var(--theme-text);
    border-color: var(--theme-border);
  }

  .cancel-btn:hover {
    background: var(--theme-surface-hover, var(--theme-surface));
  }

  .confirm-btn {
    background: var(--theme-primary);
    color: white;
  }

  .confirm-btn:hover {
    opacity: 0.9;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

</style>
