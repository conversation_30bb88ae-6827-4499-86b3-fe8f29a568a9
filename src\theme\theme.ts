/**
 * 全局主题配置文件
 * 统一管理应用程序的主题色彩和样式
 */

/**
 * 主题色彩配置接口
 */
export interface ThemeColors {
  // 主色调
  primary: string;
  primaryDark: string;
  primaryLight: string;

  // 次要色调
  secondary: string;
  secondaryDark: string;
  secondaryLight: string;

  // 背景色
  background: string;
  backgroundDark: string;
  backgroundLight: string;

  // 表面色
  surface: string;
  surfaceDark: string;
  surfaceLight: string;

  // 文本色
  text: string;
  textSecondary: string;
  textInverse: string;

  // 边框色
  border: string;
  borderLight: string;
  borderDark: string;

  // 状态色
  success: string;
  warning: string;
  error: string;
  info: string;

  // 阴影
  shadow: string;
  shadowLight: string;
  shadowDark: string;
}

/**
 * 默认主题色彩配置（基于图片中的深色主题）
 */
export const defaultTheme: ThemeColors = {
  // 主色调 - 蓝色强调色
  primary: '#007acc',        // 蓝色主色
  primaryDark: '#005a9e',    // 深蓝色
  primaryLight: '#3399dd',   // 浅蓝色

  // 次要色调
  secondary: '#007acc',      // 蓝色
  secondaryDark: '#005a9e',
  secondaryLight: '#3399dd',

  // 背景色 - 更深的黑色系
  background: '#1e1e1e',     // 主背景（深黑色）
  backgroundDark: '#181818', // 更深背景
  backgroundLight: '#252526', // 稍浅背景

  // 表面色 - 深灰色面板
  surface: '#2d2d30',        // 面板表面色
  surfaceDark: '#252526',    // 深色面板
  surfaceLight: '#3c3c3c',   // 浅色面板

  // 文本色
  text: '#cccccc',           // 主文本色（浅灰色）
  textSecondary: '#969696',  // 次要文本色
  textInverse: '#1e1e1e',    // 反色文本

  // 边框色 - 更暗的边框
  border: '#3c3c3c',         // 边框色
  borderLight: '#2d2d30',    // 浅边框
  borderDark: '#464647',     // 深边框

  // 状态色
  success: '#4ec9b0',        // 成功色（青绿色）
  warning: '#ffcc02',        // 警告色（黄色）
  error: '#f44747',          // 错误色（红色）
  info: '#007acc',           // 信息色（蓝色）

  // 阴影
  shadow: 'rgba(0, 0, 0, 0.6)',       // 阴影色
  shadowLight: 'rgba(0, 0, 0, 0.3)',  // 浅阴影
  shadowDark: 'rgba(0, 0, 0, 0.8)',   // 深阴影
};

/**
 * 浅色主题配置
 */
export const lightTheme: ThemeColors = {
  // 主色调
  primary: '#4a5568',
  primaryDark: '#2d3748',
  primaryLight: '#718096',

  // 次要色调
  secondary: '#667eea',
  secondaryDark: '#5a67d8',
  secondaryLight: '#7c3aed',

  // 背景色
  background: '#ffffff',
  backgroundDark: '#f7fafc',
  backgroundLight: '#edf2f7',

  // 表面色
  surface: '#ffffff',
  surfaceDark: '#f7fafc',
  surfaceLight: '#edf2f7',

  // 文本色
  text: '#1a202c',
  textSecondary: 'rgba(26, 32, 44, 0.8)',
  textInverse: '#ffffff',

  // 边框色
  border: 'rgba(26, 32, 44, 0.2)',
  borderLight: 'rgba(26, 32, 44, 0.1)',
  borderDark: 'rgba(26, 32, 44, 0.3)',

  // 状态色
  success: '#22c55e',
  warning: '#fbbf24',
  error: '#e53e3e',
  info: '#3b82f6',

  // 阴影
  shadow: 'rgba(0, 0, 0, 0.1)',
  shadowLight: 'rgba(0, 0, 0, 0.05)',
  shadowDark: 'rgba(0, 0, 0, 0.2)',
};

/**
 * 主题类型枚举
 */
export enum ThemeType {
  DARK = 'dark',
  LIGHT = 'light',
  AUTO = 'auto'
}

/**
 * 主题管理类
 */
export class ThemeManager {
  private static instance: ThemeManager;
  private currentTheme: ThemeType = ThemeType.DARK;
  private currentColors: ThemeColors = defaultTheme;

  private constructor() {
    this.loadThemeFromStorage();
    this.applySystemThemeListener();
  }

  /**
   * 获取主题管理器实例（单例模式）
   */
  static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }

  /**
   * 设置主题
   */
  setTheme(theme: ThemeType): void {
    this.currentTheme = theme;
    this.updateColors();
    this.saveThemeToStorage();
    this.applyCSSVariables();
  }

  /**
   * 获取当前主题
   */
  getCurrentTheme(): ThemeType {
    return this.currentTheme;
  }

  /**
   * 获取当前主题色彩
   */
  getCurrentColors(): ThemeColors {
    return this.currentColors;
  }

  /**
   * 更新颜色配置
   */
  private updateColors(): void {
    if (this.currentTheme === ThemeType.AUTO) {
      // 自动模式：根据系统偏好设置
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      this.currentColors = prefersDark ? defaultTheme : lightTheme;
    } else if (this.currentTheme === ThemeType.LIGHT) {
      this.currentColors = lightTheme;
    } else {
      this.currentColors = defaultTheme;
    }
  }

  /**
   * 应用CSS变量
   */
  private applyCSSVariables(): void {
    const root = document.documentElement;
    const colors = this.currentColors;

    // 🎯 设置新的全局颜色变量系统
    // 主要颜色
    root.style.setProperty('--color-primary', colors.primary);
    root.style.setProperty('--color-primary-hover', colors.primaryDark);
    root.style.setProperty('--color-primary-light', colors.primaryLight);

    // 次要颜色
    root.style.setProperty('--color-secondary', colors.secondary);
    root.style.setProperty('--color-secondary-hover', colors.secondaryDark);
    root.style.setProperty('--color-secondary-light', colors.secondaryLight);

    // 背景和表面颜色
    root.style.setProperty('--color-background', colors.background);
    root.style.setProperty('--color-surface', colors.surface);
    root.style.setProperty('--color-surface-secondary', colors.surfaceDark);
    root.style.setProperty('--color-surface-hover', colors.surfaceLight);

    // 文字颜色
    root.style.setProperty('--color-text', colors.text);
    root.style.setProperty('--color-text-secondary', colors.textSecondary);
    root.style.setProperty('--color-text-on-primary', colors.textInverse);

    // 边框颜色
    root.style.setProperty('--color-border', colors.border);
    root.style.setProperty('--color-border-light', colors.borderLight);
    root.style.setProperty('--color-border-dark', colors.borderDark);

    // 状态颜色
    root.style.setProperty('--color-success', colors.success);
    root.style.setProperty('--color-warning', colors.warning);
    root.style.setProperty('--color-error', colors.error);
    root.style.setProperty('--color-info', colors.info);

    // 阴影颜色
    root.style.setProperty('--color-shadow', colors.shadow);
    root.style.setProperty('--color-shadow-light', colors.shadowLight);
    root.style.setProperty('--color-shadow-dark', colors.shadowDark);

    // 🎯 保持旧的变量以兼容现有组件
    root.style.setProperty('--theme-primary', colors.primary);
    root.style.setProperty('--theme-primary-dark', colors.primaryDark);
    root.style.setProperty('--theme-primary-light', colors.primaryLight);
    root.style.setProperty('--theme-background', colors.background);
    root.style.setProperty('--theme-surface', colors.surface);
    root.style.setProperty('--theme-text', colors.text);
    root.style.setProperty('--theme-text-secondary', colors.textSecondary);
    root.style.setProperty('--theme-border', colors.border);

    // 更新body类名以支持Skeleton UI
    document.body.className = this.currentTheme === ThemeType.LIGHT ? 'light' : 'dark';

    console.log('🎨 主题颜色变量已应用', {
      theme: this.currentTheme,
      surface: colors.surface,
      background: colors.background
    });
  }

  /**
   * 从本地存储加载主题
   */
  private loadThemeFromStorage(): void {
    const savedTheme = localStorage.getItem('app-theme') as ThemeType;
    if (savedTheme && Object.values(ThemeType).includes(savedTheme)) {
      this.currentTheme = savedTheme;
    }
    this.updateColors();
    this.applyCSSVariables();
  }

  /**
   * 保存主题到本地存储
   */
  private saveThemeToStorage(): void {
    localStorage.setItem('app-theme', this.currentTheme);
  }

  /**
   * 监听系统主题变化
   */
  private applySystemThemeListener(): void {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', () => {
      if (this.currentTheme === ThemeType.AUTO) {
        this.updateColors();
        this.applyCSSVariables();
      }
    });
  }
}

// 导出主题管理器实例
export const themeManager = ThemeManager.getInstance();

// 默认导出
export default themeManager;
