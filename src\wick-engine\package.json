{"name": "wick-engine", "version": "1.0.0", "description": "This is the official implementation of the Wick Editor Core Engine. <what does it do>. It is built to be easily plugged into used by a GUI for creating and editing Wick projects (e.g. the Wick Editor) or a player for embedding and running Wick projects (e.g. the Wick Player).", "main": "index.js", "directories": {"lib": "lib", "test": "tests"}, "scripts": {"generate-docs": "jsdoc -c jsdocs.json -r src/ -d docs/; cp -r docs/* ../../wick-editor-docs/; cd ../../wick-editor-docs; git add .; git commit -m 'update docs'; git push --force;", "dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "repository": {"type": "git", "url": "git+https://github.com/Wicklets/wick-engine.git"}, "author": "<PERSON><PERSON><PERSON>", "license": "TODO ADD LICENSE", "bugs": {"url": "https://github.com/Wicklets/wick-engine/issues"}, "homepage": "https://github.com/Wicklets/wick-engine#readme", "dependencies": {"pixi.js": "^7.4.3", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.1.2", "@types/node": "^20.9.0", "gulp": "^4.0.0", "gulp-babel": "^8.0.0-beta.2", "gulp-concat": "^2.6.1", "gulp-header": "^2.0.5", "gulp-rename": "^1.4.0", "gulp-uglify": "^3.0.1", "jsdoc": "^3.6.4", "merge-stream": "^2.0.0", "typescript": "^5.2.2", "vite": "^4.5.0"}}