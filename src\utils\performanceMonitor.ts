/**
 * 性能监控工具
 * 用于监控MergePanel组件的性能指标
 */

interface PerformanceMetric {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

interface MemorySnapshot {
  timestamp: number;
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
}

export class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private memorySnapshots: MemorySnapshot[] = [];
  private readonly MAX_SNAPSHOTS = 100;

  /**
   * 开始性能测量
   */
  startMeasure(name: string, metadata?: Record<string, any>): void {
    this.metrics.set(name, {
      name,
      startTime: performance.now(),
      metadata
    });
  }

  /**
   * 结束性能测量
   */
  endMeasure(name: string): number | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`⚠️ PerformanceMonitor: 未找到测量项 "${name}"`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    metric.endTime = endTime;
    metric.duration = duration;

    console.log(`⏱️ PerformanceMonitor: ${name}`, {
      duration: `${duration.toFixed(2)}ms`,
      metadata: metric.metadata
    });

    return duration;
  }

  /**
   * 测量函数执行时间
   */
  async measureAsync<T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T> {
    this.startMeasure(name, metadata);
    try {
      const result = await fn();
      this.endMeasure(name);
      return result;
    } catch (error) {
      this.endMeasure(name);
      throw error;
    }
  }

  /**
   * 测量同步函数执行时间
   */
  measure<T>(name: string, fn: () => T, metadata?: Record<string, any>): T {
    this.startMeasure(name, metadata);
    try {
      const result = fn();
      this.endMeasure(name);
      return result;
    } catch (error) {
      this.endMeasure(name);
      throw error;
    }
  }

  /**
   * 记录内存快照
   */
  takeMemorySnapshot(): MemorySnapshot | null {
    if (!('memory' in performance)) {
      return null;
    }

    const memory = (performance as any).memory;
    const snapshot: MemorySnapshot = {
      timestamp: Date.now(),
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit
    };

    this.memorySnapshots.push(snapshot);

    // 保持快照数量在限制内
    if (this.memorySnapshots.length > this.MAX_SNAPSHOTS) {
      this.memorySnapshots.shift();
    }

    return snapshot;
  }

  /**
   * 获取内存使用趋势
   */
  getMemoryTrend(): {
    current: MemorySnapshot | null;
    peak: MemorySnapshot | null;
    average: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  } {
    if (this.memorySnapshots.length === 0) {
      return {
        current: null,
        peak: null,
        average: 0,
        trend: 'stable'
      };
    }

    const current = this.memorySnapshots[this.memorySnapshots.length - 1];
    const peak = this.memorySnapshots.reduce((max, snapshot) => 
      snapshot.usedJSHeapSize > max.usedJSHeapSize ? snapshot : max
    );

    const average = this.memorySnapshots.reduce((sum, snapshot) => 
      sum + snapshot.usedJSHeapSize, 0
    ) / this.memorySnapshots.length;

    // 计算趋势（基于最近10个快照）
    const recentSnapshots = this.memorySnapshots.slice(-10);
    let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
    
    if (recentSnapshots.length >= 2) {
      const first = recentSnapshots[0].usedJSHeapSize;
      const last = recentSnapshots[recentSnapshots.length - 1].usedJSHeapSize;
      const change = (last - first) / first;
      
      if (change > 0.1) trend = 'increasing';
      else if (change < -0.1) trend = 'decreasing';
    }

    return { current, peak, average, trend };
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    metrics: PerformanceMetric[];
    memoryTrend: ReturnType<PerformanceMonitor['getMemoryTrend']>;
    summary: {
      totalMeasurements: number;
      averageDuration: number;
      slowestOperation: PerformanceMetric | null;
    };
  } {
    const completedMetrics = Array.from(this.metrics.values()).filter(m => m.duration !== undefined);
    
    const averageDuration = completedMetrics.length > 0 
      ? completedMetrics.reduce((sum, m) => sum + (m.duration || 0), 0) / completedMetrics.length
      : 0;

    const slowestOperation = completedMetrics.reduce((slowest, current) => 
      (current.duration || 0) > (slowest?.duration || 0) ? current : slowest
    , null as PerformanceMetric | null);

    return {
      metrics: completedMetrics,
      memoryTrend: this.getMemoryTrend(),
      summary: {
        totalMeasurements: completedMetrics.length,
        averageDuration,
        slowestOperation
      }
    };
  }

  /**
   * 清空所有数据
   */
  clear(): void {
    this.metrics.clear();
    this.memorySnapshots = [];
  }

  /**
   * 格式化内存大小
   */
  static formatMemorySize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 打印性能报告到控制台
   */
  printReport(): void {
    const report = this.getPerformanceReport();
    
    console.group('📊 MergePanel 性能报告');
    
    console.log('📈 性能指标:', {
      总测量次数: report.summary.totalMeasurements,
      平均耗时: `${report.summary.averageDuration.toFixed(2)}ms`,
      最慢操作: report.summary.slowestOperation ? 
        `${report.summary.slowestOperation.name} (${report.summary.slowestOperation.duration?.toFixed(2)}ms)` : 
        '无'
    });

    if (report.memoryTrend.current) {
      console.log('💾 内存使用:', {
        当前使用: PerformanceMonitor.formatMemorySize(report.memoryTrend.current.usedJSHeapSize),
        峰值使用: PerformanceMonitor.formatMemorySize(report.memoryTrend.peak?.usedJSHeapSize || 0),
        平均使用: PerformanceMonitor.formatMemorySize(report.memoryTrend.average),
        趋势: report.memoryTrend.trend
      });
    }

    console.groupEnd();
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();
