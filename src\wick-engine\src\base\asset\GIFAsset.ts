/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { FileAsset } from "./FileAsset";
import { ImageAsset } from "./ImageAsset";

interface GIFFrameData {
  imageAsset: ImageAsset;
  delay: number;
}

export class GIFAsset extends FileAsset {
  protected _frames: GIFFrameData[];
  protected _loaded: boolean;

  /**
   * Returns valid MIME types for a GIF Asset.
   * @returns {string[]} Array of strings representing MIME types in the form image/gif.
   */
  static getValidMIMETypes(): string[] {
    return ["image/gif"];
  }

  /**
   * Returns valid extensions for a GIF asset.
   * @returns {string[]} Array of strings representing valid extensions.
   */
  static getValidExtensions(): string[] {
    return [".gif"];
  }

  /**
   * Creates a new GIFAsset.
   * @param {FileAssetArgs} args - Asset constructor args. see constructor for Wick.Asset
   */
  constructor(args: FileAssetArgs = {}) {
    super(args);
    this._frames = [];
    this._loaded = false;
  }

  protected _serialize(args?: SerializeArgs): Record<string, any> {
    const data = super._serialize(args);
    data.frames = this._frames.map((frame) => ({
      imageAssetUUID: frame.imageAsset.uuid,
      delay: frame.delay,
    }));
    return data;
  }

  protected _deserialize(data: Record<string, any>): void {
    super._deserialize(data);
    this._frames = data.frames.map((frameData: any) => ({
      imageAsset: this.project.getAssetByUUID(frameData.imageAssetUUID),
      delay: frameData.delay,
    }));
  }

  public get classname(): string {
    return "GIFAsset";
  }

  /**
   * A list of frames that use this GIF.
   * @returns {Wick.Frame[]}
   */
  public getInstances(): Wick.Frame[] {
    const frames: Wick.Frame[] = [];
    this.project.getAllFrames().forEach((frame) => {
      frame.paths.forEach((path) => {
        const images = path.getLinkedAssets();
        if (images.length > 0 && images[0].gifAssetUUID === this.uuid) {
          frames.push(frame);
        }
      });
    });
    return frames;
  }

  /**
   * Check if there are any objects in the project that use this asset.
   * @returns {boolean}
   */
  public hasInstances(): boolean {
    return this.getInstances().length > 0;
  }

  /**
   * Remove all instances of this GIF from the project.
   */
  public removeAllInstances(): void {
    this.getInstances().forEach((frame) => {
      frame.remove();
    });
  }

  /**
   * Load data in the asset
   * @param {function} callback - function to call when the data is done being loaded.
   */
  public load(callback: () => void): void {
    if (this._loaded) {
      callback();
      return;
    }

    const gifWorker = new GIF();
    gifWorker.onload = (reader: FileReader) => {
      const gif = reader.result;
      const frames = gif.decompressFrames(true);

      frames.forEach((frame: any) => {
        const canvas = document.createElement("canvas");
        canvas.width = frame.dims.width;
        canvas.height = frame.dims.height;
        const ctx = canvas.getContext("2d");

        if (ctx) {
          const imageData = ctx.createImageData(
            frame.dims.width,
            frame.dims.height
          );
          imageData.data.set(frame.patch);
          ctx.putImageData(imageData, frame.dims.left, frame.dims.top);

          const imageAsset = new ImageAsset({
            filename: this.filename + "_frame_" + this._frames.length + ".png",
            src: canvas.toDataURL(),
          });

          imageAsset.gifAssetUUID = this.uuid;
          this.project.addAsset(imageAsset);

          this._frames.push({
            imageAsset: imageAsset,
            delay: frame.delay,
          });
        }
      });

      this._loaded = true;
      callback();
    };

    gifWorker.load(this.src || "");
  }

  /**
   * The frames of the GIF.
   * @type {GIFFrameData[]}
   */
  public get frames(): GIFFrameData[] {
    return this._frames;
  }

  /**
   * Creates a new Wick Path that uses this asset's image data as it's image source.
   * @param {function} callback - called when the path is done loading.
   */
  public createInstance(callback: (path: Wick.Path) => void): void {
    if (this._frames.length === 0) {
      console.error("GIFAsset: No frames to create instance from");
      return;
    }

    Wick.Path.createImagePath(this._frames[0].imageAsset, (path) => {
      callback(path);
    });
  }
}
