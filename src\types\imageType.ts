/**
 * 图片资源相关的类型定义
 */

// 基础资源接口
export interface BaseResource {
    id: string;
    name: string;
    path?: string;           // 原始文件路径（如果有）
    createdAt: string;
    updatedAt: string;
    width?: number;
    height?: number;
    // 运行时数据
    data?: ArrayBuffer;
    blobUrl?: string;
    isLoaded: boolean;

    // 持久化数据（保存时使用）
    dataBase64?: string;

    // 原始文件信息
    originalFile?: {
        name: string;
        size: number;
        lastModified: number;
        type: string;
        webkitRelativePath?: string;  // 文件夹上传时的相对路径
    };
}

// 拆分设置接口 - 重新设计为只包含裁切设置（导出设置使用全局设置）
export interface SplitSettings {
    cellWidth: number;          // 单元格宽度
    cellHeight: number;         // 单元格高度
    gridMode: boolean;          // 网格模式
    padding: number;            // 外边距
    spacing: number;            // 间距
    autoDetect: boolean;        // 自动检测透明区域
    minWidth: number;           // 最小宽度
    minHeight: number;          // 最小高度
}

// 🎯 处理后的图片数据类型
export interface ProcessedImageData {
    original?: {
        width: number;
        height: number;
        dataUrl: string;
    };
    thumbnail?: {
        width: number;
        height: number;
        dataUrl: string;
        size: number;
    };
    preview?: {
        width: number;
        height: number;
        dataUrl: string;
        size: number;
    };
    processedAt: string;
}

// 图片资源（叶子节点或容器节点）
export interface ImageResource extends BaseResource {
    type: 'image';

    // 🎯 关键：支持拆分后的子图片
    children?: ImageResource[];    // 拆分后的子图片
    // 🎯 SplitPanel 设置
    splitSettings?: SplitSettings;
    // 🎯 SpriteCutDialog 保存的裁切数据
    cropData?: CropData;
    cropImage?:ImageResource[];
    // 🎯 新增：Worker处理后的图片数据
    processedData?: ProcessedImageData;
    // 🎯 拆分信息
    splitInfo?: {
        parentId?: string;
        region?: {
            x: number;
            y: number;
            width: number;
            height: number;
        };
        splitMethod?: 'manual' | 'auto' | 'grid';
    };
}

// 文件夹资源（容器节点）
export interface FolderResource extends BaseResource {
    type: 'folder';
    children: ResourceItem[];     // 可以包含图片或子文件夹
    isExpanded?: boolean;         // UI状态：是否展开
    isLoaded: boolean;           // 是否已加载子项

    // 文件夹不需要图片相关属性，所以重写这些
    data?: never;
    blobUrl?: never;
    dataBase64?: never;
    width?: never;
    height?: never;
}

// 🎯 全局布局设置接口
export interface LayoutSettings {
    padding: number;           // 图片与包围盒的间距
    spacing: number;           // 图片与图片的间距
    algorithm: 'maxrects' | 'shelf' | 'potpack' | 'guillotine' | 'none';
    powerOfTwo: boolean;       // 尺寸为2的幂
    allowRotation: boolean;    // 允许旋转图片
    maxWidth: number;
    maxHeight: number;
  }
// 图集资源（特殊的容器节点）
export interface AtlasResource extends BaseResource {
    type: 'atlas';
    isExpanded?: boolean;         // UI状态：是否展开
    // 🎯 图集包含的图片（可以是原图或拆分后的子图片）
    children: ImageResource[];
    layoutSettings?: LayoutSettings; // 布局设置
    // 图集画布信息
    canvas?: {
        width: number;
        height: number;
        backgroundColor?: string;
    };
}

// 联合类型
export type ResourceItem = ImageResource | FolderResource | AtlasResource;

// 布局相关类型
export interface LayoutItem {
    x: number;
    y: number;
    width: number;
    height: number;
    resource: ImageResource;
    rotated?: boolean;
}

export interface LayoutResult {
    items: LayoutItem[];
    width: number;
    height: number;
    efficiency: number;
}

export interface LayoutOptions {
    algorithm: 'maxrects' | 'shelf' | 'potpack' | 'guillotine' | 'none';
    maxWidth: number;
    maxHeight: number;
    padding: number;              // 图片与图片的间距
    boundingBoxPadding?: number;  // 🎯 新增：包围盒边距
    allowRotation: boolean;
    powerOfTwo: boolean;
}

// 裁切相关类型
export interface CropArea {
    id: string;
    x: number;
    y: number;
    width: number;
    height: number;
    name: string;
    selected?: boolean;
}

export interface CropData {
    areas: CropArea[];
    cellWidth: number;
    cellHeight: number;
    gridMode: boolean;
    gridRows: number;
    gridCols: number;
    padding: number;
    spacing: number;
    autoTrim: boolean;
    lastModified: Date;
}

