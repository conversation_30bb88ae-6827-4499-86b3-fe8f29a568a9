/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */
import Base from "./base/Base";
import { Project } from "./base/Project";
import { Frame } from "./base/Frame";
import { Tween } from "./base/Tween";
import { WickNamespace } from "./tp";
import { Clipboard } from "./Clipboard";
import { ToolSettings } from "./ToolSettings";
import { Color } from "./Color";

/**
 * This object creates a Wick namespace for wick-engine functionality and utilities.
 */
export const Wick: WickNamespace = {
  version: window.WICK_ENGINE_BUILD_VERSION || "dev",
  resourcepath: "../dist/",
  _originals: {}, // Eventually store a single instance of each type of Wick.Base object (see Wick.Base constructor).
  Clipboard,
  ToolSettings,
  Base,
  Project,
  Frame,
  Tween,
  Color,
};

console.log('Wick Engine version "' + Wick.version + '" is available.');

// Ensure that the Wick namespace is accessible in environments where globals are finicky (react, webpack, etc)
if (typeof window !== "undefined") {
  window.Wick = Wick;
}
