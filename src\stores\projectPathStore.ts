/**
 * 项目路径状态管理
 * 用于在菜单栏和快捷键系统之间共享当前项目的保存路径
 */

// 全局项目路径状态
let currentProjectPath: string | null = null;

/**
 * 获取当前项目路径
 */
export function getCurrentProjectPath(): string | null {
  return currentProjectPath;
}

/**
 * 设置当前项目路径
 */
export function setCurrentProjectPath(path: string | null): void {
  currentProjectPath = path;
  console.log('📁 ProjectPathStore: 项目路径已更新:', path);
}

/**
 * 清除当前项目路径
 */
export function clearCurrentProjectPath(): void {
  currentProjectPath = null;
  console.log('📁 ProjectPathStore: 项目路径已清除');
}

/**
 * 检查是否有当前项目路径
 */
export function hasCurrentProjectPath(): boolean {
  return currentProjectPath !== null;
}
