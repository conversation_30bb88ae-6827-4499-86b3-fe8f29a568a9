/**
 * GameSprite Engine - 项目管理
 */

import { generateId } from './types.js';
import { Scene } from './Scene.js';

export interface ProjectSettings {
  name?: string;
  width?: number;
  height?: number;
  backgroundColor?: number;
  framerate?: number;
}

export class Project {
  readonly id: string;
  
  // 项目基本信息
  private _name: string;
  private _width: number;
  private _height: number;
  private _backgroundColor: number;
  private _framerate: number;
  
  // 场景管理
  private scenes: Map<string, Scene> = new Map();
  private _currentSceneId: string | null = null;
  
  // 项目状态
  private _created: Date;
  private _modified: Date;
  
  constructor(settings: ProjectSettings = {}) {
    this.id = generateId();
    
    // 设置默认值
    this._name = settings.name || 'New Project';
    this._width = settings.width || 800;
    this._height = settings.height || 600;
    this._backgroundColor = settings.backgroundColor || 0x2a2a2a;
    this._framerate = settings.framerate || 60;
    
    // 时间戳
    this._created = new Date();
    this._modified = new Date();
    
    // 创建默认场景
    this.createDefaultScene();
    
    console.log('🎬 Project: 项目创建完成', {
      id: this.id,
      name: this._name,
      size: `${this._width}×${this._height}`,
      scenes: this.scenes.size
    });
  }
  
  // === 项目属性 ===
  get name(): string { return this._name; }
  set name(value: string) {
    this._name = value;
    this.updateModified();
  }
  
  get width(): number { return this._width; }
  set width(value: number) {
    if (value > 0) {
      this._width = value;
      this.updateSceneSizes();
      this.updateModified();
    }
  }
  
  get height(): number { return this._height; }
  set height(value: number) {
    if (value > 0) {
      this._height = value;
      this.updateSceneSizes();
      this.updateModified();
    }
  }
  
  get backgroundColor(): number { return this._backgroundColor; }
  set backgroundColor(value: number) {
    this._backgroundColor = value;
    this.updateModified();
  }
  
  get framerate(): number { return this._framerate; }
  set framerate(value: number) {
    if (value > 0) {
      this._framerate = value;
      this.updateModified();
    }
  }
  
  get created(): Date { return this._created; }
  get modified(): Date { return this._modified; }
  
  // === 场景管理 ===
  createScene(name?: string): Scene {
    const scene = new Scene(this._width, this._height, name);
    this.scenes.set(scene.id, scene);
    this.updateModified();
    
    console.log('🎭 Project: 场景创建完成', {
      sceneId: scene.id,
      sceneName: scene.name,
      totalScenes: this.scenes.size
    });
    
    return scene;
  }
  
  addScene(scene: Scene): void {
    // 确保场景尺寸与项目一致
    scene.resize(this._width, this._height);
    this.scenes.set(scene.id, scene);
    this.updateModified();
  }
  
  removeScene(sceneId: string): boolean {
    const scene = this.scenes.get(sceneId);
    if (!scene) return false;
    
    // 如果删除的是当前场景，需要切换到其他场景
    if (this._currentSceneId === sceneId) {
      const remainingScenes = Array.from(this.scenes.keys()).filter(id => id !== sceneId);
      this._currentSceneId = remainingScenes.length > 0 ? remainingScenes[0] : null;
    }
    
    scene.destroy();
    this.scenes.delete(sceneId);
    this.updateModified();
    
    return true;
  }
  
  getScene(sceneId: string): Scene | null {
    return this.scenes.get(sceneId) || null;
  }
  
  getAllScenes(): Scene[] {
    return Array.from(this.scenes.values());
  }
  
  setCurrentScene(sceneId: string): boolean {
    if (this.scenes.has(sceneId)) {
      this._currentSceneId = sceneId;
      this.updateModified();
      return true;
    }
    return false;
  }
  
  getCurrentScene(): Scene | null {
    return this._currentSceneId ? this.scenes.get(this._currentSceneId) || null : null;
  }
  
  // === 项目操作 ===
  resize(width: number, height: number): void {
    this._width = width;
    this._height = height;
    this.updateSceneSizes();
    this.updateModified();
    
    console.log('📐 Project: 项目尺寸更新', {
      newSize: `${width}×${height}`,
      affectedScenes: this.scenes.size
    });
  }
  
  // === 私有方法 ===
  private createDefaultScene(): void {
    const defaultScene = this.createScene('Main Scene');
    this._currentSceneId = defaultScene.id;
  }
  
  private updateSceneSizes(): void {
    for (const scene of this.scenes.values()) {
      scene.resize(this._width, this._height);
    }
  }
  
  private updateModified(): void {
    this._modified = new Date();
  }
  
  // === 序列化 ===
  toJSON() {
    return {
      id: this.id,
      name: this._name,
      width: this._width,
      height: this._height,
      backgroundColor: this._backgroundColor,
      framerate: this._framerate,
      created: this._created.toISOString(),
      modified: this._modified.toISOString(),
      currentSceneId: this._currentSceneId,
      scenes: Array.from(this.scenes.values()).map(scene => ({
        id: scene.id,
        name: scene.name,
        width: scene.width,
        height: scene.height
      }))
    };
  }
  
  // === 清理 ===
  destroy(): void {
    // 销毁所有场景
    for (const scene of this.scenes.values()) {
      scene.destroy();
    }
    this.scenes.clear();
    this._currentSceneId = null;
    
    console.log('🧹 Project: 项目已销毁', { id: this.id });
  }
}
