/**
 * 项目保存和加载功能
 * 处理 GameSprite Studio 项目文件的序列化和反序列化
 */

use serde::{Deserialize, Serialize};
use std::fs;
use std::path::PathBuf;
use tauri::command;
use base64::{Engine as _, engine::general_purpose};
use crate::image_processor::{batch_process_images, ImageInput, ProcessOptions};

/// 项目保存数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectSaveData {
    /// 项目元信息
    pub metadata: ProjectMetadata,
    /// 图集数据
    pub atlas_data: serde_json::Value,
    /// 资源数据
    pub resource_data: serde_json::Value,
    /// 导出设置数据
    pub export_settings: serde_json::Value,
    /// 数据校验和
    pub checksum: String,
}

/// 项目元信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectMetadata {
    /// 项目文件版本
    pub version: String,
    /// 项目名称
    pub project_name: String,
    /// 创建时间
    pub created_at: String,
    /// 最后更新时间
    pub updated_at: String,
    /// 创建者
    pub created_by: Option<String>,
    /// 项目描述
    pub description: Option<String>,
    /// 项目标签
    pub tags: Option<Vec<String>>,
}

/// 保存结果
#[derive(Debug, Serialize, Deserialize)]
pub struct SaveResult {
    pub success: bool,
    pub file_path: Option<String>,
    pub file_size: Option<u64>,
    pub error: Option<String>,
}

/// 加载结果
#[derive(Debug, Serialize, Deserialize)]
pub struct LoadResult {
    pub success: bool,
    pub data: Option<ProjectSaveData>,
    pub error: Option<String>,
    pub warnings: Option<Vec<String>>,
}

/// 计算数据校验和
fn calculate_checksum(data: &str) -> String {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};

    let mut hasher = DefaultHasher::new();
    data.hash(&mut hasher);
    format!("{:x}", hasher.finish())
}

/// 验证数据完整性
fn validate_checksum(data: &ProjectSaveData, _raw_data: &str) -> bool {
    // 重新计算除了checksum之外的数据的校验和
    let mut temp_data = data.clone();
    temp_data.checksum = String::new();

    // 使用与保存时相同的序列化方式（pretty格式）
    if let Ok(temp_json) = serde_json::to_string_pretty(&temp_data) {
        let calculated_checksum = calculate_checksum(&temp_json);
        let result = calculated_checksum == data.checksum;

        // 添加调试信息
        println!("校验和验证:");
        println!("  存储的校验和: {}", data.checksum);
        println!("  计算的校验和: {}", calculated_checksum);
        println!("  验证结果: {}", result);

        return result;
    }

    false
}

/// 保存项目文件
#[command]
pub async fn save_project_file(
    file_path: String,
    atlas_data: serde_json::Value,
    resource_data: serde_json::Value,
    export_settings: serde_json::Value,
    project_name: String,
    description: Option<String>,
    tags: Option<Vec<String>>,
) -> Result<SaveResult, String> {
    println!("开始保存项目文件: {}", file_path);

    let now = chrono::Utc::now().to_rfc3339();

    // 构建项目数据
    let mut project_data = ProjectSaveData {
        metadata: ProjectMetadata {
            version: "1.0.0".to_string(),
            project_name,
            created_at: now.clone(),
            updated_at: now,
            created_by: None, // 可以后续添加用户信息
            description,
            tags,
        },
        atlas_data,
        resource_data,
        export_settings,
        checksum: String::new(), // 稍后计算
    };

    // 先序列化不包含校验和的数据来计算校验和
    let temp_json_for_checksum = serde_json::to_string_pretty(&project_data)
        .map_err(|e| format!("序列化项目数据失败: {}", e))?;

    // 计算校验和（基于不包含校验和的数据）
    project_data.checksum = calculate_checksum(&temp_json_for_checksum);

    println!("保存时计算的校验和: {}", project_data.checksum);

    // 重新序列化包含校验和的数据
    let final_json_data = serde_json::to_string_pretty(&project_data)
        .map_err(|e| format!("序列化最终项目数据失败: {}", e))?;

    // 确保目录存在
    let path = PathBuf::from(&file_path);
    if let Some(parent) = path.parent() {
        if !parent.exists() {
            fs::create_dir_all(parent)
                .map_err(|e| format!("创建目录失败: {}", e))?;
        }
    }

    // 写入文件
    fs::write(&path, &final_json_data)
        .map_err(|e| format!("写入项目文件失败: {}", e))?;

    // 获取文件大小
    let file_size = fs::metadata(&path)
        .map(|m| m.len())
        .unwrap_or(0);

    println!("项目文件保存成功: {} ({}字节)", file_path, file_size);

    Ok(SaveResult {
        success: true,
        file_path: Some(file_path),
        file_size: Some(file_size),
        error: None,
    })
}

/// 加载项目文件
#[command]
pub async fn load_project_file(file_path: String) -> Result<LoadResult, String> {
    println!("开始加载项目文件: {}", file_path);

    let path = PathBuf::from(&file_path);

    // 检查文件是否存在
    if !path.exists() {
        return Ok(LoadResult {
            success: false,
            data: None,
            error: Some("项目文件不存在".to_string()),
            warnings: None,
        });
    }

    // 读取文件内容
    let file_content = fs::read_to_string(&path)
        .map_err(|e| format!("读取项目文件失败: {}", e))?;

    // 解析JSON数据
    let project_data: ProjectSaveData = serde_json::from_str(&file_content)
        .map_err(|e| format!("解析项目文件失败: {}", e))?;

    let mut warnings = Vec::new();

    // 验证数据完整性
    if !validate_checksum(&project_data, &file_content) {
        warnings.push("数据校验和不匹配，文件可能已损坏".to_string());
    }

    // 版本兼容性检查
    if project_data.metadata.version != "1.0.0" {
        warnings.push(format!(
            "项目文件版本 {} 可能不完全兼容当前版本",
            project_data.metadata.version
        ));
    }

    println!("项目文件加载成功: {}", file_path);

    Ok(LoadResult {
        success: true,
        data: Some(project_data),
        error: None,
        warnings: if warnings.is_empty() { None } else { Some(warnings) },
    })
}

/// 验证项目文件
#[command]
pub async fn validate_project_file(file_path: String) -> Result<bool, String> {
    println!("验证项目文件: {}", file_path);

    let path = PathBuf::from(&file_path);

    // 检查文件是否存在
    if !path.exists() {
        return Ok(false);
    }

    // 尝试读取和解析文件
    match fs::read_to_string(&path) {
        Ok(content) => {
            match serde_json::from_str::<ProjectSaveData>(&content) {
                Ok(data) => {
                    // 验证校验和
                    let is_valid = validate_checksum(&data, &content);
                    println!("项目文件验证结果: {}", is_valid);
                    Ok(is_valid)
                }
                Err(_) => Ok(false),
            }
        }
        Err(_) => Ok(false),
    }
}

/// 🎯 统一保存项目 - 新API
#[command]
pub async fn save_project_unified(
    file_path: String,
    project_name: String,
    description: Option<String>,
    tags: Option<Vec<String>>,
    image_resources: serde_json::Value,
    atlas_data: serde_json::Value,
    export_settings: serde_json::Value,
    window: tauri::Window,
) -> Result<SaveResult, String> {
    println!("🚀 开始统一保存项目: {}", file_path);

    let start_time = std::time::Instant::now();

    // 1. 处理图片资源：提取ArrayBuffer并转换为base64
    let processed_resources = process_resources_for_save(image_resources, &window).await?;

    // 2. 构建项目数据
    let now = chrono::Utc::now().to_rfc3339();
    let mut project_data = ProjectSaveData {
        metadata: ProjectMetadata {
            version: "1.0.0".to_string(),
            project_name,
            created_at: now.clone(),
            updated_at: now,
            created_by: None,
            description,
            tags,
        },
        atlas_data,
        resource_data: processed_resources,
        export_settings,
        checksum: String::new(),
    };

    // 3. 计算校验和
    let temp_json = serde_json::to_string_pretty(&project_data)
        .map_err(|e| format!("序列化项目数据失败: {}", e))?;
    project_data.checksum = calculate_checksum(&temp_json);

    // 4. 保存文件
    let final_json = serde_json::to_string_pretty(&project_data)
        .map_err(|e| format!("序列化最终数据失败: {}", e))?;

    let path = PathBuf::from(&file_path);
    if let Some(parent) = path.parent() {
        if !parent.exists() {
            fs::create_dir_all(parent)
                .map_err(|e| format!("创建目录失败: {}", e))?;
        }
    }

    fs::write(&path, &final_json)
        .map_err(|e| format!("写入文件失败: {}", e))?;

    let file_size = fs::metadata(&path).map(|m| m.len()).unwrap_or(0);
    let processing_time = start_time.elapsed().as_millis() as u64;

    println!("✅ 统一保存完成: {} ms, {} bytes", processing_time, file_size);

    Ok(SaveResult {
        success: true,
        file_path: Some(file_path),
        file_size: Some(file_size),
        error: None,
    })
}

/// 🎯 统一加载项目 - 新API
#[command]
pub async fn load_project_unified(
    file_path: String,
    window: tauri::Window,
) -> Result<LoadResult, String> {
    println!("🚀 开始统一加载项目: {}", file_path);

    let start_time = std::time::Instant::now();
    let path = PathBuf::from(&file_path);

    if !path.exists() {
        return Ok(LoadResult {
            success: false,
            data: None,
            error: Some("项目文件不存在".to_string()),
            warnings: None,
        });
    }

    // 1. 读取和解析项目文件
    let file_content = fs::read_to_string(&path)
        .map_err(|e| format!("读取文件失败: {}", e))?;

    let project_data: ProjectSaveData = serde_json::from_str(&file_content)
        .map_err(|e| format!("解析项目文件失败: {}", e))?;

    let mut warnings = Vec::new();
    if !validate_checksum(&project_data, &file_content) {
        warnings.push("数据校验和不匹配，文件可能已损坏".to_string());
    }

    // 2. 处理图片资源：base64转ArrayBuffer并重新生成processedData
    let restored_resources = process_resources_for_load(project_data.resource_data, &window).await?;

    // 3. 构建返回数据
    let result_data = ProjectSaveData {
        metadata: project_data.metadata,
        atlas_data: project_data.atlas_data,
        resource_data: restored_resources,
        export_settings: project_data.export_settings,
        checksum: project_data.checksum,
    };

    let processing_time = start_time.elapsed().as_millis() as u64;
    println!("✅ 统一加载完成: {} ms", processing_time);

    Ok(LoadResult {
        success: true,
        data: Some(result_data),
        error: None,
        warnings: if warnings.is_empty() { None } else { Some(warnings) },
    })
}

/// 🎯 处理资源用于保存：ArrayBuffer转base64，删除运行时数据
async fn process_resources_for_save(
    resources: serde_json::Value,
    window: &tauri::Window,
) -> Result<serde_json::Value, String> {
    println!("🔄 开始处理资源用于保存");

    // 递归处理资源数组
    if let Some(resource_data) = resources.as_object() {
        if let Some(resources_array) = resource_data.get("resources").and_then(|v| v.as_array()) {
            let processed_array = process_resource_array_for_save(resources_array, window)?;

            let mut result = resource_data.clone();
            result.insert("resources".to_string(), serde_json::Value::Array(processed_array));

            return Ok(serde_json::Value::Object(result));
        }
    }

    Ok(resources)
}

/// 🎯 递归处理资源数组用于保存
fn process_resource_array_for_save(
    resources: &[serde_json::Value],
    _window: &tauri::Window,
) -> Result<Vec<serde_json::Value>, String> {
    let mut processed_resources = Vec::new();

    for resource in resources {
        let mut processed_resource = resource.clone();

        if let Some(resource_obj) = processed_resource.as_object_mut() {
            // 处理图片资源
            if resource_obj.get("type").and_then(|v| v.as_str()) == Some("image") {
                println!("🔄 处理图片资源: {}",
                    resource_obj.get("name").and_then(|v| v.as_str()).unwrap_or("unknown"));

                // 🎯 关键修复：将ArrayBuffer转换为base64
                if let Some(data_value) = resource_obj.get("data") {
                    // 检查data字段是否存在且不为null
                    if !data_value.is_null() {
                        // 尝试从data字段提取ArrayBuffer数据
                        // 注意：前端传递的ArrayBuffer在JSON中可能表现为数组或其他格式
                        if let Some(data_array) = data_value.as_array() {
                            // ArrayBuffer在JSON中表现为数字数组
                            let buffer: Vec<u8> = data_array.iter()
                                .filter_map(|v| v.as_u64().map(|n| n as u8))
                                .collect();

                            if !buffer.is_empty() {
                                let base64_data = general_purpose::STANDARD.encode(&buffer);
                                resource_obj.insert("dataBase64".to_string(), serde_json::Value::String(base64_data));
                                println!("✅ 转换ArrayBuffer到base64: {} bytes", buffer.len());
                            }
                        }
                    }
                }

                // 删除运行时数据
                resource_obj.remove("data");
                resource_obj.remove("blobUrl");
                resource_obj.remove("processedData");
            }
            // 处理文件夹资源
            else if resource_obj.get("type").and_then(|v| v.as_str()) == Some("folder") {
                if let Some(children) = resource_obj.get("children").and_then(|v| v.as_array()) {
                    let processed_children = process_resource_array_for_save(children, _window)?;
                    resource_obj.insert("children".to_string(), serde_json::Value::Array(processed_children));
                }
            }
        }

        processed_resources.push(processed_resource);
    }

    Ok(processed_resources)
}

/// 🎯 处理资源用于加载：base64转ArrayBuffer，重新生成processedData
async fn process_resources_for_load(
    resources: serde_json::Value,
    window: &tauri::Window,
) -> Result<serde_json::Value, String> {
    println!("🔄 开始处理资源用于加载");

    // 递归处理资源数组
    if let Some(resource_data) = resources.as_object() {
        if let Some(resources_array) = resource_data.get("resources").and_then(|v| v.as_array()) {
            let processed_array = process_resource_array_for_load(resources_array.clone(), window.clone()).await?;

            let mut result = resource_data.clone();
            result.insert("resources".to_string(), serde_json::Value::Array(processed_array));

            return Ok(serde_json::Value::Object(result));
        }
    }

    Ok(resources)
}

/// 🎯 处理资源数组用于加载（非递归版本）
async fn process_resource_array_for_load(
    resources: Vec<serde_json::Value>,
    window: tauri::Window,
) -> Result<Vec<serde_json::Value>, String> {
        let mut processed_resources = Vec::new();
        let mut images_to_process = Vec::new();

        // 第一遍：收集需要处理的图片并恢复base64到ArrayBuffer
        for resource in resources {
            let mut processed_resource = resource.clone();

            if let Some(resource_obj) = processed_resource.as_object_mut() {
                // 处理图片资源
                if resource_obj.get("type").and_then(|v| v.as_str()) == Some("image") {
                    // 恢复base64到ArrayBuffer
                    if let Some(data_base64) = resource_obj.get("dataBase64").and_then(|v| v.as_str()) {
                        match general_purpose::STANDARD.decode(data_base64) {
                            Ok(buffer) => {
                                // 准备批量处理的数据
                                let file_name = resource_obj.get("name").and_then(|v| v.as_str()).unwrap_or("").to_string();
                                let image_input = ImageInput {
                                    id: resource_obj.get("id").and_then(|v| v.as_str()).unwrap_or("").to_string(),
                                    buffer,
                                    original_type: resource_obj
                                        .get("originalFile")
                                        .and_then(|f| f.get("type"))
                                        .and_then(|v| v.as_str())
                                        .unwrap_or("image/png")
                                        .to_string(),
                                    file_name: file_name.clone(),
                                };
                                images_to_process.push((image_input, processed_resource.clone()));

                                println!("🔄 准备处理图片: {}", file_name);
                            }
                            Err(e) => {
                                println!("❌ base64解码失败: {}", e);
                            }
                        }
                    }
                }
                // 处理文件夹资源（递归处理子资源）
                else if resource_obj.get("type").and_then(|v| v.as_str()) == Some("folder") {
                    // 递归处理文件夹中的子资源
                    if let Some(children) = resource_obj.get("children").and_then(|v| v.as_array()) {
                        let processed_children = Box::pin(process_resource_array_for_load(children.clone(), window.clone())).await?;
                        resource_obj.insert("children".to_string(), serde_json::Value::Array(processed_children));
                    }
                    processed_resources.push(processed_resource);
                } else {
                    processed_resources.push(processed_resource);
                }
            } else {
                processed_resources.push(processed_resource);
            }
        }

        // 第二遍：批量处理图片生成processedData
        if !images_to_process.is_empty() {
            println!("� 开始批量处理 {} 个图片", images_to_process.len());

            let image_inputs: Vec<ImageInput> = images_to_process.iter().map(|(input, _)| input.clone()).collect();

            let process_options = ProcessOptions {
                generate_thumbnail: Some(true),
                thumbnail_size: Some(128),
                generate_preview: Some(true),
                preview_size: Some(256),
                quality: Some(0.8),
                format: Some("webp".to_string()),
            };

            match batch_process_images(image_inputs, process_options, window.clone()).await {
                Ok(batch_response) => {
                    // 将处理结果映射回资源
                    for (_, mut resource) in images_to_process {
                        if let Some(resource_obj) = resource.as_object_mut() {
                            let resource_id = resource_obj.get("id").and_then(|v| v.as_str()).unwrap_or("");

                            // 查找对应的处理结果
                            if let Some(result) = batch_response.results.iter().find(|r| r.id == resource_id) {
                                if result.success {
                                    if let Some(processed_data) = &result.processed_data {
                                        resource_obj.insert("processedData".to_string(),
                                            serde_json::to_value(processed_data).unwrap_or(serde_json::Value::Null));
                                    }
                                }
                            }

                            // 🎯 恢复data字段为ArrayBuffer格式（数组形式）
                            if let Some(data_base64) = resource_obj.get("dataBase64").and_then(|v| v.as_str()) {
                                if let Ok(buffer) = general_purpose::STANDARD.decode(data_base64) {
                                    // 将buffer转换为数字数组（前端可以转换回ArrayBuffer）
                                    let data_array: Vec<serde_json::Value> = buffer.iter()
                                        .map(|&b| serde_json::Value::Number(serde_json::Number::from(b)))
                                        .collect();
                                    resource_obj.insert("data".to_string(), serde_json::Value::Array(data_array));
                                }
                            }

                            // 移除dataBase64
                            resource_obj.remove("dataBase64");
                        }
                        processed_resources.push(resource);
                    }

                    println!("✅ 批量处理完成，成功: {}", batch_response.total_processed);
                }
                Err(e) => {
                    println!("❌ 批量处理失败: {}", e);
                    // 即使失败也要添加资源，只是没有processedData
                    for (_, resource) in images_to_process {
                        processed_resources.push(resource);
                    }
                }
            }
        }

        Ok(processed_resources)
}