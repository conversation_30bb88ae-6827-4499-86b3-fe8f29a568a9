/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { Layer as BaseLayer } from "../base/Layer";

export class Layer extends GUIElement {
  protected _layer: BaseLayer;

  constructor(model: Base) {
    super(model);
    this._layer = model as BaseLayer;
  }

  draw(): void {
    super.draw();

    const ctx = this.ctx;

    // Draw layer background
    ctx.fillStyle = GUIElement.LAYER_BACKGROUND_COLOR;
    ctx.fillRect(0, 0, this.gridWidth, this.gridCellHeight);

    // Draw layer name
    ctx.fillStyle = GUIElement.LAYER_TEXT_COLOR;
    ctx.font = GUIElement.LAYER_TEXT_FONT;
    ctx.textBaseline = "middle";
    ctx.textAlign = "left";
    ctx.fillText(
      this._layer.identifier,
      GUIElement.LAYER_TEXT_PADDING,
      this.gridCellHeight / 2
    );

    // Draw layer border
    ctx.strokeStyle = GUIElement.LAYER_BORDER_COLOR;
    ctx.lineWidth = 1;
    ctx.strokeRect(0, 0, this.gridWidth, this.gridCellHeight);

    // Draw layer hidden/locked icons
    if (this._layer.hidden) {
      ctx.save();
      ctx.translate(
        this.gridWidth -
          GUIElement.LAYER_ICON_SIZE -
          GUIElement.LAYER_ICON_PADDING,
        this.gridCellHeight / 2 - GUIElement.LAYER_ICON_SIZE / 2
      );
      ctx.fillStyle = GUIElement.LAYER_ICON_COLOR;
      ctx.beginPath();
      ctx.arc(
        GUIElement.LAYER_ICON_SIZE / 2,
        GUIElement.LAYER_ICON_SIZE / 2,
        GUIElement.LAYER_ICON_SIZE / 2,
        0,
        Math.PI * 2
      );
      ctx.fill();
      ctx.restore();
    }

    if (this._layer.locked) {
      ctx.save();
      ctx.translate(
        this.gridWidth -
          GUIElement.LAYER_ICON_SIZE * 2 -
          GUIElement.LAYER_ICON_PADDING * 2,
        this.gridCellHeight / 2 - GUIElement.LAYER_ICON_SIZE / 2
      );
      ctx.fillStyle = GUIElement.LAYER_ICON_COLOR;
      ctx.beginPath();
      ctx.arc(
        GUIElement.LAYER_ICON_SIZE / 2,
        GUIElement.LAYER_ICON_SIZE / 2,
        GUIElement.LAYER_ICON_SIZE / 2,
        0,
        Math.PI * 2
      );
      ctx.fill();
      ctx.restore();
    }
  }
}
