<script lang="ts">
  /**
   * 左侧面板组件 - 图集管理面板
   */

  import { onMount } from 'svelte';
  import AtlasItem from './AtlasItem.svelte';
  import { atlasStore } from '../stores/atlasStore';
  import { dragStore } from '../stores/dragStore';

  import type { AtlasResource, ImageResource } from '../types/imageType';
  import { performanceMonitor } from '../utils/performanceMonitor';

  // 🎯 导入图集导出相关功能
  import {
    analyzeAtlasesForExport,
    exportAllAtlases,
    type AtlasExportPreview
  } from '../utils/atlasExportUtils';
  import { exportSettingsStore } from '../stores/exportSettingsStore';

  // 导入国际化
  import { _ } from '../lib/i18n';

  // 导入Toast提示
  import { toast } from '../stores/toastStore';

  // 🎯 监听 atlases 变化
  let atlases = $state<AtlasResource[]>([]);
  let currentDragState = $state<any>({ isDragging: false, dragData: null, mousePosition: null });

  // 🎯 计算属性 - 减少重复计算
  const hasAtlases = $derived(atlases.length > 0);
  const shouldShowVirtualization = $derived(atlases.length > 20);
  const atlasCount = $derived(atlases.length);

  // 🎯 拖拽状态管理
  let isDragOverPanel = $state(false);

  // 对话框状态（保留设置对话框，可能还需要用于编辑图集）
  let showSettingsDialog = $state(false);
  let currentAtlas = $state<AtlasResource | null>(null);

  // 🎯 导出相关状态
  let showExportPreview = $state(false);
  let exportPreviewData = $state<AtlasExportPreview | null>(null);
  let isExporting = $state(false);
  let currentExportSettings = $state<any>(null);

  // 🎯 使用$effect替代传统subscribe，自动清理
  $effect(() => {
    const unsubscribeAtlas = atlasStore.subscribe((newState) => {
      atlases = newState.atlases;
    });

    const unsubscribeDrag = dragStore.subscribe((state) => {
      currentDragState = state;
    });

    // 🎯 监听导出设置变化
    const unsubscribeExport = exportSettingsStore.subscribe(settings => {
      currentExportSettings = settings;
    });

    // 自动清理订阅
    return () => {
      unsubscribeAtlas();
      unsubscribeDrag();
      unsubscribeExport();
    };
  });







  // 🎯 组件销毁时清理资源
  $effect(() => {
    return () => {
      // 打印最终性能报告
      performanceMonitor.printReport();

      console.log('🧹 LeftPanel: 组件销毁，清理资源');
    };
  });

  // 显示设置对话框
  function showAtlasSettings(atlas: AtlasResource) {
    console.log('⚙️ LeftPanel: 显示图集设置', atlas.name);
    currentAtlas = atlas;
    showSettingsDialog = true;
  }

  // 切换图集展开状态
  function handleToggleAtlas(atlasId: string) {
    // 🎯 直接修改 AtlasResource.isExpanded 属性
    const atlas = atlases.find(a => a.id === atlasId);
    if (atlas) {
      atlasStore.updateAtlas(atlas, {
        isExpanded: !atlas.isExpanded
      });
    }
  }

  // 🎯 导出所有图集
  async function handleExportAllAtlases() {
    if (atlases.length === 0) {
      toast.warning($_('atlas.noAtlas'));
      return;
    }

    try {
      console.log('📤 LeftPanel: 开始导出所有图集', {
        atlasesCount: atlases.length
      });

      // 1. 分析导出预览
      exportPreviewData = await analyzeAtlasesForExport(atlases);
      showExportPreview = true;

    } catch (error) {
      console.error('❌ LeftPanel: 分析图集导出失败', error);
      toast.error(`${error instanceof Error ? error.message : String(error)}`, $_('export.failed'));
    }
  }

  // 🎯 确认导出
  async function confirmExport() {
    try {
      showExportPreview = false;
      isExporting = true;

      console.log('📤 LeftPanel: 确认导出所有图集');

      const result = await exportAllAtlases(atlases);

      if (result.success) {
        toast.success(
          `${$_('export.exportedCount')}: ${result.exportedCount} 个图集<br>${$_('export.skippedCount')}: ${result.skippedCount} 个图集`,
          $_('export.success')
        );
      } else {
        const errorMsg = result.errors.length > 0 ? result.errors.join('<br>') : $_('error.unknown');
        toast.error(errorMsg, $_('export.failed'));
      }

    } catch (error) {
      console.error('❌ LeftPanel: 导出图集失败', error);
      toast.error(`${error instanceof Error ? error.message : String(error)}`, $_('export.failed'));
    } finally {
      isExporting = false;
    }
  }

  // 🎯 取消导出
  function cancelExport() {
    showExportPreview = false;
    exportPreviewData = null;
  }

  // 移除图集中的子项
  function handleRemoveChild(atlasId: string, childId: string) {
    atlasStore.removeResourceFromAtlas(atlasId, childId);
  }

  // 删除图集
  function handleDeleteAtlas(atlasId: string) {
    console.log('🗑️ LeftPanel: 删除图集', atlasId);

    // 🎯 直接删除图集
    const success = atlasStore.deleteAtlas(atlasId);

    if (success) {
      console.log('✅ LeftPanel: 图集删除成功', {
        atlasId,
        remainingAtlases: atlases.length
      });
    } else {
      console.error('❌ LeftPanel: 图集删除失败', atlasId);
    }
  }

  // 🎯 拖拽事件处理
  function handlePanelMouseEnter() {
    if (currentDragState.isDragging && currentDragState.dragData) {
      isDragOverPanel = true;
      console.log('🎯 LeftPanel: 拖拽进入面板区域');
    }
  }

  function handlePanelMouseLeave() {
    isDragOverPanel = false;
  }

  function handlePanelMouseUp(event: MouseEvent) {
    if (isDragOverPanel && currentDragState.isDragging && currentDragState.dragData) {
      const target = event.target as HTMLElement;
      const dragData = currentDragState.dragData;

      // 检查是否在 AtlasItem 上释放
      const atlasItem = target.closest('.atlas-item');

      console.log('🎯 LeftPanel: 面板鼠标释放', {
        isDragOverPanel,
        isDragging: currentDragState.isDragging,
        hasDragData: !!dragData,
        isAtlasItem: !!atlasItem,
        isCroppedImage: !!(dragData.cropArea && dragData.src),
        dragType: dragData.type,
        targetElement: target.className
      });

      if (!atlasItem) {
        // 🎯 检查是否为裁切小图片
        const isCroppedImage = dragData.cropArea && dragData.src;

        if (isCroppedImage) {
          console.log('🎯 LeftPanel: 裁切小图片拖拽到空白区域，创建新图集但不设置选中状态');
        } else {
          console.log('🎯 LeftPanel: 其他类型拖拽到空白区域，创建新图集');
        }

        createAtlasFromDrag(currentDragState.dragData);
        dragStore.endDrag();
      }
      // 如果在 AtlasItem 上释放，让 AtlasItem 自己处理
    }

    isDragOverPanel = false;
  }

  // 🎯 优化的图集创建 - 添加性能监控和状态反馈
  async function createAtlasFromDrag(dragData: any) {
    if (!dragData) return;

    // 🎯 开始性能监控
    performanceMonitor.startMeasure('atlas-creation-from-drag', {
      dragType: dragData.type,
      hasCropArea: !!dragData.cropArea,
      hasResource: !!dragData.resource
    });

    try {
      // 生成图集名称
      const atlasName = generateAtlasName(dragData);

      // 计算合适的画布尺寸
      const canvasSize = calculateOptimalCanvasSize(dragData);

      console.log('🎨 LeftPanel: 创建图集从拖拽', {
        atlasName,
        canvasSize,
        dragType: dragData.type,
        hasCropArea: !!dragData.cropArea
      });

      // 创建图集
      const newAtlas = atlasStore.createAtlas(
        atlasName,
        canvasSize.width,
        canvasSize.height
      );

      // 添加图片到图集
      if (dragData.cropArea && dragData.src) {
        // 裁切图片 - 异步处理
        try {
          const croppedResource = await createCroppedResource(dragData);

          if (croppedResource) {
            atlasStore.addResourceToAtlas(newAtlas.id, croppedResource);
          }
        } catch (error) {
          console.error('❌ LeftPanel: 创建裁切资源失败', error);
          return;
        }
      } else if (dragData.resource) {
        // 完整图片
        atlasStore.addResourceToAtlas(newAtlas.id, dragData.resource);
      }

      // 🎯 首先检查是否为裁切小图片（优先级最高）
      const isCroppedResource = dragData.cropArea && dragData.src;

      if (isCroppedResource) {
        // 裁切小图片：不设置选中状态，避免面板切换
        console.log('🎯 LeftPanel: 裁切小图片拖拽，不设置选中状态，避免面板切换');
        // 不调用atlasStore.selectAtlas()，避免触发面板切换
      } else if (dragData.resource) {
        if (dragData.resource.type === 'image') {
          // ImageResource拖拽：设置atlasStore选中状态
          console.log('🎯 LeftPanel: ImageResource拖拽，设置图集选中状态');
          await atlasStore.selectAtlas(newAtlas);
        } else if (dragData.resource.type === 'atlas') {
          // AtlasResource拖拽：设置atlasStore选中状态
          console.log('🎯 LeftPanel: AtlasResource拖拽，设置atlasStore');
          await atlasStore.selectAtlas(newAtlas);
        }
      } else {
        // 其他类型：设置选中状态
        console.log('🎯 LeftPanel: 其他类型拖拽，设置图集选中状态');
        await atlasStore.selectAtlas(newAtlas);
      }

      console.log('✅ LeftPanel: 图集创建完成', {
        atlasId: newAtlas.id,
        atlasName: newAtlas.name,
        resourceType: dragData.resource?.type || 'unknown'
      });

      // 🎯 结束性能监控
      performanceMonitor.endMeasure('atlas-creation-from-drag');

    } catch (error) {
      console.error('❌ LeftPanel: 创建图集失败', error);

      performanceMonitor.endMeasure('atlas-creation-from-drag');
    }
  }

  // 🎯 生成图集名称
  function generateAtlasName(dragData: any): string {
    const baseName = dragData.name || dragData.resource?.name || '未知图片';
    const atlasName = `${baseName}_图集`;

    // 检查名称是否重复，如果重复则添加数字后缀
    let finalName = atlasName;
    let counter = 1;

    while (atlases.some((atlas: AtlasResource) => atlas.name === finalName)) {
      finalName = `${atlasName}_${counter}`;
      counter++;
    }

    return finalName;
  }

  // 🎯 计算最佳画布尺寸 - 考虑所有图片的尺寸
  function calculateOptimalCanvasSize(dragData: any) {
    // 获取当前拖拽图片的尺寸
    const dragImageWidth = dragData.width || dragData.resource?.width || 256;
    const dragImageHeight = dragData.height || dragData.resource?.height || 256;

    // 🎯 检查是否有现有图集，如果有则考虑其中最大图片的尺寸
    let maxExistingWidth = 0;
    let maxExistingHeight = 0;

    if (atlases.length > 0) {
      // 遍历所有图集中的图片，找到最大尺寸
      for (const atlas of atlases) {
        for (const child of atlas.children) {
          if (child.type === 'image') {
            maxExistingWidth = Math.max(maxExistingWidth, child.width || 0);
            maxExistingHeight = Math.max(maxExistingHeight, child.height || 0);
          }
        }
      }
    }

    // 使用当前图片和现有图片中的最大尺寸
    const maxImageWidth = Math.max(dragImageWidth, maxExistingWidth);
    const maxImageHeight = Math.max(dragImageHeight, maxExistingHeight);

    console.log('🎯 LeftPanel: 计算画布尺寸', {
      dragImage: `${dragImageWidth}×${dragImageHeight}`,
      maxExisting: `${maxExistingWidth}×${maxExistingHeight}`,
      finalMax: `${maxImageWidth}×${maxImageHeight}`
    });

    // 预留空间给更多图片，至少是最大图片尺寸的2倍
    const padding = 64;
    const minSize = 1024; // 🎯 提高最小尺寸，确保能容纳大图片

    const width = Math.max(maxImageWidth * 2 + padding, minSize);
    const height = Math.max(maxImageHeight * 2 + padding, minSize);

    // 确保是2的幂次方（可选，对某些游戏引擎更友好）
    const finalSize = {
      width: Math.pow(2, Math.ceil(Math.log2(width))),
      height: Math.pow(2, Math.ceil(Math.log2(height)))
    };

    console.log('🎯 LeftPanel: 最终画布尺寸', finalSize);

    return finalSize;
  }

  // 🎯 创建裁切图片的资源对象 - 修复Blob URL处理
  async function createCroppedResource(dragData: any): Promise<ImageResource | null> {
    if (!dragData.cropArea || !dragData.src) {
      console.warn('❌ LeftPanel: 创建裁切资源失败 - 缺少必要数据', {
        hasCropArea: !!dragData.cropArea,
        hasSrc: !!dragData.src,
        hasResource: !!dragData.resource
      });
      return null;
    }

    // 🎯 如果dragData.resource存在，直接使用它（cropAreaSelector传递的完整ImageResource）
    if (dragData.resource && dragData.resource.processedData) {
      console.log('✅ LeftPanel: 使用cropAreaSelector传递的完整ImageResource', {
        resourceId: dragData.resource.id,
        resourceName: dragData.resource.name,
        hasProcessedData: !!dragData.resource.processedData
      });
      return dragData.resource;
    }

    try {
      let bytes: Uint8Array;

      // 🎯 检查src是Data URL还是Blob URL
      if (dragData.src.startsWith('data:')) {
        // Data URL格式：data:image/png;base64,xxxxx
        console.log('🔧 LeftPanel: 处理Data URL格式');
        const base64Data = dragData.src.split(',')[1];
        if (!base64Data) {
          throw new Error('无效的Data URL格式');
        }
        const binaryString = atob(base64Data);
        bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
      } else if (dragData.src.startsWith('blob:')) {
        // Blob URL格式：blob:http://localhost:1420/xxxxx
        console.log('🔧 LeftPanel: 处理Blob URL格式');
        const response = await fetch(dragData.src);
        if (!response.ok) {
          throw new Error(`获取Blob数据失败: ${response.status}`);
        }
        const arrayBuffer = await response.arrayBuffer();
        bytes = new Uint8Array(arrayBuffer);
      } else {
        throw new Error(`不支持的URL格式: ${dragData.src.substring(0, 50)}...`);
      }

      // 创建裁切图片的资源对象
      const croppedResource: ImageResource = {
        id: `cropped_${dragData.cropArea.id}_${Date.now()}`,
        name: dragData.cropArea.name,
        type: 'image',
        path: `${dragData.resource?.path || 'unknown'}_cropped_${dragData.cropArea.name}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        isLoaded: true,
        data: bytes.buffer as ArrayBuffer,
        width: dragData.cropArea.width,
        height: dragData.cropArea.height,
        originalFile: {
          name: dragData.cropArea.name,
          size: bytes.length,
          lastModified: Date.now(),
          type: 'image/png'
        },
        // 🎯 添加processedData，使用dragData.src作为预览
        processedData: {
          original: {
            width: dragData.cropArea.width,
            height: dragData.cropArea.height,
            dataUrl: dragData.src
          },
          preview: {
            width: dragData.cropArea.width,
            height: dragData.cropArea.height,
            dataUrl: dragData.src,
            size: Math.min(dragData.cropArea.width, dragData.cropArea.height, 256)
          },
          thumbnail: {
            width: dragData.cropArea.width,
            height: dragData.cropArea.height,
            dataUrl: dragData.src,
            size: Math.min(dragData.cropArea.width, dragData.cropArea.height, 128)
          },
          processedAt: new Date().toISOString()
        },
        // 使用 splitInfo 存储裁切相关信息
        splitInfo: {
          parentId: dragData.resource?.id || 'unknown',
          region: {
            x: dragData.cropArea.x,
            y: dragData.cropArea.y,
            width: dragData.cropArea.width,
            height: dragData.cropArea.height
          },
          splitMethod: 'manual'
        }
      };

      console.log('✅ LeftPanel: 创建裁切资源成功', {
        croppedId: croppedResource.id,
        croppedName: croppedResource.name,
        croppedSize: `${croppedResource.width}×${croppedResource.height}`,
        dataSize: bytes.length,
        originalResource: dragData.resource?.name || 'unknown'
      });

      return croppedResource;
    } catch (error) {
      console.error('❌ LeftPanel: 创建裁切资源失败', error);
      return null;
    }
  }

  onMount(() => {
    console.log('✅ LeftPanel: 图集管理面板已加载');
    console.log('🔍 LeftPanel: 当前图集数量', atlases.length);
  });
</script>

<div
  class="left-panel"
  class:drag-over={isDragOverPanel}
  onmouseenter={handlePanelMouseEnter}
  onmouseleave={handlePanelMouseLeave}
  onmouseup={handlePanelMouseUp}
  role="region"
  aria-label={$_('atlas.dragArea')}
  data-drag-text={$_('atlas.dropToCreate')}
>
  <!-- 面板头部 -->
  <div class="panel-header">
    <h3>{$_('atlas.title')}</h3>
    <div class="area-title">{$_('atlas.title')}</div>
    <!-- 🎯 导出按钮 -->
    <div class="header-actions">
      <button
        class="export-all-btn"
        onclick={handleExportAllAtlases}
        disabled={atlases.length === 0 || isExporting}
        title={$_('export.all')}
      >
        {#if isExporting}
          ⏳
        {:else}
          📤
        {/if}
      </button>
    </div>


  </div>

  <!-- 图集列表 -->
  <div class="panel-content">
    {#if hasAtlases}
      <div class="atlas-list" class:virtualized={shouldShowVirtualization}>
        {#each atlases as atlas (atlas.id)}
          <AtlasItem
            {atlas}
            onToggle={handleToggleAtlas}
            onRemoveChild={handleRemoveChild}
            onDelete={handleDeleteAtlas}
          />
        {/each}
      </div>

      <!-- 🎯 图集统计信息 -->
      <div class="atlas-stats">
        <span class="stats-text">{$_('resource.resourceCount', { values: { count: atlasCount } })}</span>
        {#if shouldShowVirtualization}
          <span class="virtualization-hint">{$_('ui.virtualScrollOptimization')}</span>
        {/if}
      </div>
    {:else}
      <div class="empty-state">
        <div class="empty-icon">📁</div>
        <div class="empty-title">{$_('atlas.noAtlas')}</div>
        <div class="empty-description">
          {$_('atlas.dragToCreate')}
        </div>
      </div>
    {/if}
  </div>
</div>

<!-- 🎯 图集导出预览弹窗 -->
{#if showExportPreview && exportPreviewData}
  <div class="export-preview-overlay">
    <div class="export-preview-modal">
      <!-- 头部 -->
      <div class="modal-header">
        <h3>📤 {$_('export.preview')}</h3>
        <button class="close-btn" onclick={cancelExport}>✕</button>
      </div>

      <div class="modal-content">
        <!-- 统计信息 -->
        <div class="stats-section">
          <h4>📊 {$_('export.statistics')}</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <span class="stat-label">{$_('export.totalAtlases')}:</span>
              <span class="stat-value">{exportPreviewData.totalAtlases}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">{$_('export.validAtlases')}:</span>
              <span class="stat-value success">{exportPreviewData.validAtlases}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">{$_('export.skippedCount')}:</span>
              <span class="stat-value warning">{exportPreviewData.invalidAtlases}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">{$_('export.totalImages')}:</span>
              <span class="stat-value">{exportPreviewData.totalImages}</span>
            </div>
          </div>
        </div>

        <!-- 导出设置 -->
        {#if currentExportSettings}
          <div class="settings-section">
            <h4>⚙️ {$_('export.settings')}</h4>
            <div class="settings-grid">
              <div class="setting-row">
                <div class="setting-group">
                  <span class="setting-label">{$_('export.type')}:</span>
                  <span class="setting-value">{currentExportSettings.selectedType || 'plist'}</span>
                </div>
                <div class="setting-group">
                  <span class="setting-label">{$_('export.format')}:</span>
                  <span class="setting-value">{(currentExportSettings.format || 'png').toUpperCase()}</span>
                </div>
              </div>
              <div class="setting-row">
                <div class="setting-group">
                  <span class="setting-label">{$_('export.quality')}:</span>
                  <span class="setting-value">{Math.round((currentExportSettings.quality || 0.9) * 100)}%</span>
                </div>
                <div class="setting-group">
                  <span class="setting-label">{$_('export.includeOriginal')}:</span>
                  <span class="setting-value">{currentExportSettings.includeOriginal ? $_('ui.yes') : $_('ui.no')}</span>
                </div>
              </div>
            </div>
          </div>
        {/if}

        <!-- 图集列表 -->
        {#if exportPreviewData.atlasNames.length > 0}
          <div class="atlas-list-section">
            <h4>📂 {$_('atlas.title')}</h4>
            <div class="atlas-container">
              <div class="atlas-preview-list">
                {#each exportPreviewData.atlasNames.slice(0, 8) as atlasName}
                  <div class="atlas-preview-item">
                    <span class="atlas-icon">📁</span>
                    <span class="atlas-name">{atlasName}</span>
                  </div>
                {/each}
                {#if exportPreviewData.atlasNames.length > 8}
                  <div class="atlas-preview-item more">
                    <span class="atlas-icon">⋯</span>
                    <span class="atlas-name">{$_('atlas.moreAtlases', { values: { count: exportPreviewData.atlasNames.length - 8 } })}</span>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        {/if}

        <!-- 警告信息 -->
        {#if exportPreviewData.invalidAtlases > 0}
          <div class="warning-section">
            <div class="warning-content">
              <span class="warning-icon">⚠️</span>
              <span class="warning-text">
                {$_('ui.warning')}: {exportPreviewData.invalidAtlases} {$_('export.invalidAtlasWarning')}
              </span>
            </div>
          </div>
        {/if}
      </div>

      <!-- 底部操作 -->
      <div class="modal-footer">
        <div class="footer-info">
          <span class="time-estimate">⏱️ {$_('export.estimatedTime')}: {exportPreviewData.estimatedExportTime} {$_('ui.seconds')}</span>
        </div>
        <div class="footer-actions">
          <button class="cancel-btn" onclick={cancelExport}>{$_('actions.cancel')}</button>
          <button class="confirm-btn" onclick={confirmExport}>{$_('export.startExport')}</button>
        </div>
      </div>
    </div>
  </div>
{/if}

<style>
  .left-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--theme-background);
    overflow: hidden;
    position: relative;
    transition: all 0.2s ease;
  }

  /* 🎯 拖拽悬停效果 */
  .left-panel.drag-over {
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.1) 0%,
      rgba(147, 197, 253, 0.1) 100%);
    border: 2px dashed var(--theme-primary);
    border-radius: var(--border-radius);
  }

  .left-panel.drag-over::after {
    content: attr(data-drag-text);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--theme-primary);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    pointer-events: none;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    white-space: nowrap;
  }

  /* 面板头部 */
  .panel-header {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    padding: var(--spacing-3);
    background: #1a202c; /* 使用更深的背景色 */
    border-bottom: 1px solid var(--theme-border);
    flex-shrink: 0;
    gap: var(--spacing-2);
  }

  .panel-header h3 {
    margin: 0;
    color: #ffffff; /* 白色文字，确保在深色背景上可见 */
    font-size: var(--font-size-sm);
    font-weight: 600;
    justify-self: start; /* 靠左对齐 */
  }

  .area-title {
    font-size: var(--font-size-xs);
    color: #cccccc; /* 浅灰色文字，确保在深色背景上可见 */
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
    opacity: 0.9;
    justify-self: center; /* 居中对齐 */
  }

  /* 🎯 头部操作按钮样式 */
  .header-actions {
    justify-self: end; /* 靠右对齐 */
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
  }

  .export-all-btn {
    background: var(--theme-primary, #3b82f6);
    border: 1px solid var(--theme-primary, #3b82f6);
    border-radius: 4px;
    color: white;
    font-size: 14px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .export-all-btn:hover:not(:disabled) {
    background: var(--theme-primary-dark, #2563eb);
    border-color: var(--theme-primary-dark, #2563eb);
    transform: translateY(-1px);
  }

  .export-all-btn:disabled {
    background: var(--theme-bg-secondary);
    border-color: var(--theme-border);
    color: var(--theme-text-secondary);
    cursor: not-allowed;
    transform: none;
  }



  /* 面板内容 */
  .panel-content {
    flex: 1;
    padding: var(--spacing-3);
    overflow-y: auto;
    overflow-x: hidden;
  }

  .atlas-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
  }

  .atlas-list.virtualized {
    /* 🎯 虚拟滚动模式的样式 */
    position: relative;
  }

  /* 🎯 图集统计信息样式 */
  .atlas-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-2) var(--spacing-1);
    margin-top: var(--spacing-2);
    background: var(--theme-surface-light, rgba(0, 0, 0, 0.02));
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    color: var(--theme-text-secondary);
  }

  .stats-text {
    font-weight: 500;
  }

  .virtualization-hint {
    color: var(--theme-primary);
    font-style: italic;
    opacity: 0.8;
  }

  /* 空状态 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 200px;
    text-align: center;
    color: var(--theme-text-secondary);
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-3);
    opacity: 0.5;
  }

  .empty-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-1);
    color: var(--theme-text);
  }

  .empty-description {
    font-size: var(--font-size-sm);
    opacity: 0.8;
    max-width: 200px;
    line-height: 1.4;
  }

  /* 滚动条样式 */
  .panel-content::-webkit-scrollbar {
    width: 6px;
  }

  .panel-content::-webkit-scrollbar-track {
    background: var(--theme-surface);
  }

  .panel-content::-webkit-scrollbar-thumb {
    background: var(--theme-border);
    border-radius: 3px;
  }

  .panel-content::-webkit-scrollbar-thumb:hover {
    background: var(--theme-border-dark);
  }

  /* 🎯 导出预览弹窗样式 - 参考BottomPanel的设计 */
  .export-preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
  }

  .export-preview-modal {
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 8px;
    padding: 0;
    width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease-out;
  }

  /* 头部样式 */
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: var(--theme-surface);
    border-bottom: 1px solid var(--theme-border);
  }

  .modal-header h3 {
    margin: 0;
    color: var(--theme-text);
    font-size: 16px;
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--theme-text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .close-btn:hover {
    background: var(--theme-surface-hover, var(--theme-surface));
    color: var(--theme-text);
  }

  .modal-content {
    padding: 16px 20px;
    max-height: 60vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  /* 统计信息样式 */
  .stats-section {
    margin-bottom: 12px;
  }

  .stats-section h4 {
    margin: 0 0 8px 0;
    color: var(--theme-text);
    font-size: 14px;
    font-weight: 600;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background: var(--theme-background);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
  }

  .stat-label {
    font-size: 12px;
    color: var(--theme-text-secondary);
    font-weight: 500;
  }

  .stat-value {
    font-size: 12px;
    color: var(--theme-text);
    font-weight: 600;
    font-family: monospace;
  }

  .stat-value.success {
    color: #10b981;
  }

  .stat-value.warning {
    color: #f59e0b;
  }

  /* 设置样式 */
  .settings-section {
    margin-bottom: 12px;
  }

  .settings-section h4 {
    margin: 0 0 8px 0;
    color: var(--theme-text);
    font-size: 14px;
    font-weight: 600;
  }

  .settings-grid {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .setting-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .setting-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background: var(--theme-background);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
  }

  .setting-label {
    font-size: 12px;
    color: var(--theme-text-secondary);
    font-weight: 500;
  }

  .setting-value {
    font-size: 12px;
    color: var(--theme-text);
    font-weight: 600;
    font-family: monospace;
  }

  /* 图集列表样式 */
  .atlas-list-section {
    margin-bottom: 12px;
  }

  .atlas-list-section h4 {
    margin: 0 0 8px 0;
    color: var(--theme-text);
    font-size: 14px;
    font-weight: 600;
  }

  .atlas-container {
    height: 150px;
    overflow-y: auto;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    background: var(--theme-background);
  }

  .atlas-preview-list {
    padding: 6px;
  }

  .atlas-preview-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 6px;
    font-size: 12px;
    color: var(--theme-text);
    border-bottom: 1px solid var(--theme-border);
  }

  .atlas-preview-item:last-child {
    border-bottom: none;
  }

  .atlas-preview-item.more {
    font-style: italic;
    color: var(--theme-text-secondary);
  }

  .atlas-icon {
    font-size: 12px;
    opacity: 0.8;
  }

  .atlas-name {
    font-family: monospace;
    font-size: 11px;
  }

  /* 警告样式 */
  .warning-section {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 4px;
    padding: 8px 12px;
    margin-bottom: 12px;
  }

  .warning-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .warning-icon {
    font-size: 14px;
    flex-shrink: 0;
  }

  .warning-text {
    font-size: 12px;
    color: var(--theme-text);
    line-height: 1.4;
  }

  /* 底部样式 */
  .modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px;
    border-top: 1px solid var(--theme-border);
    background: var(--theme-surface);
  }

  .footer-info {
    font-size: 12px;
    color: var(--theme-text-secondary);
  }

  .time-estimate {
    font-size: 12px;
    color: var(--theme-text-secondary);
  }

  .footer-actions {
    display: flex;
    gap: 8px;
  }

  .cancel-btn, .confirm-btn {
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid transparent;
  }

  .cancel-btn {
    background: var(--theme-background);
    color: var(--theme-text);
    border-color: var(--theme-border);
  }

  .cancel-btn:hover {
    background: var(--theme-surface-hover, var(--theme-surface));
  }

  .confirm-btn {
    background: var(--theme-primary);
    color: white;
  }

  .confirm-btn:hover {
    opacity: 0.9;
  }

  /* 动画 */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
</style>
