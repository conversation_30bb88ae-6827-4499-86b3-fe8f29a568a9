<script lang="ts">
  /**
   * 统一Canvas预览组件 - 只提供基础预览功能
   * 扩展功能通过回调接口实现
   */

  import type { ResourceItem } from '../../types/imageType';

  // 基础变换信息接口
  export interface Transform {
    scale: number;
    translateX: number;
    translateY: number;
    imageWidth: number;
    imageHeight: number;
    canvasWidth: number;
    canvasHeight: number;
  }

  // 坐标点接口
  export interface Point {
    x: number;
    y: number;
  }

  // 扩展功能接口
  export interface PreviewExtensions {
    // 自定义渲染回调（在图片渲染之后调用）
    onRender?: (ctx: CanvasRenderingContext2D, transform: Transform) => void;

    // 鼠标事件回调
    onMouseDown?: (event: MouseEvent, canvasPos: Point, imagePos: Point) => boolean; // 返回true表示阻止默认行为
    onMouseMove?: (event: MouseEvent, canvasPos: Point, imagePos: Point) => void;
    onMouseUp?: (event: MouseEvent, canvasPos: Point, imagePos: Point) => void;

    // 键盘事件回调
    onKeyDown?: (event: KeyboardEvent) => boolean; // 返回true表示阻止默认行为

    // 滚轮事件回调
    onWheel?: (event: WheelEvent, canvasPos: Point, imagePos: Point) => boolean; // 返回true表示阻止默认行为
  }

  interface Props {
    resource: ResourceItem;
    width?: number;
    height?: number;
    showControls?: boolean;
    extensions?: PreviewExtensions;
  }

  let {
    resource,
    width = 400,
    height = 300,
    showControls = true,
    extensions
  }: Props = $props();

  // Canvas相关状态
  let canvasElement = $state<HTMLCanvasElement>();
  let ctx = $state<CanvasRenderingContext2D | null>(null);
  let canvasWidth = $state(width);
  let canvasHeight = $state(height);

  // 图片相关状态
  let originalImage = $state<HTMLImageElement>();
  let imageLoaded = $state(false);
  let imageError = $state(false);

  // 变换状态
  let scale = $state(1);
  let translateX = $state(0);
  let translateY = $state(0);

  // 交互状态
  let isDragging = $state(false);
  let dragStart = $state<Point>({ x: 0, y: 0 });
  let lastTranslate = $state<Point>({ x: 0, y: 0 });

  // 工具函数：获取MIME类型
  function getMimeType(path: string): string {
    const extension = path.toLowerCase().split('.').pop();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'svg':
        return 'image/svg+xml';
      case 'bmp':
        return 'image/bmp';
      default:
        return 'image/png';
    }
  }

  // 工具函数：从buffer创建data URL
  function createDataUrlFromBuffer(buffer: ArrayBuffer, mimeType: string): string {
    try {
      let binary = '';
      const bytes = new Uint8Array(buffer);
      const len = bytes.byteLength;
      for (let i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i]);
      }
      const base64 = btoa(binary);
      return `data:${mimeType};base64,${base64}`;
    } catch (error) {
      console.error('❌ CanvasPreview: 创建data URL失败:', error);
      return '';
    }
  }

  // 工具函数：获取Canvas坐标
  function getCanvasPosition(event: MouseEvent): Point {
    if (!canvasElement) return { x: 0, y: 0 };

    const rect = canvasElement.getBoundingClientRect();
    return {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };
  }

  // 工具函数：Canvas坐标转图片坐标
  function canvasToImagePosition(canvasPos: Point): Point {
    if (!originalImage) return { x: 0, y: 0 };

    return {
      x: (canvasPos.x - translateX) / scale,
      y: (canvasPos.y - translateY) / scale
    };
  }

  // 工具函数：获取当前变换信息
  function getTransform(): Transform {
    return {
      scale,
      translateX,
      translateY,
      imageWidth: originalImage?.width || 0,
      imageHeight: originalImage?.height || 0,
      canvasWidth,
      canvasHeight
    };
  }

  // 初始化Canvas
  function initCanvas() {
    if (!canvasElement) return;

    ctx = canvasElement.getContext('2d');
    if (!ctx) {
      console.error('❌ CanvasPreview: 无法获取Canvas上下文');
      return;
    }

    console.log('✅ CanvasPreview: Canvas初始化完成');
  }

  // 加载图片
  async function loadImage() {
    console.log('🔍 CanvasPreview: 检查资源状态', {
      hasResource: !!resource,
      resourceType: resource?.type,
      hasData: !!resource?.data,
      dataSize: resource?.data?.byteLength,
      resourceName: resource?.name
    });

    if (!resource || resource.type !== 'image' || !resource.data) {
      console.log('⚠️ CanvasPreview: 图片资源无效或data未加载', {
        hasResource: !!resource,
        type: resource?.type,
        hasData: !!resource?.data
      });
      imageLoaded = false;
      imageError = false;
      return;
    }

    try {
      imageLoaded = false;
      imageError = false;

      console.log('🔄 CanvasPreview: 开始加载图片', {
        name: resource.name,
        bufferSize: resource.data.byteLength
      });

      const img = new Image();

      img.onload = () => {
        originalImage = img;
        imageLoaded = true;
        imageError = false;

        console.log('✅ CanvasPreview: 图片加载完成', {
          width: img.width,
          height: img.height
        });

        // 更新ResourceItem的尺寸信息
        if (!resource.width || !resource.height) {
          resource.width = img.width;
          resource.height = img.height;
          console.log('🔧 CanvasPreview: 更新ResourceItem尺寸', {
            resourceName: resource.name,
            width: resource.width,
            height: resource.height
          });
        }

        // 自动适应视图
        fitToView();

        // 清理blob URL
        URL.revokeObjectURL(img.src);
      };

      img.onerror = () => {
        console.error('❌ CanvasPreview: 图片加载失败');
        imageLoaded = false;
        imageError = true;
        URL.revokeObjectURL(img.src);
      };

      // 创建blob URL
      const mimeType = resource.originalFile?.type || 'image/png';
      const blob = new Blob([resource.data], { type: mimeType });
      img.src = URL.createObjectURL(blob);

    } catch (error) {
      console.error('❌ CanvasPreview: 加载图片失败:', error);
      imageLoaded = false;
      imageError = true;
    }
  }

  // 渲染Canvas
  function render() {
    if (!ctx || !canvasElement) return;

    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // 绘制背景
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // 绘制图片
    if (originalImage && imageLoaded) {
      const imgWidth = originalImage.width * scale;
      const imgHeight = originalImage.height * scale;

      ctx.drawImage(
        originalImage,
        translateX,
        translateY,
        imgWidth,
        imgHeight
      );
    }

    // 调用扩展渲染回调
    if (extensions?.onRender && originalImage && imageLoaded) {
      extensions.onRender(ctx, getTransform());
    }

    // 绘制加载状态
    if (!imageLoaded && !imageError) {
      drawLoadingState();
    }

    // 绘制错误状态
    if (imageError) {
      drawErrorState();
    }
  }

  // 绘制加载状态
  function drawLoadingState() {
    if (!ctx) return;

    ctx.fillStyle = '#666';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('加载中...', canvasWidth / 2, canvasHeight / 2);
  }

  // 绘制错误状态
  function drawErrorState() {
    if (!ctx) return;

    ctx.fillStyle = '#ff6b6b';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('图片加载失败', canvasWidth / 2, canvasHeight / 2);
  }

  // 视图控制函数
  function zoomIn() {
    scale = Math.min(scale * 1.2, 10);
    render();
  }

  function zoomOut() {
    scale = Math.max(scale / 1.2, 0.1);
    render();
  }

  function resetView() {
    scale = 1;
    translateX = 0;
    translateY = 0;
    render();
  }

  function fitToView() {
    if (!originalImage || !imageLoaded) return;

    const imgWidth = originalImage.width;
    const imgHeight = originalImage.height;

    const scaleX = canvasWidth / imgWidth;
    const scaleY = canvasHeight / imgHeight;

    scale = Math.min(scaleX, scaleY, 1); // 不超过原始尺寸

    // 居中显示
    translateX = (canvasWidth - imgWidth * scale) / 2;
    translateY = (canvasHeight - imgHeight * scale) / 2;

    render();
  }

  // 鼠标事件处理
  function handleMouseDown(event: MouseEvent) {
    const canvasPos = getCanvasPosition(event);
    const imagePos = canvasToImagePosition(canvasPos);

    // 调用扩展回调
    if (extensions?.onMouseDown) {
      const handled = extensions.onMouseDown(event, canvasPos, imagePos);
      if (handled) return;
    }

    // 默认拖拽行为
    isDragging = true;
    dragStart = canvasPos;
    lastTranslate = { x: translateX, y: translateY };

    canvasElement!.style.cursor = 'grabbing';
  }

  function handleMouseMove(event: MouseEvent) {
    const canvasPos = getCanvasPosition(event);
    const imagePos = canvasToImagePosition(canvasPos);

    // 调用扩展回调
    if (extensions?.onMouseMove) {
      extensions.onMouseMove(event, canvasPos, imagePos);
    }

    // 默认拖拽行为
    if (isDragging) {
      translateX = lastTranslate.x + (canvasPos.x - dragStart.x);
      translateY = lastTranslate.y + (canvasPos.y - dragStart.y);
      render();
    }
  }

  function handleMouseUp(event: MouseEvent) {
    const canvasPos = getCanvasPosition(event);
    const imagePos = canvasToImagePosition(canvasPos);

    // 调用扩展回调
    if (extensions?.onMouseUp) {
      extensions.onMouseUp(event, canvasPos, imagePos);
    }

    // 默认拖拽行为
    isDragging = false;
    canvasElement!.style.cursor = 'grab';
  }

  // 滚轮事件处理
  function handleWheel(event: WheelEvent) {
    event.preventDefault();

    const canvasPos = getCanvasPosition(event);
    const imagePos = canvasToImagePosition(canvasPos);

    // 调用扩展回调
    if (extensions?.onWheel) {
      const handled = extensions.onWheel(event, canvasPos, imagePos);
      if (handled) return;
    }

    // 默认缩放行为
    const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
    const newScale = Math.max(0.1, Math.min(10, scale * zoomFactor));

    // 以鼠标位置为中心缩放
    const scaleChange = newScale / scale;
    translateX = canvasPos.x - (canvasPos.x - translateX) * scaleChange;
    translateY = canvasPos.y - (canvasPos.y - translateY) * scaleChange;
    scale = newScale;

    render();
  }

  // 键盘事件处理
  function handleKeyDown(event: KeyboardEvent) {
    // 调用扩展回调
    if (extensions?.onKeyDown) {
      const handled = extensions.onKeyDown(event);
      if (handled) return;
    }

    // 默认键盘快捷键
    switch (event.key) {
      case '+':
      case '=':
        event.preventDefault();
        zoomIn();
        break;
      case '-':
        event.preventDefault();
        zoomOut();
        break;
      case '0':
        event.preventDefault();
        resetView();
        break;
      case 'f':
      case 'F':
        event.preventDefault();
        fitToView();
        break;
    }
  }

  // 响应式效果
  $effect(() => {
    if (canvasElement) {
      initCanvas();
    }
  });

  $effect(() => {
    if (resource) {
      loadImage();
    }
  });

  $effect(() => {
    // 当图片加载完成或变换状态改变时重新渲染
    if (ctx) {
      render();
    }
  });

  // 监听尺寸变化
  $effect(() => {
    canvasWidth = width;
    canvasHeight = height;
    if (ctx) {
      render();
    }
  });
</script>

<div class="canvas-preview">
  <!-- 控制工具栏 -->
  {#if showControls}
    <div class="preview-controls">
      <button class="control-btn" onclick={zoomOut} title="缩小 (-)">🔍-</button>
      <span class="zoom-level">{Math.round(scale * 100)}%</span>
      <button class="control-btn" onclick={zoomIn} title="放大 (+)">🔍+</button>
      <button class="control-btn" onclick={resetView} title="重置 (0)">⟲</button>
      <button class="control-btn" onclick={fitToView} title="适应 (F)">📐</button>
    </div>
  {/if}

  <!-- Canvas画布 -->
  <canvas
    bind:this={canvasElement}
    width={canvasWidth}
    height={canvasHeight}
    onmousedown={handleMouseDown}
    onmousemove={handleMouseMove}
    onmouseup={handleMouseUp}
    onwheel={handleWheel}
    onkeydown={handleKeyDown}
    tabindex="0"
    style="cursor: {isDragging ? 'grabbing' : 'grab'};"
    aria-label="可交互的图片预览画布"
  ></canvas>
</div>

<style>
  .canvas-preview {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: var(--color-bg-secondary, #f8f9fa);
    border-radius: 8px;
    overflow: hidden;
  }

  .preview-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--color-bg-primary, #ffffff);
    border-bottom: 1px solid var(--color-border, #e1e5e9);
    font-size: 12px;
  }

  .control-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: 1px solid var(--color-border, #e1e5e9);
    border-radius: 4px;
    background: var(--color-bg-primary, #ffffff);
    color: var(--color-text-primary, #2c3e50);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
  }

  .control-btn:hover {
    background: var(--color-bg-hover, #f1f3f4);
    border-color: var(--color-border-hover, #d1d5db);
  }

  .control-btn:active {
    background: var(--color-bg-active, #e5e7eb);
  }

  .zoom-level {
    min-width: 40px;
    text-align: center;
    font-weight: 500;
    color: var(--color-text-secondary, #6b7280);
  }

  canvas {
    flex: 1;
    display: block;
    outline: none;
  }

  canvas:focus {
    box-shadow: inset 0 0 0 2px var(--color-primary, #3b82f6);
  }
</style>
