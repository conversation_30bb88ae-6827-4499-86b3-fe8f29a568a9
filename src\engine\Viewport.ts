/**
 * GameSprite Engine - 视口管理
 * 视口负责管理项目在canvas上的显示，包括缩放、居中和边界绘制
 */

import * as PIXI from 'pixi.js';
import { generateId } from './types.js';

export interface ViewportOptions {
  projectWidth: number;
  projectHeight: number;
  canvasWidth: number;
  canvasHeight: number;
  backgroundColor?: number;
  borderColor?: number;
  borderWidth?: number;
}

export class Viewport {
  readonly id: string;
  
  // PIXI容器
  private container: PIXI.Container;
  private contentContainer: PIXI.Container;
  private borderGraphics: PIXI.Graphics;
  
  // 视口属性
  private _projectWidth: number;
  private _projectHeight: number;
  private _canvasWidth: number;
  private _canvasHeight: number;
  private _scale: number = 1;
  private _offsetX: number = 0;
  private _offsetY: number = 0;
  
  // 样式
  private borderColor: number;
  private borderWidth: number;
  
  constructor(options: ViewportOptions) {
    this.id = generateId();
    
    this._projectWidth = options.projectWidth;
    this._projectHeight = options.projectHeight;
    this._canvasWidth = options.canvasWidth;
    this._canvasHeight = options.canvasHeight;
    this.borderColor = options.borderColor || 0x666666;
    this.borderWidth = options.borderWidth || 2;
    
    // 创建PIXI容器
    this.container = new PIXI.Container();
    this.contentContainer = new PIXI.Container();
    this.borderGraphics = new PIXI.Graphics();
    
    // 设置容器层级
    this.container.addChild(this.contentContainer);
    this.container.addChild(this.borderGraphics);
    
    // 计算初始布局
    this.updateLayout();
    
    console.log('📺 Viewport: 视口创建完成', {
      id: this.id,
      projectSize: `${this._projectWidth}×${this._projectHeight}`,
      canvasSize: `${this._canvasWidth}×${this._canvasHeight}`,
      scale: this._scale
    });
  }
  
  // === 属性访问 ===
  get projectWidth(): number { return this._projectWidth; }
  get projectHeight(): number { return this._projectHeight; }
  get canvasWidth(): number { return this._canvasWidth; }
  get canvasHeight(): number { return this._canvasHeight; }
  get scale(): number { return this._scale; }
  get offsetX(): number { return this._offsetX; }
  get offsetY(): number { return this._offsetY; }
  
  // === 布局管理 ===
  updateLayout(): void {
    this.calculateScale();
    this.calculateOffset();
    this.updateContentTransform();
    this.drawBorder();
    
    console.log('📐 Viewport: 布局更新', {
      scale: this._scale,
      offset: `${this._offsetX}, ${this._offsetY}`,
      projectSize: `${this._projectWidth}×${this._projectHeight}`,
      canvasSize: `${this._canvasWidth}×${this._canvasHeight}`
    });
  }
  
  private calculateScale(): void {
    // 计算适合canvas的缩放比例，保持3:2的默认显示比例
    const canvasAspect = this._canvasWidth / this._canvasHeight;
    const projectAspect = this._projectWidth / this._projectHeight;
    
    // 默认显示比例为3:2，即项目占canvas的2/3空间
    const displayRatio = 2 / 3;
    
    if (projectAspect > canvasAspect) {
      // 项目更宽，以宽度为准
      this._scale = (this._canvasWidth * displayRatio) / this._projectWidth;
    } else {
      // 项目更高，以高度为准
      this._scale = (this._canvasHeight * displayRatio) / this._projectHeight;
    }
  }
  
  private calculateOffset(): void {
    // 计算居中偏移
    const scaledProjectWidth = this._projectWidth * this._scale;
    const scaledProjectHeight = this._projectHeight * this._scale;
    
    this._offsetX = (this._canvasWidth - scaledProjectWidth) / 2;
    this._offsetY = (this._canvasHeight - scaledProjectHeight) / 2;
  }
  
  private updateContentTransform(): void {
    // 应用变换到内容容器
    this.contentContainer.scale.set(this._scale);
    this.contentContainer.position.set(this._offsetX, this._offsetY);
  }
  
  private drawBorder(): void {
    // 清除之前的边框
    this.borderGraphics.clear();
    
    // 绘制项目边界
    const scaledWidth = this._projectWidth * this._scale;
    const scaledHeight = this._projectHeight * this._scale;
    
    this.borderGraphics
      .rect(this._offsetX, this._offsetY, scaledWidth, scaledHeight)
      .stroke({
        color: this.borderColor,
        width: this.borderWidth
      });
  }
  
  // === 尺寸更新 ===
  resizeProject(width: number, height: number): void {
    this._projectWidth = width;
    this._projectHeight = height;
    this.updateLayout();
    
    console.log('📏 Viewport: 项目尺寸更新', {
      newSize: `${width}×${height}`
    });
  }
  
  resizeCanvas(width: number, height: number): void {
    this._canvasWidth = width;
    this._canvasHeight = height;
    this.updateLayout();
    
    console.log('🖼️ Viewport: Canvas尺寸更新', {
      newSize: `${width}×${height}`
    });
  }
  
  // === 内容管理 ===
  addContent(displayObject: PIXI.DisplayObject): void {
    this.contentContainer.addChild(displayObject);
  }
  
  removeContent(displayObject: PIXI.DisplayObject): void {
    this.contentContainer.removeChild(displayObject);
  }
  
  clearContent(): void {
    this.contentContainer.removeChildren();
  }
  
  // === PIXI对象访问 ===
  getContainer(): PIXI.Container {
    return this.container;
  }
  
  getContentContainer(): PIXI.Container {
    return this.contentContainer;
  }
  
  // === 坐标转换 ===
  screenToProject(screenX: number, screenY: number): { x: number, y: number } {
    return {
      x: (screenX - this._offsetX) / this._scale,
      y: (screenY - this._offsetY) / this._scale
    };
  }
  
  projectToScreen(projectX: number, projectY: number): { x: number, y: number } {
    return {
      x: projectX * this._scale + this._offsetX,
      y: projectY * this._scale + this._offsetY
    };
  }
  
  // === 清理 ===
  destroy(): void {
    this.clearContent();
    this.borderGraphics.destroy();
    this.contentContainer.destroy();
    this.container.destroy();
    
    console.log('🧹 Viewport: 视口已销毁', { id: this.id });
  }
}
