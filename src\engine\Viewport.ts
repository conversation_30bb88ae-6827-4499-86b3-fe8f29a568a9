/**
 * GameSprite Engine - 视口管理
 * 视口负责管理项目在canvas上的显示，包括缩放、居中和边界绘制
 */

import * as PIXI from 'pixi.js';
import { generateId } from './types.js';

export interface ViewportOptions {
  projectWidth: number;
  projectHeight: number;
  canvasWidth: number;
  canvasHeight: number;
  backgroundColor?: number;
  borderColor?: number;
  borderWidth?: number;
}

export class Viewport {
  readonly id: string;

  // PIXI容器
  private container: PIXI.Container;
  private contentContainer: PIXI.Container;
  private borderGraphics: PIXI.Graphics;

  // DOM事件监听
  private wheelHandler: ((event: WheelEvent) => void) | null = null;

  // 视口属性
  private _projectWidth: number;
  private _projectHeight: number;
  private _canvasWidth: number;
  private _canvasHeight: number;
  private _scale: number = 1;
  private _offsetX: number = 0;
  private _offsetY: number = 0;

  // 用户交互控制
  private _userZoom: number = 1;      // 用户缩放倍数
  private _minZoom: number = 0.1;     // 最小缩放
  private _maxZoom: number = 5.0;     // 最大缩放
  private _zoomStep: number = 0.1;    // 缩放步长
  private _rotation: number = 0;      // 旋转角度（弧度）
  private _panX: number = 0;          // 平移X
  private _panY: number = 0;          // 平移Y

  // 样式
  private borderColor: number;
  private borderWidth: number;

  constructor(options: ViewportOptions) {
    this.id = generateId();

    this._projectWidth = options.projectWidth;
    this._projectHeight = options.projectHeight;
    this._canvasWidth = options.canvasWidth;
    this._canvasHeight = options.canvasHeight;
    this.borderColor = options.borderColor || 0x666666;
    this.borderWidth = options.borderWidth || 2;

    // 创建PIXI容器
    this.container = new PIXI.Container();
    this.contentContainer = new PIXI.Container();
    this.borderGraphics = new PIXI.Graphics();

    // 设置容器层级
    this.container.addChild(this.contentContainer);
    this.container.addChild(this.borderGraphics);

    // 计算初始布局
    this.updateLayout();

    // 设置鼠标滚轮监听
    this.setupWheelListener();

    console.log('📺 Viewport: 视口创建完成', {
      id: this.id,
      projectSize: `${this._projectWidth}×${this._projectHeight}`,
      canvasSize: `${this._canvasWidth}×${this._canvasHeight}`,
      scale: this._scale,
      userZoom: this._userZoom
    });
  }

  // === 属性访问 ===
  get projectWidth(): number { return this._projectWidth; }
  get projectHeight(): number { return this._projectHeight; }
  get canvasWidth(): number { return this._canvasWidth; }
  get canvasHeight(): number { return this._canvasHeight; }
  get scale(): number { return this._scale; }
  get offsetX(): number { return this._offsetX; }
  get offsetY(): number { return this._offsetY; }
  get userZoom(): number { return this._userZoom; }
  get finalZoom(): number { return this._scale * this._userZoom; }

  // === 布局管理 ===
  updateLayout(): void {
    this.calculateScale();
    this.calculateOffset();
    this.updateContentTransform();
    this.drawBorder();

    console.log('📐 Viewport: 布局更新', {
      scale: this._scale,
      offset: `${this._offsetX}, ${this._offsetY}`,
      projectSize: `${this._projectWidth}×${this._projectHeight}`,
      canvasSize: `${this._canvasWidth}×${this._canvasHeight}`
    });
  }

  private calculateScale(): void {
    // 计算适合canvas的缩放比例，保持3:2的默认显示比例
    const canvasAspect = this._canvasWidth / this._canvasHeight;
    const projectAspect = this._projectWidth / this._projectHeight;

    // 默认显示比例为3:2，即项目占canvas的2/3空间
    const displayRatio = 2 / 3;

    if (projectAspect > canvasAspect) {
      // 项目更宽，以宽度为准
      this._scale = (this._canvasWidth * displayRatio) / this._projectWidth;
    } else {
      // 项目更高，以高度为准
      this._scale = (this._canvasHeight * displayRatio) / this._projectHeight;
    }
  }

  private calculateOffset(): void {
    // 计算居中偏移
    const scaledProjectWidth = this._projectWidth * this._scale;
    const scaledProjectHeight = this._projectHeight * this._scale;

    this._offsetX = (this._canvasWidth - scaledProjectWidth) / 2;
    this._offsetY = (this._canvasHeight - scaledProjectHeight) / 2;
  }

  private updateContentTransform(): void {
    // 检查容器是否存在
    if (!this.contentContainer) {
      console.warn('⚠️ Viewport: contentContainer为null，跳过变换更新');
      return;
    }

    if (!this.contentContainer.scale) {
      console.warn('⚠️ Viewport: contentContainer.scale为null，跳过变换更新');
      return;
    }

    if (!this.contentContainer.position) {
      console.warn('⚠️ Viewport: contentContainer.position为null，跳过变换更新');
      return;
    }

    try {
      // 应用变换到内容容器，结合基础缩放和用户缩放
      const finalScale = this._scale * this._userZoom;
      this.contentContainer.scale.set(finalScale);

      // 计算视口中心点
      const viewportCenterX = this._canvasWidth / 2;
      const viewportCenterY = this._canvasHeight / 2;

      // 计算项目在默认缩放下的中心点
      const projectCenterX = this._projectWidth / 2;
      const projectCenterY = this._projectHeight / 2;

      // 计算以中心点缩放的偏移
      // 基础偏移 + 平移 + 缩放中心点调整
      const scaleOffsetX = (viewportCenterX - projectCenterX * finalScale);
      const scaleOffsetY = (viewportCenterY - projectCenterY * finalScale);

      const finalOffsetX = scaleOffsetX + this._panX;
      const finalOffsetY = scaleOffsetY + this._panY;

      this.contentContainer.position.set(finalOffsetX, finalOffsetY);
    } catch (error) {
      console.error('❌ Viewport: updateContentTransform错误', error);
    }
  }

  private drawBorder(): void {
    // 检查边框图形是否存在
    if (!this.borderGraphics) {
      console.warn('⚠️ Viewport: borderGraphics不可用，跳过边框绘制');
      return;
    }

    // 清除之前的边框
    this.borderGraphics.clear();

    // 绘制项目边界，考虑用户缩放
    const finalScale = this._scale * this._userZoom;
    const scaledWidth = this._projectWidth * finalScale;
    const scaledHeight = this._projectHeight * finalScale;

    // 计算视口中心点
    const viewportCenterX = this._canvasWidth / 2;
    const viewportCenterY = this._canvasHeight / 2;

    // 计算项目在缩放后的中心点位置
    const projectCenterX = this._projectWidth / 2;
    const projectCenterY = this._projectHeight / 2;

    // 计算边框左上角位置（以中心点为基准）
    const borderX = viewportCenterX - (projectCenterX * finalScale) + this._panX;
    const borderY = viewportCenterY - (projectCenterY * finalScale) + this._panY;

    this.borderGraphics
      .rect(borderX, borderY, scaledWidth, scaledHeight)
      .stroke({
        color: this.borderColor,
        width: this.borderWidth
      });
  }

  // === 尺寸更新 ===
  resizeProject(width: number, height: number): void {
    this._projectWidth = width;
    this._projectHeight = height;
    this.updateLayout();

    console.log('📏 Viewport: 项目尺寸更新', {
      newSize: `${width}×${height}`
    });
  }

  resizeCanvas(width: number, height: number): void {
    this._canvasWidth = width;
    this._canvasHeight = height;
    this.updateLayout();

    console.log('🖼️ Viewport: Canvas尺寸更新', {
      newSize: `${width}×${height}`
    });
  }

  // === 内容管理 ===
  addContent(displayObject: PIXI.Container): void {
    this.contentContainer.addChild(displayObject);
  }

  removeContent(displayObject: PIXI.Container): void {
    this.contentContainer.removeChild(displayObject);
  }

  clearContent(): void {
    this.contentContainer.removeChildren();
  }

  // === PIXI对象访问 ===
  getContainer(): PIXI.Container {
    return this.container;
  }

  getContentContainer(): PIXI.Container {
    return this.contentContainer;
  }

  // === 坐标转换 ===
  screenToProject(screenX: number, screenY: number): { x: number, y: number } {
    return {
      x: (screenX - this._offsetX) / this._scale,
      y: (screenY - this._offsetY) / this._scale
    };
  }

  projectToScreen(projectX: number, projectY: number): { x: number, y: number } {
    return {
      x: projectX * this._scale + this._offsetX,
      y: projectY * this._scale + this._offsetY
    };
  }

  // === 缩放控制 ===
  private setupWheelListener(): void {
    // 创建滚轮事件处理函数
    this.wheelHandler = (event: WheelEvent) => {
      // 检查是否按下Ctrl键
      if (!event.ctrlKey) return;

      // 阻止默认滚动行为
      event.preventDefault();
      event.stopPropagation();

      // 计算缩放方向
      const delta = event.deltaY;
      const zoomDirection = delta > 0 ? -1 : 1;

      // 执行缩放
      this.zoom(zoomDirection * this._zoomStep);
    };

    // 监听document的滚轮事件
    document.addEventListener('wheel', this.wheelHandler, { passive: false });

    console.log('🖱️ Viewport: DOM滚轮监听已设置 (Ctrl+滚轮)');
  }

  /**
   * 缩放视口
   * @param delta 缩放增量，正数放大，负数缩小
   */
  zoom(delta: number): void {
    const newZoom = this._userZoom + delta;
    this.setZoom(newZoom);
  }

  /**
   * 设置缩放级别
   * @param zoom 缩放级别
   */
  setZoom(zoom: number): void {
    // 限制缩放范围
    const clampedZoom = Math.max(this._minZoom, Math.min(this._maxZoom, zoom));

    if (clampedZoom === this._userZoom) return;

    this._userZoom = clampedZoom;
    this.updateContentTransform();
    this.drawBorder(); // 重新绘制边界

    console.log('🔍 Viewport: 缩放更新', {
      userZoom: this._userZoom,
      finalScale: this._scale * this._userZoom
    });
  }

  /**
   * 放大
   */
  zoomIn(): void {
    this.zoom(this._zoomStep);
  }

  /**
   * 缩小
   */
  zoomOut(): void {
    this.zoom(-this._zoomStep);
  }

  /**
   * 重置缩放
   */
  resetZoom(): void {
    this.setZoom(1);
    this._panX = 0;
    this._panY = 0;
    this.updateContentTransform();
    this.drawBorder();

    console.log('🔄 Viewport: 缩放已重置');
  }

  /**
   * 获取当前缩放级别
   */
  getUserZoom(): number {
    return this._userZoom;
  }

  /**
   * 获取最终缩放级别（基础缩放 × 用户缩放）
   */
  getFinalZoom(): number {
    return this._scale * this._userZoom;
  }

  // === 清理 ===
  destroy(): void {
    // 移除DOM事件监听
    if (this.wheelHandler) {
      document.removeEventListener('wheel', this.wheelHandler);
      this.wheelHandler = null;
    }

    this.clearContent();
    this.borderGraphics.destroy();
    this.contentContainer.destroy();
    this.container.destroy();

    console.log('🧹 Viewport: 视口已销毁', { id: this.id });
  }
}
