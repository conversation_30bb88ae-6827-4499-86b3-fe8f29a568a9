<script lang="ts">
  /**
   * Toast 提示组件
   * 现代化的弹窗提示，替换原生 alert
   */
  import { onMount, onDestroy } from 'svelte';
  import { _ } from '../lib/i18n';

  // 提示类型
  export type ToastType = 'success' | 'error' | 'warning' | 'info';

  // 组件属性
  interface ToastProps {
    type?: ToastType;
    title?: string;
    message: string;
    duration?: number; // 自动关闭时间，0表示不自动关闭
    showCloseButton?: boolean;
    onClose?: (() => void) | null;
  }

  let {
    type = 'info',
    title = '',
    message,
    duration = 5000,
    showCloseButton = true,
    onClose = null
  }: ToastProps = $props();

  // 内部状态
  let visible = $state(false);
  let timeoutId: number | null = null;

  // 图标映射
  const iconMap = {
    success: '✅',
    error: '❌',
    warning: '⚠️',
    info: 'ℹ️'
  };

  // 颜色主题映射
  const themeMap = {
    success: {
      bg: 'rgba(16, 185, 129, 0.1)',
      border: 'rgba(16, 185, 129, 0.3)',
      color: '#059669',
      iconBg: 'rgba(16, 185, 129, 0.2)'
    },
    error: {
      bg: 'rgba(239, 68, 68, 0.1)',
      border: 'rgba(239, 68, 68, 0.3)',
      color: '#dc2626',
      iconBg: 'rgba(239, 68, 68, 0.2)'
    },
    warning: {
      bg: 'rgba(245, 158, 11, 0.1)',
      border: 'rgba(245, 158, 11, 0.3)',
      color: '#d97706',
      iconBg: 'rgba(245, 158, 11, 0.2)'
    },
    info: {
      bg: 'rgba(59, 130, 246, 0.1)',
      border: 'rgba(59, 130, 246, 0.3)',
      color: '#2563eb',
      iconBg: 'rgba(59, 130, 246, 0.2)'
    }
  };

  onMount(() => {
    // 显示动画
    setTimeout(() => {
      visible = true;
    }, 10);

    // 设置自动关闭
    if (duration > 0) {
      timeoutId = window.setTimeout(() => {
        handleClose();
      }, duration);
    }
  });

  onDestroy(() => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
  });

  function handleClose() {
    visible = false;
    setTimeout(() => {
      onClose?.();
    }, 300); // 等待退出动画完成
  }

  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      handleClose();
    }
  }
</script>

<svelte:window onkeydown={handleKeydown} />

<div
  class="toast-overlay"
  class:visible
  onclick={handleClose}
  onkeydown={(e) => e.key === 'Enter' && handleClose()}
  role="dialog"
  aria-modal="true"
  aria-labelledby="toast-title"
  aria-describedby="toast-message"
  tabindex="0"
>
  <div
    class="toast-container {type}"
    onclick={(e) => e.stopPropagation()}
    onkeydown={(e) => e.stopPropagation()}
    role="alertdialog"
    tabindex="-1"
    style="
      --toast-bg: {themeMap[type].bg};
      --toast-border: {themeMap[type].border};
      --toast-color: {themeMap[type].color};
      --toast-icon-bg: {themeMap[type].iconBg};
    "
  >
    <!-- 图标区域 -->
    <div class="toast-icon">
      {iconMap[type]}
    </div>

    <!-- 内容区域 -->
    <div class="toast-content">
      {#if title}
        <div class="toast-title" id="toast-title">
          {title}
        </div>
      {/if}
      <div class="toast-message" id="toast-message">
        {@html message}
      </div>
    </div>

    <!-- 关闭按钮 -->
    {#if showCloseButton}
      <button
        class="toast-close"
        onclick={handleClose}
        title={$_('actions.close')}
        aria-label={$_('actions.close')}
      >
        ✕
      </button>
    {/if}

    <!-- 进度条（仅在有自动关闭时显示） -->
    {#if duration > 0}
      <div class="toast-progress">
        <div
          class="toast-progress-bar"
          style="animation-duration: {duration}ms"
        ></div>
      </div>
    {/if}
  </div>
</div>

<style>
  .toast-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(2px);
  }

  .toast-overlay.visible {
    opacity: 1;
  }

  .toast-container {
    background: var(--theme-surface, white);
    border: 1px solid var(--toast-border);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    min-width: 320px;
    margin: 20px;
    position: relative;
    transform: translateY(-20px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    overflow: hidden;
  }

  .toast-overlay.visible .toast-container {
    transform: translateY(0) scale(1);
  }

  .toast-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--toast-color);
  }

  .toast-container {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: var(--toast-bg);
  }

  .toast-icon {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--toast-icon-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-top: 2px;
  }

  .toast-content {
    flex: 1;
    min-width: 0;
  }

  .toast-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--toast-color);
    margin-bottom: 4px;
    line-height: 1.4;
  }

  .toast-message {
    font-size: 14px;
    color: var(--theme-text, #333);
    line-height: 1.5;
    word-wrap: break-word;
  }

  .toast-close {
    flex-shrink: 0;
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: var(--theme-text-secondary, #666);
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.2s ease;
    margin-top: 2px;
  }

  .toast-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: var(--theme-text, #333);
  }

  .toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .toast-progress-bar {
    height: 100%;
    background: var(--toast-color);
    width: 100%;
    transform: translateX(-100%);
    animation: progress linear forwards;
  }

  @keyframes progress {
    from {
      transform: translateX(-100%);
    }
    to {
      transform: translateX(0);
    }
  }

  /* 响应式设计 */
  @media (max-width: 480px) {
    .toast-container {
      margin: 10px;
      min-width: auto;
      max-width: calc(100vw - 20px);
    }
  }

  /* 深色主题适配 */
  @media (prefers-color-scheme: dark) {
    .toast-container {
      background: var(--theme-surface, #2a2a2a);
    }

    .toast-message {
      color: var(--theme-text, #e0e0e0);
    }
  }
</style>
