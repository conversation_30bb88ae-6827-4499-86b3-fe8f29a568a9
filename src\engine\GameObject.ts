/**
 * GameSprite Engine - 简化版 GameObject
 */

import * as PIXI from 'pixi.js';
import type { Point, Rectangle } from './types.js';
import { generateId } from './types.js';

export class GameObject {
  // 基础属性
  readonly id: string;
  name: string;
  
  // 坐标
  private _x: number = 0;
  private _y: number = 0;
  
  // 大小
  private _width: number = 0;
  private _height: number = 0;
  
  // 显示属性
  private _visible: boolean = true;
  private _alpha: number = 1;
  
  // PIXI 对象
  protected pixiObject: PIXI.Container;
  
  // 层级关系
  parent: GameObject | null = null;
  children: GameObject[] = [];
  
  constructor(name: string = 'GameObject') {
    this.id = generateId();
    this.name = name;
    this.pixiObject = new PIXI.Container();
    
    // 同步属性
    this.syncToPixi();
  }
  
  // === 坐标属性 ===
  get x(): number { return this._x; }
  set x(value: number) {
    this._x = value;
    this.pixiObject.x = value;
  }
  
  get y(): number { return this._y; }
  set y(value: number) {
    this._y = value;
    this.pixiObject.y = value;
  }
  
  // === 大小属性 ===
  get width(): number { return this._width; }
  set width(value: number) {
    this._width = Math.max(0, value);
  }
  
  get height(): number { return this._height; }
  set height(value: number) {
    this._height = Math.max(0, value);
  }
  
  // === 显示属性 ===
  get visible(): boolean { return this._visible; }
  set visible(value: boolean) {
    this._visible = value;
    this.pixiObject.visible = value;
  }
  
  get alpha(): number { return this._alpha; }
  set alpha(value: number) {
    this._alpha = Math.max(0, Math.min(1, value));
    this.pixiObject.alpha = this._alpha;
  }
  
  // === 便捷方法 ===
  setPosition(x: number, y: number): void {
    this.x = x;
    this.y = y;
  }
  
  setSize(width: number, height: number): void {
    this.width = width;
    this.height = height;
  }
  
  // === 层级管理 ===
  addChild(child: GameObject): void {
    if (child.parent) {
      child.parent.removeChild(child);
    }
    
    child.parent = this;
    this.children.push(child);
    this.pixiObject.addChild(child.pixiObject);
  }
  
  removeChild(child: GameObject): boolean {
    const index = this.children.indexOf(child);
    if (index !== -1) {
      this.children.splice(index, 1);
      child.parent = null;
      this.pixiObject.removeChild(child.pixiObject);
      return true;
    }
    return false;
  }
  
  removeChildren(): void {
    while (this.children.length > 0) {
      this.removeChild(this.children[0]);
    }
  }
  
  // === 更新 ===
  update(deltaTime: number): void {
    // 更新子对象
    this.children.forEach(child => child.update(deltaTime));
  }
  
  // === 边界检测 ===
  getBounds(): Rectangle {
    return {
      x: this.x,
      y: this.y,
      width: this.width,
      height: this.height
    };
  }
  
  hitTest(point: Point): boolean {
    const bounds = this.getBounds();
    return point.x >= bounds.x && 
           point.x <= bounds.x + bounds.width && 
           point.y >= bounds.y && 
           point.y <= bounds.y + bounds.height;
  }
  
  // === 工具方法 ===
  getPixiObject(): PIXI.Container {
    return this.pixiObject;
  }
  
  show(): void {
    this.visible = true;
  }
  
  hide(): void {
    this.visible = false;
  }
  
  // === 私有方法 ===
  private syncToPixi(): void {
    this.pixiObject.x = this._x;
    this.pixiObject.y = this._y;
    this.pixiObject.visible = this._visible;
    this.pixiObject.alpha = this._alpha;
  }
  
  // === 清理 ===
  destroy(): void {
    this.removeChildren();
    
    if (this.parent) {
      this.parent.removeChild(this);
    }
    
    this.pixiObject.destroy();
  }
}
