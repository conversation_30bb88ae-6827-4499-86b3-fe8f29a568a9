/**
 * 内存管理工具
 * 提供内存监控、清理和优化功能
 */

import { performanceMonitor } from './performanceMonitor';

interface MemoryThreshold {
  warning: number;  // 警告阈值 (MB)
  critical: number; // 临界阈值 (MB)
  cleanup: number;  // 清理阈值 (MB)
}

interface MemoryStats {
  used: number;
  total: number;
  limit: number;
  percentage: number;
  trend: 'increasing' | 'decreasing' | 'stable';
}

/**
 * 内存管理器
 */
export class MemoryManager {
  private static instance: MemoryManager;
  private thresholds: MemoryThreshold = {
    warning: 100,   // 100MB
    critical: 200,  // 200MB
    cleanup: 250    // 250MB
  };
  private cleanupCallbacks: Array<() => void | Promise<void>> = [];
  private monitoringInterval: number | null = null;
  private lastMemoryCheck = 0;
  private memoryHistory: number[] = [];
  private readonly HISTORY_SIZE = 10;

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  /**
   * 🎯 开始内存监控
   */
  startMonitoring(intervalMs = 5000): void {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    this.monitoringInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, intervalMs) as any;

    console.log('🔍 MemoryManager: 开始内存监控', {
      interval: `${intervalMs}ms`,
      thresholds: this.thresholds
    });
  }

  /**
   * 停止内存监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('🛑 MemoryManager: 停止内存监控');
    }
  }

  /**
   * 🎯 检查内存使用情况
   */
  checkMemoryUsage(): MemoryStats | null {
    if (!('memory' in performance)) {
      return null;
    }

    const memory = (performance as any).memory;
    const usedMB = memory.usedJSHeapSize / 1024 / 1024;
    const totalMB = memory.totalJSHeapSize / 1024 / 1024;
    const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;
    const percentage = (usedMB / limitMB) * 100;

    // 更新历史记录
    this.memoryHistory.push(usedMB);
    if (this.memoryHistory.length > this.HISTORY_SIZE) {
      this.memoryHistory.shift();
    }

    // 计算趋势
    const trend = this.calculateMemoryTrend();

    const stats: MemoryStats = {
      used: usedMB,
      total: totalMB,
      limit: limitMB,
      percentage,
      trend
    };

    // 检查阈值
    this.checkThresholds(stats);

    return stats;
  }

  /**
   * 计算内存使用趋势
   */
  private calculateMemoryTrend(): 'increasing' | 'decreasing' | 'stable' {
    if (this.memoryHistory.length < 3) {
      return 'stable';
    }

    const recent = this.memoryHistory.slice(-3);
    const first = recent[0];
    const last = recent[recent.length - 1];
    const diff = last - first;

    if (Math.abs(diff) < 5) { // 5MB以内认为稳定
      return 'stable';
    }

    return diff > 0 ? 'increasing' : 'decreasing';
  }

  /**
   * 🎯 检查内存阈值并触发相应操作
   */
  private checkThresholds(stats: MemoryStats): void {
    const { used, percentage, trend } = stats;

    if (used > this.thresholds.cleanup) {
      console.warn('🚨 MemoryManager: 内存使用超过清理阈值', {
        used: `${used.toFixed(2)}MB`,
        threshold: `${this.thresholds.cleanup}MB`,
        percentage: `${percentage.toFixed(1)}%`
      });
      this.triggerCleanup('critical');
    } else if (used > this.thresholds.critical) {
      console.warn('⚠️ MemoryManager: 内存使用达到临界阈值', {
        used: `${used.toFixed(2)}MB`,
        threshold: `${this.thresholds.critical}MB`,
        percentage: `${percentage.toFixed(1)}%`,
        trend
      });
      this.triggerCleanup('aggressive');
    } else if (used > this.thresholds.warning) {
      console.warn('💡 MemoryManager: 内存使用达到警告阈值', {
        used: `${used.toFixed(2)}MB`,
        threshold: `${this.thresholds.warning}MB`,
        percentage: `${percentage.toFixed(1)}%`,
        trend
      });
      if (trend === 'increasing') {
        this.triggerCleanup('gentle');
      }
    }
  }

  /**
   * 🎯 触发内存清理
   */
  async triggerCleanup(level: 'gentle' | 'aggressive' | 'critical'): Promise<void> {
    console.log(`🧹 MemoryManager: 开始${level}级别内存清理`);

    performanceMonitor.startMeasure('memory-cleanup', { level });

    const memoryBefore = this.getCurrentMemoryUsage();

    try {
      // 执行注册的清理回调
      for (const callback of this.cleanupCallbacks) {
        try {
          await callback();
        } catch (error) {
          console.error('❌ MemoryManager: 清理回调执行失败', error);
        }
      }

      // 根据清理级别执行不同的清理策略
      switch (level) {
        case 'gentle':
          await this.gentleCleanup();
          break;
        case 'aggressive':
          await this.aggressiveCleanup();
          break;
        case 'critical':
          await this.criticalCleanup();
          break;
      }

      // 强制垃圾回收（如果支持）
      if ('gc' in window && typeof (window as any).gc === 'function') {
        (window as any).gc();
      }

    } finally {
      const memoryAfter = this.getCurrentMemoryUsage();
      const memoryFreed = memoryBefore - memoryAfter;

      console.log(`✅ MemoryManager: ${level}级别清理完成`, {
        memoryBefore: `${memoryBefore.toFixed(2)}MB`,
        memoryAfter: `${memoryAfter.toFixed(2)}MB`,
        memoryFreed: `${memoryFreed.toFixed(2)}MB`
      });

      performanceMonitor.endMeasure('memory-cleanup');
    }
  }

  /**
   * 温和清理
   */
  private async gentleCleanup(): Promise<void> {
    // 清理过期的缓存
    this.notifyCleanupListeners('cache-expired');
  }

  /**
   * 激进清理
   */
  private async aggressiveCleanup(): Promise<void> {
    // 清理所有非关键缓存
    this.notifyCleanupListeners('cache-non-critical');
    
    // 延迟一帧让浏览器有机会回收内存
    await new Promise(resolve => requestAnimationFrame(resolve));
  }

  /**
   * 临界清理
   */
  private async criticalCleanup(): Promise<void> {
    // 清理所有可清理的缓存
    this.notifyCleanupListeners('cache-all');
    
    // 多次延迟让浏览器有更多机会回收内存
    for (let i = 0; i < 3; i++) {
      await new Promise(resolve => requestAnimationFrame(resolve));
    }
  }

  /**
   * 通知清理监听器
   */
  private notifyCleanupListeners(type: string): void {
    window.dispatchEvent(new CustomEvent('memory-cleanup', {
      detail: { type }
    }));
  }

  /**
   * 注册清理回调
   */
  registerCleanupCallback(callback: () => void | Promise<void>): void {
    this.cleanupCallbacks.push(callback);
  }

  /**
   * 移除清理回调
   */
  unregisterCleanupCallback(callback: () => void | Promise<void>): void {
    const index = this.cleanupCallbacks.indexOf(callback);
    if (index > -1) {
      this.cleanupCallbacks.splice(index, 1);
    }
  }

  /**
   * 设置内存阈值
   */
  setThresholds(thresholds: Partial<MemoryThreshold>): void {
    this.thresholds = { ...this.thresholds, ...thresholds };
    console.log('⚙️ MemoryManager: 更新内存阈值', this.thresholds);
  }

  /**
   * 获取当前内存使用量
   */
  getCurrentMemoryUsage(): number {
    if (!('memory' in performance)) {
      return 0;
    }
    return (performance as any).memory.usedJSHeapSize / 1024 / 1024;
  }

  /**
   * 获取内存统计信息
   */
  getMemoryStats(): MemoryStats | null {
    return this.checkMemoryUsage();
  }

  /**
   * 格式化内存大小
   */
  static formatMemorySize(bytes: number): string {
    const mb = bytes / 1024 / 1024;
    return `${mb.toFixed(2)}MB`;
  }
}

// 导出单例实例
export const memoryManager = MemoryManager.getInstance();
