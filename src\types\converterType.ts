/**
 * 数据转换相关的类型定义
 */

import type { ResourceItem } from './imageType';
import type { ProjectFile, ProjectData, SerializableResourceItem, LoadResult, SaveResult } from './projectType';

// 数据转换器接口
export interface ProjectDataConverter {
    // 运行时 → 序列化
    serialize(project: ProjectData): Promise<ProjectFile>;
    
    // 序列化 → 运行时
    deserialize(projectFile: ProjectFile): Promise<ProjectData>;
    
    // 增量保存（只保存变更的部分）
    serializeIncremental(
        currentProject: ProjectFile,
        changes: ResourceItem[]
    ): Promise<Partial<ProjectFile>>;
    
    // 工具函数
    arrayBufferToBase64(buffer: ArrayBuffer): string;
    base64ToArrayBuffer(base64: string): ArrayBuffer;
    generateChecksum(data: ArrayBuffer): string;
    validateChecksum(data: ArrayBuffer, checksum: string): boolean;
}

// 资源转换器接口
export interface ResourceConverter {
    // 图片资源转换
    imageToSerializable(image: ResourceItem): Promise<SerializableResourceItem>;
    serializableToImage(serializable: SerializableResourceItem): Promise<ResourceItem>;
    
    // 文件夹资源转换
    folderToSerializable(folder: ResourceItem): Promise<SerializableResourceItem>;
    serializableToFolder(serializable: SerializableResourceItem): Promise<ResourceItem>;
    
    // 图集资源转换
    atlasToSerializable(atlas: ResourceItem): Promise<SerializableResourceItem>;
    serializableToAtlas(serializable: SerializableResourceItem): Promise<ResourceItem>;
}

// 文件处理器接口
export interface FileHandler {
    // 文件读取
    readFile(file: File): Promise<ArrayBuffer>;
    readFileAsText(file: File): Promise<string>;
    readFileAsDataURL(file: File): Promise<string>;
    
    // 文件保存
    saveAsFile(data: string | ArrayBuffer, filename: string, mimeType?: string): Promise<void>;
    saveAsJSON(data: any, filename: string): Promise<void>;
    
    // 文件验证
    validateFile(file: File): Promise<boolean>;
    getFileInfo(file: File): FileInfo;
}

// 文件信息
export interface FileInfo {
    name: string;
    size: number;
    type: string;
    lastModified: number;
    extension: string;
    isImage: boolean;
    isProject: boolean;
}

// 压缩相关接口
export interface CompressionHandler {
    // 压缩项目文件
    compress(projectFile: ProjectFile): Promise<CompressedProjectFile>;
    
    // 解压项目文件
    decompress(compressed: CompressedProjectFile): Promise<ProjectFile>;
    
    // 获取压缩信息
    getCompressionInfo(data: any): CompressionInfo;
}

// 压缩后的项目文件
export interface CompressedProjectFile {
    metadata: ProjectFile['metadata'];
    compressedData: string;  // 压缩后的数据
    compressionMethod: 'gzip' | 'lz4' | 'brotli';
    originalSize: number;
    compressedSize: number;
    checksum: string;
}

// 压缩信息
export interface CompressionInfo {
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    method: string;
    time: number; // 压缩耗时（毫秒）
}

// 导入处理器接口
export interface ImportHandler {
    // 处理文件夹上传
    handleFolderUpload(files: FileList): Promise<LoadResult<ResourceItem[]>>;
    
    // 处理图片上传
    handleImageUpload(files: FileList): Promise<LoadResult<ResourceItem[]>>;
    
    // 处理项目文件上传
    handleProjectUpload(file: File): Promise<LoadResult<ProjectData>>;
    
    // 根据路径重建文件夹结构
    buildFolderStructure(files: File[]): Promise<ResourceItem>;
    
    // 批量处理文件
    processBatch(files: File[], options?: BatchProcessOptions): Promise<LoadResult<ResourceItem[]>>;
}

// 批量处理选项
export interface BatchProcessOptions {
    maxConcurrent?: number;      // 最大并发数
    chunkSize?: number;          // 分块大小
    onProgress?: (progress: BatchProgress) => void;
    onError?: (error: Error, file: File) => void;
}

// 批量处理进度
export interface BatchProgress {
    total: number;
    completed: number;
    failed: number;
    currentFile?: string;
    percentage: number;
}

// 导出处理器接口
export interface ExportHandler {
    // 导出项目文件
    exportProject(project: ProjectData, options?: ExportOptions): Promise<SaveResult>;
    
    // 导出图集
    exportAtlas(atlas: ResourceItem, options?: AtlasExportOptions): Promise<SaveResult>;
    
    // 导出图片
    exportImage(image: ResourceItem, options?: ImageExportOptions): Promise<SaveResult>;
    
    // 批量导出
    exportBatch(items: ResourceItem[], options?: BatchExportOptions): Promise<SaveResult[]>;
}

// 导出选项
export interface ExportOptions {
    format?: 'json' | 'compressed';
    includeMetadata?: boolean;
    includeWorkspace?: boolean;
    compression?: boolean;
}

// 图集导出选项
export interface AtlasExportOptions {
    format: 'png' | 'jpg' | 'webp';
    quality?: number;
    includeDataFile?: boolean;
    dataFormat?: 'json' | 'xml' | 'txt';
    scale?: number;
}

// 图片导出选项
export interface ImageExportOptions {
    format: 'png' | 'jpg' | 'webp';
    quality?: number;
    scale?: number;
    crop?: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
}

// 批量导出选项
export interface BatchExportOptions extends ExportOptions {
    outputDirectory?: string;
    namePattern?: string;  // 文件名模式，如 "{name}_{index}"
    onProgress?: (progress: BatchProgress) => void;
}

// 数据验证器接口
export interface DataValidator {
    // 验证项目文件
    validateProject(project: ProjectFile): Promise<ValidationResult>;
    
    // 验证资源
    validateResource(resource: ResourceItem): Promise<ValidationResult>;
    
    // 验证数据完整性
    validateIntegrity(data: any, checksum: string): boolean;
    
    // 修复数据
    repairData(data: any): Promise<RepairResult>;
}

// 验证结果
export interface ValidationResult {
    isValid: boolean;
    errors: ValidationError[];
    warnings: ValidationWarning[];
}

// 验证错误
export interface ValidationError {
    code: string;
    message: string;
    path?: string;
    severity: 'error' | 'warning';
}

// 验证警告
export interface ValidationWarning {
    code: string;
    message: string;
    suggestion?: string;
}

// 修复结果
export interface RepairResult {
    success: boolean;
    repairedItems: string[];
    unrepairedItems: string[];
    changes: RepairChange[];
}

// 修复变更
export interface RepairChange {
    type: 'add' | 'remove' | 'modify';
    path: string;
    oldValue?: any;
    newValue?: any;
    reason: string;
}
