/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Paper.js-drawing-tools.
 *
 * Paper.js-drawing-tools is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Paper.js-drawing-tools is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Paper.js-drawing-tools.  If not, see <https://www.gnu.org/licenses/>.
 */

/*
    paper-erase.js
    Adds erase() to the paper Layer class which erases paths in that layer using
    the shape of a given path. Use this to make a vector eraser!

    by zrispo (github.com/zrispo) (<EMAIL>)
 */

import * as paper from "paper";

interface SplitPathResult {
  fill: paper.Path;
  stroke: paper.Path;
}

// Splits a CompoundPath with multiple CW children into individual pieces
function splitCompoundPath(compoundPath: paper.CompoundPath): void {
  // Create lists of 'holes' (CCW children) and 'parts' (CW children)
  const holes: paper.Path[] = [];
  const parts: paper.Path[] = [];
  compoundPath.children.forEach((child: paper.Path) => {
    if (!child.clockwise) {
      holes.push(child);
    } else {
      const part = child.clone({ insert: false });
      part.fillColor = compoundPath.fillColor;
      part.insertAbove(compoundPath);
      parts.push(part);
    }
  });

  // Find hole ownership for each 'part'
  const resolvedHoles: paper.Path[] = [];
  parts.forEach((part) => {
    let cmp: paper.CompoundPath | undefined;
    holes.forEach((hole) => {
      if (part.bounds.contains(hole.bounds)) {
        if (!cmp) {
          cmp = new paper.CompoundPath({ insert: false });
          cmp.insertAbove(part);
          cmp.addChild(part.clone({ insert: false }));
        }
        cmp.addChild(hole);
        resolvedHoles.push(hole);
      }
      if (cmp) {
        cmp.fillColor = compoundPath.fillColor;
        cmp.insertAbove(part);
        part.remove();
      }
    });
  });

  // If any holes could not find a path to be a part of, turn them into their own paths
  holes
    .filter((hole) => resolvedHoles.indexOf(hole) === -1)
    .forEach((hole) => {
      hole.clockwise = !hole.clockwise;
      paper.project.activeLayer.addChild(hole);
    });

  compoundPath.remove();
}

function eraseFill(
  path: paper.Path | paper.CompoundPath,
  eraserPath: paper.Path
): void {
  if ("closePath" in path && typeof path.closePath === "function") {
    path.closePath();
  }
  const res = path.subtract(eraserPath, {
    insert: false,
    trace: true,
  }) as paper.CompoundPath | paper.Path;

  res.fillColor = path.fillColor;
  if ("children" in res && res.children) {
    res.insertAbove(path);
    res.data = {};
    path.remove();
    splitCompoundPath(res as paper.CompoundPath);
  } else {
    if ((res as paper.Path).segments.length > 0) {
      res.data = {};
      res.insertAbove(path);
    }
    path.remove();
  }
  path.remove();
}

function eraseStroke(
  path: paper.Path | paper.CompoundPath,
  eraserPath: paper.Path
): void {
  const res = path.subtract(eraserPath, {
    insert: false,
    trace: false,
  }) as paper.CompoundPath | paper.Path;

  if ("children" in res && res.children) {
    // Since the path is only strokes, it's trivial to split it into individual paths
    const children: paper.Path[] = [];
    res.children.forEach((child) => {
      child.data = {};
      children.push(child as paper.Path);
      child.name = null;
    });
    children.forEach((child) => {
      child.insertAbove(path);
    });
    res.remove();
  } else {
    res.remove();
    if ((res as paper.Path).segments.length > 0) {
      res.insertAbove(path);
    }
  }
  path.remove();
}

function splitPath(path: paper.Path): SplitPathResult {
  const fill = path.clone({ insert: false });
  fill.name = null;
  fill.strokeColor = null;
  fill.strokeWidth = 1;

  const stroke = path.clone({ insert: false });
  stroke.name = null;
  stroke.fillColor = null;

  fill.insertAbove(path);
  stroke.insertAbove(fill);
  path.remove();

  return {
    fill,
    stroke,
  };
}

function eraseWithPath(this: paper.Layer, eraserPath: paper.Path): void {
  const erasables = this.children.filter(
    (path): path is paper.Path | paper.CompoundPath =>
      path instanceof paper.Path || path instanceof paper.CompoundPath
  );

  const touchingPaths = this.children.filter((child) =>
    eraserPath.bounds.intersects(child.bounds)
  );

  touchingPaths.forEach((path) => {
    if (path instanceof paper.Path || path instanceof paper.CompoundPath) {
      if (path.strokeColor && path.fillColor) {
        const res = splitPath(path as paper.Path);
        eraseFill(res.fill, eraserPath);
        eraseStroke(res.stroke, eraserPath);
      } else if (path.fillColor) {
        eraseFill(path, eraserPath);
      } else if (path.strokeColor) {
        eraseStroke(path, eraserPath);
      }
    }
  });
}

// Extend paper.Layer with erase method
declare module "paper" {
  interface Layer {
    erase(eraserPath: paper.Path): void;
  }
}

paper.Layer.inject({
  erase: eraseWithPath,
});
