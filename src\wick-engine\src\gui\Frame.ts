/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { Ghost } from "./Ghost";

export class Frame extends GUIElement {
  static FRAME_CONTENTFUL_FILL_COLOR = "#fff";
  static FRAME_UNCONTENTFUL_FILL_COLOR = "#eee";
  static FRAME_BORDER_RADIUS = 3;
  static FRAME_HIGHLIGHT_STROKEWIDTH = 2;
  static FRAME_HANDLE_WIDTH = 10;
  static FRAME_CONTENT_DOT_RADIUS = 3;
  static FRAME_CONTENT_DOT_STROKE_WIDTH = 1;
  static FRAME_CONTENT_DOT_COLOR = "#666";
  static FRAME_SCRIPT_DOT_COLOR = "#00ff00";
  static SELECTED_ITEM_BORDER_COLOR = "#00ff00";
  static FRAME_HOVERED_OVER = "rgba(0,0,0,0.1)";

  protected _ghost: Ghost | null;
  protected _clickedEdge: "left" | "right" | null;

  constructor(model: Base) {
    super(model);

    this.canAutoScrollX = true;
    this.canAutoScrollY = true;

    this._ghost = null;
    this._clickedEdge = null;
  }

  draw(): void {
    super.draw();

    const ctx = this.ctx;

    // Fade out frames is layer is hidden
    if (this.model.parentLayer.hidden) ctx.globalAlpha = 0.3;

    // Frame body
    const widthPx = this.model.length * this.gridCellWidth - 1;
    const heightPx = this.gridCellHeight - 1;

    const edge = this._mouseOverFrameEdge();

    if (
      this.model.contentful ||
      this.model.tweens.length > 0 ||
      this.model.sound
    ) {
      ctx.fillStyle = Frame.FRAME_CONTENTFUL_FILL_COLOR;
    } else {
      ctx.fillStyle = Frame.FRAME_UNCONTENTFUL_FILL_COLOR;
    }
    ctx.beginPath();
    ctx.roundRect(0, 0, widthPx, heightPx, Frame.FRAME_BORDER_RADIUS);
    ctx.fill();
    if (!edge && (this.mouseState === "over" || this.mouseState === "down")) {
      ctx.lineWidth = 3;
      ctx.strokeStyle = Frame.FRAME_HOVERED_OVER;
      ctx.stroke();
    }

    // Add selection highlight if necessary
    if (this.model.isSelected) {
      ctx.strokeStyle = Frame.SELECTED_ITEM_BORDER_COLOR;
      ctx.lineWidth = Frame.FRAME_HIGHLIGHT_STROKEWIDTH;
      ctx.stroke();
    }

    // Frame body edge
    if (edge) {
      this.cursor = "ew-resize";

      const edgeGradient = ctx.createLinearGradient(
        widthPx - Frame.FRAME_HANDLE_WIDTH,
        0,
        widthPx,
        0
      );
      edgeGradient.addColorStop(0, "rgba(255,222,35, 0.0)");
      edgeGradient.addColorStop(1, "rgba(255,222,35, 1.0)");
      ctx.fillStyle = edgeGradient;
      ctx.strokeStyle = edgeGradient;
      ctx.lineWidth = 5;

      ctx.save();
      if (edge === "left") {
        ctx.translate(widthPx, 0);
        ctx.scale(-1, 1);
      }

      ctx.beginPath();
      ctx.roundRect(0, 0, widthPx, heightPx, Frame.FRAME_BORDER_RADIUS);
      ctx.fill();
      ctx.stroke();

      ctx.restore();
    } else {
      this.cursor = "grab";
    }

    // Frame scripts dot
    if (this.model.hasContentfulScripts) {
      ctx.fillStyle = Frame.FRAME_SCRIPT_DOT_COLOR;
      ctx.beginPath();
      ctx.arc(
        this.gridCellWidth / 2,
        0,
        Frame.FRAME_CONTENT_DOT_RADIUS * 1.3,
        0,
        Math.PI
      );
      ctx.fill();
    }

    // Frame identifier
    if (this.model.identifier) {
      ctx.save();
      ctx.beginPath();
      ctx.rect(
        0,
        0,
        this.model.length * this.gridCellWidth,
        this.gridCellHeight
      );
      ctx.clip();
      ctx.font = "12px Courier New";
      ctx.fillStyle = "black";
      ctx.fillText(this.model.identifier, 0, 12);
      ctx.restore();
    }

    if (this.model.tweens.length === 0 && !this.model.sound) {
      // Frame contentful dot
      ctx.fillStyle = Frame.FRAME_CONTENT_DOT_COLOR;
      if (this.model.contentful) {
        ctx.strokeStyle = Frame.FRAME_CONTENT_DOT_COLOR;
      } else {
        ctx.strokeStyle = "#aaa";
      }
      ctx.lineWidth = Frame.FRAME_CONTENT_DOT_STROKE_WIDTH;

      let r = Frame.FRAME_CONTENT_DOT_RADIUS;
      if (this.project.frameSizeMode === "small") {
        r *= 0.75;
      } else if (this.project.frameSizeMode === "large") {
        r *= 1.25;
      }

      ctx.beginPath();
      ctx.arc(
        this.gridCellWidth / 2,
        this.gridCellHeight / 2,
        r,
        0,
        2 * Math.PI
      );
      if (this.model.contentful) {
        ctx.fill();
      }
      ctx.stroke();
    } else if (this.model.sound) {
      // Sound waveform
      const framerate = this.model.project.framerate;
      const sound = this.model.sound;
      const waveform = sound.waveform;

      const soundLengthMS = sound.duration * 1000;
      const frameLengthMS = (1 / framerate) * this.model.length * 1000;

      const frameLengthPx = this.model.length * this.gridCellWidth;
      const cropPx = (frameLengthMS / soundLengthMS) * 1200; // base waveform image size: 1200px

      // Determining Pxls/milliseconds to shift waveform.
      const msPerFrame = 1000 / framerate;
      const pxPerMS = msPerFrame / this.gridCellWidth;
      const shiftSoundStart = -(this.model.soundStart * (1 / pxPerMS));

      const volumeCropAmt =
        (waveform.height / 2) * (1 - 1 / this.model.soundVolume);
      ctx.drawImage(
        waveform,
        0,
        volumeCropAmt,
        cropPx,
        waveform.height - volumeCropAmt * 2,
        shiftSoundStart,
        0,
        frameLengthPx,
        this.gridCellHeight
      );
    } else if (this.model.tweens.length > 0) {
      // Tweens
      this.model.tweens.forEach((tween) => {
        ctx.save();
        ctx.translate(
          (tween.playheadPosition - 1) * this.gridCellWidth +
            this.gridCellWidth / 2,
          this.gridCellHeight / 2
        );
        tween.guiElement.draw();
        ctx.restore();
      });
    }

    ctx.globalAlpha = 1.0;

    // Draw drag ghost
    if (this._ghost) {
      this._ghost.draw();
    }
  }

  onMouseDown(e: MouseEvent): void {
    this._clickedEdge = this._mouseOverFrameEdge();

    const playheadPosition =
      this.model.start + Math.floor(this.localMouse.x / this.gridCellWidth);
    this.model.project.activeTimeline.playheadPosition = playheadPosition;

    if (this.model.isSelected) {
      if (e.shiftKey) {
        this.model.project.selection.deselect(this.model);
      }
    } else {
      if (!e.shiftKey) {
        this.model.project.selection.clear();
      }
      this.model.project.selection.select(this.model);
      this.model.parentLayer.activate();
    }

    this.projectWasModified();
  }

  onMouseDrag(e: MouseEvent): void {
    if (!this._ghost) {
      const edge = this._clickedEdge;
      if (edge) {
        // Create ghost for resizing frame
        this._ghost = new Ghost(this.model);
      } else {
        // Create ghost for moving frame
        this._ghost = new Ghost(this.model);
      }
    }
  }

  onMouseUp(e: MouseEvent): void {
    if (this._ghost) {
      this._ghost.finish();
      this._ghost = null;
    }
  }

  protected _mouseOverFrameEdge(): "left" | "right" | null {
    const localMouse = this.localMouse;
    const widthPx = this.model.length * this.gridCellWidth;

    if (
      localMouse.x > widthPx - Frame.FRAME_HANDLE_WIDTH &&
      localMouse.x < widthPx
    ) {
      return "right";
    } else if (localMouse.x > 0 && localMouse.x < Frame.FRAME_HANDLE_WIDTH) {
      return "left";
    }

    return null;
  }
}
