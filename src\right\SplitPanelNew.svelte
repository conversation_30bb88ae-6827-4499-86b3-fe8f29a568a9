<script lang="ts">
  /**
   * 新的SplitPanel组件 - 单向监听架构
   * 1. 不监听selectionStore
   * 2. 通过props接收currentResource
   * 3. 只负责设置selectionStore
   * 4. ImageCanvas监听selectionStore变化
   */

  // 移除已删除的 selectionStore 相关引用
  import { resourceActions } from '../stores/resourceStore';
  import type { CropArea, ImageResource, ResourceItem, CropData } from '../types/imageType';
  import { blobUrlManager } from '../utils/blobUrlManager';
  import Image from '../components/ui/Image.svelte';
  import { cropWorkerManager, type CropAreaResult } from '../workers/cropWorkerManager';
  import { exportSettingsActions } from '../stores/exportSettingsStore';
  import { open } from '@tauri-apps/plugin-dialog';
  import { exportManager } from '../exportDialog/exportUtils';
  import { registerExporters } from '../exportDialog/formats';
  import type { ExportItem } from '../exportDialog/exportTypes';

  // 🎯 Props接口
  interface Props {
    currentResource: ImageResource;
  }

  let { currentResource }: Props = $props();

  // 🎯 本地状态管理 - 不再监听selectionStore
  let cropSettings = $state<CropSettings>({
    cellWidth: 64,
    cellHeight: 64,
    gridMode: false,
    padding: 0,
    autoDetect: false,
    minWidth: 1,
    minHeight: 1
  });

  // 🎯 跟踪当前加载的资源ID，避免重复加载
  let currentResourceId = $state<string | null>(null);

  // 🎯 裁切相关状态
  let cropAreas = $state<CropArea[]>([]);
  let selectedCropAreaId = $state<string | null>(null);
  let isGenerating = $state(false);
  let isExporting = $state(false);

  // 🎯 图片缓存相关
  let sourceImageCache: { [resourceId: string]: HTMLImageElement } = {};
  let canvasPool: HTMLCanvasElement[] = [];

  // 🎯 裁切图片缓存
  interface CacheEntry {
    url: string;
    timestamp: number;
    size: number;
  }
  let cropImageCache = $state<Map<string, CacheEntry>>(new Map());
  const MAX_CACHE_SIZE = 50 * 1024 * 1024; // 50MB限制
  let currentCacheSize = $state(0);

  // 🎯 虚拟化和渐进式加载配置
  const shouldUseVirtualization = $derived(cropAreas.length > 20);
  let cropGridElement = $state<HTMLDivElement>();
  let containerHeight = $state(300);
  let scrollTop = $state(0);

  const gridConfig = $derived(() => ({
    itemWidth: 120,
    itemHeight: 120,
    containerHeight,
    gap: 8,
    minColumns: 2,
    maxColumns: 6
  }));

  // 🎯 计算虚拟化参数
  const virtualConfig = $derived(() => {
    if (!shouldUseVirtualization || !cropGridElement) return null;

    const config = gridConfig();
    const { itemWidth, itemHeight, gap, minColumns, maxColumns } = config;
    const containerWidth = cropGridElement.clientWidth || 400;

    const columns = Math.max(minColumns, Math.min(maxColumns, Math.floor((containerWidth + gap) / (itemWidth + gap))));
    const rows = Math.ceil(cropAreas.length / columns);
    const totalHeight = rows * (itemHeight + gap) - gap;

    const startRow = Math.floor(scrollTop / (itemHeight + gap));
    const endRow = Math.min(rows - 1, Math.ceil((scrollTop + containerHeight) / (itemHeight + gap)) + 1);

    const startIndex = startRow * columns;
    const endIndex = Math.min(cropAreas.length - 1, (endRow + 1) * columns - 1);

    return {
      columns,
      rows,
      totalHeight,
      startIndex,
      endIndex,
      itemHeight: itemHeight + gap,
      visibleItems: cropAreas.slice(startIndex, endIndex + 1)
    };
  });

  // 🎯 渐进式加载队列
  const loadingQueue = new Set<string>();
  const loadedImages = new Map<string, string>();
  let loadingCount = 0;
  const MAX_CONCURRENT_LOADS = 3;

  // 🎯 Worker相关状态
  let workerResults = $state(new Map<string, string>()); // areaId -> dataUrl
  let isWorkerProcessing = $state(false);
  let workerProgress = $state({ completed: 0, total: 0, percentage: 0 });
  let workerTaskId = $state<string | null>(null); // 当前Worker任务ID

  // 🎯 使用Worker生成所有裁切图片
  async function generateCropImagesWithWorker(): Promise<void> {
    if (!currentResource?.data || !currentResource.id || cropAreas.length === 0) {
      console.warn('⚠️ SplitPanelNew: 无法使用Worker生成裁切图片 - 缺少必要数据');
      return;
    }

    try {
      // 🎯 清空之前的结果，避免显示旧数据
      workerResults.clear();
      isWorkerProcessing = true;
      workerProgress = { completed: 0, total: cropAreas.length, percentage: 0 };
      workerTaskId = `${currentResource.id}_${Date.now()}`;

      console.log('🚀 SplitPanelNew: 开始使用Worker生成裁切图片', {
        areasCount: cropAreas.length,
        resourceId: currentResource.id,
        taskId: workerTaskId
      });

      // 🎯 清理cropAreas数据，确保可序列化
      console.log('🔍 SplitPanelNew: 检查原始cropAreas数据', {
        areasCount: cropAreas.length,
        firstAreaKeys: cropAreas[0] ? Object.keys(cropAreas[0]) : [],
        firstArea: cropAreas[0]
      });

      const serializableCropAreas = cropAreas.map(area => {
        const cleanArea = {
          id: String(area.id),
          name: String(area.name),
          x: Number(area.x),
          y: Number(area.y),
          width: Number(area.width),
          height: Number(area.height)
        };
        return cleanArea;
      });

      console.log('📤 SplitPanelNew: 准备发送到Worker的数据', {
        bufferSize: currentResource.data.byteLength,
        bufferType: currentResource.data.constructor.name,
        areasCount: serializableCropAreas.length,
        firstArea: serializableCropAreas[0],
        originalType: (currentResource as ImageResource).originalFile?.type
      });

      const result = await cropWorkerManager.generateCropAreas(
        {
          buffer: currentResource.data,
          originalType: (currentResource as ImageResource).originalFile?.type,
          cropAreas: serializableCropAreas,
          quality: 0.9
        },
        (progress) => {
          workerProgress = progress;
          console.log('📊 SplitPanelNew: Worker进度更新', progress);
        }
      );

      // 🎯 将Worker结果存储到响应式状态
      const newResults = new Map<string, string>();
      for (const cropResult of result.cropResults) {
        newResults.set(cropResult.areaId, cropResult.dataUrl);

        // 同时更新传统缓存以保持兼容性
        const cacheKey = `${currentResource.id}_${cropResult.areaId}`;
        const estimatedSize = cropResult.width * cropResult.height * 4;

        cropImageCache.set(cacheKey, {
          url: cropResult.dataUrl,
          timestamp: Date.now(),
          size: estimatedSize
        });
        currentCacheSize += estimatedSize;
      }

      // 🎯 一次性更新workerResults，触发UI重新渲染
      workerResults = newResults;

      console.log('✅ SplitPanelNew: Worker裁切图片生成完成', {
        totalProcessed: result.totalProcessed,
        totalAreas: result.totalAreas,
        cacheSize: workerResults.size
      });

    } catch (error) {
      console.error('❌ SplitPanelNew: Worker裁切图片生成失败', error);
    } finally {
      isWorkerProcessing = false;
    }
  }

  // 🎯 获取裁切图片（仅使用Worker结果）
  function getCropImageFromWorker(area: CropArea): string | null {
    // 只返回Worker结果，不回退到传统方法
    return workerResults.get(area.id) || null;
  }

  // 🎯 渐进式加载函数（优先使用Worker结果）
  async function loadCropImageProgressively(area: CropArea): Promise<string | null> {
    // 首先检查Worker结果
    if (workerResults.has(area.id)) {
      return workerResults.get(area.id)!;
    }

    // 如果Worker正在处理，等待一下
    if (isWorkerProcessing) {
      return null; // 显示加载状态
    }

    // 🎯 不再回退到传统方法，避免阻塞UI
    // 如果Worker没有结果，就显示加载状态
    console.log('⏳ SplitPanelNew: 等待Worker处理结果', area.id);
    return null;
  }

  // 🎯 处理滚动事件
  function handleScroll(event: Event) {
    const target = event.target as HTMLElement;
    scrollTop = target.scrollTop;
  }

  // 🎯 监听容器尺寸变化
  $effect(() => {
    if (cropGridElement) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          containerHeight = entry.contentRect.height;
        }
      });

      resizeObserver.observe(cropGridElement);

      return () => {
        resizeObserver.disconnect();
      };
    }
  });

  // 🎯 监听currentResource变化，加载对应的设置
  $effect(() => {
    if (currentResource?.type === 'image') {
      const imageResource = currentResource as ImageResource;

      // 🎯 只有当资源ID变化时才重新加载设置
      if (imageResource.id !== currentResourceId) {
        currentResourceId = imageResource.id;

        // 🎯 清空之前的Worker结果，避免显示错误数据
        workerResults = new Map();
        isWorkerProcessing = false;
        workerProgress = { completed: 0, total: 0, percentage: 0 };
        workerTaskId = null;

        if (imageResource.splitSettings) {
          // 从resource.splitSettings加载到本地cropSettings
          console.log('📥 SplitPanelNew: 从resource.splitSettings加载设置');
          cropSettings = { ...imageResource.splitSettings };
        } else if (imageResource.cropData) {
          // 从resource.cropData加载设置
          console.log('📥 SplitPanelNew: 从resource.cropData加载设置');
          cropSettings = {
            cellWidth: imageResource.cropData.cellWidth || 64,
            cellHeight: imageResource.cropData.cellHeight || 64,
            gridMode: imageResource.cropData.gridMode === true,
            padding: imageResource.cropData.padding || 0,
            autoDetect: imageResource.cropData.autoTrim || false,
            minWidth: 1,
            minHeight: 1
          };
        } else {
          // 使用默认设置
          console.log('📥 SplitPanelNew: 使用默认设置');
          cropSettings = {
            cellWidth: 64,
            cellHeight: 64,
            gridMode: false, // 默认不勾选网格模式
            padding: 0,
            autoDetect: false,
            minWidth: 1,
            minHeight: 1
          };
        }

        // 🎯 立即同步到selectionStore
        updateSelectionCropSettings(cropSettings);

        // 🎯 加载cropAreas
        if (imageResource.cropData?.areas) {
          cropAreas = imageResource.cropData.areas;

          // 🎯 如果有现有的裁切区域，立即使用Worker生成图片
          if (cropAreas.length > 0) {
            console.log('🚀 SplitPanelNew: 检测到现有裁切区域，使用Worker生成图片', {
              areasCount: cropAreas.length,
              resourceName: imageResource.name
            });

            // 异步生成裁切图片，不阻塞UI
            generateCropImagesWithWorker().catch(error => {
              console.error('❌ SplitPanelNew: Worker生成现有裁切图片失败', error);
            });
          }
        } else {
          cropAreas = [];
        }
      }
    } else {
      // 清空当前资源ID和相关状态
      currentResourceId = null;
      cropAreas = [];
      selectedCropAreaId = null;

      // 清理图片缓存
      for (const entry of cropImageCache.values()) {
        try {
          blobUrlManager.revokeObjectURL(entry.url);
        } catch (error) {
          console.warn('⚠️ SplitPanelNew: 清理缓存失败', error);
        }
      }
      cropImageCache.clear();
      currentCacheSize = 0;
    }
  });

  // 🎯 裁切设置更新函数 - 单向数据流
  function updateCropSettings(key: keyof CropSettings, value: any) {
    console.log('🎯 SplitPanelNew: 更新裁切设置', { key, value });

    // 🎯 处理 autoDetect 和 gridMode 的互斥逻辑
    let newSettings = { ...cropSettings, [key]: value };

    if (key === 'autoDetect' && value === true) {
      // 开启自动检测时，关闭网格模式
      newSettings.gridMode = false;
      console.log('🎯 SplitPanelNew: 开启自动检测，关闭网格模式');
    } else if (key === 'gridMode' && value === true) {
      // 开启网格模式时，关闭自动检测
      newSettings.autoDetect = false;
      console.log('🎯 SplitPanelNew: 开启网格模式，关闭自动检测');
    }

    // 1. 更新本地cropSettings
    cropSettings = newSettings;

    // 2. 立即同步到selectionStore
    updateSelectionCropSettings(cropSettings);

    // 🎯 暂时移除resource.splitSettings的更新，避免触发resourceStore的selectResource
    // 这样可以避免selectionStore被重置
  }

  // 🎯 处理网格模式切换
  function handleGridModeChange(enabled: boolean) {
    console.log('🎯 SplitPanelNew: 网格模式切换', { enabled });

    // 🎯 使用updateCropSettings来处理互斥逻辑
    updateCropSettings('gridMode', enabled);

    if (!enabled) {
      // 🎯 关闭网格模式时，清空所有网格数据
      console.log('🗑️ SplitPanelNew: 关闭网格模式，清空所有数据');

      // 重置本地cropAreas
      cropAreas = [];
      selectedCropAreaId = null;

      // 清空全局裁切区域
      clearCropAreas();

      // 清空resource中的cropData
      if (currentResource?.type === 'image') {
        const imageResource = currentResource as ImageResource;
        resourceActions.updateResource(imageResource.id, {
          cropData: undefined
        });
      }

      // 清理相关的图片缓存
      for (const entry of cropImageCache.values()) {
        try {
          blobUrlManager.revokeObjectURL(entry.url);
        } catch (error) {
          console.warn('⚠️ SplitPanelNew: 清理缓存失败', error);
        }
      }
      cropImageCache.clear();
      currentCacheSize = 0;
    }
    // 🎯 开启网格模式时，不需要手动生成网格
    // ImageCanvas会监听cropSettings.gridMode变化并自动生成网格
  }

  // 🎯 移除generateGrid函数，让ImageCanvas自己处理网格生成

  // 🎯 重置所有数据
  function resetAllData() {
    console.log('🔄 SplitPanelNew: 开始重置所有数据');

    // 1. 重置本地cropSettings到默认值
    cropSettings = {
      cellWidth: 64,
      cellHeight: 64,
      gridMode: false, // 默认不勾选网格模式
      padding: 0,
      autoDetect: false,
      minWidth: 1,
      minHeight: 1
    };

    // 2. 重置本地cropAreas
    cropAreas = [];
    selectedCropAreaId = null;

    // 3. 同步到selectionStore
    updateSelectionCropSettings(cropSettings);

    // 4. 清空所有裁切区域（全局状态）
    clearCropAreas();

    // 5. 清空resource中的数据
    if (currentResource?.type === 'image') {
      const imageResource = currentResource as ImageResource;
      resourceActions.updateResource(imageResource.id, {
        splitSettings: undefined,
        cropData: undefined
      });
    }

    // 6. 清理图片缓存
    for (const entry of cropImageCache.values()) {
      try {
        blobUrlManager.revokeObjectURL(entry.url);
      } catch (error) {
        console.warn('⚠️ SplitPanelNew: 清理缓存失败', error);
      }
    }
    cropImageCache.clear();
    currentCacheSize = 0;

    console.log('✅ SplitPanelNew: 重置完成', {
      localCropAreas: cropAreas.length,
      selectedCropAreaId,
      cacheCleared: true
    });
  }

  // 🎯 计算网格信息
  const gridInfo = $derived(() => {
    if (!currentResource || !cropSettings.gridMode) {
      return { rows: 0, cols: 0, total: 0 };
    }

    const imageResource = currentResource as ImageResource;
    if (!imageResource.width || !imageResource.height) {
      return { rows: 0, cols: 0, total: 0 };
    }

    const { cellWidth, cellHeight, padding } = cropSettings;
    const spacing = 0; // 🎯 固定间距为0，spacing已迁移到ExportPanel
    const { width: imgWidth, height: imgHeight } = imageResource;

    const availableWidth = imgWidth - 2 * padding;
    const availableHeight = imgHeight - 2 * padding;

    const cellWithSpacingW = cellWidth + spacing;
    const cellWithSpacingH = cellHeight + spacing;

    const cols = Math.floor((availableWidth + spacing) / cellWithSpacingW);
    const rows = Math.floor((availableHeight + spacing) / cellWithSpacingH);

    return { rows, cols, total: rows * cols };
  });

  // 🎯 预设尺寸
  const presetSizes = [
    { name: '16×16', width: 16, height: 16 },
    { name: '32×32', width: 32, height: 32 },
    { name: '48×48', width: 48, height: 48 },
    { name: '64×64', width: 64, height: 64 },
    { name: '128×128', width: 128, height: 128 },
    { name: '256×256', width: 256, height: 256 }
  ];

  // 🎯 应用预设尺寸
  function applyPresetSize(preset: { width: number; height: number }) {
    updateCropSettings('cellWidth', preset.width);
    updateCropSettings('cellHeight', preset.height);

    // 🎯 不需要手动生成网格，ImageCanvas会监听cropSettings变化并自动处理

    console.log('🎯 SplitPanelNew: 应用预设尺寸', preset);
  }

  // 🎯 组件销毁时清理资源
  $effect(() => {
    return () => {
      console.log('🧹 SplitPanelNew: 开始组件销毁清理');

      // 清理图片缓存
      for (const entry of cropImageCache.values()) {
        try {
          blobUrlManager.revokeObjectURL(entry.url);
        } catch (error) {
          console.warn('⚠️ SplitPanelNew: 清理Blob URL失败', error);
        }
      }

      // 按来源清理所有SplitPanelNew相关的Blob URL
      blobUrlManager.cleanupBySource('SplitPanelNew');
      cropImageCache.clear();
      currentCacheSize = 0;

      // 清理Canvas池
      canvasPool = [];

      // 清理源图片缓存
      sourceImageCache = {};

      // 清理Worker状态
      workerResults = new Map();
      isWorkerProcessing = false;
      workerProgress = { completed: 0, total: 0, percentage: 0 };
      workerTaskId = null;

      console.log('🧹 SplitPanelNew: 组件销毁清理完成');
    };
  });

  // 🎯 Canvas池管理
  function getCanvas(width: number, height: number): HTMLCanvasElement {
    let canvas = canvasPool.pop();
    if (!canvas) {
      canvas = document.createElement('canvas');
    }
    canvas.width = width;
    canvas.height = height;
    return canvas;
  }

  function recycleCanvas(canvas: HTMLCanvasElement) {
    if (canvasPool.length < 5) {
      canvasPool.push(canvas);
    }
  }

  // 🎯 获取或创建源图片对象
  async function getSourceImage(resourceData: ArrayBuffer, resourceId: string): Promise<HTMLImageElement | null> {
    if (sourceImageCache[resourceId]) {
      return sourceImageCache[resourceId];
    }

    return new Promise((resolve) => {
      try {
        const img = new window.Image();
        const blob = new Blob([resourceData]);
        const url = URL.createObjectURL(blob);

        img.onload = () => {
          URL.revokeObjectURL(url);
          sourceImageCache[resourceId] = img;
          resolve(img);
        };

        img.onerror = () => {
          console.error('图片加载失败');
          URL.revokeObjectURL(url);
          resolve(null);
        };

        img.src = url;
      } catch (error) {
        console.error('创建源图片失败:', error);
        resolve(null);
      }
    });
  }

  // 🎯 生成裁切区域图片
  async function generateCropAreaImage(area: CropArea): Promise<string | null> {
    if (!currentResource?.data || !currentResource.id) {
      console.warn('⚠️ SplitPanelNew: 无法生成裁切图片 - 缺少资源数据');
      return null;
    }

    try {
      const sourceImg = await getSourceImage(currentResource.data, currentResource.id);
      if (!sourceImg) {
        console.error('❌ SplitPanelNew: 无法获取源图片');
        return null;
      }

      const canvas = getCanvas(area.width, area.height);
      const ctx = canvas.getContext('2d');

      if (!ctx) {
        console.error('❌ SplitPanelNew: 无法获取Canvas上下文');
        recycleCanvas(canvas);
        return null;
      }

      ctx.clearRect(0, 0, area.width, area.height);
      ctx.drawImage(
        sourceImg,
        area.x, area.y, area.width, area.height,
        0, 0, area.width, area.height
      );

      return new Promise((resolve) => {
        canvas.toBlob((blob) => {
          recycleCanvas(canvas);

          if (blob) {
            const url = blobUrlManager.createObjectURL(blob, `SplitPanelNew-${area.id}`);
            resolve(url);
          } else {
            console.error('❌ SplitPanelNew: Canvas转Blob失败');
            resolve(null);
          }
        }, 'image/png', 0.9);
      });

    } catch (error) {
      console.error('❌ SplitPanelNew: 生成裁切区域图片失败:', error);
      return null;
    }
  }

  // 🎯 获取裁切区域图片（带缓存）
  async function getCropAreaImageSrc(area: CropArea): Promise<string | null> {
    const cacheKey = `${currentResource?.id}_${area.id}_${area.x}_${area.y}_${area.width}_${area.height}`;

    const cached = cropImageCache.get(cacheKey);
    if (cached) {
      cached.timestamp = Date.now();
      return cached.url;
    }

    const imageUrl = await generateCropAreaImage(area);
    if (imageUrl) {
      const estimatedSize = area.width * area.height * 4;

      while (currentCacheSize + estimatedSize > MAX_CACHE_SIZE && cropImageCache.size > 0) {
        const oldestKey = Array.from(cropImageCache.keys())[0];
        const oldEntry = cropImageCache.get(oldestKey);
        if (oldEntry) {
          blobUrlManager.revokeObjectURL(oldEntry.url);
          currentCacheSize -= oldEntry.size;
          cropImageCache.delete(oldestKey);
        }
      }

      cropImageCache.set(cacheKey, {
        url: imageUrl,
        timestamp: Date.now(),
        size: estimatedSize
      });
      currentCacheSize += estimatedSize;
    }

    return imageUrl;
  }

  // 🎯 获取图片尺寸（如果尚未获取）
  async function ensureImageDimensions(imageResource: ImageResource): Promise<{ width: number; height: number } | null> {
    // 如果已经有尺寸信息，直接返回
    if (imageResource.width && imageResource.height) {
      return { width: imageResource.width, height: imageResource.height };
    }

    console.log('🔍 SplitPanelNew: 图片尺寸未知，尝试获取尺寸', imageResource.name);

    // 如果没有数据，先加载数据
    if (!imageResource.data) {
      console.log('📥 SplitPanelNew: 加载图片数据...');
      await resourceActions.loadResourceBuffer(imageResource.id);

      // 重新获取更新后的resource
      const updatedResource = await resourceActions.getResourceById(imageResource.id);
      if (updatedResource && updatedResource.type === 'image') {
        Object.assign(imageResource, updatedResource);
      }
    }

    if (!imageResource.data) {
      console.error('❌ SplitPanelNew: 无法加载图片数据');
      return null;
    }

    // 创建图片对象获取尺寸
    return new Promise((resolve) => {
      const img = new window.Image();

      img.onload = () => {
        const width = img.naturalWidth;
        const height = img.naturalHeight;

        console.log('✅ SplitPanelNew: 获取到图片尺寸', {
          name: imageResource.name,
          size: `${width}×${height}`
        });

        // 更新resource的尺寸信息
        resourceActions.updateResource(imageResource.id, {
          width,
          height
        });

        // 更新本地引用
        imageResource.width = width;
        imageResource.height = height;

        URL.revokeObjectURL(img.src);
        resolve({ width, height });
      };

      img.onerror = () => {
        console.error('❌ SplitPanelNew: 无法加载图片获取尺寸');
        URL.revokeObjectURL(img.src);
        resolve(null);
      };

      // 创建图片URL
      const mimeType = imageResource.originalFile?.type || 'image/png';
      const blob = new Blob([imageResource.data!], { type: mimeType });
      img.src = URL.createObjectURL(blob);
    });
  }

  // 🎯 执行裁切操作
  async function performCrop() {
    if (!currentResource || currentResource.type !== 'image') {
      console.warn('⚠️ SplitPanelNew: 无法执行裁切 - 没有选中的图片资源');
      return;
    }

    const imageResource = currentResource as ImageResource;

    if (!cropSettings.gridMode && !cropSettings.autoDetect) {
      console.warn('⚠️ SplitPanelNew: 无法执行裁切 - 网格模式和自动检测均未启用');
      return;
    }

    isGenerating = true;
    console.log('🎯 SplitPanelNew: 开始执行裁切操作');

    try {
      // 🎯 确保图片尺寸已获取
      const dimensions = await ensureImageDimensions(imageResource);
      if (!dimensions) {
        console.error('❌ SplitPanelNew: 无法获取图片尺寸');
        isGenerating = false;
        return;
      }

      console.log('✅ SplitPanelNew: 图片尺寸确认', {
        name: imageResource.name,
        size: `${dimensions.width}×${dimensions.height}`
      });

      // 🎯 根据设置选择生成方式
      if (cropSettings.autoDetect) {
        console.log('🎯 SplitPanelNew: 使用自动检测模式生成裁切区域');
        // 自动检测模式：ImageCanvas会自动调用generateAutoCropAreas
        // 这里只需要触发ImageCanvas的重新处理
        updateSelectionCropSettings(cropSettings);
      } else if (cropSettings.gridMode) {
        console.log('🎯 SplitPanelNew: 使用网格模式生成裁切区域');
        // 使用selectionStore的方法生成网格
        generateGridCropAreas(dimensions.width, dimensions.height);
      }

      // 等待一下让selectionStore更新完成，然后获取生成的cropAreas
      setTimeout(() => {
        let currentSelection: any = null;
        const unsubscribe = selectionStore.subscribe(state => currentSelection = state);
        unsubscribe();

        const generatedAreas = currentSelection?.cropAreas || [];

        // 清理旧的图片缓存（因为cropAreas变化了）
        for (const entry of cropImageCache.values()) {
          try {
            blobUrlManager.revokeObjectURL(entry.url);
          } catch (error) {
            console.warn('⚠️ SplitPanelNew: 清理旧缓存失败', error);
          }
        }
        cropImageCache.clear();
        currentCacheSize = 0;

        // 更新本地cropAreas
        cropAreas = [...generatedAreas];
        selectedCropAreaId = null; // 重置选中状态

        // 保存cropData到resource
        const newCropData: CropData = {
          areas: generatedAreas,
          cellWidth: cropSettings.cellWidth,
          cellHeight: cropSettings.cellHeight,
          gridMode: cropSettings.gridMode,
          gridRows: gridInfo().rows,
          gridCols: gridInfo().cols,
          padding: cropSettings.padding,
          spacing: 0, // 🎯 固定间距为0，spacing已迁移到ExportPanel
          autoTrim: cropSettings.autoDetect,
          lastModified: new Date()
        };

        resourceActions.updateResource(imageResource.id, {
          cropData: newCropData
        });

        console.log('✅ SplitPanelNew: 裁切操作完成', {
          gridSize: `${gridInfo().cols}×${gridInfo().rows}`,
          totalAreas: generatedAreas.length,
          areasGenerated: generatedAreas.length
        });

        // 🎯 使用Worker生成裁切图片
        if (generatedAreas.length > 0) {
          console.log('🚀 SplitPanelNew: 开始使用Worker生成裁切图片');
          generateCropImagesWithWorker().catch(error => {
            console.error('❌ SplitPanelNew: Worker生成裁切图片失败', error);
          });
        }

        isGenerating = false;
      }, 100);

    } catch (error) {
      console.error('❌ SplitPanelNew: 裁切操作失败:', error);
      isGenerating = false;
    }
  }

  // 🎯 导出功能
  async function handleExport() {
    if (!currentResource || currentResource.type !== 'image') {
      console.warn('⚠️ SplitPanelNew: 无法导出 - 没有选中的图片资源');
      return;
    }

    const imageResource = currentResource as ImageResource;

    // 检查是否有裁切区域
    if (!cropAreas || cropAreas.length === 0) {
      alert('没有裁切区域，无法导出。请先执行裁切操作。');
      return;
    }

    // 检查资源是否有有效数据
    if (!imageResource.data || imageResource.data.byteLength === 0) {
      alert('图片数据无效，无法导出');
      return;
    }

    try {
      isExporting = true;
      console.log('🚀 SplitPanelNew: 开始导出图片切割', imageResource.name);

      // 打开文件夹选择对话框
      const selectedFolder = await open({
        directory: true,
        title: '选择导出文件夹'
      });

      if (!selectedFolder) {
        console.log('❌ SplitPanelNew: 用户取消了文件夹选择');
        return;
      }

      console.log('📁 SplitPanelNew: 选择的导出文件夹', selectedFolder);

      // 🎯 确保导出器已注册
      registerExporters();
      console.log('✅ SplitPanelNew: 导出器注册完成');

      // 获取当前导出设置
      const exportSettings = await exportSettingsActions.getCurrentSettings();
      console.log('⚙️ SplitPanelNew: 当前导出设置', exportSettings);

      console.log('📊 SplitPanelNew: 裁切区域数量', cropAreas.length);

      // 创建导出项目
      const exportItems: ExportItem[] = [{
        id: imageResource.id,
        name: imageResource.name,
        resource: {
          id: imageResource.id,
          name: imageResource.name,
          buffer: new Uint8Array(imageResource.data),
          path: imageResource.path || imageResource.name,
          width: imageResource.width || 0,
          height: imageResource.height || 0
        } as any,
        cropAreas: cropAreas
      }];

      console.log('📝 SplitPanelNew: 创建导出项目', exportItems);

      // 执行导出
      const result = await exportManager.export(
        exportSettings.targetEngine,
        exportItems,
        {
          type: exportSettings.targetEngine,
          outputPath: selectedFolder as string,
          fileName: exportSettings.fileName || imageResource.name,
          format: exportSettings.format,
          quality: exportSettings.quality,
          includeOriginal: exportSettings.includeOriginal,
          namePrefix: exportSettings.namePrefix,
          nameSuffix: exportSettings.nameSuffix
        },
        selectedFolder as string,
        (progress) => {
          console.log('📊 SplitPanelNew: 导出进度', progress);
        }
      );

      if (result.success) {
        console.log('✅ SplitPanelNew: 导出成功', result);
        alert(`导出成功！\n文件数量: ${result.files.length}\n总大小: ${(result.totalSize / 1024 / 1024).toFixed(2)} MB`);
      } else {
        console.error('❌ SplitPanelNew: 导出失败', result);
        alert(`导出失败: ${result.error || '未知错误'}`);
      }

    } catch (error) {
      console.error('❌ SplitPanelNew: 导出过程中发生错误', error);
      alert(`导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      isExporting = false;
    }
  }
</script>

<div class="split-panel-new">
  <div class="panel-content">
    {#if currentResource && currentResource.type === 'image'}
      <!-- 🎯 图片资源信息 -->
      <!-- <div class="settings-section">
        <h4>📋 图片资源信息</h4>
        <p>资源名称: {currentResource.name}</p>
        <p>尺寸: {currentResource.width || '未知'}×{currentResource.height || '未知'}</p>
      </div> -->

      <!-- 🎯 裁切设置 -->
      <div class="settings-section">
        <h4>⚙️ 裁切设置</h4>

        <!-- 单元格尺寸 -->
        <div class="setting-group">
          <div class="setting-item">
            <label for="cell-width">单元格宽度</label>
            <input
              id="cell-width"
              type="number"
              min="1"
              value={cropSettings.cellWidth}
              oninput={(e) => updateCropSettings('cellWidth', parseInt((e.target as HTMLInputElement).value))}
            />
          </div>
          <div class="setting-item">
            <label for="cell-height">单元格高度</label>
            <input
              id="cell-height"
              type="number"
              min="1"
              value={cropSettings.cellHeight}
              oninput={(e) => updateCropSettings('cellHeight', parseInt((e.target as HTMLInputElement).value))}
            />
          </div>
        </div>

        <!-- 预设尺寸 -->
        <div class="setting-group">
          <div class="setting-label">快速尺寸</div>
          <div class="preset-buttons">
            {#each presetSizes as preset}
              <button
                class="preset-btn"
                class:active={cropSettings.cellWidth === preset.width && cropSettings.cellHeight === preset.height}
                onclick={() => applyPresetSize(preset)}
                title="设置为 {preset.name}"
              >
                {preset.name}
              </button>
            {/each}
          </div>
        </div>

        <!-- 🎯 移除间距设置，外边距已迁移到ExportPanel -->

        <!-- 模式选择 -->
        <div class="setting-group">
          <label class="checkbox-label">
            <input
              type="checkbox"
              checked={cropSettings.gridMode}
              onchange={(e) => handleGridModeChange((e.target as HTMLInputElement).checked)}
            />
            网格模式
            {#if cropSettings.gridMode && gridInfo().total > 0}
              <span class="grid-info">({gridInfo().cols}×{gridInfo().rows} = {gridInfo().total}个)</span>
            {/if}
          </label>
          <label class="checkbox-label">
            <input
              type="checkbox"
              checked={cropSettings.autoDetect}
              onchange={(e) => updateCropSettings('autoDetect', (e.target as HTMLInputElement).checked)}
            />
            自动检测透明区域
            {#if cropSettings.autoDetect}
              <span class="grid-info">(智能识别非透明区域)</span>
            {/if}
          </label>

          <!-- 🎯 模式说明 -->
          <div class="mode-hint">
            {#if cropSettings.autoDetect}
              <span class="hint-text">🤖 自动检测模式：智能识别图片中的非透明区域，适用于精灵图</span>
            {:else if cropSettings.gridMode}
              <span class="hint-text">📐 网格模式：按固定尺寸切割图片，适用于规整的图集</span>
            {:else}
              <span class="hint-text">💡 请选择一种裁切模式：网格模式或自动检测</span>
            {/if}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="setting-group">
          <div class="button-row">
            <button
              class="crop-btn"
              onclick={performCrop}
              disabled={!currentResource || (!cropSettings.gridMode && !cropSettings.autoDetect) || isGenerating}
            >
              {#if isGenerating}
                ⏳ 裁切中...
              {:else}
                ✂️ 执行裁切
              {/if}
            </button>
            <button
              class="reset-btn"
              onclick={resetAllData}
              disabled={!currentResource}
            >
              🔄 重置数据
            </button>
          </div>
        </div>

        <!-- 🎯 导出按钮 -->
        <div class="setting-group">
          <button
            class="export-btn"
            class:loading={isExporting}
            onclick={handleExport}
            disabled={isExporting || !currentResource || !cropAreas.length}
          >
            {#if isExporting}
              <span class="loading-spinner"></span>
              导出中...
            {:else}
              📤 导出切割图片
            {/if}
          </button>
          <p class="export-hint">
            使用全局导出设置进行导出，可在右侧导出面板中配置
          </p>
        </div>
      </div>

      <!-- 🎯 裁切预览区域 -->
      {#if cropAreas.length > 0}
        <div class="settings-section">
          <h4>📋 裁切预览 ({cropAreas.length})</h4>

          <!-- 🎯 Worker进度显示 -->
          {#if isWorkerProcessing}
            <div class="worker-progress">
              <div class="progress-bar">
                <div class="progress-fill" style="width: {workerProgress.percentage}%"></div>
              </div>
              <div class="progress-text">
                Worker处理中: {workerProgress.completed}/{workerProgress.total} ({workerProgress.percentage.toFixed(1)}%)
              </div>
            </div>
          {/if}

          <div
            class="crop-areas-grid"
            class:virtualized={shouldUseVirtualization}
            bind:this={cropGridElement}
            onscroll={handleScroll}
            style={shouldUseVirtualization ? `height: ${containerHeight}px; overflow-y: auto;` : ''}
          >
            {#if shouldUseVirtualization && virtualConfig()}
              <!-- 🎯 虚拟化渲染 -->
              {@const vConfig = virtualConfig()}
              {#if vConfig}
                <div style="height: {vConfig.totalHeight}px; position: relative;">
                  {#each vConfig.visibleItems as area, index (area.id)}
                    {@const actualIndex = vConfig.startIndex + index}
                    {@const row = Math.floor(actualIndex / vConfig.columns)}
                    {@const col = actualIndex % vConfig.columns}
                    {@const top = row * vConfig.itemHeight}
                    {@const left = col * (gridConfig().itemWidth + gridConfig().gap)}

                  {@const imageSrc = getCropImageFromWorker(area)}
                  <div
                    class="crop-area-card virtualized-item"
                    class:selected={selectedCropAreaId === area.id}
                    style="position: absolute; top: {top}px; left: {left}px; width: {gridConfig().itemWidth}px; height: {gridConfig().itemHeight - gridConfig().gap}px;"
                    onclick={() => { selectedCropAreaId = area.id; selectCropArea(area.id); }}
                    onkeydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); selectedCropAreaId = area.id; selectCropArea(area.id); } }}
                    role="button"
                    tabindex="0"
                  >
                    <div class="crop-image-container">
                      {#if imageSrc}
                        <Image
                          src={imageSrc}
                          alt={area.name}
                          className="crop-image"
                          fit="contain"
                          draggable={true}
                          dragData={{
                            type: 'image',
                            src: imageSrc,
                            alt: area.name,
                            name: area.name,
                            resourceId: currentResource?.id,
                            resource: currentResource,
                            width: area.width,
                            height: area.height,
                            cropArea: area,
                            buffer: currentResource?.data
                          }}
                        />
                      {:else}
                        <div class="loading-image">⏳</div>
                      {/if}

                      <div class="crop-info-overlay">
                        <div class="crop-name">{area.name}</div>
                        <div class="crop-coords">{area.width}×{area.height}</div>
                      </div>
                    </div>
                  </div>
                {/each}
                </div>
              {/if}
            {:else}
              <!-- 🎯 常规渲染 -->
              {#each cropAreas as area (area.id)}
                {@const imageSrc = getCropImageFromWorker(area)}
                <div
                  class="crop-area-card"
                  class:selected={selectedCropAreaId === area.id}
                  onclick={() => { selectedCropAreaId = area.id; selectCropArea(area.id); }}
                  onkeydown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); selectedCropAreaId = area.id; selectCropArea(area.id); } }}
                  role="button"
                  tabindex="0"
                >
                  <div class="crop-image-container">
                    {#if imageSrc}
                      <Image
                        src={imageSrc}
                        alt={area.name}
                        className="crop-image"
                        fit="contain"
                        draggable={true}
                        dragData={{
                          type: 'image',
                          src: imageSrc,
                          alt: area.name,
                          name: area.name,
                          resourceId: currentResource?.id,
                          resource: currentResource,
                          width: area.width,
                          height: area.height,
                          cropArea: area,
                          buffer: currentResource?.data
                        }}
                      />
                    {:else}
                      <div class="loading-image">⏳</div>
                    {/if}

                    <div class="crop-info-overlay">
                      <div class="crop-name">{area.name}</div>
                      <div class="crop-coords">{area.width}×{area.height}</div>
                    </div>
                  </div>
                </div>
              {/each}
            {/if}
          </div>
        </div>
      {/if}
    {:else}
      <!-- 没有选中图片资源时显示提示 -->
      <div class="empty-state">
        <div class="empty-icon">🖼️</div>
        <div class="empty-title">请选择图片资源</div>
        <div class="empty-description">
          选择一个图片资源来进行裁切设置
        </div>
      </div>
    {/if}
  </div>
</div>

<style>
  .split-panel-new {
    /* 🎯 移除固定高度，适应滚动布局 */
    display: flex;
    flex-direction: column;
    background: var(--theme-surface);
    color: var(--theme-text);
  }

  .panel-content {
    /* 🎯 移除flex和overflow，让内容自然流动 */
    padding: 1rem;
  }

  /* 设置区域样式 */
  .settings-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--theme-background);
    border: 1px solid var(--theme-border);
    border-radius: 6px;
  }

  .settings-section h4 {
    margin: 0 0 1rem 0;
    color: var(--theme-text);
    font-size: 0.95rem;
    font-weight: 600;
  }



  /* 设置组样式 */
  .setting-group {
    margin-bottom: 1rem;
  }

  .setting-group:last-child {
    margin-bottom: 0;
  }

  .setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
  }

  .setting-item:last-child {
    margin-bottom: 0;
  }

  .setting-item label,
  .setting-label {
    color: var(--theme-text);
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 80px;
  }

  .setting-item input {
    width: 120px;
    padding: 0.5rem;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    background: var(--theme-surface);
    color: var(--theme-text);
    font-size: 0.875rem;
  }

  .setting-item input:focus {
    outline: none;
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  /* 预设按钮样式 */
  .preset-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
  }

  .preset-btn {
    padding: 0.5rem;
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    background: var(--theme-surface);
    color: var(--theme-text);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .preset-btn:hover {
    border-color: var(--theme-primary);
    background: var(--theme-primary-light, rgba(59, 130, 246, 0.1));
  }

  .preset-btn.active {
    border-color: var(--theme-primary);
    background: var(--theme-primary);
    color: white;
  }

  /* 复选框样式 */
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: var(--theme-text);
    font-size: 0.875rem;
    cursor: pointer;
  }

  .checkbox-label:last-child {
    margin-bottom: 0;
  }

  .checkbox-label input[type="checkbox"] {
    width: auto;
    margin: 0;
  }

  .grid-info {
    color: var(--theme-text-secondary);
    font-size: 0.75rem;
    margin-left: 0.5rem;
  }

  /* 🎯 模式提示样式 */
  .mode-hint {
    margin-top: 0.75rem;
    padding: 0.5rem;
    background: var(--theme-surface-light, rgba(59, 130, 246, 0.05));
    border: 1px solid var(--theme-border);
    border-radius: 4px;
  }

  .hint-text {
    font-size: 0.75rem;
    color: var(--theme-text-secondary);
    line-height: 1.4;
    display: block;
  }

  /* 按钮行样式 */
  .button-row {
    display: flex;
    gap: 0.5rem;
  }

  /* 裁切按钮样式 */
  .crop-btn {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--theme-primary, #3b82f6);
    border-radius: 4px;
    background: var(--theme-primary, #3b82f6);
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .crop-btn:hover:not(:disabled) {
    background: var(--theme-primary-dark, #2563eb);
    transform: translateY(-1px);
  }

  .crop-btn:disabled {
    background: var(--theme-border, #ccc);
    border-color: var(--theme-border, #ccc);
    cursor: not-allowed;
    transform: none;
    opacity: 0.6;
  }

  /* 重置按钮样式 */
  .reset-btn {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--theme-warning, #dc3545);
    border-radius: 4px;
    background: var(--theme-warning, #dc3545);
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .reset-btn:hover:not(:disabled) {
    background: var(--theme-warning-dark, #c82333);
    transform: translateY(-1px);
  }

  .reset-btn:disabled {
    background: var(--theme-border, #ccc);
    border-color: var(--theme-border, #ccc);
    cursor: not-allowed;
    transform: none;
    opacity: 0.6;
  }

  /* 导出按钮样式 */
  .export-btn {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--theme-success, #10b981);
    border-radius: 4px;
    background: var(--theme-success, #10b981);
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .export-btn:hover:not(:disabled) {
    background: var(--theme-success-dark, #059669);
    transform: translateY(-1px);
  }

  .export-btn:disabled {
    background: var(--theme-border, #ccc);
    border-color: var(--theme-border, #ccc);
    cursor: not-allowed;
    transform: none;
    opacity: 0.6;
  }

  .export-btn.loading {
    cursor: wait;
  }

  .loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .export-hint {
    margin: 0.5rem 0 0 0;
    font-size: 0.75rem;
    color: var(--theme-text-secondary);
    text-align: center;
    line-height: 1.4;
  }

  /* 🎯 裁切预览区域样式 */
  .crop-areas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.5rem;
    max-height: 300px;
    overflow-y: auto;
    padding: 0.5rem;
    border: 1px solid var(--theme-border);
    border-radius: 6px;
    background: var(--theme-background);
  }

  /* 🎯 虚拟化样式 */
  .crop-areas-grid.virtualized {
    display: block;
    position: relative;
  }

  .virtualized-item {
    position: absolute !important;
  }

  /* 🎯 Worker进度样式 */
  .worker-progress {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--theme-surface-light, rgba(59, 130, 246, 0.05));
    border: 1px solid var(--theme-primary, #3b82f6);
    border-radius: 6px;
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: var(--theme-border, #e5e7eb);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--theme-primary, #3b82f6), var(--theme-success, #10b981));
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  .progress-text {
    font-size: 0.75rem;
    color: var(--theme-text-secondary);
    text-align: center;
    font-weight: 500;
  }

  .crop-area-card {
    position: relative;
    display: flex;
    flex-direction: column;
    border: 2px solid var(--theme-border);
    border-radius: 6px;
    background: var(--theme-surface);
    cursor: pointer;
    transition: all 0.2s ease;
    overflow: hidden;
    height: 100px; /* 固定高度，防止内容溢出 */
  }

  .crop-area-card:hover {
    border-color: var(--theme-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .crop-area-card.selected {
    border-color: var(--theme-primary);
    background: var(--theme-primary-light, rgba(59, 130, 246, 0.1));
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }

  .crop-image-container {
    width: 100%;
    height: 100%; /* 填充整个卡片高度 */
    overflow: hidden;
    background: var(--theme-surface-light);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  /* 🎯 裁切图片样式 */
  :global(.crop-image) {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    display: block;
  }



  .loading-image,
  .error-image {
    font-size: 1.5rem;
    opacity: 0.5;
    color: var(--theme-text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .loading-image {
    animation: pulse 1.5s ease-in-out infinite;
  }

  .error-image {
    color: #dc3545;
  }

  @keyframes pulse {
    0%, 100% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
  }

  .crop-info-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.3), transparent);
    color: white;
    padding: 0.25rem 0.375rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    pointer-events: none;
    max-height: 30px; /* 限制覆盖层高度 */
    box-sizing: border-box;
  }

  .crop-name {
    font-size: 0.65rem;
    font-weight: 500;
    color: white;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    flex: 1;
    margin-right: 0.25rem;
  }

  .crop-coords {
    font-size: 0.6rem;
    color: rgba(255, 255, 255, 0.9);
    font-family: monospace;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    flex-shrink: 0;
  }

  /* 空状态样式 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
    min-height: 300px;
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--theme-text);
    margin-bottom: 0.5rem;
  }

  .empty-description {
    font-size: 0.875rem;
    color: var(--theme-text-secondary);
    line-height: 1.5;
    max-width: 300px;
  }
</style>
