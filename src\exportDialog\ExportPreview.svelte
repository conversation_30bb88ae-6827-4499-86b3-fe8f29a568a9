<script lang="ts">
  /**
   * 导出预览组件 - 使用统一Canvas预览组件 + 裁切区域覆盖层
   */

  import type { ExportItem } from './exportTypes';
  import CanvasPreview, { type PreviewExtensions, type Transform } from '../components/ui/CanvasPreview.svelte';

  interface Props {
    items: ExportItem[];
    selectedItemIndex?: number;
  }

  let {
    items,
    selectedItemIndex = $bindable(0)
  }: Props = $props();

  // 当前选中的导出项
  const currentItem = $derived(() => {
    const item = items[selectedItemIndex] || null;
    console.log('🔍 ExportPreview: currentItem', {
      selectedItemIndex,
      itemsLength: items.length,
      hasItem: !!item,
      itemName: item?.name,
      hasResource: !!item?.resource,
      resourceName: item?.resource?.name,
      resourceType: item?.resource?.type,
      hasBuffer: !!item?.resource?.buffer,
      bufferSize: item?.resource?.buffer?.byteLength
    });
    return item;
  });

  // 当前资源
  const currentResource = $derived(() => {
    const item = currentItem();
    const resource = item?.resource;
    console.log('🔍 ExportPreview: currentResource', {
      hasItem: !!item,
      hasResource: !!resource,
      resourceName: resource?.name,
      resourceType: resource?.type,
      hasBuffer: !!resource?.buffer,
      bufferSize: resource?.buffer?.byteLength
    });
    return resource;
  });

  // 扩展功能：渲染裁切区域覆盖层
  const previewExtensions: PreviewExtensions = {
    onRender: (ctx: CanvasRenderingContext2D, transform: Transform) => {
      const item = currentItem();
      if (!item?.cropAreas || item.cropAreas.length === 0) return;

      console.log('🔍 ExportPreview: 渲染裁切区域', {
        transform,
        cropAreasCount: item.cropAreas.length
      });

      // 绘制裁切区域
      ctx.save();

      item.cropAreas.forEach((area, index) => {
        // 正确计算裁切区域在Canvas上的位置
        const x = transform.translateX + area.x * transform.scale;
        const y = transform.translateY + area.y * transform.scale;
        const width = area.width * transform.scale;
        const height = area.height * transform.scale;

        // 绘制半透明填充
        ctx.fillStyle = 'rgba(255, 0, 0, 0.1)';
        ctx.fillRect(x, y, width, height);

        // 绘制边框
        ctx.strokeStyle = '#ff0000';
        ctx.lineWidth = 2;
        ctx.setLineDash([5, 5]);
        ctx.strokeRect(x, y, width, height);

        // 绘制标签
        ctx.fillStyle = '#ff0000';
        ctx.font = `${Math.max(12, 12 * transform.scale)}px Arial`;
        ctx.fillText(
          area.name || `区域${index + 1}`,
          x + 4,
          y + 16
        );
      });

      ctx.restore();
    }
  };
</script>

<div class="export-preview">
  <div class="preview-header">
    <h4>预览</h4>
  </div>

  <div class="preview-content">
    {#if currentResource()}
      <!-- 使用统一的Canvas预览组件 -->
      <CanvasPreview
        resource={currentResource()}
        width={400}
        height={300}
        showControls={true}
        extensions={previewExtensions}
      />
    {:else if currentItem()}
      <div class="no-preview">
        <div class="no-preview-icon">🖼️</div>
        <span>图片数据未加载</span>
      </div>
    {:else}
      <div class="no-preview">
        <div class="no-preview-icon">📷</div>
        <span>请选择要导出的资源</span>
      </div>
    {/if}
  </div>

  <!-- 多个项目时的切换器 -->
  {#if items.length > 1}
    <div class="item-switcher">
      {#each items as item, index}
        <button
          class="item-btn"
          class:active={index === selectedItemIndex}
          onclick={() => selectedItemIndex = index}
          title={item.name}
        >
          {item.name}
        </button>
      {/each}
    </div>
  {/if}
</div>

<style>
  .export-preview {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--theme-surface-light);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius-large);
    overflow: hidden;
  }

  .preview-header {
    padding: var(--spacing-3);
    border-bottom: 1px solid var(--theme-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--theme-surface);
  }

  .preview-header h4 {
    margin: 0;
    font-size: var(--font-size-md);
    font-weight: 600;
    color: var(--theme-text);
  }

  .preview-content {
    flex: 1;
    position: relative;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.8);
  }

  .no-preview {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    color: var(--theme-text-secondary);
  }

  .no-preview-icon {
    font-size: 3rem;
    opacity: 0.5;
  }

  .item-switcher {
    padding: var(--spacing-2);
    border-top: 1px solid var(--theme-border);
    display: flex;
    gap: var(--spacing-1);
    overflow-x: auto;
    background: var(--theme-surface);
  }

  .item-btn {
    background: var(--theme-surface-light);
    border: 1px solid var(--theme-border);
    border-radius: var(--border-radius);
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-xs);
    color: var(--theme-text);
    cursor: pointer;
    transition: var(--transition-base);
    white-space: nowrap;
    flex-shrink: 0;
  }

  .item-btn:hover {
    background: var(--theme-surface);
    border-color: var(--theme-primary);
  }

  .item-btn.active {
    background: var(--theme-primary);
    color: var(--theme-text-inverse);
    border-color: var(--theme-primary);
  }
</style>
