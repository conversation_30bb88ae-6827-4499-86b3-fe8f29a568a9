/**
 * 虚拟网格滚动组件
 * 专门用于图片网格的高效渲染，支持动态列数和响应式布局
 */

<script lang="ts">
  import { onMount } from 'svelte';

  interface Props<T> {
    items: T[];
    itemWidth: number;
    itemHeight: number;
    containerWidth?: number;
    containerHeight: number;
    gap?: number;
    minColumns?: number;
    maxColumns?: number;
    overscan?: number;
    onItemRender?: (item: T, index: number) => void;
    onVisibleRangeChange?: (start: number, end: number) => void;
  }

  let {
    items,
    itemWidth,
    itemHeight,
    containerWidth = 0,
    containerHeight,
    gap = 16,
    minColumns = 1,
    maxColumns = 10,
    overscan = 2,
    onItemRender,
    onVisibleRangeChange
  }: Props<any> = $props();

  // 状态管理
  let scrollContainer: HTMLDivElement;
  let containerElement: HTMLDivElement;
  let scrollTop = $state(0);
  let isScrolling = $state(false);
  let scrollTimeout: number;
  let resizeObserver: ResizeObserver;

  // 计算属性
  const columns = $derived(() => {
    if (containerWidth === 0) return minColumns;
    const availableWidth = containerWidth - gap;
    const possibleColumns = Math.floor((availableWidth + gap) / (itemWidth + gap));
    return Math.max(minColumns, Math.min(maxColumns, possibleColumns));
  });

  const rows = $derived(Math.ceil(items.length / columns));
  const rowHeight = $derived(itemHeight + gap);
  const totalHeight = $derived(rows * rowHeight - gap);
  
  const visibleRowCount = $derived(Math.ceil(containerHeight / rowHeight));
  const startRow = $derived(Math.max(0, Math.floor(scrollTop / rowHeight) - overscan));
  const endRow = $derived(Math.min(rows - 1, startRow + visibleRowCount + overscan * 2));
  
  const startIndex = $derived(startRow * columns);
  const endIndex = $derived(Math.min(items.length - 1, (endRow + 1) * columns - 1));
  const visibleItems = $derived(items.slice(startIndex, endIndex + 1));
  const offsetY = $derived(startRow * rowHeight);

  // 监听可见范围变化
  $effect(() => {
    if (onVisibleRangeChange) {
      onVisibleRangeChange(startIndex, endIndex);
    }
  });

  // 滚动事件处理
  function handleScroll(event: Event) {
    const target = event.target as HTMLDivElement;
    scrollTop = target.scrollTop;
    isScrolling = true;

    // 清除之前的定时器
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }

    // 设置滚动结束检测
    scrollTimeout = setTimeout(() => {
      isScrolling = false;
    }, 150);

    // 触发项目渲染回调
    if (onItemRender) {
      visibleItems.forEach((item, index) => {
        onItemRender(item, startIndex + index);
      });
    }
  }

  // 获取项目的网格位置
  function getItemPosition(index: number) {
    const row = Math.floor(index / columns);
    const col = index % columns;
    return {
      x: col * (itemWidth + gap),
      y: row * rowHeight,
      row,
      col
    };
  }

  // 滚动到指定索引
  function scrollToIndex(index: number, behavior: ScrollBehavior = 'smooth') {
    if (!scrollContainer) return;

    const position = getItemPosition(index);
    scrollContainer.scrollTo({
      top: position.y,
      behavior
    });
  }

  // 滚动到指定项目
  function scrollToItem(item: any, behavior: ScrollBehavior = 'smooth') {
    const index = items.indexOf(item);
    if (index !== -1) {
      scrollToIndex(index, behavior);
    }
  }

  // 获取当前可见范围
  function getVisibleRange() {
    return {
      start: startIndex,
      end: endIndex,
      startRow,
      endRow,
      items: visibleItems,
      columns
    };
  }

  // 组件挂载时设置ResizeObserver
  onMount(() => {
    if (containerElement) {
      resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          containerWidth = entry.contentRect.width;
        }
      });
      resizeObserver.observe(containerElement);
    }

    return () => {
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
      if (scrollTimeout) {
        clearTimeout(scrollTimeout);
      }
    };
  });

  // 暴露方法给父组件
  export { scrollToIndex, scrollToItem, getVisibleRange, getItemPosition };
</script>

<div bind:this={containerElement} class="virtual-grid-wrapper">
  <div
    bind:this={scrollContainer}
    class="virtual-grid-container"
    style="height: {containerHeight}px;"
    onscroll={handleScroll}
  >
    <!-- 总高度占位符 -->
    <div class="virtual-grid-spacer" style="height: {totalHeight}px;">
      <!-- 可见项目容器 -->
      <div
        class="virtual-grid-content"
        class:scrolling={isScrolling}
        style="transform: translateY({offsetY}px);"
      >
        <!-- 渲染可见的行 -->
        {#each Array(endRow - startRow + 1) as _, rowIndex}
          {@const currentRow = startRow + rowIndex}
          {@const rowStartIndex = currentRow * columns}
          {@const rowEndIndex = Math.min(rowStartIndex + columns - 1, items.length - 1)}
          
          {#if rowStartIndex < items.length}
            <div 
              class="virtual-grid-row" 
              style="height: {itemHeight}px; margin-bottom: {gap}px;"
              data-row={currentRow}
            >
              {#each Array(columns) as _, colIndex}
                {@const itemIndex = rowStartIndex + colIndex}
                {#if itemIndex <= rowEndIndex && itemIndex < items.length}
                  <div
                    class="virtual-grid-item"
                    style="width: {itemWidth}px; height: {itemHeight}px; margin-right: {colIndex < columns - 1 ? gap : 0}px;"
                    data-index={itemIndex}
                  >
                    <slot item={items[itemIndex]} index={itemIndex} />
                  </div>
                {/if}
              {/each}
            </div>
          {/if}
        {/each}
      </div>
    </div>
  </div>

  <!-- 网格信息显示（开发模式） -->
  {#if import.meta.env.DEV}
    <div class="virtual-grid-debug">
      <small>
        网格: {columns}列 × {rows}行 | 
        可见: {startRow}-{endRow}行 ({startIndex}-{endIndex}) | 
        总计: {items.length}项
      </small>
    </div>
  {/if}
</div>

<style>
  .virtual-grid-wrapper {
    position: relative;
    width: 100%;
  }

  .virtual-grid-container {
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    width: 100%;
  }

  .virtual-grid-spacer {
    position: relative;
    width: 100%;
  }

  .virtual-grid-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    will-change: transform;
  }

  .virtual-grid-content.scrolling {
    pointer-events: none; /* 滚动时禁用交互，提升性能 */
  }

  .virtual-grid-row {
    display: flex;
    align-items: flex-start;
    width: 100%;
  }

  .virtual-grid-item {
    flex-shrink: 0;
    box-sizing: border-box;
  }

  .virtual-grid-debug {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 10px;
    pointer-events: none;
    z-index: 1000;
  }

  /* 优化滚动性能 */
  .virtual-grid-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 自定义滚动条 */
  .virtual-grid-container::-webkit-scrollbar {
    width: 8px;
  }

  .virtual-grid-container::-webkit-scrollbar-track {
    background: var(--theme-surface-light, #f1f5f9);
    border-radius: 4px;
  }

  .virtual-grid-container::-webkit-scrollbar-thumb {
    background: var(--theme-border-dark, #cbd5e1);
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  .virtual-grid-container::-webkit-scrollbar-thumb:hover {
    background: var(--theme-primary, #3b82f6);
  }
</style>
