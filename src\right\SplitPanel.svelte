<script lang="ts">
  /**
   * 拆分设置面板组件
   * 专门用于设置SplitSettings接口中定义的属性
   */

  import { resourceStore } from '../stores/resourceStore';
  import type { SplitSettings, ImageResource, ResourceItem, CropArea, CropData } from '../types/imageType';
  import { exportImageResource, showExportSuccess, showExportError } from './exportUtils';

  // 导入国际化
  import { _ } from '../lib/i18n';

  interface Props {
    currentResource: ResourceItem | null;
  }

  let { currentResource }: Props = $props();

  // 默认的拆分设置
  const defaultSplitSettings: SplitSettings = {
    cellWidth: 32,
    cellHeight: 32,
    gridMode: false,
    padding: 0,
    spacing: 0,
    autoDetect: false,
    minWidth: 1,
    minHeight: 1
  };

  // 当前的拆分设置
  let splitSettings = $state<SplitSettings>({ ...defaultSplitSettings });

  // 🎯 只监听currentResource的ID变化，避免循环依赖
  let lastResourceId = $state<string | null>(null);

  // 🎯 导出相关状态
  let isExporting = $state(false);
  let exportMessage = $state('');

  // 监听currentResource变化，仅在资源ID变化时加载设置
  $effect(() => {
    const currentResourceId = currentResource?.id || null;

    // 只有当资源ID真正变化时才更新设置
    if (lastResourceId !== currentResourceId) {
      lastResourceId = currentResourceId;

      if (currentResource?.type === 'image') {
        const imageResource = currentResource as ImageResource;

        if (imageResource.splitSettings) {
          // 从资源中加载已保存的设置
          splitSettings = { ...imageResource.splitSettings };
          console.log('📥 SplitPanel: 从resource.splitSettings加载设置', {
            resourceName: imageResource.name,
            resourceId: imageResource.id,
            settings: splitSettings
          });
        } else {
          // 使用默认设置
          splitSettings = { ...defaultSplitSettings };
          console.log('📥 SplitPanel: 使用默认设置', {
            resourceName: imageResource.name,
            resourceId: imageResource.id,
            settings: splitSettings
          });
        }
      } else {
        // 非图片资源，重置为默认设置
        splitSettings = { ...defaultSplitSettings };
        console.log('📥 SplitPanel: 重置为默认设置（非图片资源）');
      }
    }
  });

  // 生成cropData的函数
  function generateCropData(settings: SplitSettings, imageResource: ImageResource): CropData | null {
    if (!settings.gridMode || !imageResource.width || !imageResource.height) {
      return null;
    }

    const cropAreas: CropArea[] = [];
    const imageWidth = imageResource.width;
    const imageHeight = imageResource.height;

    // 计算可用区域（减去外边距）
    const availableWidth = imageWidth - settings.padding * 2;
    const availableHeight = imageHeight - settings.padding * 2;

    // 计算单元格步长（包含间距）
    const cellStepWidth = settings.cellWidth + settings.spacing;
    const cellStepHeight = settings.cellHeight + settings.spacing;

    // 计算行列数
    const cols = cellStepWidth > 0 ? Math.floor(availableWidth / cellStepWidth) : 0;
    const rows = cellStepHeight > 0 ? Math.floor(availableHeight / cellStepHeight) : 0;

    console.log('🎯 SplitPanel: 生成cropData', {
      imageSize: `${imageWidth}×${imageHeight}`,
      availableSize: `${availableWidth}×${availableHeight}`,
      cellSize: `${settings.cellWidth}×${settings.cellHeight}`,
      gridSize: `${cols}×${rows}`,
      totalCells: cols * rows
    });

    // 生成所有网格单元格
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const x = settings.padding + col * cellStepWidth;
        const y = settings.padding + row * cellStepHeight;

        // 确保不超出图片边界
        if (x + settings.cellWidth <= imageWidth && y + settings.cellHeight <= imageHeight) {
          cropAreas.push({
            id: `cell_${row}_${col}`,
            x,
            y,
            width: settings.cellWidth,
            height: settings.cellHeight,
            name: `${col}_${row}_${settings.cellWidth}_${settings.cellHeight}`
          });
        }
      }
    }

    // 🎯 返回完整的CropData对象
    return {
      areas: cropAreas,
      cellWidth: settings.cellWidth,
      cellHeight: settings.cellHeight,
      gridMode: settings.gridMode,
      gridRows: rows,
      gridCols: cols,
      padding: settings.padding,
      spacing: settings.spacing,
      autoTrim: settings.autoDetect,
      lastModified: new Date()
    };
  }

  // 更新设置的通用函数
  function updateSetting(key: keyof SplitSettings, value: any) {
    console.log('🎯 SplitPanel: 更新设置', { key, value });

    // 处理 autoDetect 和 gridMode 的互斥逻辑
    let newSettings = { ...splitSettings, [key]: value };

    if (key === 'autoDetect' && value === true) {
      // 开启自动检测时，关闭网格模式
      newSettings.gridMode = false;
      console.log('🎯 SplitPanel: 开启自动检测，关闭网格模式');
    } else if (key === 'gridMode' && value === true) {
      // 🎯 开启网格模式时，关闭自动检测，并设置默认32×32
      newSettings.autoDetect = false;
      newSettings.cellWidth = 32;
      newSettings.cellHeight = 32;
      console.log('🎯 SplitPanel: 开启网格模式，关闭自动检测，设置32×32');
    } else if (key === 'gridMode' && value === false) {
      // 🎯 关闭网格模式时，清除cropData
      console.log('🎯 SplitPanel: 关闭网格模式，将清除cropData');
    }

    // 更新本地状态
    splitSettings = newSettings;

    // 直接更新resourceStore中的selectedResource
    if (currentResource?.type === 'image') {
      const imageResource = currentResource as ImageResource;

      // 🎯 根据网格模式决定是否生成cropData
      let cropData: CropData | null = null;
      if (newSettings.gridMode) {
        // 只有在网格模式开启时才生成cropData
        cropData = generateCropData(newSettings, imageResource);
      }

      // 直接修改selectedResource并触发store更新
      resourceStore.update(state => {
        if (state.selectedResource?.id === imageResource.id && state.selectedResource.type === 'image') {
          const updatedResource = state.selectedResource as ImageResource;
          updatedResource.splitSettings = newSettings;

          // 🎯 设置或清除cropData
          if (newSettings.gridMode && cropData) {
            updatedResource.cropData = cropData;
          } else {
            // 关闭网格模式时删除cropData
            updatedResource.cropData = undefined;
          }

          updatedResource.updatedAt = new Date().toISOString();
          return {
            ...state,
          };
        }
        return state;
      });

      console.log('💾 SplitPanel: 设置已保存到selectedResource', {
        resourceId: imageResource.id,
        resourceName: imageResource.name,
        updatedSetting: { [key]: value },
        gridMode: newSettings.gridMode,
        cropDataCount: cropData?.areas.length || 0,
        cropDataAction: newSettings.gridMode ? '生成cropData' : '删除cropData'
      });
    }
  }

  // 重置所有设置
  function resetSettings() {
    console.log('🔄 SplitPanel: 重置所有设置');

    splitSettings = { ...defaultSplitSettings };

    if (currentResource?.type === 'image') {
      const imageResource = currentResource as ImageResource;

      // 直接更新resourceStore中的selectedResource
      resourceStore.update(state => {
        if (state.selectedResource?.id === imageResource.id) {
          const updatedResource = {
            ...state.selectedResource,
            splitSettings: undefined, // 清除保存的设置
            cropData: undefined, // 🎯 同时清除cropData
            updatedAt: new Date().toISOString()
          } as ImageResource;

          return {
            ...state,
            selectedResource: updatedResource
          };
        }
        return state;
      });

      console.log('🗑️ SplitPanel: 已清除selectedResource中的设置和cropData', {
        resourceId: imageResource.id,
        resourceName: imageResource.name
      });
    }
  }

  // 🎯 导出当前图片资源
  async function handleExport() {
    if (!currentResource || currentResource.type !== 'image') {
      console.warn('🎯 SplitPanel: 当前资源不是图片，无法导出');
      return;
    }

    const imageResource = currentResource as ImageResource;

    // 🎯 检查是否有裁切信息
    if (!imageResource.cropData || !imageResource.cropData.areas || imageResource.cropData.areas.length === 0) {
      console.warn('🎯 SplitPanel: 没有裁切信息，无法导出');
      exportMessage = $_('crop.noCropData');

      // 5秒后清除提示消息
      setTimeout(() => {
        exportMessage = '';
      }, 5000);
      return;
    }

    try {
      isExporting = true;
      exportMessage = $_('status.exporting');

      console.log('📤 SplitPanel: 开始导出图片资源', {
        resourceId: imageResource.id,
        resourceName: imageResource.name,
        hasCropData: !!imageResource.cropData,
        cropAreasCount: imageResource.cropData.areas.length
      });

      const success = await exportImageResource(imageResource);

      if (success) {
        exportMessage = $_('export.success');
        showExportSuccess(1, $_('dialog.selectFolder'));

        // 3秒后清除成功消息
        setTimeout(() => {
          exportMessage = '';
        }, 3000);
      } else {
        exportMessage = $_('export.failed');
        showExportError($_('error.exportFailed'));

        // 5秒后清除错误消息
        setTimeout(() => {
          exportMessage = '';
        }, 5000);
      }

    } catch (error) {
      console.error('❌ SplitPanel: 导出失败', error);
      exportMessage = $_('export.failed');
      showExportError(error instanceof Error ? error.message : $_('error.unknown'));

      // 5秒后清除错误消息
      setTimeout(() => {
        exportMessage = '';
      }, 5000);
    } finally {
      isExporting = false;
    }
  }

  // 预设尺寸选项
  const presetSizes = [
    { name: '16×16', width: 16, height: 16 },
    { name: '32×32', width: 32, height: 32 },
    { name: '48×48', width: 48, height: 48 },
    { name: '64×64', width: 64, height: 64 },
    { name: '128×128', width: 128, height: 128 },
    { name: '256×256', width: 256, height: 256 }
  ];

  // 应用预设尺寸
  function applyPresetSize(preset: { width: number; height: number }) {
    updateSetting('cellWidth', preset.width);
    updateSetting('cellHeight', preset.height);
    console.log('🎯 SplitPanel: 应用预设尺寸', preset);
  }


</script>

<div class="split-panel">
  {#if currentResource?.type === 'image'}
    <!-- 拆分设置 -->
    <div class="setting-group">
      <div class="group-title">{$_('crop.title')}
              <!-- cropData信息显示 -->
      {#if currentResource?.type === 'image' && (currentResource as ImageResource).cropData}
      <div class="crop-info">
        <div class="info-item">
          📐 {$_('crop.grid')}: {(currentResource as ImageResource).cropData?.gridCols}×{(currentResource as ImageResource).cropData?.gridRows}
        </div>
        <div class="info-item">
          📁 {$_('crop.areas')}: {(currentResource as ImageResource).cropData?.areas.length || 0}
        </div>
      </div>
    {/if}
      </div>

      <!-- 模式选择 -->
      <div class="mode-options">
        <label class="mode-option">
          <input
            type="checkbox"
            bind:checked={splitSettings.gridMode}
            onchange={() => updateSetting('gridMode', splitSettings.gridMode)}
          />
          <span class="mode-label">📐 {$_('crop.gridMode')}</span>
        </label>

        <label class="mode-option">
          <input
            type="checkbox"
            bind:checked={splitSettings.autoDetect}
            onchange={() => updateSetting('autoDetect', splitSettings.autoDetect)}
          />
          <span class="mode-label">🤖 {$_('crop.autoDetect')}</span>
        </label>
      </div>

      <!-- 预设尺寸 -->
      <div class="preset-sizes">
        <div class="preset-label">{$_('ui.quickSettings')}:</div>
        <div class="preset-buttons">
          {#each presetSizes as preset}
            <button
              class="preset-btn"
              onclick={() => applyPresetSize(preset)}
              class:active={splitSettings.cellWidth === preset.width && splitSettings.cellHeight === preset.height}
            >
              {preset.name}
            </button>
          {/each}
        </div>
      </div>

      <!-- 单元格尺寸 -->
      <div class="input-row">
        <div class="input-group">
          <label for="cellWidth">{$_('crop.cellWidth')}</label>
          <input
            id="cellWidth"
            type="number"
            min="1"
            max="2048"
            bind:value={splitSettings.cellWidth}
            onchange={() => updateSetting('cellWidth', splitSettings.cellWidth)}
          />
          <span class="unit">px</span>
        </div>

        <div class="input-group">
          <label for="cellHeight">{$_('crop.cellHeight')}</label>
          <input
            id="cellHeight"
            type="number"
            min="1"
            max="2048"
            bind:value={splitSettings.cellHeight}
            onchange={() => updateSetting('cellHeight', splitSettings.cellHeight)}
          />
          <span class="unit">px</span>
        </div>
      </div>

      <!-- 边距和间距 -->
      <div class="input-row">
        <div class="input-group">
          <label for="padding">{$_('crop.padding')}</label>
          <input
            id="padding"
            type="number"
            min="0"
            max="100"
            bind:value={splitSettings.padding}
            onchange={() => updateSetting('padding', splitSettings.padding)}
          />
          <span class="unit">px</span>
        </div>

        <div class="input-group">
          <label for="spacing">{$_('crop.spacing')}</label>
          <input
            id="spacing"
            type="number"
            min="0"
            max="100"
            bind:value={splitSettings.spacing}
            onchange={() => updateSetting('spacing', splitSettings.spacing)}
          />
          <span class="unit">px</span>
        </div>
      </div>


      <!-- 模式说明 -->
      <div class="mode-hint">
        {#if splitSettings.autoDetect}
          <span class="hint-text">🤖 {$_('crop.autoDetect')}: {$_('crop.autoDetectHint')}</span>
        {:else if splitSettings.gridMode}
          <span class="hint-text">📐 {$_('crop.gridMode')}: {$_('crop.gridModeHint')}</span>
        {:else}
          <span class="hint-text">💡 {$_('crop.selectModeHint')}</span>
        {/if}
      </div>
    </div>

    <!-- 🎯 参考AtlasMergeSettings的操作按钮布局 -->
    <div class="setting-actions">
      <button class="reset-button" onclick={resetSettings} disabled={isExporting}>
        {$_('actions.reset')}
      </button>

      <button class="export-button" onclick={handleExport} disabled={isExporting}>
        {#if isExporting}
          ⏳ {$_('status.exporting')}
        {:else}
          📤 {$_('export.croppedImages')}
        {/if}
      </button>
    </div>

    <!-- 导出状态消息 -->
    {#if exportMessage}
      <div class="export-message"
           class:success={exportMessage.includes('成功')}
           class:error={exportMessage.includes('失败')}
           class:warning={exportMessage.includes('没有裁切信息')}>
        {exportMessage}
      </div>
    {/if}
  {:else}
    <!-- 未选中图片资源时的提示 -->
    <div class="empty-state">
      <div class="empty-icon">🖼️</div>
      <div class="empty-text">请选择一个图片资源进行拆分设置</div>
    </div>
  {/if}
</div>

<style>
  .split-panel {
    padding: 1rem;
    background: var(--theme-surface);
    color: var(--theme-text);
    height: 100%;
    overflow-y: auto;
  }

  .setting-group {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--theme-background);
    border-radius: 6px;
    border: 1px solid var(--theme-border);
  }

  .group-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--theme-text);
    margin-bottom: 0.75rem;
    padding-bottom: 0.4rem;
    border-bottom: 1px solid var(--theme-border);
  }

  /* 模式选择 */
  .mode-options {
    display: flex;
    flex-direction: row;
    gap: 1rem;
    margin-bottom: 0.75rem;
  }

  .mode-option {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    cursor: pointer;
    padding: 0.3rem 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  .mode-option:hover {
    background: var(--theme-hover);
  }

  .mode-option input[type="checkbox"] {
    margin: 0;
  }

  .mode-label {
    font-size: 0.85rem;
    color: var(--theme-text);
  }

  .mode-hint {
    padding: 0.5rem;
    background: var(--theme-surface);
    border-radius: 4px;
    border-left: 3px solid var(--theme-primary);
    margin-top: 0.5rem;
  }

  .hint-text {
    font-size: 0.8rem;
    color: var(--theme-text-secondary);
    line-height: 1.3;
  }

  /* 预设尺寸 */
  .preset-sizes {
    margin-bottom: 0.75rem;
  }

  .preset-label {
    font-size: 0.85rem;
    color: var(--theme-text-secondary);
    margin-bottom: 0.4rem;
  }

  .preset-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .preset-btn {
    padding: 0.4rem 0.8rem;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    color: var(--theme-text);
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .preset-btn:hover {
    background: var(--theme-hover);
    border-color: var(--theme-primary);
  }

  .preset-btn.active {
    background: var(--theme-primary);
    color: white;
    border-color: var(--theme-primary);
  }

  /* 输入行 */
  .input-row {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    width: 100%;
    box-sizing: border-box;
  }

  .input-group {
    flex: 1;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 0.4rem;
    min-width: 0; /* 允许收缩 */
  }

  .input-group label {
    font-size: 0.75rem;
    color: var(--theme-text-secondary);
    font-weight: 500;
    white-space: nowrap;
    flex-shrink: 0; /* 标签不收缩 */
    width: 60px; /* 固定标签宽度 */
  }

  .input-group input {
    width: 50px; /* 固定输入框宽度 */
    padding: 0.3rem 0.4rem;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    color: var(--theme-text);
    font-size: 0.8rem;
    text-align: center; /* 数字居中显示 */
  }

  .input-group input:focus {
    outline: none;
    border-color: var(--theme-primary);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }

  .unit {
    font-size: 0.7rem;
    color: var(--theme-text-secondary);
    white-space: nowrap;
    flex-shrink: 0; /* 单位不收缩 */
    width: 20px; /* 固定单位宽度 */
  }

  /* 🎯 参考AtlasMergeSettings的操作区域样式 */
  .setting-actions {
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid var(--theme-border);
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  /* 🎯 参考AtlasMergeSettings的按钮样式 */
  .reset-button {
    width: 100%;
    padding: 0.75rem;
    background: var(--theme-surface);
    border: 1px solid var(--theme-border);
    border-radius: 4px;
    color: var(--theme-text);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .reset-button:hover:not(:disabled) {
    background: var(--theme-hover, var(--theme-surface));
    border-color: var(--theme-primary);
  }

  .reset-button:disabled {
    background: var(--theme-background);
    color: var(--theme-text-secondary);
    cursor: not-allowed;
    opacity: 0.6;
  }

  .export-button {
    width: 100%;
    padding: 0.75rem;
    background: var(--theme-primary, #3b82f6);
    border: 1px solid var(--theme-primary, #3b82f6);
    border-radius: 4px;
    color: white;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .export-button:hover:not(:disabled) {
    background: var(--theme-primary-dark, #2563eb);
    border-color: var(--theme-primary-dark, #2563eb);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  .export-button:disabled {
    background: var(--theme-background);
    border-color: var(--theme-border);
    color: var(--theme-text-secondary);
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
  }

  /* 🎯 参考AtlasMergeSettings的消息样式 */
  .export-message {
    margin-top: 0.5rem;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    text-align: center;
    font-weight: 500;
    animation: slideIn 0.3s ease-out;
  }

  .export-message.success {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.3);
  }

  .export-message.warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.3);
  }

  .export-message.error {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.3);
  }

  /* cropData信息显示 */
  .crop-info {
    padding: 0.75rem;
    background: var(--theme-surface);
    border-radius: 4px;
    border: 1px solid var(--theme-border);
    margin-top: 0.5rem;
  }

  .info-item {
    font-size: 0.85rem;
    color: var(--theme-text);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .info-item:last-child {
    margin-bottom: 0;
  }

  /* 空状态 */
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
    color: var(--theme-text-secondary);
  }

  .empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 1rem;
    line-height: 1.5;
  }

  /* 滚动条样式 */
  .split-panel::-webkit-scrollbar {
    width: 8px;
  }

  .split-panel::-webkit-scrollbar-track {
    background: var(--theme-surface);
    border-radius: 4px;
  }

  .split-panel::-webkit-scrollbar-thumb {
    background: var(--theme-border);
    border-radius: 4px;
    transition: background 0.2s ease;
  }

  .split-panel::-webkit-scrollbar-thumb:hover {
    background: var(--theme-text-secondary);
  }

  /* 🎯 添加动画 */
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
