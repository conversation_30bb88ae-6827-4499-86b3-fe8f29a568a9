/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { View } from "./View";
import { Selection } from "../base/Selection";
import * as paper from "paper";

interface PivotPoint {
  x: number;
  y: number;
}

export class SelectionView extends View {
  protected layer: paper.Layer;
  protected _widget: any; // paper.SelectionWidget type from paper-ext
  protected _model: Selection;
  protected dirty: boolean;

  /**
   * Create a new Selection view.
   */
  constructor(model: Selection) {
    super(model);

    this.layer = new this.paper.Layer();

    this._widget = new (paper as any).SelectionWidget({
      layer: this.layer,
    });
    (this.paper.project as any).selectionWidget = this._widget;
  }

  /**
   * The selection widget
   */
  get widget(): any {
    if (this.dirty) {
      this.dirty = false;
      this.render();
    }
    return this._widget;
  }

  /**
   * Apply changes from the widget to the model
   */
  applyChanges(): void {
    this.model.widgetRotation = this.widget.rotation;
    this.model.pivotPoint = {
      x: this.widget.pivot.x,
      y: this.widget.pivot.y,
    };
  }

  get x(): number {
    return this.widget.position.x;
  }

  set x(x: number) {
    this.widget.position = new paper.Point(x, this.widget.position.y);
    this.model.project.view.applyChanges();
  }

  get y(): number {
    return this.widget.position.y;
  }

  set y(y: number) {
    this.widget.position = new paper.Point(this.widget.position.x, y);
    this.model.project.view.applyChanges();
  }

  get width(): number {
    return this.widget.width;
  }

  set width(width: number) {
    this.widget.width = width;
    this.model.project.view.applyChanges();
  }

  get height(): number {
    return this.widget.height;
  }

  set height(height: number) {
    this.widget.height = height;
    this.model.project.view.applyChanges();
  }

  get rotation(): number {
    return this.widget.rotation;
  }

  set rotation(rotation: number) {
    this.widget.rotation = rotation;
    this.model.project.view.applyChanges();
    this.model.widgetRotation = rotation;
  }

  /**
   * Flip the selection horizontally
   */
  flipHorizontally(): void {
    this.widget.flipHorizontally();
    this.model.project.view.applyChanges();
  }

  /**
   * Flip the selection vertically
   */
  flipVertically(): void {
    this.widget.flipVertically();
    this.model.project.view.applyChanges();
  }

  /**
   * Send selected objects to back
   */
  sendToBack(): void {
    (paper as any).OrderingUtils.sendToBack(this._getSelectedObjectViews());
    this.model.project.view.applyChanges();
  }

  /**
   * Bring selected objects to front
   */
  bringToFront(): void {
    (paper as any).OrderingUtils.bringToFront(this._getSelectedObjectViews());
    this.model.project.view.applyChanges();
  }

  /**
   * Move selected objects forwards
   */
  moveForwards(): void {
    (paper as any).OrderingUtils.moveForwards(this._getSelectedObjectViews());
    this.model.project.view.applyChanges();
  }

  /**
   * Move selected objects backwards
   */
  moveBackwards(): void {
    (paper as any).OrderingUtils.moveBackwards(this._getSelectedObjectViews());
    this.model.project.view.applyChanges();
  }

  render(): void {
    this._widget.build({
      boxRotation: this.model.widgetRotation,
      items: this._getSelectedObjectViews(),
      pivot: new paper.Point(this.model.pivotPoint.x, this.model.pivotPoint.y),
    });
  }

  protected _getSelectedObjects(): any[] {
    return this.model.getSelectedObjects("Canvas");
  }

  protected _getObjectViews(objects: any[]): any[] {
    return objects.map((object) => {
      return object.view.item || object.view.group;
    });
  }

  protected _getObjectsBounds(objects: any[]): paper.Rectangle {
    return this.widget._calculateBoundingBoxOfItems(
      this._getObjectViews(objects)
    );
  }

  protected _getSelectedObjectViews(): any[] {
    return this._getObjectViews(this._getSelectedObjects());
  }

  protected _getSelectedObjectsBounds(): paper.Rectangle {
    return this._getObjectsBounds(this._getSelectedObjects());
  }
}
