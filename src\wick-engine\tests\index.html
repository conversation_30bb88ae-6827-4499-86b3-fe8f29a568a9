<html>
<head>
    <meta charset="utf-8">
    <title>Wick Engine Tests</title>
    <link href="mocha/mocha.css" rel="stylesheet" />
</head>
<body>
  <div id='interact-message'>Please click anywhere on the page <br/><em>(sounds won't test properly unless the page is interacted with)</em></div>
  <div id="mocha"></div>
</body>

<!-- Wick Engine -->

<script src="../dist/wickengine.js"></script>

<!-- Testing Utils -->

<script src="util/FileSaver.js"></script>
<script src="util/TestUtils.js"></script>

<!-- Chai + Mocha testing framework -->

<script src="chai/chai.js"></script>
<script src="mocha/mocha.js"></script>

<!-- Load all tests -->

<script>mocha.setup('bdd')</script>
<script src="test.Wick.FileCache.js"></script>
<script src="test.Wick.ObjectCache.js"></script>
<script src="test.Wick.BuiltinAssets.js"></script>
<script src="test.Wick.Base.js"></script>
<script src="test.Wick.Tickable.js"></script>
<script src="test.Wick.Path.js"></script>
<script src="test.Wick.Frame.js"></script>
<script src="test.Wick.Layer.js"></script>
<script src="test.Wick.Clip.js"></script>
<script src="test.Wick.Button.js"></script>
<script src="test.Wick.FileAsset.js"></script>
<script src="test.Wick.SoundAsset.js"></script>
<script src="test.Wick.ImageAsset.js"></script>
<script src="test.Wick.ImageSequence.js"></script>
<script src="test.Wick.FontAsset.js"></script>
<script src="test.Wick.ClipAsset.js"></script>
<script src="test.Wick.GIFAsset.js"></script>
<script src="test.Wick.Tween.js"></script>
<script src="test.Wick.Transformation.js"></script>
<script src="test.Wick.Color.js"></script>
<script src="test.Wick.Timeline.js"></script>
<script src="test.Wick.Project.js"></script>
<script src="test.Wick.WickFile.js"></script>
<script src="test.Wick.WickObjectFile.js"></script>
<script src="test.Wick.AutoSave.js"></script>
<script src="test.Wick.AudioTrack.js"></script>
<script src="test.Wick.Selection.js"></script>
<script src="test.Wick.History.js"></script>
<script src="test.Wick.Clipboard.js"></script>
<script src="test.Wick.View.Project.js"></script>
<script src="test.Wick.View.Selection.js"></script>
<script src="test.Wick.View.Clip.js"></script>
<script src="test.Wick.View.Timeline.js"></script>
<script src="test.Wick.View.Layer.js"></script>
<script src="test.Wick.View.Frame.js"></script>
<script src="test.Wick.View.Path.js"></script>
<script src="test.Wick.Tools.Brush.js"></script>
<script src="test.Wick.Tools.Cursor.js"></script>
<script src="test.Wick.Tools.Ellipse.js"></script>
<script src="test.Wick.Tools.Eraser.js"></script>
<script src="test.Wick.Tools.Eyedropper.js"></script>
<script src="test.Wick.Tools.FillBucket.js"></script>
<script src="test.Wick.Tools.Interact.js"></script>
<script src="test.Wick.Tools.Line.js"></script>
<script src="test.Wick.Tools.None.js"></script>
<script src="test.Wick.Tools.Pan.js"></script>
<script src="test.Wick.Tools.PathCursor.js"></script>
<script src="test.Wick.Tools.Pencil.js"></script>
<script src="test.Wick.Tools.Rectangle.js"></script>
<script src="test.Wick.Tools.Text.js"></script>
<script src="test.Wick.Tools.Zoom.js"></script>
<script src="test.Wick.HTMLExport.js"></script>
<script src="test.Wick.HTMLPreview.js"></script>
<script src="test.Wick.ZIPExport.js"></script>
<script src="test.Wick.GUIElement.Project.js"></script>

<script src="paper-ext/test.Layer.erase.js"></script>
<script src="paper-ext/test.Paper.hole.js"></script>
<script src="paper-ext/test.Paper.SelectionWidget.js"></script>
<script src="paper-ext/test.Path.potrace.js"></script>
<script src="paper-ext/test.TextItem.edit.js"></script>
<script src="paper-ext/test.View.pressure.js"></script>
<script src="paper-ext/test.View.gestures.js"></script>
<script src="paper-ext/test.View.scrollToZoom.js"></script>

<!--<script src="test.performance.js"></script>-->

<!-- Run tests -->

<script>
  window.onload = () => {
    localforage.config({
        name        : 'WickEditorTests',
        description : 'Live Data storage of the Wick Editor test suite.'
    });
    window.onmousedown = () => {
      window.onmousedown = () => {};
      document.getElementById('interact-message').style.display = 'none';
      window.expect = chai.expect;
      mocha.checkLeaks();
      mocha.run();
    }
    // if you get tired of clicking, uncomment this line:
    window.onmousedown();
  }
</script>

</html>
