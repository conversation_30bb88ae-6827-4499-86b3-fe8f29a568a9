/**
 * 图集管理Store - 专门管理图集合并的数据
 */

import { writable } from 'svelte/store';
import type { AtlasResource, ResourceItem, LayoutSettings } from '../types/imageType';

// 图集Store状态接口
interface AtlasStoreState {
  atlases: AtlasResource[];
  selectedAtlas: AtlasResource | null;
}

// 添加资源到图集的结果类型
export interface AddResourceResult {
  success: boolean;
  reason?: 'duplicate' | 'invalid_type' | 'atlas_not_found' | 'unknown_error';
  message?: string;
  resourceId?: string;
}

// 创建响应式状态
const atlasStoreState = writable<AtlasStoreState>({
  atlases: [],
  selectedAtlas: null
});

// 图集计数器
let atlasCounter = 1;

/**
 * 图集管理Store
 */
export const atlasStore = {
  // 订阅状态变化
  subscribe: atlasStoreState.subscribe,

  // 获取当前状态
  get state() {
    let currentState: AtlasStoreState;
    atlasStoreState.subscribe(state => currentState = state)();
    return currentState!;
  },

  get atlases() {
    return this.state.atlases;
  },

  get selectedAtlas() {
    return this.state.selectedAtlas;
  },

  // 创建新图集
  createAtlas(name?: string, width?: number, height?: number): AtlasResource {
    const newAtlas: AtlasResource = {
      id: `atlas_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      name: name || `图集_${atlasCounter}`,
      type: 'atlas',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isLoaded: true,
      children: [],
      isExpanded: true,  // 新创建的图集默认展开
      // 🎯 设置默认布局设置
      layoutSettings: {
        padding: 10,
        spacing: 5,
        algorithm: 'maxrects',
        powerOfTwo: false,
        allowRotation: false,
        maxWidth: width || 1024,
        maxHeight: height || 1024
      }
    };

    // 如果提供了尺寸，则添加canvas信息
    if (width && height) {
      newAtlas.canvas = {
        width,
        height,
        backgroundColor: '#ffffff'
      };
    }

    atlasStoreState.update(state => ({
      ...state,
      atlases: [...state.atlases, newAtlas]
    }));

    atlasCounter++;

    console.log('✅ AtlasStore: 创建新图集', {
      atlasId: newAtlas.id,
      atlasName: newAtlas.name,
      canvasSize: newAtlas.canvas ? `${newAtlas.canvas.width}x${newAtlas.canvas.height}` : '未设置',
      totalAtlases: this.state.atlases.length
    });

    return newAtlas;
  },

  // 删除图集
  deleteAtlas(atlasId: string): boolean {
    const currentState = this.state;
    const index = currentState.atlases.findIndex(atlas => atlas.id === atlasId);
    if (index > -1) {
      const deletedAtlas = currentState.atlases[index];

      atlasStoreState.update(state => ({
        ...state,
        atlases: state.atlases.filter(atlas => atlas.id !== atlasId),
        selectedAtlas: state.selectedAtlas?.id === atlasId ? null : state.selectedAtlas
      }));

      console.log('🗑️ AtlasStore: 删除图集', {
        atlasId,
        atlasName: deletedAtlas.name,
        remainingAtlases: this.state.atlases.length
      });

      return true;
    }
    return false;
  },

  // 选择图集
  async selectAtlas(atlas: AtlasResource | null): Promise<void> {
    // 🎯 先清理selectedResource，确保状态一致性
    try {
      const { resourceActions } = await import('./resourceStore');
      resourceActions.clearSelection();

      console.log('🧹 AtlasStore: 清理selectedResource，准备选择图集', {
        atlasId: atlas?.id,
        atlasName: atlas?.name
      });
    } catch (error) {
      console.warn('⚠️ AtlasStore: 清理selectedResource时发生错误', error);
    }

    atlasStoreState.update(state => ({
      ...state,
      selectedAtlas: atlas
    }));

    if (atlas) {
      console.log('🎯 AtlasStore: 选择图集', {
        atlasId: atlas.id,
        atlasName: atlas.name
      });
    } else {
      console.log('🎯 AtlasStore: 取消选择图集');
    }
  },

  // 检查资源是否已存在于图集中
  isResourceExistsInAtlas(atlasId: string, resource: ResourceItem): boolean {
    const atlas = this.state.atlases.find(a => a.id === atlasId);
    if (!atlas) return false;

    // 检查是否有相同名称和路径的资源
    return atlas.children.some(child => {
      // 对于裁切图片，比较原始资源ID和裁切区域信息
      if (resource.splitInfo && child.splitInfo) {
        return resource.splitInfo.parentId === child.splitInfo.parentId &&
               resource.splitInfo.region.x === child.splitInfo.region.x &&
               resource.splitInfo.region.y === child.splitInfo.region.y &&
               resource.splitInfo.region.width === child.splitInfo.region.width &&
               resource.splitInfo.region.height === child.splitInfo.region.height;
      }

      // 对于普通图片，比较名称和路径
      return child.name === resource.name && child.path === resource.path;
    });
  },

  // 添加资源到图集（新版本，返回详细结果）
  addResourceToAtlas(atlasId: string, resource: ResourceItem): AddResourceResult {
    const atlas = this.state.atlases.find(a => a.id === atlasId);

    if (!atlas) {
      return {
        success: false,
        reason: 'atlas_not_found',
        message: 'Atlas not found'
      };
    }

    if (resource.type !== 'image') {
      return {
        success: false,
        reason: 'invalid_type',
        message: 'Only image resources can be added to atlas'
      };
    }

    // 检查是否已存在
    if (this.isResourceExistsInAtlas(atlasId, resource)) {
      return {
        success: false,
        reason: 'duplicate',
        message: `Resource "${resource.name}" already exists in this atlas`
      };
    }

    try {
      // 创建资源副本，为重复添加生成新的ID
      const resourceCopy = {
        ...resource,
        id: `${resource.id}_copy_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
      };

      atlasStoreState.update(state => ({
        ...state,
        atlases: state.atlases.map(a =>
          a.id === atlasId
            ? { ...a, children: [...a.children, resourceCopy], updatedAt: new Date().toISOString() }
            : a
        )
      }));

      console.log('➕ AtlasStore: 添加资源到图集', {
        atlasId,
        originalResourceId: resource.id,
        newResourceId: resourceCopy.id,
        resourceName: resource.name,
        childrenCount: atlas.children.length + 1
      });

      return {
        success: true,
        resourceId: resourceCopy.id
      };
    } catch (error) {
      console.error('❌ AtlasStore: 添加资源失败', error);
      return {
        success: false,
        reason: 'unknown_error',
        message: `Failed to add resource: ${error}`
      };
    }
  },

  // 保持向后兼容的旧版本方法
  addResourceToAtlasLegacy(atlasId: string, resource: ResourceItem): boolean {
    const result = this.addResourceToAtlas(atlasId, resource);
    return result.success;
  },

  // 从图集移除资源
  removeResourceFromAtlas(atlasId: string, resourceId: string): boolean {
    const atlas = this.state.atlases.find(a => a.id === atlasId);
    if (atlas) {
      const resourceIndex = atlas.children.findIndex(child => child.id === resourceId);
      if (resourceIndex > -1) {
        const removedResource = atlas.children[resourceIndex];

        atlasStoreState.update(state => ({
          ...state,
          atlases: state.atlases.map(a =>
            a.id === atlasId
              ? {
                  ...a,
                  children: a.children.filter(child => child.id !== resourceId),
                  updatedAt: new Date().toISOString()
                }
              : a
          )
        }));

        console.log('➖ AtlasStore: 从图集移除资源', {
          atlasId,
          resourceId,
          resourceName: removedResource.name,
          remainingChildren: atlas.children.length - 1
        });

        return true;
      }
    }
    return false;
  },

  // 获取图集
  getAtlas(atlasId: string): AtlasResource | null {
    const atlas = this.state.atlases.find(atlas => atlas.id === atlasId) || null;
    if (atlas && !atlas.canvas) {
      // 为旧的图集数据添加默认canvas属性
      atlas.canvas = {
        width: 1024,
        height: 1024,
        backgroundColor: '#ffffff'
      };
    }
    return atlas;
  },

  // 获取当前选中的图集
  getCurrentSelectedAtlas(): AtlasResource | null {
    return this.state.selectedAtlas;
  },

  // 更新图集属性
  updateAtlas(atlas: AtlasResource, updates: Partial<AtlasResource>): void {
    atlasStoreState.update(state => {
      const updatedAtlas = { ...atlas, ...updates, updatedAt: new Date().toISOString() };

      return {
        ...state,
        atlases: state.atlases.map(a => a.id === atlas.id ? updatedAtlas : a),
        // 🎯 如果当前选中的是这个图集，也要更新selectedAtlas的引用
        selectedAtlas: state.selectedAtlas?.id === atlas.id ? updatedAtlas : state.selectedAtlas
      };
    });

    console.log('🔄 AtlasStore: 更新图集属性', {
      atlasId: atlas.id,
      atlasName: atlas.name,
      updates
    });
  },

  // 更新图集的布局设置
  updateAtlasLayoutSettings(atlas: AtlasResource, layoutSettings: Partial<LayoutSettings>): void {
    atlasStoreState.update(state => {
      const updatedAtlas = {
        ...atlas,
        layoutSettings: atlas.layoutSettings ? { ...atlas.layoutSettings, ...layoutSettings } : undefined,
        updatedAt: new Date().toISOString()
      };

      return {
        ...state,
        atlases: state.atlases.map(a => a.id === atlas.id ? updatedAtlas : a),
        // 🎯 如果当前选中的是这个图集，也要更新selectedAtlas的引用
        selectedAtlas: state.selectedAtlas?.id === atlas.id ? updatedAtlas : state.selectedAtlas
      };
    });

    console.log('🔄 AtlasStore: 更新图集布局设置', {
      atlasId: atlas.id,
      atlasName: atlas.name,
      layoutSettings
    });
  },

  // 清空所有图集
  clearAllAtlases(): void {
    atlasStoreState.set({
      atlases: [],
      selectedAtlas: null
    });
    atlasCounter = 1;

    console.log('🧹 AtlasStore: 清空所有图集');
  },

  // 🎯 导出图集数据（用于项目保存）
  exportAtlasData(): any {
    const state = this.state;
    console.log('📤 AtlasStore: 导出图集数据', {
      atlasCount: state.atlases.length,
      selectedAtlasId: state.selectedAtlas?.id
    });

    return {
      atlases: state.atlases,
      selectedAtlas: state.selectedAtlas,
      atlasCounter,
      exportedAt: new Date().toISOString()
    };
  },

  // 🎯 导入图集数据（用于项目加载）
  importAtlasData(data: any): void {
    console.log('📥 AtlasStore: 导入图集数据', {
      atlasCount: data.atlases?.length || 0,
      selectedAtlasId: data.selectedAtlas?.id
    });

    if (data.atlases && Array.isArray(data.atlases)) {
      atlasStoreState.set({
        atlases: data.atlases,
        selectedAtlas: data.selectedAtlas || null
      });

      // 恢复图集计数器
      if (data.atlasCounter && typeof data.atlasCounter === 'number') {
        atlasCounter = data.atlasCounter;
      }

      console.log('✅ AtlasStore: 图集数据导入完成', {
        atlasCount: data.atlases.length,
        selectedAtlasId: data.selectedAtlas?.id,
        atlasCounter
      });
    } else {
      console.warn('⚠️ AtlasStore: 无效的图集数据，跳过导入');
    }
  }
};
