<!DOCTYPE html>
<html>
  <head>
    <title>Wick Engine Example</title>
  </head>
  <body>
    <div id="wickCanvas"></div>
    <!-- <script src="dist/wickengine.js"></script> -->
    <script>
      // 创建一个新的 Wick 项目
      const project = new Wick.Project();
      // 设置项目属性
      project.width = 800;
      project.height = 600;
      // 初始化视图
      project.view = new Wick.View.Project({
        project: project,
        canvasContainer: document.getElementById("wickCanvas"),
      });

      // 重置视图大小和位置
      project.recenter();
    </script>
  </body>
</html>
