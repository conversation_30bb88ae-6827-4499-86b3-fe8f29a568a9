/**
 * 简化的图片处理Worker - 只处理图片，直接返回结果
 * 支持批量处理和进度回调
 */

// 🎯 简化的消息类型
export interface WorkerMessage {
  id: string;
  type: 'process-images';
  payload: {
    images: ImageInput[];
    options: ProcessOptions;
  };
}

export interface WorkerResponse {
  id: string;
  type: 'progress' | 'success' | 'error';
  payload: any;
}

// 🎯 输入图片数据
interface ImageInput {
  id: string;
  buffer: ArrayBuffer;
  originalType?: string;
}

// 🎯 处理选项
interface ProcessOptions {
  // 输出选项
  generateThumbnail?: boolean;    // 是否生成缩略图
  thumbnailSize?: number;         // 缩略图尺寸，默认128
  generatePreview?: boolean;      // 是否生成预览图
  previewSize?: number;          // 预览图尺寸，默认256

  // 质量设置
  quality?: number;              // 图片质量，默认0.8
  format?: 'webp' | 'jpeg' | 'png'; // 输出格式，默认webp

  // 批量回调设置
  batchCallback?: number;        // 每处理多少个回调一次，默认10
}

// 🎯 处理结果
interface ProcessedImage {
  id: string;
  uniqueId?: string;  // 🎯 新增：唯一资源ID
  success: boolean;
  original?: {
    width: number;
    height: number;
    dataUrl: string;
  };
  thumbnail?: {
    width: number;
    height: number;
    dataUrl: string;
    size: number;
  };
  preview?: {
    width: number;
    height: number;
    dataUrl: string;
    size: number;
  };
  error?: string;
}

// Worker内部状态
let currentTaskId: string | null = null;

/**
 * 🎯 简化的主消息处理器
 */
self.onmessage = async (event: MessageEvent<WorkerMessage>) => {
  const { id, type, payload } = event.data;
  currentTaskId = id;

  try {
    switch (type) {
      case 'process-images':
        await processImages(id, payload.images, payload.options);
        break;
      default:
        throw new Error(`未知的任务类型: ${type}`);
    }
  } catch (error) {
    sendResponse(id, 'error', {
      message: error instanceof Error ? error.message : '未知错误',
      stack: error instanceof Error ? error.stack : undefined
    });
  } finally {
    currentTaskId = null;
  }
};

/**
 * 发送响应消息
 */
function sendResponse(id: string, type: 'success' | 'error' | 'progress', payload: any) {
  const response: WorkerResponse = { id, type, payload };
  self.postMessage(response);
}

/**
 * 🎯 批量处理图片 - 简化的核心函数
 */
async function processImages(taskId: string, images: ImageInput[], options: ProcessOptions) {
  console.log('� Worker: 开始批量处理图片', {
    taskId,
    count: images.length,
    options
  });

  const results: ProcessedImage[] = [];
  const batchCallback = options.batchCallback || 10;
  let processed = 0;

  for (const imageInput of images) {
    try {
      const processedImage = await processSingleImage(imageInput, options);
      results.push(processedImage);
      processed++;

      // 🎯 每处理指定数量回调一次
      if (processed % batchCallback === 0 || processed === images.length) {
        sendResponse(taskId, 'progress', {
          completed: processed,
          total: images.length,
          percentage: Math.round((processed / images.length) * 100),
          results: results.slice(-batchCallback) // 返回最新处理的结果
        });
      }

    } catch (error) {
      console.error('❌ Worker: 处理图片失败', { id: imageInput.id, error });
      results.push({
        id: imageInput.id,
        success: false,
        error: error instanceof Error ? error.message : '处理失败'
      });
      processed++;
    }
  }

  // 🎯 处理完成，返回所有结果
  sendResponse(taskId, 'success', {
    results,
    totalProcessed: processed,
    totalImages: images.length
  });

  console.log('✅ Worker: 批量处理完成，发送success响应', {
    taskId,
    totalResults: results.length
  });
}

/**
 * 🎯 处理单个图片 - 根据选项生成不同尺寸的图片
 */
async function processSingleImage(imageInput: ImageInput, options: ProcessOptions): Promise<ProcessedImage> {
  console.log('🔄 Worker: 处理单个图片', {
    id: imageInput.id,
    bufferSize: imageInput.buffer.byteLength
  });

  try {
    // 🎯 生成唯一的资源ID
    const uniqueId = `img_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    console.log('🆔 Worker: 生成唯一资源ID', uniqueId);

    // 创建ImageBitmap
    console.log('📦 Worker: 创建Blob和ImageBitmap');
    const blob = new Blob([imageInput.buffer], {
      type: imageInput.originalType || 'image/png'
    });
    const imageBitmap = await createImageBitmap(blob);

    console.log('✅ Worker: ImageBitmap创建成功', {
      width: imageBitmap.width,
      height: imageBitmap.height
    });

    // 生成原图DataURL
    console.log('🎨 Worker: 生成原图DataURL');
    const originalDataUrl = await generateImageDataUrl(
      imageBitmap,
      imageBitmap.width,
      imageBitmap.height,
      options.quality || 0.9,
      options.format || 'webp'
    );
    console.log('✅ Worker: 原图DataURL生成成功');

    const result: ProcessedImage = {
      id: imageInput.id,  // 保留原始处理ID
      uniqueId: uniqueId, // 🎯 新增：唯一资源ID
      success: true,
      original: {
        width: imageBitmap.width,
        height: imageBitmap.height,
        dataUrl: originalDataUrl
      }
    };

    // 🎯 生成缩略图
    if (options.generateThumbnail !== false) {
      console.log('🖼️ Worker: 生成缩略图');
      const thumbnailSize = options.thumbnailSize || 128;
      const thumbnailData = await generatePreview(imageBitmap, thumbnailSize, options.quality || 0.8);
      result.thumbnail = {
        width: thumbnailData.width,
        height: thumbnailData.height,
        dataUrl: thumbnailData.dataUrl,
        size: thumbnailSize
      };
      console.log('✅ Worker: 缩略图生成成功');
    }

    // 🎯 生成预览图
    if (options.generatePreview !== false) {
      console.log('🖼️ Worker: 生成预览图');
      const previewSize = options.previewSize || 256;
      const previewData = await generatePreview(imageBitmap, previewSize, options.quality || 0.8);
      result.preview = {
        width: previewData.width,
        height: previewData.height,
        dataUrl: previewData.dataUrl,
        size: previewSize
      };
      console.log('✅ Worker: 预览图生成成功');
    }

    // 清理资源
    imageBitmap.close();
    console.log('✅ Worker: 图片处理完成', imageInput.id);

    return result;

  } catch (error) {
    console.error('❌ Worker: 处理图片失败', {
      id: imageInput.id,
      error: error instanceof Error ? error.message : String(error)
    });

    return {
      id: imageInput.id,
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * 🎯 生成图片DataURL
 */
async function generateImageDataUrl(
  imageBitmap: ImageBitmap,
  width: number,
  height: number,
  quality: number,
  format: 'webp' | 'jpeg' | 'png'
): Promise<string> {
  const canvas = new OffscreenCanvas(width, height);
  const ctx = canvas.getContext('2d')!;

  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  ctx.drawImage(imageBitmap, 0, 0, width, height);

  const blob = await canvas.convertToBlob({
    type: `image/${format}`,
    quality
  });

  const arrayBuffer = await blob.arrayBuffer();
  const uint8Array = new Uint8Array(arrayBuffer);

  // 🎯 修复栈溢出：分块处理大文件的base64转换
  let binaryString = '';
  const chunkSize = 8192; // 8KB chunks
  for (let i = 0; i < uint8Array.length; i += chunkSize) {
    const chunk = uint8Array.slice(i, i + chunkSize);
    binaryString += String.fromCharCode.apply(null, Array.from(chunk));
  }

  const base64 = btoa(binaryString);
  return `data:image/${format};base64,${base64}`;
}





/**
 * 生成预览图片
 */
async function generatePreview(
  imageBitmap: ImageBitmap,
  maxSize: number,
  quality: number
): Promise<{ blob: Blob; dataUrl: string; width: number; height: number }> {
  const { width, height } = imageBitmap;

  // 计算缩放比例
  const scale = Math.min(maxSize / width, maxSize / height, 1);
  const newWidth = Math.round(width * scale);
  const newHeight = Math.round(height * scale);

  // 创建画布并绘制
  const canvas = new OffscreenCanvas(newWidth, newHeight);
  const ctx = canvas.getContext('2d')!;

  // 高质量缩放
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  ctx.drawImage(imageBitmap, 0, 0, newWidth, newHeight);

  // 生成Blob和DataURL
  const blob = await canvas.convertToBlob({
    type: 'image/webp',
    quality
  });

  // 🎯 转换为DataURL - 修复栈溢出问题
  const arrayBuffer = await blob.arrayBuffer();
  const uint8Array = new Uint8Array(arrayBuffer);

  // 分块处理避免栈溢出
  let binaryString = '';
  const chunkSize = 8192; // 8KB chunks
  for (let i = 0; i < uint8Array.length; i += chunkSize) {
    const chunk = uint8Array.slice(i, i + chunkSize);
    binaryString += String.fromCharCode.apply(null, Array.from(chunk));
  }

  const base64 = btoa(binaryString);
  const dataUrl = `data:image/webp;base64,${base64}`;

  return {
    blob,
    dataUrl,
    width: newWidth,
    height: newHeight
  };
}



/**
 * 错误处理
 */
self.onerror = (error) => {
  console.error('🚨 Worker: 未捕获的错误', error);
  if (currentTaskId) {
    sendResponse(currentTaskId, 'error', {
      message: 'Worker内部错误',
      details: typeof error === 'string' ? error : (error as any).message || '未知错误'
    });
  }
};

console.log('🚀 ImageProcessor Worker 已启动');
