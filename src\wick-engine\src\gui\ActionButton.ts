/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { Button, ButtonArgs } from "./Button";
import { Bounds } from "./GUIElement";

interface ActionButtonArgs extends ButtonArgs {
  icon?: string;
  width?: number;
  height?: number;
  toggled?: boolean;
}

export class ActionButton extends Button {
  static ACTION_BUTTON_RADIUS = 20;

  protected icon: string;
  protected width: number;
  protected height: number;
  protected toggled: boolean;

  constructor(model: Base, args: ActionButtonArgs = {}) {
    super(model, args);

    this.icon = args.icon || "";
    this.width = args.width || ActionButton.ACTION_BUTTON_RADIUS;
    this.height = args.height || ActionButton.ACTION_BUTTON_RADIUS;
    this.toggled = args.toggled || false;
  }

  draw(isActive: boolean = true): void {
    super.draw();

    const ctx = this.ctx;

    // Disable pointer cursor if the button isn't active
    this.cursor = isActive ? "pointer" : "default";

    // Button Circle
    if ((isActive && this.mouseState === "over") || this.toggled) {
      ctx.fillStyle = "rgba(0, 0, 0, 0.1)";
      ctx.beginPath();
      ctx.roundRect(
        -this.width,
        -this.height,
        this.width * 2,
        this.height * 2,
        3
      );
      ctx.fill();
    }

    // Button Icon
    const w = this.width * 0.8;
    const h = this.height * 0.8;
    ctx.drawImage(this.getIcon(this.icon), -w, -h, w * 2, h * 2);
  }

  get bounds(): Bounds {
    return {
      x: -this.width,
      y: -this.height,
      width: this.width * 2,
      height: this.height * 2,
    };
  }

  protected getIcon(iconName: string): HTMLImageElement {
    // This method should be implemented to return the icon image
    // For now, we'll return a placeholder
    return new Image();
  }
}
