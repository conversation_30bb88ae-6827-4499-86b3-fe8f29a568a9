/**
 * 语言状态管理Store
 */

import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import { locale } from 'svelte-i18n';
import { setLocale, supportedLocales, type SupportedLocale } from '../lib/i18n';

// 当前语言状态
export const currentLocale = writable<SupportedLocale>('zh-CN');

// 语言切换状态
export const isChangingLocale = writable(false);

// 语言加载状态
export const isLocaleLoading = writable(false);

// 当前语言信息
export const currentLocaleInfo = derived(
  currentLocale,
  ($currentLocale) => supportedLocales.find(l => l.code === $currentLocale)
);

// 可用语言列表
export const availableLocales = writable(supportedLocales);

/**
 * 切换语言
 */
export async function changeLocale(newLocale: SupportedLocale): Promise<void> {
  if (!supportedLocales.some(l => l.code === newLocale)) {
    console.warn(`Unsupported locale: ${newLocale}`);
    return;
  }

  try {
    isChangingLocale.set(true);
    isLocaleLoading.set(true);

    // 使用i18n的setLocale函数
    setLocale(newLocale);
    
    // 更新store状态
    currentLocale.set(newLocale);

    // 保存到localStorage
    if (browser) {
      localStorage.setItem('locale', newLocale);
    }

    console.log(`🌐 Language changed to: ${newLocale}`);
    
    // 可以在这里添加其他语言切换后的逻辑
    // 比如重新加载某些数据、更新UI等
    
  } catch (error) {
    console.error('Failed to change locale:', error);
  } finally {
    isChangingLocale.set(false);
    isLocaleLoading.set(false);
  }
}

/**
 * 初始化语言设置
 */
export function initLocaleStore(): void {
  if (!browser) return;

  // 监听i18n locale变化
  locale.subscribe((newLocale) => {
    if (newLocale) {
      currentLocale.set(newLocale as SupportedLocale);
    }
  });

  console.log('🌐 Locale store initialized');
}

/**
 * 获取下一个语言（用于快速切换）
 */
export function getNextLocale(): SupportedLocale {
  const current = currentLocale;
  let currentValue: SupportedLocale = 'zh-CN';
  
  current.subscribe(value => {
    currentValue = value;
  })();

  const currentIndex = supportedLocales.findIndex(l => l.code === currentValue);
  const nextIndex = (currentIndex + 1) % supportedLocales.length;
  return supportedLocales[nextIndex].code;
}

/**
 * 切换到下一个语言
 */
export async function switchToNextLocale(): Promise<void> {
  const nextLocale = getNextLocale();
  await changeLocale(nextLocale);
}

/**
 * 重置为默认语言
 */
export async function resetToDefaultLocale(): Promise<void> {
  await changeLocale('zh-CN');
}

/**
 * 检查是否支持指定语言
 */
export function isSupportedLocale(locale: string): locale is SupportedLocale {
  return supportedLocales.some(l => l.code === locale);
}

// 导出常用的语言检查函数
export { isChineseLocale, isEnglishLocale } from '../lib/i18n/utils';
