/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { View } from "./View";
import { Clip } from "../base/Clip";
import * as paper from "paper";

export class ClipView extends View {
  protected static readonly BORDER_STROKE_WIDTH = 2;
  protected static readonly BORDER_STROKE_COLOR_NORMAL = "#2636E1";
  protected static readonly BORDER_STROKE_COLOR_HAS_CODE = "#01C094";
  protected static readonly BORDER_STROKE_COLOR_HAS_CODE_ERROR = "#E61E07";
  protected static readonly PLACEHOLDER_SIZE = 10;

  protected group: paper.Group;
  protected _bounds: paper.Rectangle;
  protected _model: Clip;

  /**
   * Creates a new Clip view.
   */
  constructor(model: Clip) {
    super(model);

    this.group = new this.paper.Group();
    this.group.remove();
    this.group.applyMatrix = false;

    this._bounds = new paper.Rectangle();
  }

  get bounds(): paper.Rectangle {
    return this._bounds;
  }

  get absoluteBounds(): paper.Rectangle {
    return this.group.bounds;
  }

  get points(): number[][] {
    const group = this.group;
    const initial: number[][] = [];
    const convert = (point: paper.Point): number[][] => [[point.x, point.y]];
    const compare = (list1: number[][], list2: number[][]): number[][] =>
      list1.concat(list2);

    return this.reducePointsFromGroup(group, initial, convert, compare);
  }

  /**
   * Reduce points from a group
   * @param group - The paper group of objects
   * @param initial - The initial value, should be of return type
   * @param convert - Function to convert point to return type
   * @param compare - Function to compare return types
   */
  protected reducePointsFromGroup<T>(
    group: paper.Group,
    initial: T,
    convert: (point: paper.Point) => T,
    compare: (a: T, b: T) => T
  ): T {
    let val = initial;
    for (let i = 0; i < group.children.length; i++) {
      const child = group.children[i];
      if (child.className === "Layer") {
        const ch = (child as paper.Layer).children;
        for (let j = 0; j < ch.length; j++) {
          const item = ch[j];
          if (item.className === "Path") {
            const matrix = item.globalMatrix;
            const path = item as paper.Path;
            for (let s = 0; s < path.segments.length; s++) {
              val = compare(
                val,
                convert(matrix.transform(path.segments[s].point))
              );
            }
          } else if (item.className === "CompoundPath") {
            const compoundPath = item as paper.CompoundPath;
            for (let p = 0; p < compoundPath.children.length; p++) {
              const path = compoundPath.children[p] as paper.Path;
              const matrix = item.globalMatrix;
              for (let s = 0; s < path.segments.length; s++) {
                val = compare(
                  val,
                  convert(matrix.transform(path.segments[s].point))
                );
              }
            }
          } else if (item.className === "Group") {
            val = compare(
              val,
              this.reducePointsFromGroup(
                item as paper.Group,
                initial,
                convert,
                compare
              )
            );
          }
        }
      }
    }
    return val;
  }

  render(): void {
    // Prevent an unselectable object from being rendered
    // due to a clip having no content on the first frame.
    this.model.ensureActiveFrameIsContentful();

    // Render timeline view
    this.model.timeline.view.render();

    // Add some debug info to the paper group
    this.group.data.wickType = "clip";
    this.group.data.wickUUID = this.model.uuid;

    // Add frame views from timeline
    this.group.removeChildren();
    this.group.addChildren(this.model.timeline.view.frameLayers);

    // Update transformations
    this.group.matrix.set(new paper.Matrix());
    this._bounds = this.group.bounds.clone();

    this.group.pivot = new this.paper.Point(0, 0);
    this.group.position.x = this.model.transformation.x;
    this.group.position.y = this.model.transformation.y;
    this.group.scaling.x = this.model.transformation.scaleX;
    this.group.scaling.y = this.model.transformation.scaleY;
    this.group.rotation = this.model.transformation.rotation;
    this.group.opacity = this.model.transformation.opacity;
  }

  generateBorder(): paper.Group {
    const group = new this.paper.Group({ insert: false });
    group.locked = true;
    group.data.wickType = "clip_border_" + this.model.uuid;

    const bounds = this.bounds;

    // Change border colors based on if the Clip caused an error
    let strokeColor = ClipView.BORDER_STROKE_COLOR_NORMAL;
    if (
      this.model.project.error &&
      this.model.project.error.uuid === this.model.uuid
    ) {
      strokeColor = ClipView.BORDER_STROKE_COLOR_HAS_CODE_ERROR;
    } else if (this.model.hasContentfulScripts) {
      strokeColor = ClipView.BORDER_STROKE_COLOR_HAS_CODE;
    }

    const border = new paper.Path.Rectangle({
      rectangle: bounds,
      strokeColor: strokeColor,
      strokeWidth: ClipView.BORDER_STROKE_WIDTH,
      fillColor: null,
      insert: false,
    });

    group.addChild(border);

    return group;
  }
}
