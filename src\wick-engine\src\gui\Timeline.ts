/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { Breadcrumbs } from "./Breadcrumbs";
import { ActionButtonsContainer } from "./ActionButtonsContainer";
import { LayersContainer } from "./LayersContainer";
import { FramesContainer } from "./FramesContainer";
import { NumberLine } from "./NumberLine";
import { Scrollbar } from "./Scrollbar";

/**
 * The Timeline is responsible for drawing the following GUI elements:
 * - Breadcrumbs
 * - Frames Container
 * - Layers Container
 * - Horizontal + Vertical Scrollbars
 * - Number Line
 */
export class Timeline extends GUIElement {
  static BREADCRUMBS_HEIGHT = 30;
  static LAYERS_CONTAINER_WIDTH = 150;
  static NUMBER_LINE_HEIGHT = 20;
  static SCROLLBAR_SIZE = 15;

  protected breadcrumbs: Breadcrumbs;
  protected actionButtonsContainer: ActionButtonsContainer;
  protected layersContainer: LayersContainer;
  protected framesContainer: FramesContainer;
  protected numberLine: NumberLine;
  protected horizontalScrollbar: Scrollbar;
  protected verticalScrollbar: Scrollbar;

  /**
   * Create a new Timeline
   */
  constructor(model: Base) {
    super(model);

    this.breadcrumbs = new Breadcrumbs(model);
    this.actionButtonsContainer = new ActionButtonsContainer(model);
    this.layersContainer = new LayersContainer(model);
    this.framesContainer = new FramesContainer(model);
    this.numberLine = new NumberLine(model);
    this.horizontalScrollbar = new Scrollbar(model, "horizontal");
    this.verticalScrollbar = new Scrollbar(model, "vertical");
  }

  /**
   * Draw this GUIElement
   */
  draw(): void {
    super.draw();

    const ctx = this.ctx;

    ctx.save();
    ctx.translate(0, Timeline.BREADCRUMBS_HEIGHT);
    ctx.save();
    ctx.translate(0, Timeline.NUMBER_LINE_HEIGHT);
    // Frames
    ctx.save();
    ctx.translate(Timeline.LAYERS_CONTAINER_WIDTH, 0);
    ctx.save();
    ctx.translate(-this.project.scrollX, -this.project.scrollY);
    this.framesContainer.draw();
    ctx.restore();
    ctx.restore();
    ctx.restore();

    // Number Line
    ctx.save();
    ctx.translate(-this.project.scrollX + Timeline.LAYERS_CONTAINER_WIDTH, 0);
    this.numberLine.draw();
    ctx.restore();

    // Layers
    ctx.save();
    ctx.translate(0, Timeline.NUMBER_LINE_HEIGHT);
    ctx.save();
    ctx.translate(0, -this.project.scrollY);
    this.layersContainer.draw();
    ctx.restore();
    ctx.restore();

    // Action buttons
    this.actionButtonsContainer.draw();
    ctx.restore();

    // Scrollbars
    ctx.save();
    ctx.translate(
      Timeline.LAYERS_CONTAINER_WIDTH,
      Timeline.BREADCRUMBS_HEIGHT + Timeline.NUMBER_LINE_HEIGHT
    );
    ctx.save();
    ctx.translate(
      0,
      this.canvas.height - this.currentTranslation.y - Timeline.SCROLLBAR_SIZE
    );
    this.horizontalScrollbar.draw();
    ctx.restore();

    ctx.save();
    ctx.translate(
      this.canvas.width - this.currentTranslation.x - Timeline.SCROLLBAR_SIZE,
      0
    );
    this.verticalScrollbar.draw();
    ctx.restore();
    ctx.restore();

    // Breadcrumbs
    this.breadcrumbs.draw();

    // Drop shadows
    ctx.save();
    ctx.translate(Timeline.LAYERS_CONTAINER_WIDTH, Timeline.BREADCRUMBS_HEIGHT);
    ctx.fillStyle = "rgba(0,0,0,0.2)";
    ctx.beginPath();
    ctx.rect(0, 0, 2, this.canvas.height);
    ctx.fill();
    ctx.beginPath();
    ctx.rect(0, 0, 1, this.canvas.height);
    ctx.fill();
    ctx.restore();
  }
}
