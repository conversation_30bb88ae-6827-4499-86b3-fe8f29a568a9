<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { PaneGroup, Pane, PaneResizer } from "paneforge";

  // 导入各个面板组件
  import TopPanel from "../top/TopPanel.svelte";
  import LeftPanel from "../left/LeftPanel.svelte";
  import CenterPanel from "../center/CenterPanel.svelte";
  import RightPanel from "../right/RightPanel.svelte";
  import BottomPanel from "../bottom/BottomPanel.svelte";
  import MenuBarPanel from "../menuBar/menuBarPanel.svelte";

  // 导入主题提供者
  import ThemeProvider from "../theme/ThemeProvider.svelte";

  // 导入拖拽预览组件
  import DragPreview from "../components/drag/DragPreview.svelte";

  // 导入Toast容器
  import ToastContainer from "../components/ToastContainer.svelte";

  // 导入快捷键系统
  import { initShortcutKeys } from "../shortcutKey";

  // 导入国际化
  import { initI18n } from "../lib/i18n";
  import { initLocaleStore } from "../stores/localeStore";

  // 导入全局导出状态
  import { globalExportState, type GlobalExportState } from "../stores/exportSettingsStore";

  // 🎯 导入性能优化工具
  import { memoryManager } from "../utils/memoryManager";
  import { performanceMonitor } from "../utils/performanceMonitor";
  import { imagePreviewCache } from "../utils/imagePreviewCache";
  import { blobUrlManager } from "../utils/blobUrlManager";

  // CenterPanel 组件引用
  let centerPanelRef: CenterPanel | null = $state(null);

  // 🎯 全局导出状态
  let exportState = $state<GlobalExportState>({
    isExporting: false,
    exportType: null,
    currentFile: '',
    progress: { current: 0, total: 0, stage: 'preparing' },
    startTime: null
  });

  // 订阅全局导出状态
  globalExportState.subscribe(state => {
    exportState = state;
  });

  // 🎯 获取当前图集的图片数据的方法
  async function getCurrentAtlasImageData(): Promise<{ blob: Blob; width: number; height: number } | null> {
    if (!centerPanelRef) {
      console.warn('❌ +page.svelte: CenterPanel 引用不可用');
      return null;
    }

    return await centerPanelRef.getCurrentAtlasImageData();
  }



  // 🎯 应用启动时初始化性能优化系统
  onMount(async () => {
    console.log('=== 应用启动，初始化系统 ===');

    try {
      // 🌐 初始化国际化
      console.log('🌐 初始化国际化系统...');
      await initI18n();
      initLocaleStore();
      console.log('✅ 国际化系统初始化完成');
      // 🎯 启动内存监控
      memoryManager.setThresholds({
        warning: 100,   // 100MB
        critical: 200,  // 200MB
        cleanup: 300    // 300MB
      });
      memoryManager.startMonitoring(10000); // 每10秒检查一次
      console.log('✅ 内存监控系统启动');

      // 🎯 性能监控系统已自动启动
      console.log('✅ 性能监控系统就绪');

      // 🎯 预热图片缓存系统
      console.log('✅ 图片缓存系统预热完成');

      // 初始化快捷键系统
      console.log('=== 初始化快捷键系统 ===');
      initShortcutKeys();
      console.log('快捷键系统初始化完成');

      // 🎯 打印初始性能报告
      setTimeout(() => {
        const memoryStats = memoryManager.getMemoryStats();
        const cacheStats = imagePreviewCache.getStats();
        const blobStats = blobUrlManager.getStats();

        console.log('📊 应用启动性能报告', {
          内存使用: memoryStats ? `${memoryStats.used.toFixed(2)}MB (${memoryStats.percentage.toFixed(1)}%)` : '不支持',
          图片缓存: `${cacheStats.count}个条目, ${(cacheStats.totalSize / 1024 / 1024).toFixed(2)}MB`,
          BlobURL管理: `${blobStats.totalUrls}个URL, ${(blobStats.totalSize / 1024 / 1024).toFixed(2)}MB`,
          启动时间: `${performance.now().toFixed(2)}ms`
        });
      }, 1000);

    } catch (error) {
      console.error('性能优化系统初始化失败:', error);
    }
  });

  // 🎯 应用销毁时清理资源
  onDestroy(() => {
    console.log('=== 应用销毁，清理性能优化资源 ===');

    // 停止内存监控
    memoryManager.stopMonitoring();

    // 打印最终性能报告
    performanceMonitor.printReport();
    blobUrlManager.printReport();

    // 清理缓存
    imagePreviewCache.clearAll();

    // 🎯 清理所有Blob URL
    blobUrlManager.cleanup();

    console.log('✅ 性能优化资源清理完成');
  });
</script>

<ThemeProvider>
  <div class="app-layout">
    <!-- 菜单栏 - 始终显示，不被覆盖 -->
    <div class="menu-bar-container">
      <MenuBarPanel />
    </div>

    <!-- 主要的垂直布局：顶部、中间、底部 -->
    <div class="main-content">
      <PaneGroup direction="vertical">
        <!-- 顶部面板（标题栏） -->
        <!-- <Pane defaultSize={8} minSize={6} maxSize={10} class="top-pane">
          <TopPanel />
        </Pane> -->

      <!-- <PaneResizer /> -->

      <!-- 中间区域：左、中、右的水平布局 -->
      <Pane defaultSize={77}>
        <PaneGroup direction="horizontal">
          <!-- 左侧面板 -->
          <Pane defaultSize={20} minSize={15} maxSize={35}>
            <LeftPanel />
          </Pane>

          <PaneResizer />

          <!-- 中心面板 -->
          <Pane defaultSize={60} minSize={30}>
            <CenterPanel bind:this={centerPanelRef} />
          </Pane>

          <PaneResizer />

          <!-- 右侧面板 -->
          <Pane defaultSize={20} minSize={15} maxSize={35}>
            <RightPanel {getCurrentAtlasImageData} />
          </Pane>
        </PaneGroup>
      </Pane>

      <PaneResizer />

      <!-- 底部面板 -->
      <Pane defaultSize={15} minSize={10} maxSize={30}>
        <BottomPanel />
      </Pane>



      </PaneGroup>
    </div>

    <!-- 拖拽预览容器 - 放在最顶层 -->
    <DragPreview />

    <!-- Toast 提示容器 - 放在最顶层 -->
    <ToastContainer />

    <!-- 🎯 全局导出遮罩层 -->
    {#if exportState.isExporting}
      <div class="export-overlay">
        <div class="export-modal">
          <div class="export-icon">
            {exportState.exportType === 'folder' ? '📁' : '📤'}
          </div>
          <h3>正在导出...</h3>
          <div class="export-info">
            <p>类型: {exportState.exportType === 'folder' ? '文件夹导出' : '单个导出'}</p>
            <p>进度: {exportState.progress.current} / {exportState.progress.total}</p>
            {#if exportState.currentFile}
              <p class="current-file">当前: {exportState.currentFile}</p>
            {/if}
          </div>
          <div class="progress-bar">
            <div
              class="progress-fill"
              style="width: {exportState.progress.total > 0 ? (exportState.progress.current / exportState.progress.total * 100) : 0}%"
            ></div>
          </div>
          <div class="export-stage">
            阶段: {exportState.progress.stage === 'preparing' ? '准备中' :
                   exportState.progress.stage === 'processing' ? '处理中' :
                   exportState.progress.stage === 'saving' ? '保存中' : '完成'}
          </div>
        </div>
      </div>
    {/if}
  </div>
</ThemeProvider>

<style>
  :global(html, body) {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: #0f0f0f;
    background-color: #f6f6f6;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
  }

  .app-layout {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  /* 菜单栏容器 */
  .menu-bar-container {
    flex-shrink: 0;
    z-index: 10000;
    position: relative;
  }

  /* 主内容区域 */
  .main-content {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  /* PaneForge 样式覆盖 */
  :global([data-pane-group]) {
    height: 100%;
  }

  /* 顶层 PaneGroup 需要允许下拉菜单溢出 */
  :global([data-pane-group]:first-child) {
    overflow: visible;
  }

  :global([data-pane]) {
    height: 100%;
    overflow: hidden;
  }

  /* 顶部面板需要允许下拉菜单溢出 */
  :global(.top-pane[data-pane]) {
    overflow: visible !important;
    z-index: 2000;
  }

  /* 🎯 使用深色分割线 */
  :global([data-pane-resizer]) {
    background: #3a3a3a;
    transition: background-color 0.2s;
  }

  :global([data-pane-resizer]:hover) {
    background: #4a4a4a;
  }

  :global([data-pane-resizer][data-direction="horizontal"]) {
    width: 4px;
    cursor: col-resize;
  }

  :global([data-pane-resizer][data-direction="vertical"]) {
    height: 4px;
    cursor: row-resize;
  }

  /* 🎯 全局导出遮罩层样式 */
  .export-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(4px);
    z-index: 99999;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease-out;
  }

  .export-modal {
    background: var(--theme-surface, #ffffff);
    border: 1px solid var(--theme-border, #e5e7eb);
    border-radius: 12px;
    padding: 2rem;
    min-width: 320px;
    max-width: 480px;
    text-align: center;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    animation: slideIn 0.3s ease-out;
  }

  .export-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: pulse 2s ease-in-out infinite;
  }

  .export-modal h3 {
    margin: 0 0 1.5rem 0;
    color: var(--theme-text, #1f2937);
    font-size: 1.5rem;
    font-weight: 600;
  }

  .export-info {
    margin-bottom: 1.5rem;
    text-align: left;
  }

  .export-info p {
    margin: 0.5rem 0;
    color: var(--theme-text-secondary, #6b7280);
    font-size: 0.875rem;
  }

  .current-file {
    font-family: monospace;
    background: var(--theme-surface-light, #f9fafb);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    word-break: break-all;
  }

  .progress-bar {
    width: 100%;
    height: 8px;
    background: var(--theme-surface-light, #f3f4f6);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
  }

  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--theme-primary, #3b82f6), var(--theme-primary-light, #60a5fa));
    border-radius: 4px;
    transition: width 0.3s ease;
    animation: shimmer 2s ease-in-out infinite;
  }

  .export-stage {
    color: var(--theme-text-secondary, #6b7280);
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  @keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: 200px 0; }
  }



  /* 深色模式支持 */
  @media (prefers-color-scheme: dark) {
    :global(html, body) {
      color: #f6f6f6;
      background-color: #2f2f2f;
    }

    /* 🎯 深色模式下使用更深的分割线 */
    :global([data-pane-resizer]) {
      background: #2a2a2a;
    }

    :global([data-pane-resizer]:hover) {
      background: #3a3a3a;
    }
  }
</style>
