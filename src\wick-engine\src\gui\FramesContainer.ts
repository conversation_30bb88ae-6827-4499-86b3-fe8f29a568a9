/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { Frame } from "../base/Frame";
import { SelectionBox } from "./SelectionBox";
import { FrameGhost } from "./FrameGhost";

interface FrameStrips {
  [key: string]: Frame;
}

export class FramesContainer extends GUIElement {
  protected _frameStrips: FrameStrips;
  protected _frameGhost: FrameGhost | null;
  protected _selectionBox: SelectionBox | null;
  protected addFrameCol: number;
  protected addFrameRow: number;

  constructor(model: Base) {
    super(model);

    this.canAutoScrollX = true;
    this.canAutoScrollY = true;

    this._frameStrips = {};
    this._frameGhost = null;
    this._selectionBox = null;
    this.addFrameCol = 0;
    this.addFrameRow = 0;
  }

  draw(): void {
    super.draw();

    this.addFrameCol = Math.floor(this.localMouse.x / this.gridCellWidth);
    this.addFrameRow = Math.floor(this.localMouse.y / this.gridCellHeight);

    const ctx = this.ctx;

    // Background
    ctx.fillStyle = GUIElement.TIMELINE_BACKGROUND_COLOR;
    ctx.beginPath();
    ctx.rect(
      this.project.scrollX,
      this.project.scrollY,
      this.canvas.width,
      this.canvas.height
    );
    ctx.fill();

    // Add a small buffer to prevent some graphics from being cut off
    ctx.save();
    ctx.translate(2, 2);

    // Draw frame strips
    const layers = this.model.layers;
    layers.forEach((layer) => {
      const i = layer.index;

      ctx.save();
      ctx.translate(0, i * this.gridCellHeight);
      if (layer.isActive) {
        ctx.fillStyle = GUIElement.FRAMES_STRIP_ACTIVE_FILL_COLOR;
      } else {
        ctx.fillStyle = GUIElement.FRAMES_STRIP_INACTIVE_FILL_COLOR;
      }

      const width = this.canvas.width;
      const height = GUIElement.GRID_DEFAULT_CELL_HEIGHT - 2;

      ctx.beginPath();
      ctx.rect(this.project.scrollX, 0, width, height);
      ctx.fill();
      ctx.restore();
    });

    // Draw grid
    ctx.lineWidth = 1;
    ctx.strokeStyle = GUIElement.FRAMES_CONTAINER_VERTICAL_GRID_STROKE_COLOR;
    const skip = Math.round(this.project.scrollX / this.gridCellWidth);
    for (let i = -1; i < this.canvas.width / this.gridCellWidth + 1; i++) {
      ctx.beginPath();
      const x = (i + skip) * this.gridCellWidth;
      ctx.moveTo(x, this.project.scrollY);
      ctx.lineTo(x, this.project.scrollY + this.canvas.height);
      ctx.stroke();
    }

    // Draw frames
    const frames = this.model.getAllFrames();

    const draggingFrames = frames.filter((frame) => {
      if (frame.guiElement._ghost) return true;
      if (frame.tweens.find((tween) => tween.guiElement._ghost)) {
        return true;
      }
      return false;
    });

    frames.forEach((frame) => {
      if (draggingFrames.indexOf(frame) !== -1) return;
      this._drawFrame(frame, true);
    });

    // Make sure to render the frames being dragged last.
    draggingFrames.forEach((frame) => {
      this._drawFrame(frame, false);
    });

    // Add frame overlay
    if (
      this.mouseState === "over" &&
      !this._selectionBox &&
      this._addFrameOverlayIsActive()
    ) {
      this.cursor = "pointer";

      const x = this.addFrameCol * this.gridCellWidth;
      const y = this.addFrameRow * this.gridCellHeight;

      ctx.fillStyle = GUIElement.ADD_FRAME_OVERLAY_FILL_COLOR;

      ctx.beginPath();
      ctx.roundRect(
        x,
        y,
        this.gridCellWidth,
        this.gridCellHeight,
        GUIElement.FRAME_BORDER_RADIUS
      );
      ctx.fill();

      // Plus sign
      ctx.font = "30px bold Courier New";
      ctx.fillStyle = GUIElement.ADD_FRAME_OVERLAY_PLUS_COLOR;
      ctx.globalAlpha = 0.5;
      ctx.fillText(
        "+",
        x + this.gridCellWidth / 2 - 8,
        y + this.gridCellHeight / 2 + 8
      );
      ctx.globalAlpha = 1.0;
    } else {
      this.cursor = "default";
    }

    // Selection box
    if (this._selectionBox) {
      this._selectionBox.draw();
    }

    // Top drop shadow
    ctx.fillStyle = "rgba(0,0,0,0.2)";
    ctx.beginPath();
    ctx.rect(
      this.project.scrollX - 2,
      this.project.scrollY - 2,
      this.canvas.width,
      2
    );
    ctx.fill();
    ctx.beginPath();
    ctx.rect(
      this.project.scrollX - 2,
      this.project.scrollY - 2,
      this.canvas.width,
      1
    );
    ctx.fill();

    ctx.restore();
  }

  protected _drawFrame(frame: Frame, enableCull: boolean): void {
    const ctx = this.ctx;

    // Optimization: don't render frames that are outside the scroll area
    // This really speeds things up!!
    const frameStartX = (frame.start - 1) * this.gridCellWidth;
    const frameStartY = frame.parentLayer.index * this.gridCellHeight;
    const frameEndX = frameStartX + frame.length * this.gridCellWidth;
    const frameEndY = frameStartY + this.gridCellHeight;
    const framesContainerWidth =
      this.canvas.width - GUIElement.LAYERS_CONTAINER_WIDTH;
    const framesContainerHeight =
      this.canvas.height -
      GUIElement.BREADCRUMBS_HEIGHT -
      GUIElement.NUMBER_LINE_HEIGHT;

    if (enableCull) {
      const scrollX = this.project.scrollX;
      const scrollY = this.project.scrollY;
      if (frameEndX < scrollX || frameEndY < scrollY) {
        return;
      }
      if (
        frameStartX > scrollX + framesContainerWidth ||
        frameStartY > scrollY + framesContainerHeight
      ) {
        return;
      }
    }

    // Draw the frame
    ctx.save();
    ctx.translate(frameStartX, frameStartY);
    frame.guiElement.draw();
    ctx.restore();
  }

  onMouseDrag(e: MouseEvent): void {
    if (!this._selectionBox) {
      this._selectionBox = new SelectionBox(this.model);
    }

    // Move the playhead when the selection box is dragged.
    const newPlayhead = this.addFrameCol + 1;
    if (this.model.playheadPosition !== newPlayhead) {
      this.model.playheadPosition = newPlayhead;
      this.projectWasSoftModified();
    }
  }

  onMouseUp(e: MouseEvent): void {
    if (this._selectionBox) {
      if (!e.shiftKey) {
        this.model.project.selection.clear();
      }

      // The selection box was just finished, select frames with the box bounds
      this._selectionBox.finish();
      this._selectionBox = null;
    }
  }

  protected _addFrameOverlayIsActive(): boolean {
    return (
      this.model.activeLayer &&
      this.addFrameRow === this.model.activeLayer.index
    );
  }
}
