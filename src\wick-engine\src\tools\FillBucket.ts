/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Tool } from "./Tool";

export class FillBucket extends Tool {
  protected name: string;
  protected project: any;

  /**
   * Creates an instance of the fill bucket tool.
   */
  constructor() {
    super();

    this.name = "fillbucket";
  }

  get doubleClickEnabled(): boolean {
    return false;
  }

  /**
   * The cursor style for the fill bucket tool.
   */
  get cursor(): string {
    return "url(cursors/fillbucket.png) 32 32, auto";
  }

  get isDrawingTool(): boolean {
    return true;
  }

  onActivate(e: any): void {}

  onDeactivate(e: any): void {}

  onMouseDown(e: any): void {
    setTimeout(() => {
      this.setCursor("wait");
    }, 0);

    setTimeout(() => {
      (this.paper as any).hole({
        point: e.point,
        bgColor: new (this.paper as any).Color(
          this.project.backgroundColor.hex
        ),
        gapFillAmount: this.getSetting("gapFillAmount"),
        layers: this.project.activeFrames
          .filter((frame: any) => {
            return !frame.parentLayer.hidden;
          })
          .map((frame: any) => {
            return frame.view.objectsLayer;
          }),
        onFinish: (path: any) => {
          this.setCursor("default");
          if (path) {
            path.fillColor = this.getSetting("fillColor").rgba;
            path.name = null;
            this.addPathToProject();
            if (e.item) {
              path.insertAbove(e.item);
            } else {
              this.paper.project.activeLayer.addChild(path);
              (this.paper as any).OrderingUtils.sendToBack([path]);
            }
            this.fire("canvasModified", {}, "fillbucket");
          }
        },
        onError: (message: string) => {
          this.setCursor("default");
          this.project.errorOccured(message);
        },
      });
    }, 50);
  }

  onMouseDrag(e: any): void {}

  onMouseUp(e: any): void {}

  // Helper methods (to be implemented)
  protected getSetting(name: string): any {
    return {};
  }
  protected addPathToProject(): void {}
}
