/**
 * Canvas数据缓存系统
 * 用于缓存图集Canvas生成的图片数据，避免重复生成
 */

import type { AtlasData } from '../types/imageType';
import type { MergeSettings } from '../stores/mergeSettingsStore';

interface CacheEntry {
  data: Blob;
  timestamp: number;
  hash: string;
  size: number;
}

export class CanvasDataCache {
  private cache = new Map<string, CacheEntry>();
  private readonly MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB限制
  private readonly CACHE_EXPIRE_TIME = 10 * 60 * 1000; // 10分钟过期
  private currentCacheSize = 0;

  /**
   * 生成缓存键的哈希值
   */
  private generateHash(atlas: AtlasData, mergeSettings: MergeSettings): string {
    // 基于图集内容、设置和最后修改时间生成哈希
    const hashData = {
      atlasId: atlas.id,
      lastModified: atlas.lastModified?.getTime() || 0,
      childrenCount: atlas.children?.length || 0,
      childrenHash: this.generateChildrenHash(atlas.children || []),
      settings: {
        algorithm: mergeSettings.algorithm,
        padding: mergeSettings.padding,
        maxWidth: mergeSettings.maxWidth,
        maxHeight: mergeSettings.maxHeight,
        powerOfTwo: mergeSettings.powerOfTwo,
        allowRotation: mergeSettings.allowRotation
      }
    };

    return btoa(JSON.stringify(hashData)).replace(/[+/=]/g, '');
  }

  /**
   * 生成子图片的哈希值
   */
  private generateChildrenHash(children: any[]): string {
    return children.map(child => `${child.id}_${child.lastModified?.getTime() || 0}`).join('|');
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.CACHE_EXPIRE_TIME) {
        expiredKeys.push(key);
        this.currentCacheSize -= entry.size;

        // 释放Blob URL
        if (entry.data instanceof Blob) {
          URL.revokeObjectURL(URL.createObjectURL(entry.data));
        }
      }
    }

    expiredKeys.forEach(key => this.cache.delete(key));

    if (expiredKeys.length > 0) {
      console.log('🧹 CanvasDataCache: 清理过期缓存', {
        expiredCount: expiredKeys.length,
        remainingCount: this.cache.size,
        currentSize: `${(this.currentCacheSize / 1024 / 1024).toFixed(2)}MB`
      });
    }
  }

  /**
   * 清理最旧的缓存项
   */
  private evictOldestCache(): void {
    if (this.cache.size === 0) return;

    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      const entry = this.cache.get(oldestKey);
      if (entry) {
        this.currentCacheSize -= entry.size;
        this.cache.delete(oldestKey);

        // 释放Blob URL
        if (entry.data instanceof Blob) {
          URL.revokeObjectURL(URL.createObjectURL(entry.data));
        }

        console.log('🗑️ CanvasDataCache: 清理最旧缓存', {
          key: oldestKey,
          remainingCount: this.cache.size,
          currentSize: `${(this.currentCacheSize / 1024 / 1024).toFixed(2)}MB`
        });
      }
    }
  }

  /**
   * 获取缓存的Canvas数据
   */
  async getCachedData(atlas: AtlasData, mergeSettings: MergeSettings): Promise<Blob | null> {
    const hash = this.generateHash(atlas, mergeSettings);

    // 清理过期缓存
    this.cleanExpiredCache();

    const cached = this.cache.get(hash);
    if (cached) {
      // 更新访问时间
      cached.timestamp = Date.now();

      console.log('✅ CanvasDataCache: 缓存命中', {
        hash: hash.substring(0, 8),
        size: `${(cached.size / 1024).toFixed(2)}KB`,
        age: `${((Date.now() - cached.timestamp) / 1000).toFixed(1)}s`
      });

      return cached.data;
    }

    return null;
  }

  /**
   * 设置缓存数据
   */
  setCachedData(atlas: AtlasData, mergeSettings: MergeSettings, data: Blob): void {
    const hash = this.generateHash(atlas, mergeSettings);
    const size = data.size;

    // 检查缓存大小限制
    while (this.currentCacheSize + size > this.MAX_CACHE_SIZE && this.cache.size > 0) {
      this.evictOldestCache();
    }

    // 如果数据太大，不缓存
    if (size > this.MAX_CACHE_SIZE * 0.5) {
      console.warn('⚠️ CanvasDataCache: 数据过大，跳过缓存', {
        size: `${(size / 1024 / 1024).toFixed(2)}MB`,
        maxSize: `${(this.MAX_CACHE_SIZE / 1024 / 1024).toFixed(2)}MB`
      });
      return;
    }

    this.cache.set(hash, {
      data,
      timestamp: Date.now(),
      hash,
      size
    });

    this.currentCacheSize += size;

    console.log('💾 CanvasDataCache: 缓存数据', {
      hash: hash.substring(0, 8),
      size: `${(size / 1024).toFixed(2)}KB`,
      totalCached: this.cache.size,
      totalSize: `${(this.currentCacheSize / 1024 / 1024).toFixed(2)}MB`
    });
  }

  /**
   * 清空所有缓存
   */
  clearAll(): void {
    // 释放所有Blob URL
    for (const entry of this.cache.values()) {
      if (entry.data instanceof Blob) {
        URL.revokeObjectURL(URL.createObjectURL(entry.data));
      }
    }

    this.cache.clear();
    this.currentCacheSize = 0;

    console.log('🧹 CanvasDataCache: 清空所有缓存');
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      count: this.cache.size,
      totalSize: this.currentCacheSize,
      maxSize: this.MAX_CACHE_SIZE,
      usage: (this.currentCacheSize / this.MAX_CACHE_SIZE) * 100
    };
  }
}

// 全局缓存实例
export const canvasDataCache = new CanvasDataCache();
