/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { View } from "./View";
import { Project } from "../base/Project";
import * as paper from "paper";

interface PanPosition {
  x: number;
  y: number;
}

interface CanvasDimensions {
  width: number;
  height: number;
}

type FitMode = "center" | "fill";
type RenderMode = "svg" | "webgl";

export class ProjectView extends View {
  protected static readonly DEFAULT_CANVAS_BG_COLOR = "rgb(187, 187, 187)";
  protected static readonly VALID_FIT_MODES: FitMode[] = ["center", "fill"];
  protected static readonly VALID_RENDER_MODES: RenderMode[] = ["svg", "webgl"];
  protected static readonly ORIGIN_CROSSHAIR_COLOR = "#CCCCCC";
  protected static readonly ORIGIN_CROSSHAIR_SIZE = 100;
  protected static readonly ORIGIN_CROSSHAIR_THICKNESS = 1;
  protected static readonly ZOOM_MIN = 0.1;
  protected static readonly ZOOM_MAX = 10.0;
  protected static readonly PAN_LIMIT = 10000;

  protected _fitMode: FitMode | null;
  protected _canvasContainer: HTMLElement | null;
  protected _canvasBGColor: string | null;
  protected _svgCanvas: HTMLCanvasElement | null;
  protected _svgBackgroundLayer: paper.Layer | null;
  protected _svgBordersLayer: paper.Layer | null;
  protected _svgGUILayer: paper.Layer | null;
  protected _pan: PanPosition;
  protected _zoom: number;
  protected _model: Project;

  /**
   * Create a new Project View.
   */
  constructor(model: Project) {
    super(model);

    this._fitMode = null;
    this.fitMode = "center";

    this._canvasContainer = null;
    this._canvasBGColor = null;

    this._svgCanvas = null;
    this._svgBackgroundLayer = null;
    this._svgBordersLayer = null;
    this._svgGUILayer = null;

    this._pan = { x: 0, y: 0 };
    this._zoom = 1;
  }

  /**
   * Determines the way the project will scale itself based on its container.
   * 'center' will keep the project at its original resolution, and center it inside its container.
   * 'fill' will stretch the project to fit the container (while maintaining its original aspect ratio).
   *
   * Note: For these changes to be reflected after setting fitMode, you must call Project.View.resize().
   */
  set fitMode(fitMode: FitMode) {
    if (ProjectView.VALID_FIT_MODES.indexOf(fitMode) === -1) {
      console.error("Invalid fitMode: " + fitMode);
      console.error(
        "Supported fitModes: " + ProjectView.VALID_FIT_MODES.join(",")
      );
    } else {
      this._fitMode = fitMode;
    }
  }

  get fitMode(): FitMode | null {
    return this._fitMode;
  }

  /**
   * The current canvas being rendered to.
   */
  get canvas(): HTMLCanvasElement | null {
    return this._svgCanvas;
  }

  /**
   * Get the current width/height of the canvas.
   */
  get canvasDimensions(): CanvasDimensions {
    return {
      width: this._svgCanvas?.offsetWidth || 0,
      height: this._svgCanvas?.offsetHeight || 0,
    };
  }

  /**
   * The zoom amount. 1 = 100% zoom
   */
  get zoom(): number {
    return this._zoom;
  }

  set zoom(zoom: number) {
    this._zoom = zoom;
  }

  /**
   * The amount to pan the view. (0,0) is the center.
   */
  get pan(): PanPosition {
    const pan = {
      x: -this.paper.view.center.x,
      y: -this.paper.view.center.y,
    };
    if (this.model.focus.isRoot) {
      pan.x += this.model.width / 2;
      pan.y += this.model.height / 2;
    }
    return pan;
  }

  set pan(pan: PanPosition) {
    this._pan = {
      x: pan.x,
      y: pan.y,
    };
    if (this.model.focus.isRoot) {
      this._pan.x -= this.model.width / 2;
      this._pan.y -= this.model.height / 2;
    }
  }

  /**
   * The element to insert the project's canvas into.
   */
  set canvasContainer(canvasContainer: HTMLElement) {
    this._canvasContainer = canvasContainer;
  }

  get canvasContainer(): HTMLElement | null {
    return this._canvasContainer;
  }

  /**
   * The background color of the canvas.
   */
  set canvasBGColor(canvasBGColor: string) {
    this._canvasBGColor = canvasBGColor;
  }

  get canvasBGColor(): string | null {
    return this._canvasBGColor;
  }

  /**
   * Render the view.
   */
  render(): void {
    this.zoom = this.model.zoom;
    this.pan = this.model.pan;

    this._buildSVGCanvas();
    this._displayCanvasInContainer(this._svgCanvas);
    this.resize();
    this._renderSVGCanvas();
    this._updateCanvasContainerBGColor();
  }

  /**
   * Render all frames in the project to make sure everything is loaded correctly.
   */
  prerender(): void {
    this.render();
    this.model.getAllFrames().forEach((frame) => {
      frame.view.render();
    });
  }

  /**
   * Resize the canvas to fit it's container div.
   */
  resize(): void {
    if (!this._canvasContainer || !this._svgCanvas) return;

    const containerWidth = this._canvasContainer.offsetWidth;
    const containerHeight = this._canvasContainer.offsetHeight;

    if (this._fitMode === "center") {
      this._svgCanvas.style.width = this.model.width + "px";
      this._svgCanvas.style.height = this.model.height + "px";
      this._svgCanvas.style.position = "absolute";
      this._svgCanvas.style.left =
        containerWidth / 2 - this.model.width / 2 + "px";
      this._svgCanvas.style.top =
        containerHeight / 2 - this.model.height / 2 + "px";
    } else if (this._fitMode === "fill") {
      const containerRatio = containerWidth / containerHeight;
      const projectRatio = this.model.width / this.model.height;

      if (containerRatio > projectRatio) {
        this._svgCanvas.style.height = containerHeight + "px";
        this._svgCanvas.style.width = containerHeight * projectRatio + "px";
      } else {
        this._svgCanvas.style.width = containerWidth + "px";
        this._svgCanvas.style.height = containerWidth / projectRatio + "px";
      }

      this._svgCanvas.style.position = "absolute";
      this._svgCanvas.style.left =
        containerWidth / 2 - parseInt(this._svgCanvas.style.width) / 2 + "px";
      this._svgCanvas.style.top =
        containerHeight / 2 - parseInt(this._svgCanvas.style.height) / 2 + "px";
    }

    this.paper.view.viewSize = new paper.Size(
      this.model.width,
      this.model.height
    );
  }

  protected _buildSVGCanvas(): void {
    // Implementation details...
  }

  protected _displayCanvasInContainer(canvas: HTMLCanvasElement | null): void {
    // Implementation details...
  }

  protected _renderSVGCanvas(): void {
    // Implementation details...
  }

  protected _updateCanvasContainerBGColor(): void {
    // Implementation details...
  }
}
