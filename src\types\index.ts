/**
 * 类型系统入口文件
 * 统一导出所有类型定义
 */

// 图片资源相关类型
export type {
    BaseResource,
    ImageResource,
    FolderResource,
    AtlasResource,
    ResourceItem,
    LayoutItem,
    LayoutResult,
    LayoutOptions,
} from './imageType';

// 项目相关类型
export type {
    ProjectFile,
    SerializableResourceItem,
    SerializableImageResource,
    SerializableFolderResource,
    SerializableAtlasResource,
    ProjectData,
    ProjectError,
    LoadResult,
    SaveResult,
    VersionMigration,
    ValidationResult
} from './projectType';

// 数据转换相关类型
export type {
    ProjectDataConverter,
    ResourceConverter,
    FileHandler,
    FileInfo,
    CompressionHandler,
    CompressedProjectFile,
    CompressionInfo,
    ImportHandler,
    BatchProcessOptions,
    BatchProgress,
    ExportHandler,
    ExportOptions,
    AtlasExportOptions,
    ImageExportOptions,
    BatchExportOptions,
    DataValidator,
    ValidationError,
    ValidationWarning,
    RepairResult,
    RepairChange
} from './converterType';

// 状态管理相关类型
export type {
    ProjectState,
    ResourceState,
    UIState,
    PanelState,
    Notification,
    NotificationAction,
    DragDropState,
    AtlasWorkState,
    EditState,
    EditAction,
    SelectionBounds,
    AppState,
    StateAction,
    StateManager,
    StateListener,
    StateMiddleware,
    StateSelector,
    StateUpdater,
    AsyncStateOperation,
    StatePersistConfig,
    StateTransform,
    StateValidator,
    StateDebugInfo
} from './stateType';

// 常用类型别名
export type ID = string;
export type Timestamp = string;
export type FilePath = string;
export type URL = string;
export type Base64 = string;
export type JSON = string;

// 常用枚举
export enum ResourceType {
    IMAGE = 'image',
    FOLDER = 'folder',
    ATLAS = 'atlas'
}

export enum LayoutAlgorithm {
    MAXRECTS = 'maxrects',
    SHELF = 'shelf',
    POTPACK = 'potpack',
    GUILLOTINE = 'guillotine'
}

export enum ExportFormat {
    PNG = 'png',
    JPG = 'jpg',
    WEBP = 'webp'
}

export enum Theme {
    LIGHT = 'light',
    DARK = 'dark'
}

export enum NotificationType {
    INFO = 'info',
    SUCCESS = 'success',
    WARNING = 'warning',
    ERROR = 'error'
}

// 工具类型
export type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type DeepRequired<T> = {
    [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;

// 函数类型
export type AsyncFunction<T = any, R = any> = (arg: T) => Promise<R>;
export type EventHandler<T = any> = (event: T) => void;
export type Callback<T = any> = (data: T) => void;
export type Predicate<T = any> = (item: T) => boolean;

// 响应式类型（用于Svelte）
export type Readable<T> = {
    subscribe(fn: (value: T) => void): () => void;
};

export type Writable<T> = Readable<T> & {
    set(value: T): void;
    update(fn: (value: T) => T): void;
};

// API响应类型
export interface APIResponse<T = any> {
    success: boolean;
    data?: T;
    error?: {
        code: string;
        message: string;
        details?: any;
    };
    meta?: {
        timestamp: string;
        version: string;
        requestId?: string;
    };
}

// 分页类型
export interface Pagination {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
}

export interface PaginatedResult<T> {
    items: T[];
    pagination: Pagination;
}

// 搜索类型
export interface SearchOptions {
    query: string;
    filters?: Record<string, any>;
    sort?: {
        field: string;
        order: 'asc' | 'desc';
    };
    pagination?: Pick<Pagination, 'page' | 'pageSize'>;
}

export interface SearchResult<T> extends PaginatedResult<T> {
    query: string;
    took: number; // 搜索耗时（毫秒）
    facets?: Record<string, any>;
}

// 配置类型
export interface AppConfig {
    version: string;
    environment: 'development' | 'production' | 'test';
    features: {
        [key: string]: boolean;
    };
    limits: {
        maxFileSize: number;
        maxProjectSize: number;
        maxConcurrentUploads: number;
    };
    ui: {
        theme: Theme;
        language: string;
        animations: boolean;
    };
}

// 性能监控类型
export interface PerformanceMetrics {
    memory: {
        used: number;
        total: number;
        percentage: number;
    };
    timing: {
        [operation: string]: {
            count: number;
            totalTime: number;
            averageTime: number;
            maxTime: number;
            minTime: number;
        };
    };
    errors: {
        count: number;
        recent: Array<{
            message: string;
            timestamp: string;
            stack?: string;
        }>;
    };
}

// 日志类型
export enum LogLevel {
    DEBUG = 'debug',
    INFO = 'info',
    WARN = 'warn',
    ERROR = 'error'
}

export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: string;
    category?: string;
    data?: any;
    stack?: string;
}

// 键盘快捷键类型
export interface KeyboardShortcut {
    key: string;
    modifiers?: ('ctrl' | 'alt' | 'shift' | 'meta')[];
    description: string;
    action: () => void;
    enabled?: boolean;
}

// 插件系统类型（为未来扩展预留）
export interface Plugin {
    name: string;
    version: string;
    description: string;
    author: string;
    main: string;
    dependencies?: string[];
    permissions?: string[];
}

export interface PluginAPI {
    registerCommand(name: string, handler: Function): void;
    registerPanel(name: string, component: any): void;
    registerExporter(name: string, exporter: any): void;
    registerImporter(name: string, importer: any): void;
}
