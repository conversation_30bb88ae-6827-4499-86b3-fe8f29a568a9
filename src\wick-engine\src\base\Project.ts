import * as PIXI from "pixi.js";
import { Base } from "./Base";
// import { View } from "../view/View";

/**
 * Wick项目类，管理整个项目的核心功能
 */
export class Project extends Base {
  private app: PIXI.Application;
  private _width: number;
  private _height: number;
  private _backgroundColor: number;
  private _zoom: number;
  private _pan: { x: number; y: number };
  private _rotation: number;

  /**
   * 创建一个项目
   * @param width - 项目宽度（像素）
   * @param height - 项目高度（像素）
   * @param backgroundColor - 项目背景颜色（十六进制）
   */
  constructor(
    args: { width?: number; height?: number; backgroundColor?: number } = {}
  ) {
    super({ identifier: "Project" });

    this._width = args.width || 720;
    this._height = args.height || 480;
    this._backgroundColor = args.backgroundColor || 0xffffff;
    this._zoom = 1.0;
    this._pan = { x: 0, y: 0 };
    this._rotation = 0;

    // 初始化PIXI应用
    this.app = new PIXI.Application({
      width: this._width,
      height: this._height,
      backgroundColor: this._backgroundColor,
      antialias: true,
    });

    // 设置舞台中心点
    this.app.stage.position.set(this._width / 2, this._height / 2);
  }

  /**
   * 将项目视图挂载到DOM容器
   */
  mount(container: HTMLElement): void {
    container.appendChild(this.app.view as HTMLCanvasElement);
  }

  /**
   * 重置视图位置和缩放
   */
  recenter(): void {
    this._pan = { x: 0, y: 0 };
    this._zoom = 1.0;
    this._rotation = 0;
    this.updateViewTransform();
  }

  /**
   * 更新视图变换
   */
  private updateViewTransform(): void {
    this.app.stage.position.set(
      this._width / 2 + this._pan.x,
      this._height / 2 + this._pan.y
    );
    this.app.stage.scale.set(this._zoom);
    this.app.stage.rotation = this._rotation;
  }

  /**
   * 获取项目宽度
   */
  get width(): number {
    return this._width;
  }

  /**
   * 获取项目高度
   */
  get height(): number {
    return this._height;
  }

  /**
   * 获取项目背景颜色
   */
  get backgroundColor(): number {
    return this._backgroundColor;
  }

  /**
   * 获取项目缩放值
   */
  get zoom(): number {
    return this._zoom;
  }

  /**
   * 设置项目缩放值
   */
  set zoom(value: number) {
    this._zoom = value;
    this.updateViewTransform();
  }

  /**
   * 获取项目平移值
   */
  get pan(): { x: number; y: number } {
    return this._pan;
  }

  /**
   * 设置项目平移值
   */
  set pan(value: { x: number; y: number }) {
    this._pan = value;
    this.updateViewTransform();
  }

  /**
   * 获取项目旋转值
   */
  get rotation(): number {
    return this._rotation;
  }

  /**
   * 设置项目旋转值
   */
  set rotation(value: number) {
    this._rotation = value;
    this.updateViewTransform();
  }

  /**
   * 获取PIXI应用实例
   */
  get view(): PIXI.Application {
    return this.app;
  }
}
