/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import * as paper from "paper";

interface HandleArgs {
  name: string;
  type: "rotation" | "scale" | "pivot";
  center: paper.Point;
  fillColor: string;
  strokeColor: string;
}

interface BuildArgs {
  boxRotation?: number;
  items?: paper.Item[];
  pivot?: paper.Point;
}

class SelectionWidget {
  static BOX_STROKE_WIDTH = 1;
  static BOX_STROKE_COLOR = "rgba(100,150,255,1.0)";
  static HANDLE_RADIUS = 5;
  static HANDLE_STROKE_WIDTH = SelectionWidget.BOX_STROKE_WIDTH;
  static HANDLE_STROKE_COLOR = SelectionWidget.BOX_STROKE_COLOR;
  static HANDLE_FILL_COLOR = "rgba(255,255,255,0.3)";
  static PIVOT_STROKE_WIDTH = SelectionWidget.BOX_STROKE_WIDTH;
  static PIVOT_FILL_COLOR = "rgba(255,255,255,0.5)";
  static PIVOT_STROKE_COLOR = "rgba(0,0,0,1)";
  static PIVOT_RADIUS = SelectionWidget.HANDLE_RADIUS;
  static ROTATION_HOTSPOT_RADIUS = 20;
  static ROTATION_HOTSPOT_FILLCOLOR = "rgba(100,150,255,0.5)";
  static GHOST_STROKE_COLOR = "rgba(0, 0, 0, 1.0)";
  static GHOST_STROKE_WIDTH = 1;

  private _layer: paper.Layer;
  private _item: paper.Group;
  private _itemsInSelection: paper.Item[] = [];
  private _boxRotation: number = 0;
  private _pivot: paper.Point = new paper.Point();
  private _boundingBox: paper.Rectangle = new paper.Rectangle();
  private _currentTransformation: "translate" | "scale" | "rotate" | null =
    null;
  private _ghost: paper.Group | null = null;
  private _pivotPointHandle: paper.Path.Circle | null = null;
  private _center: paper.Point = new paper.Point();

  constructor(args: { layer?: paper.Layer } = {}) {
    this._layer = args.layer || paper.project.activeLayer;
    this._item = new paper.Group({ insert: false });
  }

  get item(): paper.Group {
    return this._item;
  }

  get layer(): paper.Layer {
    return this._layer;
  }

  set layer(layer: paper.Layer) {
    this._layer = layer;
  }

  get boxRotation(): number {
    return this._boxRotation;
  }

  set boxRotation(boxRotation: number) {
    this._boxRotation = boxRotation;
  }

  get itemsInSelection(): paper.Item[] {
    return this._itemsInSelection;
  }

  get pivot(): paper.Point {
    return this._pivot;
  }

  set pivot(pivot: paper.Point) {
    this._pivot = pivot;
  }

  get position(): paper.Point {
    return this._boundingBox.topLeft.rotate(this.rotation, this.pivot);
  }

  set position(position: paper.Point) {
    const d = position.subtract(this.position);
    this.translateSelection(d);
  }

  get width(): number {
    return this._boundingBox.width;
  }

  set width(width: number) {
    const d = width / this.width;
    if (d === 0) {
      this.scaleSelection(new paper.Point(0.001, 1.0));
    } else {
      this.scaleSelection(new paper.Point(d, 1.0));
    }
  }

  get height(): number {
    return this._boundingBox.height;
  }

  set height(height: number) {
    const d = height / this.height;
    this.scaleSelection(new paper.Point(1.0, d));
  }

  get rotation(): number {
    return this._boxRotation;
  }

  set rotation(rotation: number) {
    const d = rotation - this.rotation;
    this.rotateSelection(d);
  }

  get boundingBox(): paper.Rectangle {
    return this._boundingBox;
  }

  get currentTransformation(): "translate" | "scale" | "rotate" | null {
    return this._currentTransformation;
  }

  set currentTransformation(
    currentTransformation: "translate" | "scale" | "rotate" | null
  ) {
    if (
      currentTransformation &&
      ["translate", "scale", "rotate"].indexOf(currentTransformation) === -1
    ) {
      console.error(
        "Paper.SelectionWidget: Invalid transformation type: " +
          currentTransformation
      );
      this._currentTransformation = null;
    } else {
      this._currentTransformation = currentTransformation;
    }
  }

  build(args: BuildArgs = {}): void {
    this._itemsInSelection = args.items || [];
    this._boxRotation = args.boxRotation || 0;
    this._pivot = args.pivot || new paper.Point();

    this._boundingBox = this._calculateBoundingBox();

    this.item.remove();
    this.item.removeChildren();

    if (this._ghost) {
      this._ghost.remove();
    }
    if (this._pivotPointHandle) {
      this._pivotPointHandle.remove();
    }

    if (this._itemsInSelection.length > 0) {
      this._center = this._calculateBoundingBoxOfItems(
        this._itemsInSelection
      ).center;
      this._buildGUI();
      this.layer.addChild(this.item);
    }
  }

  startTransformation(item: paper.Item): void {
    this._ghost = this._buildGhost();
    this._layer.addChild(this._ghost);

    if (item.data.handleType === "rotation") {
      this.currentTransformation = "rotate";
    } else if (item.data.handleType === "scale") {
      this.currentTransformation = "scale";
    } else {
      this.currentTransformation = "translate";
    }

    this._ghost.data.initialPosition = this._ghost.position;
    this._ghost.data.scale = new paper.Point(1, 1);
  }

  updateTransformation(item: paper.Item, e: paper.MouseEvent): void {
    if (!this._ghost) return;

    if (this.currentTransformation === "translate") {
      this._ghost.position = this._ghost.position.add(e.delta);
    } else if (this.currentTransformation === "scale") {
      const lastPoint = e.point.subtract(e.delta);
      const currentPoint = e.point;
      lastPoint.rotate(-this.boxRotation, this.pivot);
      currentPoint.rotate(-this.boxRotation, this.pivot);
      const pivotToLastPointVector = lastPoint.subtract(this.pivot);
      const pivotToCurrentPointVector = currentPoint.subtract(this.pivot);
      const scaleAmt = pivotToCurrentPointVector.divide(pivotToLastPointVector);

      if (
        item.data.handleEdge === "topCenter" ||
        item.data.handleEdge === "bottomCenter"
      ) {
        scaleAmt.x = 1.0;
      }
      if (
        item.data.handleEdge === "leftCenter" ||
        item.data.handleEdge === "rightCenter"
      ) {
        scaleAmt.y = 1.0;
      }

      if (e.modifiers.shift) {
        scaleAmt.y = scaleAmt.x;
      }

      this._ghost.data.scale = this._ghost.data.scale.multiply(scaleAmt);

      this._ghost.matrix = new paper.Matrix();
      this._ghost.rotate(-this.boxRotation);
      this._ghost.scale(
        this._ghost.data.scale.x,
        this._ghost.data.scale.y,
        this.pivot
      );
      this._ghost.rotate(this.boxRotation);
    } else if (this.currentTransformation === "rotate") {
      const lastPoint = e.point.subtract(e.delta);
      const currentPoint = e.point;
      const pivotToLastPointVector = lastPoint.subtract(this.pivot);
      const pivotToCurrentPointVector = currentPoint.subtract(this.pivot);
      const pivotToLastPointAngle = pivotToLastPointVector.angle;
      const pivotToCurrentPointAngle = pivotToCurrentPointVector.angle;
      const rotation = pivotToCurrentPointAngle - pivotToLastPointAngle;
      this._ghost.rotate(rotation, this.pivot);
      this.boxRotation += rotation;
    }
  }

  finishTransformation(item: paper.Item): void {
    if (!this._currentTransformation || !this._ghost) return;

    this._ghost.remove();

    if (this.currentTransformation === "translate") {
      const d = this._ghost.position.subtract(this._ghost.data.initialPosition);
      this.translateSelection(d);
    } else if (this.currentTransformation === "scale") {
      this.scaleSelection(this._ghost.data.scale);
    } else if (this.currentTransformation === "rotate") {
      this.rotateSelection(this._ghost.rotation);
    }

    this._currentTransformation = null;
  }

  flipHorizontally(): void {
    this.scaleSelection(new paper.Point(-1.0, 1.0));
  }

  flipVertically(): void {
    this.scaleSelection(new paper.Point(1.0, -1.0));
  }

  translateSelection(delta: paper.Point): void {
    this._itemsInSelection.forEach((item) => {
      item.position = item.position.add(delta);
    });
    this.pivot = this.pivot.add(delta);
  }

  scaleSelection(scale: paper.Point): void {
    this._itemsInSelection.forEach((item) => {
      item.rotate(-this.boxRotation, this.pivot);
      item.scale(scale, this.pivot);
      item.rotate(this.boxRotation, this.pivot);
    });
  }

  rotateSelection(angle: number): void {
    this._itemsInSelection.forEach((item) => {
      item.rotate(angle, this.pivot);
    });
  }

  private _buildGUI(): void {
    this.item.addChild(this._buildBorder());

    if (this._itemsInSelection.length > 1) {
      this.item.addChildren(this._buildItemOutlines());
    }

    const guiElements: paper.Item[] = [];

    guiElements.push(this._buildRotationHotspot("topLeft"));
    guiElements.push(this._buildRotationHotspot("topRight"));
    guiElements.push(this._buildRotationHotspot("bottomLeft"));
    guiElements.push(this._buildRotationHotspot("bottomRight"));

    guiElements.push(this._buildScalingHandle("topLeft"));
    guiElements.push(this._buildScalingHandle("topRight"));
    guiElements.push(this._buildScalingHandle("bottomLeft"));
    guiElements.push(this._buildScalingHandle("bottomRight"));
    guiElements.push(this._buildScalingHandle("topCenter"));
    guiElements.push(this._buildScalingHandle("bottomCenter"));
    guiElements.push(this._buildScalingHandle("leftCenter"));
    guiElements.push(this._buildScalingHandle("rightCenter"));

    this.item.addChildren(guiElements);

    this._pivotPointHandle = this._buildPivotPointHandle();
    this.layer.addChild(this._pivotPointHandle);

    this.item.rotate(this.boxRotation, this._center);

    this.item.children.forEach((child) => {
      child.data.isSelectionBoxGUI = true;
    });
  }

  private _buildBorder(): paper.Path.Rectangle {
    const border = new paper.Path.Rectangle({
      name: "border",
      from: this.boundingBox.topLeft,
      to: this.boundingBox.bottomRight,
      strokeWidth: SelectionWidget.BOX_STROKE_WIDTH,
      strokeColor: SelectionWidget.BOX_STROKE_COLOR,
      insert: false,
    });
    border.data.isBorder = true;
    return border;
  }

  private _buildItemOutlines(): paper.Path.Rectangle[] {
    return this._itemsInSelection.map((item) => {
      const clone = item.clone({ insert: false });
      clone.rotate(-this.boxRotation, this._center);
      const bounds = clone.bounds;
      const border = new paper.Path.Rectangle({
        from: bounds.topLeft,
        to: bounds.bottomRight,
        strokeWidth: SelectionWidget.BOX_STROKE_WIDTH,
        strokeColor: SelectionWidget.BOX_STROKE_COLOR,
      });
      border.remove();
      return border;
    });
  }

  private _buildScalingHandle(edge: string): paper.Path.Circle {
    return this._buildHandle({
      name: edge,
      type: "scale",
      center: this.boundingBox[edge],
      fillColor: SelectionWidget.HANDLE_FILL_COLOR,
      strokeColor: SelectionWidget.HANDLE_STROKE_COLOR,
    });
  }

  private _buildPivotPointHandle(): paper.Path.Circle {
    const handle = this._buildHandle({
      name: "pivot",
      type: "pivot",
      center: this.pivot,
      fillColor: SelectionWidget.PIVOT_FILL_COLOR,
      strokeColor: SelectionWidget.PIVOT_STROKE_COLOR,
    });
    handle.locked = true;
    return handle;
  }

  private _buildHandle(args: HandleArgs): paper.Path.Circle {
    if (!args.name) console.error("_createHandle: args.name is required");
    if (!args.type) console.error("_createHandle: args.type is required");
    if (!args.center) console.error("_createHandle: args.center is required");
    if (!args.fillColor)
      console.error("_createHandle: args.fillColor is required");
    if (!args.strokeColor)
      console.error("_createHandle: args.strokeColor is required");

    const circle = new paper.Path.Circle({
      center: args.center,
      radius: SelectionWidget.HANDLE_RADIUS / paper.view.zoom,
      strokeWidth: SelectionWidget.HANDLE_STROKE_WIDTH / paper.view.zoom,
      strokeColor: args.strokeColor,
      fillColor: args.fillColor,
      insert: false,
    });
    circle.applyMatrix = false;
    circle.data.isSelectionBoxGUI = true;
    circle.data.handleType = args.type;
    circle.data.handleEdge = args.name;
    return circle;
  }

  private _buildRotationHotspot(cornerName: string): paper.Path {
    const r = SelectionWidget.ROTATION_HOTSPOT_RADIUS / paper.view.zoom;
    const hotspot = new paper.Path([
      new paper.Point(0, 0),
      new paper.Point(0, r),
      new paper.Point(r, r),
      new paper.Point(r, -r),
      new paper.Point(-r, -r),
      new paper.Point(-r, 0),
    ]);
    hotspot.fillColor = new paper.Color(
      SelectionWidget.ROTATION_HOTSPOT_FILLCOLOR
    );
    hotspot.position.x = this.boundingBox[cornerName].x;
    hotspot.position.y = this.boundingBox[cornerName].y;

    const rotationAngles: { [key: string]: number } = {
      topRight: 0,
      bottomRight: 90,
      bottomLeft: 180,
      topLeft: 270,
    };
    hotspot.rotate(rotationAngles[cornerName]);

    hotspot.data.handleType = "rotation";
    hotspot.data.handleEdge = cornerName;

    return hotspot;
  }

  private _buildGhost(): paper.Group {
    const ghost = new paper.Group({
      insert: false,
      applyMatrix: false,
    });

    this._itemsInSelection.forEach((item) => {
      const outline = item.clone();
      outline.remove();
      outline.fillColor = new paper.Color("rgba(0,0,0,0)");
      outline.strokeColor = new paper.Color(SelectionWidget.GHOST_STROKE_COLOR);
      outline.strokeWidth = SelectionWidget.GHOST_STROKE_WIDTH * 2;
      ghost.addChild(outline);

      const outline2 = outline.clone();
      outline2.remove();
      outline2.fillColor = new paper.Color("rgba(0,0,0,0)");
      outline2.strokeColor = new paper.Color("#ffffff");
      outline2.strokeWidth = SelectionWidget.GHOST_STROKE_WIDTH;
      ghost.addChild(outline2);
    });

    const boundsOutline = new paper.Path.Rectangle({
      from: this.boundingBox.topLeft,
      to: this.boundingBox.bottomRight,
      fillColor: new paper.Color("rgba(0,0,0,0)"),
      strokeColor: new paper.Color(SelectionWidget.GHOST_STROKE_COLOR),
      strokeWidth: SelectionWidget.GHOST_STROKE_WIDTH,
      applyMatrix: false,
    });
    boundsOutline.rotate(this.boxRotation, this._center);
    ghost.addChild(boundsOutline);

    ghost.opacity = 0.5;

    return ghost;
  }

  private _calculateBoundingBox(): paper.Rectangle {
    if (this._itemsInSelection.length === 0) {
      return new paper.Rectangle();
    }

    const center = this._calculateBoundingBoxOfItems(
      this._itemsInSelection
    ).center;

    const itemsForBoundsCalc = this._itemsInSelection.map((item) => {
      const clone = item.clone();
      clone.rotate(-this.boxRotation, center);
      clone.remove();
      return clone;
    });

    return this._calculateBoundingBoxOfItems(itemsForBoundsCalc);
  }

  private _calculateBoundingBoxOfItems(items: paper.Item[]): paper.Rectangle {
    let bounds: paper.Rectangle | null = null;
    items.forEach((item) => {
      bounds = bounds ? bounds.unite(item.bounds) : item.bounds;
    });
    return bounds || new paper.Rectangle();
  }
}

(paper as any).PaperScope.inject({
  SelectionWidget: SelectionWidget,
});
