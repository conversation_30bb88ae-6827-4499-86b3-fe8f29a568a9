/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { View } from "./View";
import { Clip } from "../base/Clip";

export class Button extends View {
  protected _model: Clip;

  /**
   * Create a new Button view
   * @param model - The Button object containing the data to use to draw this View
   */
  constructor(model: Clip) {
    super(model);
  }

  /**
   * The Button object to use the data from to create this View
   */
  set model(model: Clip) {
    this._model = model;
  }

  get model(): Clip {
    return this._model;
  }

  /**
   * Render the button view
   */
  render(): void {
    super.render();
    // Additional button-specific rendering logic can be added here
  }
}
