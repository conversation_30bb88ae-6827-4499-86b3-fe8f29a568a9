/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Asset } from "./Asset";

interface FileAssetArgs extends AssetArgs {
  filename?: string;
  src?: string;
}

export class FileAsset extends Asset {
  protected fileExtension: string | null;
  protected MIMEType: string | null;
  protected filename: string;

  /**
   * Returns all valid MIME types for files which can be converted to Wick Assets.
   * @return {string[]} Array of strings of MIME types in the form MediaType/Subtype.
   */
  static getValidMIMETypes(): string[] {
    let imageTypes = Wick.ImageAsset.getValidMIMETypes();
    let soundTypes = Wick.SoundAsset.getValidMIMETypes();
    let fontTypes = Wick.FontAsset.getValidMIMETypes();
    let clipTypes = Wick.ClipAsset.getValidMIMETypes();
    let svgTypes = Wick.SVGAsset.getValidMIMETypes();
    let gifTypes = Wick.GIFAsset.getValidMIMETypes();
    return imageTypes
      .concat(soundTypes)
      .concat(fontTypes)
      .concat(clipTypes)
      .concat(svgTypes)
      .concat(gifTypes);
  }

  /**
   * Returns all valid extensions types for files which can be attempted to be
   * converted to Wick Assets.
   * @return  {string[]} Array of strings representing extensions.
   */
  static getValidExtensions(): string[] {
    let imageExtensions = Wick.ImageAsset.getValidExtensions();
    let soundExtensions = Wick.SoundAsset.getValidExtensions();
    let fontExtensions = Wick.FontAsset.getValidExtensions();
    let clipExtensions = Wick.ClipAsset.getValidExtensions();
    let svgExtensions = Wick.SVGAsset.getValidExtensions();
    let gifExtensions = Wick.GIFAsset.getValidExtensions();
    return imageExtensions
      .concat(soundExtensions)
      .concat(fontExtensions)
      .concat(clipExtensions)
      .concat(svgExtensions)
      .concat(gifExtensions);
  }

  /**
   * Create a new FileAsset.
   * @param {FileAssetArgs} args - Asset constructor arguments
   */
  constructor(args: FileAssetArgs = {}) {
    args.name = args.filename;
    super(args);

    this.fileExtension = null;
    this.MIMEType = null;
    this.filename = args.filename || "";
    this.src = args.src;
  }

  protected _serialize(args?: SerializeArgs): Record<string, any> {
    const data = super._serialize(args);

    data.filename = this.filename;
    data.MIMEType = this.MIMEType;
    data.fileExtension = this.fileExtension;

    if (args?.includeOriginalSource) {
      data.originalSource = this.src;
    }

    return data;
  }

  protected _deserialize(data: Record<string, any>): void {
    super._deserialize(data);

    this.filename = data.filename;
    this.MIMEType = data.MIMEType;
    this.fileExtension = data.fileExtension;

    if (data.originalSource) {
      this.src = data.originalSource;
    }
  }

  public get classname(): string {
    return "FileAsset";
  }

  /**
   * The source of the data of the asset, in base64. Returns null if the file is not found.
   * @type {string | null}
   */
  public get src(): string | null {
    let file = Wick.FileCache.getFile(this.uuid);
    if (file) return file.src;
    return null;
  }

  public set src(src: string | null) {
    if (src) {
      Wick.FileCache.addFile(src, this.uuid);
      this.fileExtension = this._fileExtensionOfString(src);
      this.MIMEType = this._MIMETypeOfString(src);
    }
  }

  /**
   * Loads data about the file into the asset.
   */
  public load(callback: () => void): void {
    callback();
  }

  /**
   * Copies the FileAsset and also copies the src in FileCache.
   * @return {FileAsset}
   */
  public copy(): FileAsset {
    const copy = super.copy() as FileAsset;
    copy.src = this.src;
    return copy;
  }

  protected _MIMETypeOfString(string: string): string | null {
    return string.split(":")[1]?.split(",")[0]?.split(";")[0] || null;
  }

  protected _fileExtensionOfString(string: string): string | null {
    const MIMEType = this._MIMETypeOfString(string);
    return MIMEType ? MIMEType.split("/")[1] : null;
  }
}
