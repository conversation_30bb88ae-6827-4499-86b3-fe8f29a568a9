/**
 * 多尺寸缩略图管理器
 * 管理不同尺寸的缩略图生成、缓存和使用
 */

import { LRUCache } from './lruCache';
import { workerManager } from './workerManager';
import type { ResourceItem } from '../types/imageType';

// 缩略图尺寸配置
export const THUMBNAIL_SIZES = {
  TINY: 32,      // 极小图标
  SMALL: 64,     // 小图标
  MEDIUM: 128,   // 中等预览
  LARGE: 256,    // 大预览
  XLARGE: 512    // 超大预览
} as const;

export type ThumbnailSize = keyof typeof THUMBNAIL_SIZES;

interface ThumbnailEntry {
  size: number;
  dataUrl: string;
  blob: Blob;
  width: number;
  height: number;
  quality: number;
  timestamp: number;
}

interface ThumbnailSet {
  resourceId: string;
  originalWidth: number;
  originalHeight: number;
  thumbnails: Map<number, ThumbnailEntry>;
  lastAccessed: number;
}

export class ThumbnailManager {
  private cache: LRUCache<ThumbnailSet>;
  private generationQueue = new Map<string, Promise<ThumbnailSet>>();
  private qualitySettings = new Map<number, number>();

  constructor() {
    // 初始化缓存 - 更大的缓存空间用于缩略图
    this.cache = new LRUCache<ThumbnailSet>(
      200 * 1024 * 1024, // 200MB
      500 // 最多500个资源的缩略图集
    );

    // 设置不同尺寸的质量
    this.qualitySettings.set(THUMBNAIL_SIZES.TINY, 0.6);
    this.qualitySettings.set(THUMBNAIL_SIZES.SMALL, 0.7);
    this.qualitySettings.set(THUMBNAIL_SIZES.MEDIUM, 0.8);
    this.qualitySettings.set(THUMBNAIL_SIZES.LARGE, 0.85);
    this.qualitySettings.set(THUMBNAIL_SIZES.XLARGE, 0.9);

    console.log('🖼️ ThumbnailManager: 初始化完成');
  }

  /**
   * 获取缩略图
   */
  async getThumbnail(
    resource: ResourceItem,
    size: ThumbnailSize | number,
    options: {
      progressive?: boolean;
      onProgress?: (progress: any) => void;
    } = {}
  ): Promise<ThumbnailEntry | null> {
    if (resource.type !== 'image' || !resource.data) {
      return null;
    }

    const targetSize = typeof size === 'string' ? THUMBNAIL_SIZES[size] : size;
    const cacheKey = resource.id;

    // 检查缓存
    let thumbnailSet = this.cache.get(cacheKey);
    
    if (thumbnailSet) {
      thumbnailSet.lastAccessed = Date.now();
      const thumbnail = thumbnailSet.thumbnails.get(targetSize);
      
      if (thumbnail) {
        console.log('✅ ThumbnailManager: 缓存命中', {
          resourceId: resource.id,
          size: targetSize
        });
        return thumbnail;
      }
    }

    // 生成缩略图集
    if (!thumbnailSet) {
      thumbnailSet = await this.generateThumbnailSet(resource, options);
    }

    // 返回指定尺寸的缩略图
    return thumbnailSet.thumbnails.get(targetSize) || null;
  }

  /**
   * 生成完整的缩略图集
   */
  private async generateThumbnailSet(
    resource: ResourceItem,
    options: {
      progressive?: boolean;
      onProgress?: (progress: any) => void;
    } = {}
  ): Promise<ThumbnailSet> {
    const cacheKey = resource.id;

    // 检查是否已在生成队列中
    if (this.generationQueue.has(cacheKey)) {
      return this.generationQueue.get(cacheKey)!;
    }

    // 创建生成Promise
    const generationPromise = this.doGenerateThumbnailSet(resource, options);
    this.generationQueue.set(cacheKey, generationPromise);

    try {
      const result = await generationPromise;
      
      // 缓存结果
      const estimatedSize = this.estimateThumbnailSetSize(result);
      this.cache.set(cacheKey, result, estimatedSize);
      
      console.log('✅ ThumbnailManager: 缩略图集生成完成', {
        resourceId: resource.id,
        thumbnailCount: result.thumbnails.size,
        estimatedSize: `${(estimatedSize / 1024).toFixed(1)}KB`
      });

      return result;
    } finally {
      this.generationQueue.delete(cacheKey);
    }
  }

  /**
   * 实际生成缩略图集
   */
  private async doGenerateThumbnailSet(
    resource: ResourceItem,
    options: {
      progressive?: boolean;
      onProgress?: (progress: any) => void;
    } = {}
  ): Promise<ThumbnailSet> {
    console.log('🔄 ThumbnailManager: 开始生成缩略图集', {
      resourceId: resource.id,
      resourceName: resource.name,
      progressive: options.progressive
    });

    const sizes = Object.values(THUMBNAIL_SIZES);
    
    if (options.progressive) {
      // 渐进式生成 - 先生成小尺寸，再生成大尺寸
      return this.generateProgressiveThumbnails(resource, sizes, options.onProgress);
    } else {
      // 批量生成所有尺寸
      return this.generateBatchThumbnails(resource, sizes, options.onProgress);
    }
  }

  /**
   * 渐进式生成缩略图
   */
  private async generateProgressiveThumbnails(
    resource: ResourceItem,
    sizes: number[],
    onProgress?: (progress: any) => void
  ): Promise<ThumbnailSet> {
    const thumbnailSet: ThumbnailSet = {
      resourceId: resource.id,
      originalWidth: resource.width || 0,
      originalHeight: resource.height || 0,
      thumbnails: new Map(),
      lastAccessed: Date.now()
    };

    // 按尺寸从小到大排序
    const sortedSizes = [...sizes].sort((a, b) => a - b);
    
    for (let i = 0; i < sortedSizes.length; i++) {
      const size = sortedSizes[i];
      const quality = this.qualitySettings.get(size) || 0.8;

      try {
        // 使用Worker生成单个缩略图
        const result = await workerManager.executeTask('generate-thumbnails', {
          id: `${resource.id}_${size}`,
          buffer: resource.data,
          originalType: resource.originalFile?.type,
          sizes: [size],
          quality
        });

        if (result.thumbnails && result.thumbnails.length > 0) {
          const thumbnail = result.thumbnails[0];
          thumbnailSet.thumbnails.set(size, {
            size,
            dataUrl: thumbnail.dataUrl,
            blob: thumbnail.blob,
            width: thumbnail.width,
            height: thumbnail.height,
            quality,
            timestamp: Date.now()
          });

          // 发送进度更新
          if (onProgress) {
            onProgress({
              stage: 'progressive',
              completed: i + 1,
              total: sortedSizes.length,
              currentSize: size,
              thumbnail: thumbnail.dataUrl
            });
          }

          console.log('📸 ThumbnailManager: 渐进式缩略图完成', {
            resourceId: resource.id,
            size,
            progress: `${i + 1}/${sortedSizes.length}`
          });
        }
      } catch (error) {
        console.error('❌ ThumbnailManager: 生成缩略图失败', {
          resourceId: resource.id,
          size,
          error
        });
      }
    }

    return thumbnailSet;
  }

  /**
   * 批量生成缩略图
   */
  private async generateBatchThumbnails(
    resource: ResourceItem,
    sizes: number[],
    onProgress?: (progress: any) => void
  ): Promise<ThumbnailSet> {
    const thumbnailSet: ThumbnailSet = {
      resourceId: resource.id,
      originalWidth: resource.width || 0,
      originalHeight: resource.height || 0,
      thumbnails: new Map(),
      lastAccessed: Date.now()
    };

    try {
      // 使用Worker批量生成所有尺寸
      const result = await workerManager.executeTask('generate-thumbnails', {
        id: resource.id,
        buffer: resource.data,
        originalType: resource.originalFile?.type,
        sizes,
        quality: 0.8
      }, {
        onProgress: (progress) => {
          if (onProgress) {
            onProgress({
              stage: 'batch',
              ...progress
            });
          }
        }
      });

      // 处理结果
      if (result.thumbnails) {
        for (const thumbnail of result.thumbnails) {
          const quality = this.qualitySettings.get(thumbnail.size) || 0.8;
          thumbnailSet.thumbnails.set(thumbnail.size, {
            size: thumbnail.size,
            dataUrl: thumbnail.dataUrl,
            blob: thumbnail.blob,
            width: thumbnail.width,
            height: thumbnail.height,
            quality,
            timestamp: Date.now()
          });
        }
      }

      console.log('📸 ThumbnailManager: 批量缩略图完成', {
        resourceId: resource.id,
        thumbnailCount: thumbnailSet.thumbnails.size
      });

    } catch (error) {
      console.error('❌ ThumbnailManager: 批量生成缩略图失败', {
        resourceId: resource.id,
        error
      });
    }

    return thumbnailSet;
  }

  /**
   * 估算缩略图集大小
   */
  private estimateThumbnailSetSize(thumbnailSet: ThumbnailSet): number {
    let totalSize = 0;
    for (const thumbnail of thumbnailSet.thumbnails.values()) {
      // 估算DataURL的大小
      totalSize += thumbnail.dataUrl.length * 0.75; // Base64编码约为原始大小的75%
    }
    return totalSize;
  }

  /**
   * 预加载缩略图
   */
  async preloadThumbnails(
    resources: ResourceItem[],
    size: ThumbnailSize | number = 'MEDIUM',
    options: {
      maxConcurrent?: number;
      onProgress?: (completed: number, total: number) => void;
    } = {}
  ): Promise<void> {
    const { maxConcurrent = 3, onProgress } = options;
    const imageResources = resources.filter(r => r.type === 'image' && r.data);
    
    console.log('🚀 ThumbnailManager: 开始预加载缩略图', {
      count: imageResources.length,
      size,
      maxConcurrent
    });

    let completed = 0;
    const batches = [];
    
    // 分批处理
    for (let i = 0; i < imageResources.length; i += maxConcurrent) {
      batches.push(imageResources.slice(i, i + maxConcurrent));
    }

    for (const batch of batches) {
      await Promise.allSettled(
        batch.map(async (resource) => {
          try {
            await this.getThumbnail(resource, size, { progressive: true });
            completed++;
            if (onProgress) {
              onProgress(completed, imageResources.length);
            }
          } catch (error) {
            console.error('❌ ThumbnailManager: 预加载失败', {
              resourceId: resource.id,
              error
            });
            completed++;
            if (onProgress) {
              onProgress(completed, imageResources.length);
            }
          }
        })
      );
    }

    console.log('✅ ThumbnailManager: 预加载完成', {
      completed,
      total: imageResources.length
    });
  }

  /**
   * 清理过期缩略图
   */
  cleanup(): void {
    const maxAge = 30 * 60 * 1000; // 30分钟
    this.cache.cleanupExpired(maxAge);
  }

  /**
   * 获取统计信息
   */
  getStats() {
    const cacheStats = this.cache.getStats();
    return {
      ...cacheStats,
      generationQueue: this.generationQueue.size,
      thumbnailSizes: Object.entries(THUMBNAIL_SIZES)
    };
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.cache.clear();
    this.generationQueue.clear();
    console.log('🧹 ThumbnailManager: 已销毁');
  }
}

// 全局实例
export const thumbnailManager = new ThumbnailManager();

// 定期清理
setInterval(() => {
  thumbnailManager.cleanup();
}, 5 * 60 * 1000); // 每5分钟清理一次
