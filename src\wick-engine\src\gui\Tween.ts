/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import { GUIElement } from "./GUIElement";
import { Tween as BaseTween } from "../base/Tween";

export class Tween extends GUIElement {
  protected _tween: BaseTween;

  constructor(model: Base) {
    super(model);
    this._tween = model as BaseTween;
  }

  draw(): void {
    super.draw();

    const ctx = this.ctx;

    // Draw tween diamond
    ctx.save();
    ctx.translate(
      this._tween.playheadPosition * this.gridCellWidth,
      this._tween.parentLayer.index * this.gridCellHeight +
        this.gridCellHeight / 2
    );
    ctx.rotate(Math.PI / 4);

    const r = GUIElement.TWEEN_DIAMOND_RADIUS;
    ctx.fillStyle = GUIElement.TWEEN_DIAMOND_COLOR;
    ctx.strokeStyle = GUIElement.TWEEN_DIAMOND_BORDER_COLOR;
    ctx.lineWidth = GUIElement.TWEEN_DIAMOND_BORDER_WIDTH;

    ctx.beginPath();
    ctx.roundRect(-r, -r, r * 2, r * 2, 3);
    ctx.fill();
    ctx.stroke();

    ctx.restore();
  }
}
