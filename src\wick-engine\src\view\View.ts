/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { Base } from "../base/Base";
import * as paper from "paper";

type EventHandler = (e: Event, actionName?: string) => void;

interface EventHandlers {
  [key: string]: EventHandler[];
}

export class View {
  protected static _paperScope: paper.PaperScope;
  protected _model: Base;
  protected _eventHandlers: EventHandlers;

  /**
   * The paper.js scope that all Wick.View subclasses will use to render to.
   */
  static get paperScope(): paper.PaperScope {
    if (!this._paperScope) {
      this._paperScope = new paper.PaperScope();

      // Create dummy paper.js instance so we can access paper classes
      const canvas = window.document.createElement("canvas");
      this._paperScope.setup(canvas);
    }

    // Use active paper scope for window.paper alias
    window.paper = this._paperScope;

    // Activate the paper scope
    this._paperScope.activate();

    return this._paperScope;
  }

  /**
   * Create a new View instance
   * @param model - The object containing the data to use to draw this View
   */
  constructor(model: Base) {
    this._model = model;
    this._eventHandlers = {};
  }

  /**
   * The object to use the data from to create this View
   */
  set model(model: Base) {
    this._model = model;
  }

  get model(): Base {
    return this._model;
  }

  /**
   * The paper.js scope instance
   */
  get paper(): paper.PaperScope {
    return View.paperScope;
  }

  /**
   * Render the view
   */
  render(): void {
    // Implemented by subclasses
  }

  /**
   * Register an event handler
   * @param eventName - Name of the event to handle
   * @param fn - Function to call when the event occurs
   */
  on(eventName: string, fn: EventHandler): void {
    if (!this._eventHandlers[eventName]) {
      this._eventHandlers[eventName] = [];
    }
    this._eventHandlers[eventName].push(fn);
  }

  /**
   * Fire an event
   * @param eventName - Name of the event to fire
   * @param e - Event object
   * @param actionName - Optional action name associated with the event
   */
  fireEvent(eventName: string, e: Event, actionName?: string): void {
    const eventFns = this._eventHandlers[eventName];
    if (!eventFns) return;
    eventFns.forEach((fn) => {
      fn(e, actionName);
    });
  }
}

// Add paper to window type
declare global {
  interface Window {
    paper: paper.PaperScope;
  }
}
