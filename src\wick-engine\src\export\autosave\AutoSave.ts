/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

declare global {
  interface Window {
    Wick: any;
  }
}

interface AutoSaveData {
  uuid: string;
  name: string;
  lastModified: number;
  projectData: any;
  objectsData: any[];
}

export class AutoSave {
  private static ENABLE_PERF_TIMERS = false;

  /**
   * The key used to store the list of autosaved projects.
   */
  static get AUTOSAVES_LIST_KEY(): string {
    return "autosaveList";
  }

  /**
   * The prefix to use for keys to save project autosave data.
   */
  static get AUTOSAVE_DATA_PREFIX(): string {
    return "autosave_";
  }

  /**
   * Saves a given project to localforage.
   * @param project - the project to store in the AutoSave system
   * @param callback - callback function to execute after saving
   */
  static save(project: any, callback: () => void): void {
    if (this.ENABLE_PERF_TIMERS) console.time("serialize step");
    const autosaveData = this.generateAutosaveData(project);
    if (this.ENABLE_PERF_TIMERS) console.timeEnd("serialize step");

    if (this.ENABLE_PERF_TIMERS) console.time("localforage step");
    this.addAutosaveToList(autosaveData, () => {
      this.writeAutosaveData(autosaveData, () => {
        if (this.ENABLE_PERF_TIMERS) console.timeEnd("localforage step");
        callback();
      });
    });
  }

  /**
   * Loads a given project from localforage.
   * @param uuid - the UUID of the project to load from the AutoSave system
   * @param callback - callback function that receives the loaded project
   */
  static load(uuid: string, callback: (project: any) => void): void {
    this.readAutosaveData(uuid, (autosaveData: AutoSaveData) => {
      this.generateProjectFromAutosaveData(autosaveData, (project: any) => {
        callback(project);
      });
    });
  }

  /**
   * Deletes a project with a given UUID in the autosaves.
   * @param uuid - uuid of project to delete
   * @param callback - callback function to execute after deletion
   */
  static delete(uuid: string, callback: () => void): void {
    this.removeAutosaveFromList(uuid, () => {
      this.deleteAutosaveData(uuid, () => {
        callback();
      });
    });
  }

  /**
   * Generates an object that is writable to localforage from a project.
   * @param project - The project to generate data for
   */
  private static generateAutosaveData(project: any): AutoSaveData {
    if (this.ENABLE_PERF_TIMERS) console.time("generate objects list");
    const objects = (window.Wick.ObjectCache as any).getActiveObjects(project);
    if (this.ENABLE_PERF_TIMERS) console.timeEnd("generate objects list");

    if (this.ENABLE_PERF_TIMERS) console.time("serialize objects list");
    const projectData = project.serialize();
    const objectsData = objects.map((object: any) => object.serialize());
    if (this.ENABLE_PERF_TIMERS) console.timeEnd("serialize objects list");

    return {
      uuid: projectData.uuid,
      name: projectData.name,
      lastModified: projectData.metadata.lastModified,
      projectData,
      objectsData,
    };
  }

  private static addAutosaveToList(
    data: AutoSaveData,
    callback: () => void
  ): void {
    // Implementation details would go here
    callback();
  }

  private static writeAutosaveData(
    data: AutoSaveData,
    callback: () => void
  ): void {
    // Implementation details would go here
    callback();
  }

  private static readAutosaveData(
    uuid: string,
    callback: (data: AutoSaveData) => void
  ): void {
    // Implementation details would go here
    callback({
      uuid: "",
      name: "",
      lastModified: Date.now(),
      projectData: {},
      objectsData: [],
    });
  }

  private static generateProjectFromAutosaveData(
    data: AutoSaveData,
    callback: (project: any) => void
  ): void {
    // Implementation details would go here
    callback({});
  }

  private static removeAutosaveFromList(
    uuid: string,
    callback: () => void
  ): void {
    // Implementation details would go here
    callback();
  }

  private static deleteAutosaveData(uuid: string, callback: () => void): void {
    // Implementation details would go here
    callback();
  }
}

// Add to global Wick namespace
if (typeof window !== "undefined") {
  window.Wick = window.Wick || {};
  window.Wick.AutoSave = AutoSave;
}
