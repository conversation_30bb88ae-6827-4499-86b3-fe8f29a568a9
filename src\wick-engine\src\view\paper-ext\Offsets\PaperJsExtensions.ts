import * as paper from "paper";
import { MY_EPSILON, ONE_THIRD, TWO_THIRD } from "./Consts";

declare module "paper" {
  interface Point {
    Lerp(other: paper.Point, percentage: number): paper.Point;
  }

  interface Curve {
    SimpleOffset(startoffset: number, endoffset?: number): paper.Curve;
    RecursiveDivide(flattolerance: number): paper.Curve[];
    GetPart(start: number, end: number): paper.Curve;
    SplitAtTimes(timelist: number | number[]): paper.Curve[];
    ComplexOffset(startoffset: number, endoffset?: number): paper.Path | null;
    SplitWhereRadiusIsLargerThan(
      startoffset: number,
      endoffset?: number
    ): paper.Curve[];
    AnyChanceBBoxWillIntersect(
      othercurve: paper.Curve,
      expandamount: number
    ): boolean;
    GetShortestDistanceBetween(othercurve: paper.Curve): {
      Distance: number;
      MyPos: paper.Point;
      OtherPos: paper.Point;
    };
  }
}

paper.Point.inject({
  Lerp(other: paper.Point, percentage: number): paper.Point {
    if (this.equals(other)) {
      return other;
    }
    return this.add(other.subtract(this).multiply(percentage));
  },
});

paper.Curve.inject({
  SimpleOffset(
    startoffset: number,
    endoffset: number | null = null
  ): paper.Curve {
    if (endoffset != null) {
      const c1 = this.SimpleOffset(startoffset);
      const c2 = this.SimpleOffset(endoffset);
      return new paper.Curve(c1.segment1, c2.segment2);
    }
    if (startoffset === 0) {
      return this.clone();
    }
    if (this.isStraight()) {
      const norm = this.getNormalAtTime(0).normalize(startoffset);
      return GetCurveLine(this.point1.add(norm), this.point2.add(norm));
    }
    const offset1 = this.handle1.rotate(-90).normalize(startoffset);
    const offset2 = this.point2
      .subtract(this.point1)
      .rotate(-90)
      .normalize(startoffset);
    const offset3 = this.handle2.rotate(90).normalize(startoffset);
    const globalhandle1 = this.point1.add(this.handle1);
    const globalhandle2 = this.point2.add(this.handle2);
    const l1 = new paper.Line(
      this.point1.add(offset1),
      globalhandle1.add(offset1)
    );
    const l2 = new paper.Line(
      globalhandle1.add(offset2),
      globalhandle2.add(offset2)
    );
    const l3 = new paper.Line(
      this.point2.add(offset3),
      globalhandle2.add(offset3)
    );
    const p1 = l1.getPoint();
    const p2 = l3.getPoint();
    const h1 = l1.intersect(l2, true).subtract(p1);
    const h2 = l2.intersect(l3, true).subtract(p2);
    return new paper.Curve(p1, h1, h2, p2);
  },

  RecursiveDivide(flattolerance: number): paper.Curve[] {
    if (this.bounds.center.isClose(this.getPointAtTime(0.5), flattolerance)) {
      return [this.clone()];
    }
    const halves = this.SplitAtTimes([0.5]);
    return halves[0]
      .RecursiveDivide(flattolerance)
      .concat(halves[1].RecursiveDivide(flattolerance));
  },

  GetPart(start: number, end: number): paper.Curve {
    if (start !== 0 && end !== 1) {
      return this.SplitAtTimes([start, end])[1];
    }
    if (start !== 0) {
      return this.SplitAtTimes([start])[1];
    }
    if (end !== 1) {
      return this.SplitAtTimes([end])[0];
    }
    return this.clone();
  },

  SplitAtTimes(timelist: number | number[]): paper.Curve[] {
    const curvelist: paper.Curve[] = [];
    if (!Array.isArray(timelist)) {
      timelist = [timelist];
    }
    timelist.sort((a, b) => a - b);

    if (this.isStraight()) {
      timelist.splice(0, 0, 0);
      timelist.push(1);
      for (let i = 0; i < timelist.length - 1; i++) {
        const p1 = this.getPointAtTime(timelist[i]);
        const p2 = this.getPointAtTime(timelist[i + 1]);
        curvelist.push(GetCurveLine(p1, p2));
      }
    } else {
      curvelist.push(this.clone());
      let index = 0;
      for (let i = 0, prevT: number, l = timelist.length; i < l; i++) {
        const t = timelist[i];
        const curve = curvelist[index].divideAtTime(
          i ? (t - prevT) / (1 - prevT) : t
        );
        prevT = t;
        if (curve) {
          curvelist.splice(++index, 0, curve);
        }
      }
    }
    return curvelist;
  },

  ComplexOffset(
    startoffset: number,
    endoffset: number | null = null
  ): paper.Path | null {
    const usesmoothing = endoffset == null || endoffset !== startoffset;
    if (endoffset == null) {
      endoffset = startoffset;
    }
    const brokencurves = this.SplitWhereRadiusIsLargerThan(
      startoffset,
      endoffset
    );
    const trimedcurves = TrimCurves(brokencurves, startoffset, endoffset, 1);
    const initaloffsets = GetOffsetsForCurves(
      trimedcurves,
      startoffset,
      endoffset
    );
    const tpaths: paper.Path[] = [];
    const finalcurves: paper.Curve[] = [];

    for (let i = 0; i < trimedcurves.length; i++) {
      const microcurves = trimedcurves[i].RecursiveDivide(0.1);
      const offsets = GetOffsetsForCurves(
        microcurves,
        initaloffsets[i][0],
        initaloffsets[i][1]
      );
      for (let ii = 0; ii < microcurves.length; ii++) {
        microcurves[ii] = microcurves[ii].SimpleOffset(
          offsets[ii][0],
          offsets[ii][1]
        );
        tpaths.push(MakePathFromCurves(microcurves[ii]));
      }
      finalcurves.push(...microcurves);
    }

    if (finalcurves.length === 0) {
      return null;
    }

    const pp = MakePathFromCurves(finalcurves);
    if (usesmoothing) {
      pp.simplify(LINE_TOLERANCE);
    }
    return pp;
  },

  SplitWhereRadiusIsLargerThan(
    startoffset: number,
    endoffset: number | null = null
  ): paper.Curve[] {
    if (endoffset == null) {
      endoffset = startoffset;
    }
    const initialpeaktimes = (paper.Curve as any).getPeaks(this.values);
    const peaktimes: number[] = [];

    for (let i = 0; i < initialpeaktimes.length; i++) {
      const radiuscurviture =
        1 / Lerp(startoffset, endoffset, initialpeaktimes[i]);
      const curvecurviture = this.getCurvatureAtTime(initialpeaktimes[i]);
      if (
        Math.sign(radiuscurviture) !== Math.sign(curvecurviture) &&
        Math.abs(curvecurviture) >= Math.abs(radiuscurviture) &&
        curvecurviture !== 0
      ) {
        peaktimes.push(initialpeaktimes[i]);
      }
    }
    return this.SplitAtTimes(peaktimes);
  },

  AnyChanceBBoxWillIntersect(
    othercurve: paper.Curve,
    expandamount: number
  ): boolean {
    expandamount = Math.abs(expandamount);
    const bb1 = this.bounds.expand(expandamount);
    const bb2 = othercurve.bounds.expand(expandamount);
    return bb1.intersects(bb2);
  },

  GetShortestDistanceBetween(othercurve: paper.Curve): {
    Distance: number;
    MyPos: paper.Point;
    OtherPos: paper.Point;
  } {
    const chosencurve = Number(othercurve.length < this.length);
    const [c1, c2] = chosencurve ? [othercurve, this] : [this, othercurve];

    let bestdistance = Number.MAX_VALUE;
    let bestpointc1: paper.Point | null = null;
    let bestpointc2: paper.Point | null = null;

    for (let i = 0; i <= 1.0; i += 0.1) {
      const c1point = c1.getPointAtTime(i);
      const c2point = c2.getNearestPoint(c1point);
      const distance = c2point.getDistance(c1point);

      if (distance < bestdistance) {
        bestdistance = distance;
        bestpointc1 = c1point;
        bestpointc2 = c2point;
      }
    }

    return {
      Distance: bestdistance,
      MyPos: chosencurve ? bestpointc2! : bestpointc1!,
      OtherPos: chosencurve ? bestpointc1! : bestpointc2!,
    };
  },
});
