/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

import { FileAsset } from "./FileAsset";
import { Howl } from "howler";

interface PlayOptions {
  seekMS?: number;
  volume?: number;
  loop?: boolean;
}

export class SoundAsset extends FileAsset {
  protected _waveform: HTMLImageElement | null;
  protected _howlInstance: Howl | null;

  /**
   * Returns valid MIME types for a Sound Asset.
   * @returns {string[]} Array of strings representing MIME types in the form audio/Subtype.
   */
  static getValidMIMETypes(): string[] {
    const mp3Types = [
      "audio/mp3",
      "audio/mpeg3",
      "audio/x-mpeg-3",
      "audio/mpeg",
      "video/mpeg",
      "video/x-mpeg",
    ];
    const oggTypes = ["audio/ogg", "video/ogg", "application/ogg"];
    const wavTypes = [
      "audio/wave",
      "audio/wav",
      "audio/x-wav",
      "audio/x-pn-wav",
    ];
    return mp3Types.concat(oggTypes).concat(wavTypes);
  }

  /**
   * Returns valid extensions for a sound asset.
   * @returns {string[]} Array of strings representing valid
   */
  static getValidExtensions(): string[] {
    return [".mp3", ".ogg", ".wav"];
  }

  /**
   * Creates a new SoundAsset.
   * @param {object} args - Asset constructor args. see constructor for Wick.Asset
   */
  constructor(args: FileAssetArgs = {}) {
    super(args);
    this._waveform = null;
    this._howlInstance = null;
  }

  protected _serialize(args?: SerializeArgs): Record<string, any> {
    const data = super._serialize(args);
    return data;
  }

  protected _deserialize(data: Record<string, any>): void {
    super._deserialize(data);
  }

  public get classname(): string {
    return "SoundAsset";
  }

  /**
   * Plays this asset's sound.
   * @param {PlayOptions} options - Options for playing the sound
   * @return {number} The id of the sound instance that was played.
   */
  public play(options?: PlayOptions): number {
    if (!options) options = {};
    if (options.seekMS === undefined) options.seekMS = 0;
    if (options.volume === undefined) options.volume = 1.0;
    if (options.loop === undefined) options.loop = false;

    // don't do anything if the project is muted...
    if (this.project.muted) {
      return 0;
    }

    const id = this._howl.play();

    this._howl.seek(options.seekMS / 1000, id);
    this._howl.volume(options.volume, id);
    this._howl.loop(options.loop, id);

    return id;
  }

  /**
   * Stops this asset's sound.
   * @param {number} id - (optional) the ID of the instance to stop. If ID is not given, every instance of this sound will stop.
   */
  public stop(id?: number): void {
    // Howl instance was never created, sound has never played yet, so do nothing
    if (!this._howl) {
      return;
    }

    if (id === undefined) {
      this._howl.stop();
    } else {
      this._howl.stop(id);
    }
  }

  /**
   * The length of the sound in seconds
   * @type {number}
   */
  public get duration(): number {
    return this._howl.duration();
  }

  /**
   * A list of frames that use this sound.
   * @returns {Wick.Frame[]}
   */
  public getInstances(): Wick.Frame[] {
    const frames: Wick.Frame[] = [];
    this.project.getAllFrames().forEach((frame) => {
      if (frame._soundAssetUUID === this.uuid) {
        frames.push(frame);
      }
    });
    return frames;
  }

  /**
   * Check if there are any objects in the project that use this asset.
   * @returns {boolean}
   */
  public hasInstances(): boolean {
    return this.getInstances().length > 0;
  }

  /**
   * Remove the sound from any frames in the project that use this asset as their sound.
   */
  public removeAllInstances(): void {
    this.getInstances().forEach((frame) => {
      frame.removeSound();
    });
  }

  /**
   * Loads data about the sound into the asset.
   * @param {function} callback - function to call when the data is done being loaded.
   */
  public load(callback: () => void): void {
    this._generateWaveform(() => {
      this._waitForHowlLoad(() => {
        callback();
      });
    });
  }

  /**
   * Image of the waveform of this sound.
   * @type {HTMLImageElement}
   */
  public get waveform(): HTMLImageElement | null {
    return this._waveform;
  }

  protected get _howl(): Howl {
    // Lazily create howler instance
    if (!this._howlInstance) {
      // This fixes OGGs in firefox, as video/ogg is sometimes set as the MIMEType, which Howler doesn't like.
      let srcFixed = this.src;
      if (srcFixed) {
        srcFixed = srcFixed.replace("video/ogg", "audio/ogg");
      }

      this._howlInstance = new Howl({
        src: [srcFixed || ""],
      });
    }

    return this._howlInstance;
  }

  protected _waitForHowlLoad(callback: () => void): void {
    if (this._howl.state() === "loaded") {
      callback();
    } else {
      this._howl.on("load", () => {
        callback();
      });
    }
  }

  protected _generateWaveform(callback: () => void): void {
    if (this._waveform) {
      callback();
      return;
    }

    const soundSrc = this.src;

    if (!soundSrc) {
      console.log("error", this, soundSrc);
      return;
    }

    const scwf = new SCWF();
    scwf.generate(soundSrc, {
      onComplete: (png: string, pixels: any) => {
        this._waveform = new Image();
        this._waveform.onload = () => {
          callback();
        };
        this._waveform.src = png;
      },
    });
  }
}
