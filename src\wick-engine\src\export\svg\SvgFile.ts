/*
 * Copyright 2020 WICKLETS LLC
 *
 * This file is part of Wick Engine.
 *
 * Wick Engine is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * Wick Engine is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Wick Engine.  If not, see <https://www.gnu.org/licenses/>.
 */

declare global {
  interface Window {
    Wick: any;
  }
}

interface SvgFileOptions {
  width?: number;
  height?: number;
  onProgress?: (frame: number, maxFrames: number) => void;
  onFinish: (svgString: string) => void;
}

export class SvgFile {
  /**
   * Create an SVG file from a project.
   * @param project - The project to create an SVG from
   * @param options - Configuration options for SVG generation
   */
  static toSVGFile(project: any, options: SvgFileOptions): void {
    const { onProgress, onFinish } = options;

    project.generateSVGSequence({
      width: options.width,
      height: options.height,
      onFinish: (svgData: string) => {
        onFinish(svgData);
      },
      onProgress: onProgress,
    });
  }
}

// Add to global Wick namespace
if (typeof window !== "undefined") {
  window.Wick = window.Wick || {};
  window.Wick.SvgFile = SvgFile;
}
